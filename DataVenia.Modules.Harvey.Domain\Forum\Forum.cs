﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.Forum;
public sealed class Forum : Entity
{
    public readonly static Forum Other = Create("Other", "Outro").Value;
    public string Id { get; private set; }
    public string DisplayName { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    private Forum() { }

    public static Result<Forum> Create(string name, string displayName)
    {
        return new Forum() { DisplayName = displayName, Id = name, CreatedAt = DateTime.UtcNow };
    }
}
