﻿using System.Net;
using System.Net.Http.Json;
using System.Text;
using DataVenia.Common.Domain;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Infrastructure.Identity;

public sealed class KeyCloakClient
{
    private readonly HttpClient _httpClient;
    private readonly KeyCloakOptions _options;
    private readonly ILogger<KeyCloakClient> _logger;

    public string PublicClientId { get; private set; }

    public KeyCloakClient(HttpClient httpClient, KeyCloakOptions options, ILogger<KeyCloakClient> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _options = options ?? throw new ArgumentNullException(nameof(options));
        _logger = logger;

        PublicClientId = options.PublicClientId ?? throw new NullReferenceException(nameof(options.PublicClientId));

        if (!string.IsNullOrWhiteSpace(options.AdminUrl))
        {
            _httpClient.BaseAddress = new Uri(options.AdminUrl);
        }
    }

    public async Task<Result<string>> RegisterUserAsync(UserRepresentation user,
        CancellationToken cancellationToken = default)
    {
        try
        {
            HttpResponseMessage httpResponseMessage = await _httpClient.PostAsJsonAsync(
                "users",
                user,
                cancellationToken);

            httpResponseMessage.EnsureSuccessStatusCode();

            return ExtractIdentityIdFromLocationHeader(httpResponseMessage);
        }
        catch (HttpRequestException exception) when (exception.StatusCode == HttpStatusCode.Conflict)
        {
            return Result.Failure<string>(new Error("RegisterUser.InvalidEmail", "Email provided is already registered",
                ErrorType.Conflict));
        }
    }

    private static string ExtractIdentityIdFromLocationHeader(
        HttpResponseMessage httpResponseMessage)
    {
        const string usersSegmentName = "users/";

        string? locationHeader = httpResponseMessage.Headers.Location?.PathAndQuery;

        if (locationHeader is null)
        {
            throw new InvalidOperationException("Location header is null");
        }

        int userSegmentValueIndex = locationHeader.IndexOf(
            usersSegmentName,
            StringComparison.InvariantCultureIgnoreCase);

        string identityId = locationHeader.Substring(userSegmentValueIndex + usersSegmentName.Length);

        return identityId;
    }

    public async Task<UserRepresentation?> GetUserByEmailAsync(string email,
        CancellationToken cancellationToken = default)
    {
        string url = $"users?email={Uri.EscapeDataString(email)}";

        HttpResponseMessage httpResponseMessage = await _httpClient.GetAsync(url, cancellationToken);

        httpResponseMessage.EnsureSuccessStatusCode();

        List<UserRepresentation>? users =
            await httpResponseMessage.Content.ReadFromJsonAsync<List<UserRepresentation>>(
                cancellationToken: cancellationToken);

        return users?.FirstOrDefault();
    }

    public async Task<Result<KeycloakTokenResponse>> AuthenticateAsync(FormUrlEncodedContent requestContent,
        CancellationToken cancellationToken)
    {   
        using var authRequest = new HttpRequestMessage(HttpMethod.Post, new Uri(_options.TokenUrl));

        authRequest.Content = requestContent;

        try
        {
            using HttpResponseMessage authenticationResponse =
                await _httpClient.SendAsync(authRequest, cancellationToken);

            authenticationResponse.EnsureSuccessStatusCode();

            KeycloakTokenResponse? tokens =
                await authenticationResponse.Content.ReadFromJsonAsync<KeycloakTokenResponse>(
                    cancellationToken: cancellationToken);

            if (tokens?.AccessToken != null)
                return tokens;

            return Result.Failure<KeycloakTokenResponse>(new Error("Unauthorized", "Unable to authenticate user",
                ErrorType.Validation));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error authenticating user {@RequestContent}", requestContent);
            return Result.Failure<KeycloakTokenResponse>(new Error("Internal.Server.Error",
                "Unable to authenticate user",
                ErrorType.InternalServerError));
        }
    }

    public async Task<Result> ResetPasswordAsync(Guid userId, string password,
        CancellationToken cancellationToken)
    {
        string url = $"{_httpClient.BaseAddress}users/{userId}/reset-password";
        using var authRequest = new HttpRequestMessage(HttpMethod.Put, new Uri(url));

        authRequest.Content =
            new StringContent($"{{ \"type\": \"password\", \"temporary\": false, \"value\": \"{password}\" }}", 
                Encoding.UTF8, 
                "application/json");

        try
        {
            HttpResponseMessage resetPasswordResponse = await _httpClient.SendAsync(authRequest, cancellationToken);

            if (resetPasswordResponse.IsSuccessStatusCode)
                return Result.Success();

            return Result.Failure(new Error("Reset.Password.Fail", "Failed to reset password",
                ErrorType.InternalServerError));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error to reset password for userId: {UserId}", userId);
            return Result.Failure(new Error("Reset.Password.Fail", "Failed to reset password",
                ErrorType.InternalServerError));
        }
    }
}
