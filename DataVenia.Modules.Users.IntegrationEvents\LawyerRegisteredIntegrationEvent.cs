﻿using DataVenia.Common.Application.EventBus;
using OabDomain = DataVenia.Modules.Users.Domain.Oab.Oab;

namespace DataVenia.Modules.Users.IntegrationEvents;

public sealed class LawyerRegisteredIntegrationEvent : IntegrationEvent
{
    public LawyerRegisteredIntegrationEvent(
        Guid id,
        DateTime occurredOnUtc,
        Guid lawyerId,
        string email,
        string firstName,
        string lastName,
        string? oab,
        string? rg,
        string? cpf,
        string? cnh, 
        string? ctps,
        string? passport, 
        string? pis,
        string? voterId) : base(id, occurredOnUtc)
    {
        LawyerId = lawyerId;
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        Oab = oab;
        Rg = rg;
        Cpf = cpf;
        Cnh = cnh;
        Ctps = ctps;
        Passport = passport;
        Pis = pis;
        VoterId = voterId;
    }

    public Guid LawyerId { get; init; }
    public string Email { get; init; }
    public string FirstName { get; init; }
    public string LastName { get; init; }
    public string? Oab { get; init; }
    public string? Rg { get; init; }
    public string? Cpf { get; init; }
    public string? Cnh { get; init; }
    public string? Ctps { get; init; }
    public string? Passport { get; init; }
    public string? Pis { get; init; }
    public string? VoterId { get; init; }
}
