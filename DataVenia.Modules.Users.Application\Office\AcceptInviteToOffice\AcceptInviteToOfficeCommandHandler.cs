﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Office.AcceptInviteToOffice;
public sealed class AcceptInviteToOfficeCommandHandler(
    IOfficeUserRepository officeLawyerRepository,
    IUnitOfWork unitOfWork,
    ILogger<AcceptInviteToOfficeCommandHandler> logger) : ICommandHandler<AcceptInviteToOfficeCommand>
{
    public async Task<Result> Handle(AcceptInviteToOfficeCommand request, CancellationToken cancellationToken)
    {
        OfficeUser? invite = null;

        try
        {
            invite = await officeLawyerRepository.GetSingleByFilterAsync(
                lcl => lcl.UserId == request.lawyerId && lcl.OfficeId == request.officeId, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting invite. {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }
        

        if (invite == null || invite.InvitationStatus != InvitationStatus.PENDING)
            return Result.Failure(OfficeUserErrors.UserCantAcceptInvite);

        invite.Accept();

        officeLawyerRepository.Update(invite);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        } catch (Exception ex)
        {
            logger.LogError(ex, "Error saving officeLawyer to database. {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return Result.Success();
    }
}
