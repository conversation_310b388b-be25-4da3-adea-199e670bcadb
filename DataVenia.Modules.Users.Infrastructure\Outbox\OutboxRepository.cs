﻿using DataVenia.Modules.Users.Domain.Outbox;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Users.Infrastructure.Outbox;

public sealed class OutboxRepository : IOutboxRepository
{
    private readonly UsersDbContext _context;

    public OutboxRepository(UsersDbContext context)
    {
        _context = context;
    }

    public void Insert(Domain.Outbox.Outbox message)
    {
        _context.Set<Domain.Outbox.Outbox>().Add(message);
    }

    public async Task<IEnumerable<Domain.Outbox.Outbox>> GetUnprocessedMessagesAsync(CancellationToken cancellationToken)
    {
        return await _context.Set<Domain.Outbox.Outbox>()
            .Where(m => !m.Processed)
            .ToListAsync(cancellationToken);
    }

    public void MarkAsProcessed(Domain.Outbox.Outbox message)
    {
        message.Processed = true;
        message.ProcessedAt = DateTime.UtcNow;
        _context.Set<Domain.Outbox.Outbox>().Update(message);
    }
}
