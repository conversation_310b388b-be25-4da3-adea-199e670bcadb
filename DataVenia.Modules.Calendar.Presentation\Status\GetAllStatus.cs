﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Status.GetAllStatus;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Calendar.Presentation.Status;

internal sealed class GetAllStatus : IEndpoint
{
    private readonly ILogger<GetAllStatus> _logger;

    public GetAllStatus(ILogger<GetAllStatus> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        // passar officeId
        routeBuilder.MapGet("/offices/{officeId}/status/appointments", async (Guid officeId, ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<StatusResponse>> result = await sender.Send(new GetAllStatusQuery(officeId));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statuses");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("office:status:read")
        .WithTags(Tags.Calendar);
    }
}
