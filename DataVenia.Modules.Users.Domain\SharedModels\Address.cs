﻿using DataVenia.Modules.Users.Domain.Lawyers;
using OfficeDomain = DataVenia.Modules.Users.Domain.Office.Office;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
namespace DataVenia.Modules.Users.Domain.SharedModels;
public sealed class Address
{
    public Guid Id { get; set; }
    public string Neighborhood { get; set; }
    public string City { get; set; }
    public string Street { get; set; }
    public string PostalCode { get; set; }
    public string State { get; set; }
    public string Complement { get; set; }

    public Guid? ClientId { get; set; }
    public ClientDomain Client { get; set; }

    public Guid? OfficeId { get; set; }
    public OfficeDomain Office { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}
