﻿using DataVenia.Common.Application.Messaging;

namespace DataVenia.Modules.AssociationHub.Application.Associations.CreateAssociation;

public sealed record CreateAssociationCommand(
    string EntityType,
    Guid EntityId,
    IReadOnlyCollection<AssociationTarget> Associations) : ICommandFr;

public sealed record AssociationTarget(
    string TargetEntityType,
    IReadOnlyCollection<Guid> TargetEntityIds);
