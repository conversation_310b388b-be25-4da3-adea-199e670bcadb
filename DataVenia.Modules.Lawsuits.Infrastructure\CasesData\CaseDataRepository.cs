﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Cases;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.CasesData;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.CasesData;

public class CaseDataRepository(LawsuitsDbContext context) : ICaseDataRepository
{
    public void Insert(CaseData caseData)
    {
        context.CaseDatas.Add(caseData);
    }

    public async Task<Result<CaseData?>> GetLatestByCaseIdAsync(Guid caseId, CancellationToken cancellationToken)
    {
        CaseData? @case = await context.CaseDatas
            .Where(l => l.CaseId == caseId)
            .OrderByDescending(l => l.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);  // Fetch the first matching record or return null if none found

        return @case;
    }

    public void Update(CaseData caseData)
    {
        context.CaseDatas.Update(caseData);
    }
}
