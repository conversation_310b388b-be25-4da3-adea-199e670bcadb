using System.Globalization;
using System.Runtime.Serialization.Json;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace DataVenia.Common.Domain.Helpers;

public static class JsonHelper
{
    private static JsonSerializerOptions CustomJsonSerializerOptions { get; } = new()
    {
        PropertyNameCaseInsensitive = true,
        Converters = { new CustomDateTimeConverter() }
    };

    public static string Serialize<T>(T obj)
    {
        return JsonSerializer.Serialize(obj);
    }

    public static FluentResults.Result<T> Deserialize<T>(string? json) where T : class
    {
        try
        {
            if (string.IsNullOrWhiteSpace(json))
                return FluentResults.Result.Fail<T>("Error deserializing payload: Payload is null");

            var obj = JsonSerializer.Deserialize<T>(json, CustomJsonSerializerOptions);

            return obj == null
                ? FluentResults.Result.Fail<T>("Error deserializing payload")
                : FluentResults.Result.Ok(obj);
        }
        catch (Exception ex)
        {
            return FluentResults.Result.Fail($"Error {ex.Message}");
        }
    }
}
public class CustomDateTimeConverter : JsonConverter<DateTime?>
{
    private static readonly string[] Formats = new[]
    {
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss.fffZ",
        "yyyy-MM-ddTHH:mm:ssZ",
        "yyyy-MM-ddTHH:mm:ss",
        "o" // ISO 8601 format
    };

    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();

            if (string.IsNullOrWhiteSpace(stringValue))
                return null;

            if (DateTime.TryParseExact(stringValue, Formats, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var parsed))
            {
                return parsed;
            }

            if (DateTime.TryParse(stringValue, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out parsed))
            {
                return parsed;
            }

            throw new JsonException($"Unable to parse datetime: {stringValue}");
        }

        if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }

        return reader.GetDateTime();
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        if (value.HasValue)
        {
            writer.WriteStringValue(value.Value.ToUniversalTime().ToString("o")); // ISO 8601
        }
        else
        {
            writer.WriteNullValue();
        }
    }
}
