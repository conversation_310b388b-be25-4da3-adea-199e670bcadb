﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.CasesData;
using DataVenia.Modules.Lawsuits.Domain.CasesParties;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Infrastructure.CaseParty;

public sealed class CasePartyRepository(LawsuitsDbContext context, ILogger<CasePartyRepository> logger) : ICasePartyRepository
{
    public void Insert(Domain.CasesParties.CaseParty caseParty)
    {
        context.CaseParties.Update(caseParty);
    }

    public async Task<Result<IReadOnlyCollection<Domain.CasesParties.CaseParty>>> GetCasePartiesAsync(
            Guid caseId,
            CancellationToken cancellationToken = default)
    {
        try
        {
            List<Domain.CasesParties.CaseParty> caseParties = await context.Set<Domain.CasesParties.CaseParty>().
                Where(lp => context.Set<CaseData>()
                    .Where(ld => ld.CaseId == caseId)
                    .Select(ld => ld.Id)
                    .Contains(lp.CaseDataId))
                .ToListAsync(cancellationToken);

            return caseParties;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting case parties");
            return Result.Failure<IReadOnlyCollection<Domain.CasesParties.CaseParty>>(new Error("Internal.Server.Error",
                "Something weird happened.", ErrorType.InternalServerError));
        }
    }
}

