﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Lawsuits.Domain.DTOs;

namespace DataVenia.Modules.Lawsuits.Application.Cases.UpdateCase;
public sealed record UpdateCaseCommand(
    Guid UrlCaseId,
    string Title,
    Guid CaseId,
    Guid? FolderId,
    // List<PartyDto> Parties,
    string Description,
    string Observations,
    decimal CauseValue,
    decimal ConvictionValue,
    List<Guid> ResponsibleIds,
    string Access,
    Guid LawyerId,
    Guid OfficeId
): ICommand;
