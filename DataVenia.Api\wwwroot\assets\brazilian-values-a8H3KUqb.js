var f=function(r){return r.every(function(a){return r[0]===a})},m=/\D/g,i=function(r){return r.replace(m,"")},p=function(r){return i(r).split("").map(Number)},l=function(r,a){var e=[0,0];return a.reduce(function(t,o,c){var $=t[0],d=t[1];return[c===0?0:$+r[c-1]*o,d+r[c]*o]},e)},u=function(r){return r%11<2?0:11-r%11},v=/^(\d{11}|\d{3}\.\d{3}\.\d{3}\-\d{2})$/,T=function(r){if(!v.test(r))return!1;var a=p(r);if(f(a))return!1;var e=[11,10,9,8,7,6,5,4,3,2],t=l(a,e);return a[9]===u(t[0])&&a[10]===u(t[1])},g=function(r){return i(r).replace(/(\d{2})(\d)/,"$1.$2").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d)/,"$1/$2").replace(/(\d{4})(\d{1,2})$/,"$1-$2")},s=function(r){return i(r).replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d{1,2})$/,"$1-$2")},P=function(r){var a,e;return((e=(a=r.match(/\d/g))===null||a===void 0?void 0:a.length)!==null&&e!==void 0?e:0)<=11},h=function(r){return P(r)?s(r):g(r)},C=function(r,a){a===void 0&&(a=2);var e=i(r);if(e.length===8)return e.replace(/(^\d{4})(\d{4}$)/gi,"$1-$2");if(e.length===9)return e.replace(/(^\d{5})(\d{4}$)/gi,"$1-$2");if(e.length===10)return e.replace(/(^\d{2})(\d{4})(\d{4}$)/gi,"($1) $2-$3");if(e.length===11)return e.replace(/(^\d{2})(\d{4,5})(\d{4}$)/gi,"($1) $2-$3");if(e.length===12)return e.replace(/(^\d{3})(\d{5})(\d{4}$)/gi,"$1 $2-$3");var t=new RegExp("([0-9]{"+a+"})([0-9][0-9])([0-9]{5})([0-9]{4})","gi");return e.replace(t,"+$1 $2 $3-$4")};export{C as a,h as f,T as i};
