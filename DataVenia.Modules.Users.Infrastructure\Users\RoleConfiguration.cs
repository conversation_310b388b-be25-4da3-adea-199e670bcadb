﻿using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Users.Infrastructure.Users;

internal sealed class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.ToTable("role");

        builder.HasKey(r => r.Name);

        builder.Property(r => r.Name).HasMaxLength(50);

        builder
            .HasMany<OfficeUser>()
            .WithOne(ou => ou.Role)
            .HasForeignKey(ou => ou.RoleName);


        builder.HasData(
            Role.OfficeMember,
            Role.OfficeAdministrator,
            Role.OfficeClient,
            Role.SystemAdministrator,
            Role.SystemMember);
    }
}
