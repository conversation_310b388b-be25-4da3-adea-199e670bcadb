﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
public static class OfficeUserErrors
{
    public static readonly Error UserUnauthorizedToInvite = Error.Problem("OfficeLawyer.UserUnauthorized", "You can't invite this user to the office.");
    public static readonly Error UserCantAcceptInvite = Error.Conflict("UserCantAcceptInvite", "The invite cant be accepted by the user or it doesnt exist");
}
