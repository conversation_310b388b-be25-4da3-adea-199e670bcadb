{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "ConnectionStrings": {"Database": "Host=datavenia.Database;Port=5432;Database=datavenia;Username=********;Password=********;Include Error Detail=true;", "Cache": "localhost:6379"}, "Authentication": {"Audience": "account", "TokenValidationParameters": {"ValidIssuers": ["http://datavenia.identity:8080/realms/datavenia", "http://localhost:18080/realms/datavenia"]}, "MetadataAddress": "http://datavenia.identity:8080/realms/datavenia/.well-known/openid-configuration", "RequireHttpsMetadata": false}, "KeyCloak": {"HealthUrl": "http://datavenia.identity:8080/health/"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"serverUrl": "http://localhost:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "DataVenia.Api"}}}