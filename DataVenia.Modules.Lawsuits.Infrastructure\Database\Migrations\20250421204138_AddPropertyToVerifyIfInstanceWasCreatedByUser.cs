﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddPropertyToVerifyIfInstanceWasCreatedByUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "is_instance_created_by_user",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "lawsuit_step",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    lawsuit_id = table.Column<Guid>(type: "uuid", nullable: false),
                    legal_instance_id = table.Column<string>(type: "text", maxLength: 50, nullable: false),
                    title = table.Column<string>(type: "text", nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    occurred_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    action_by = table.Column<string>(type: "text", nullable: true),
                    secret = table.Column<bool>(type: "boolean", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_step", x => x.id);
                    table.ForeignKey(
                        name: "fk_lawsuit_step_lawsuit_lawsuit_id",
                        column: x => x.lawsuit_id,
                        principalSchema: "lawsuit",
                        principalTable: "lawsuit",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_step_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_step",
                column: "lawsuit_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "lawsuit_step",
                schema: "lawsuit");

            migrationBuilder.DropColumn(
                name: "is_instance_created_by_user",
                schema: "lawsuit",
                table: "lawsuit_data");
        }
    }
}
