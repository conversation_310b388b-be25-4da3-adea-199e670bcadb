using System.Collections.Concurrent;
using FluentResults;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.Infrastructure.Facades;

public class InMemoryBlobStorage : IBlobStorage
{
    private readonly ConcurrentDictionary<string, byte[]> _storage = new();
    private readonly ILogger<InMemoryBlobStorage> _logger;

    public InMemoryBlobStorage(ILogger<InMemoryBlobStorage> logger)
    {
        _logger = logger;
    }

    public async Task<Result> UploadAsync(string bucketName, Stream inputStream, string objectName, string contentType = "application/octet-stream")
    {
        try
        {
            if (inputStream == null)
                return Result.Fail("Input stream cannot be null.");

            using var memoryStream = new MemoryStream();
            await inputStream.CopyToAsync(memoryStream);
            _storage[objectName] = memoryStream.ToArray();

            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to upload object '{ObjectName}' to bucket '{BucketName}'.", objectName, bucketName);
            return Result.Fail($"Failed to upload: {ex.Message}");
        }
    }

    public async Task<Result> DownloadAsync(string bucketName, string objectName, Stream outputStream, CancellationToken cancellationToken)
    {
        try
        {
            if (outputStream == null)
                return Result.Fail("Output stream cannot be null.");

            if (!_storage.TryGetValue(objectName, out byte[] data))
                return Result.Fail($"Object '{objectName}' not found.");

            var ro = new ReadOnlyMemory<byte>(data);

            await outputStream.WriteAsync(ro, cancellationToken: cancellationToken);
            outputStream.Position = 0;

            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to download object '{ObjectName}' from bucket '{BucketName}'.", objectName, bucketName);
            return Result.Fail($"Failed to download: {ex.Message}");
        }
    }

    public Task<Result> DeleteAsync(string bucketName, string objectName)
    {
        if (_storage.TryRemove(objectName, out _))
        {
            return Task.FromResult(Result.Ok());
        }

        return Task.FromResult(Result.Fail($"Object '{objectName}' not found."));
    }

    public Task<Result<bool>> ExistsAsync(string bucketName, string objectName)
    {
        bool exists = _storage.ContainsKey(objectName);
        return Task.FromResult(Result.Ok(exists));
    }

    public Task<Result<IList<string>>> ListAsync(string bucketName, string? prefix = null)
    {
        try
        {
            IList<string> keys = string.IsNullOrEmpty(prefix)
                ? new List<string>(_storage.Keys)
                : new List<string>(_storage.Keys).FindAll(k => k.StartsWith(prefix, StringComparison.InvariantCulture));

            return Task.FromResult(Result.Ok(keys));
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to list objects in bucket '{BucketName}'.", bucketName);
            return Task.FromResult(Result.Fail<IList<string>>($"Failed to list objects: {ex.Message}"));
        }
    }
}

