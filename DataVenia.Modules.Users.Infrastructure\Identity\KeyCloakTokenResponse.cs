﻿using System.Text.Json.Serialization;

namespace DataVenia.Modules.Users.Infrastructure.Identity;
public sealed record KeycloakTokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; init; }

    [JsonPropertyName("expires_in")]
    public int AccessTokenExpiresIn { get; init; }

    [JsonPropertyName("refresh_expires_in")]
    public int RefreshTokenExpiresIn { get; init; }

    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; init; }

    [JsonPropertyName("token_type")]
    public string TokenType { get; init; }

    [JsonPropertyName("id_token")]
    public string IdToken { get; init; }
}
