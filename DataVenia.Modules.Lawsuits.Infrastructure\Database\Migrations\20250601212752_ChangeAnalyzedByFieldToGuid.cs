﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class ChangeAnalyzedByFieldToGuid : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // First, create a temporary column for the new UUID data
            migrationBuilder.AddColumn<Guid>(
                name: "analyzed_by_temp",
                schema: "lawsuit",
                table: "data_divergence",
                type: "uuid",
                nullable: true);

            // Convert existing text values to UUID where possible
            migrationBuilder.Sql(@"
                UPDATE lawsuit.data_divergence
                SET analyzed_by_temp = analyzed_by::uuid
                WHERE analyzed_by IS NOT NULL
                AND analyzed_by ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'");

            // Drop the old column
            migrationBuilder.DropColumn(
                name: "analyzed_by",
                schema: "lawsuit",
                table: "data_divergence");

            // Rename the temporary column to the original name
            migrationBuilder.RenameColumn(
                name: "analyzed_by_temp",
                schema: "lawsuit",
                table: "data_divergence",
                newName: "analyzed_by");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // First, create a temporary column for the text data
            migrationBuilder.AddColumn<string>(
                name: "analyzed_by_temp",
                schema: "lawsuit",
                table: "data_divergence",
                type: "text",
                nullable: true);

            // Convert existing UUID values to text
            migrationBuilder.Sql(@"
                UPDATE lawsuit.data_divergence
                SET analyzed_by_temp = analyzed_by::text
                WHERE analyzed_by IS NOT NULL");

            // Drop the old column
            migrationBuilder.DropColumn(
                name: "analyzed_by",
                schema: "lawsuit",
                table: "data_divergence");

            // Rename the temporary column to the original name
            migrationBuilder.RenameColumn(
                name: "analyzed_by_temp",
                schema: "lawsuit",
                table: "data_divergence",
                newName: "analyzed_by");
        }
    }
}
