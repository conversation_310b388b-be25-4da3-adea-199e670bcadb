namespace DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;

public sealed class EntityConfiguration
{
    public string EntityType { get; init; } = string.Empty;
    public string GetEndpointTemplate { get; init; } = string.Empty;
    public string PatchEndpointTemplate { get; init; } = string.Empty;
    public string AssociationPropertyName { get; init; } = "associations";
    public bool IsEnabled { get; init; } = true;
    public IReadOnlyCollection<string> AllowedAssociations { get; init; } = [];
    public AssociationMode AssociationMode { get; init; } = AssociationMode.Whitelist;

    public string BuildGetEndpoint(Guid entityId, params object[] parameters)
    {
        var allParams = new object[] { entityId }.Concat(parameters).ToArray();
        return string.Format(GetEndpointTemplate, allParams);
    }

    public string BuildPatchEndpoint(Guid entityId, params object[] parameters)
    {
        var allParams = new object[] { entityId }.Concat(parameters).ToArray();
        return string.Format(PatchEndpointTemplate, allParams);
    }

    public bool CanAssociateWith(string targetEntityType)
    {
        return AssociationMode switch
        {
            AssociationMode.Whitelist => AllowedAssociations.Contains(targetEntityType),
            AssociationMode.Blacklist => !AllowedAssociations.Contains(targetEntityType),
            AssociationMode.AllowAll => true,
            _ => false
        };
    }
}

public enum AssociationMode
{
    /// <summary>
    /// Only allow associations with entities in the AllowedAssociations list
    /// </summary>
    Whitelist,

    /// <summary>
    /// Allow associations with all entities except those in the AllowedAssociations list
    /// </summary>
    Blacklist,

    /// <summary>
    /// Allow associations with any entity type
    /// </summary>
    AllowAll
}

public sealed class EntityConfigurationRegistry
{
    private readonly Dictionary<string, EntityConfiguration> _configurations = new();

    public void Register(EntityConfiguration configuration)
    {
        _configurations[configuration.EntityType] = configuration;
    }

    public EntityConfiguration? GetConfiguration(string entityType)
    {
        return _configurations.TryGetValue(entityType, out var config) ? config : null;
    }

    public bool IsEntitySupported(string entityType)
    {
        return _configurations.ContainsKey(entityType) && _configurations[entityType].IsEnabled;
    }

    public bool CanAssociate(string sourceEntityType, string targetEntityType)
    {
        var sourceConfig = GetConfiguration(sourceEntityType);
        var targetConfig = GetConfiguration(targetEntityType);

        // Both entities must be supported
        if (sourceConfig == null || !sourceConfig.IsEnabled ||
            targetConfig == null || !targetConfig.IsEnabled)
        {
            return false;
        }

        // Check if source can associate with target
        return sourceConfig.CanAssociateWith(targetEntityType);
    }

    public IReadOnlyCollection<string> GetAllowedAssociations(string entityType)
    {
        var config = GetConfiguration(entityType);
        if (config == null || !config.IsEnabled)
        {
            return [];
        }

        return config.AssociationMode switch
        {
            AssociationMode.Whitelist => config.AllowedAssociations,
            AssociationMode.Blacklist => _configurations.Keys
                .Where(key => key != entityType && !config.AllowedAssociations.Contains(key))
                .ToList().AsReadOnly(),
            AssociationMode.AllowAll => _configurations.Keys
                .Where(key => key != entityType)
                .ToList().AsReadOnly(),
            _ => []
        };
    }

    public IReadOnlyCollection<string> GetForbiddenAssociations(string entityType)
    {
        var config = GetConfiguration(entityType);
        if (config == null || !config.IsEnabled)
        {
            return _configurations.Keys.ToList().AsReadOnly();
        }

        return config.AssociationMode switch
        {
            AssociationMode.Whitelist => _configurations.Keys
                .Where(key => key != entityType && !config.AllowedAssociations.Contains(key))
                .ToList().AsReadOnly(),
            AssociationMode.Blacklist => config.AllowedAssociations,
            AssociationMode.AllowAll => [],
            _ => _configurations.Keys.ToList().AsReadOnly()
        };
    }

    public IReadOnlyCollection<EntityConfiguration> GetAllConfigurations()
    {
        return _configurations.Values.ToList().AsReadOnly();
    }

    public AssociationValidationResult ValidateAssociations(string sourceEntityType, IEnumerable<string> targetEntityTypes)
    {
        var validTargets = new List<string>();
        var invalidTargets = new List<string>();
        var unsupportedTargets = new List<string>();

        foreach (var targetEntityType in targetEntityTypes)
        {
            if (!IsEntitySupported(targetEntityType))
            {
                unsupportedTargets.Add(targetEntityType);
                continue;
            }

            if (CanAssociate(sourceEntityType, targetEntityType))
            {
                validTargets.Add(targetEntityType);
            }
            else
            {
                invalidTargets.Add(targetEntityType);
            }
        }

        return new AssociationValidationResult(
            validTargets.AsReadOnly(),
            invalidTargets.AsReadOnly(),
            unsupportedTargets.AsReadOnly());
    }
}

public sealed record AssociationValidationResult(
    IReadOnlyCollection<string> ValidTargets,
    IReadOnlyCollection<string> InvalidTargets,
    IReadOnlyCollection<string> UnsupportedTargets)
{
    public bool IsValid => InvalidTargets.Count == 0 && UnsupportedTargets.Count == 0;
    public bool HasErrors => InvalidTargets.Count > 0 || UnsupportedTargets.Count > 0;
}
