<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <OutputType>Library</OutputType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.10"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DataVenia.Common.Presentation\DataVenia.Common.Presentation.csproj" />
      <ProjectReference Include="..\DataVenia.Modules.LawsuitSync.Application\DataVenia.Modules.LawsuitSync.Application.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="FluentResults">
        <HintPath>..\..\..\..\.nuget\packages\fluentresults\3.16.0\lib\netstandard2.1\FluentResults.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
