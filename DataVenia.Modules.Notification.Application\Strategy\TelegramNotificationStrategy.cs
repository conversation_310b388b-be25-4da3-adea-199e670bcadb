using DataVenia.Common.Domain;
using DataVenia.Modules.Notification.Domain.Infrastructure.Clients;
using DataVenia.Modules.Notification.Domain.Models.Telegram;

namespace DataVenia.Modules.Notification.Application.Strategy;

public sealed class TelegramNotificationStrategy(TelegramApiClient apiClient, string telegramBotToken)
    : INotificationStrategy
{
    public async Task<Result> SendNotificationAsync(string message, string recipient, string? subject = null)
    {
        var content = new TelegramSendMessageRequest
        {
            ChatId = recipient,
            Text = message
        };

        TelegramResponse? response = await apiClient.SendMessageAsync(telegramBotToken, content);

        return response?.Ok == true
            ? Result.Success()
            : Result.Failure(new Error("500", "Fail to send telegram notification", ErrorType.InternalServerError));
    }
}
