﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.LawsuitSync.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "lawsuit-sync");

            migrationBuilder.CreateTable(
                name: "tb_lawsuit_events_history",
                schema: "lawsuit-sync",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    lawsuit_monitoring_configuration_external_id = table.Column<Guid>(type: "uuid", nullable: false),
                    cnj = table.Column<string>(type: "text", nullable: false),
                    monitoring_type = table.Column<string>(type: "text", nullable: false),
                    lawsuit_status = table.Column<string>(type: "text", nullable: false),
                    event_date = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    @event = table.Column<string>(name: "event", type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_tb_lawsuit_events_history", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "tb_lawsuit_monitoring_configuration",
                schema: "lawsuit-sync",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    external_id = table.Column<Guid>(type: "uuid", nullable: false),
                    cnj = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    last_update = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    lawsuit_sync_source = table.Column<string>(type: "text", nullable: false),
                    monitoring_type = table.Column<string>(type: "text", nullable: false),
                    current_status = table.Column<string>(type: "text", nullable: false),
                    platform = table.Column<string>(type: "text", nullable: true),
                    court_division = table.Column<string>(type: "text", nullable: true),
                    legal_instance = table.Column<string>(type: "text", nullable: true),
                    schedule_cron_expression = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_tb_lawsuit_monitoring_configuration", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_tb_lawsuit_monitoring_configuration_cnj",
                schema: "lawsuit-sync",
                table: "tb_lawsuit_monitoring_configuration",
                column: "cnj");

            migrationBuilder.CreateIndex(
                name: "ix_tb_lawsuit_monitoring_configuration_external_id",
                schema: "lawsuit-sync",
                table: "tb_lawsuit_monitoring_configuration",
                column: "external_id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "tb_lawsuit_events_history",
                schema: "lawsuit-sync");

            migrationBuilder.DropTable(
                name: "tb_lawsuit_monitoring_configuration",
                schema: "lawsuit-sync");
        }
    }
}
