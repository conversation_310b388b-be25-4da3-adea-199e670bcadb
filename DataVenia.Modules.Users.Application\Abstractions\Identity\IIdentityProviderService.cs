﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.User.Login;

namespace DataVenia.Modules.Users.Application.Abstractions.Identity;
public interface IIdentityProviderService
{
    Task<Result<string>> RegisterUserAsync(UserModel user, CancellationToken cancellationToken = default);
    Task<Result<string?>> GetUserIdByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<Result<LoginResponse>> AuthenticateAsync(string username, string password, CancellationToken cancellationToken);
    Task<Result> ResetPasswordAsync(Guid identityId, string password, CancellationToken cancellationToken);
    Task<Result<LoginResponse>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken);
}
