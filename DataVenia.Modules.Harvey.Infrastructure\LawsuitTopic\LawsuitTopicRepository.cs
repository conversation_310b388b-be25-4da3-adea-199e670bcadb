﻿using DataVenia.Modules.Harvey.Domain.LawsuitTopic;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Harvey.Infrastructure.LawsuitTopic;

public sealed class LawsuitTopicRepository(HarveyDbContext context) : ILawsuitTopicRepository
{
    public async Task<IReadOnlyCollection<Domain.LawsuitTopic.LawsuitTopic>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await context.LawsuitTopics.ToListAsync(cancellationToken);
    }

    public async Task<Domain.LawsuitTopic.LawsuitTopic?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await context.LawsuitTopics.FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public void Add(Domain.LawsuitTopic.LawsuitTopic lawsuitTopic)
    {
        context.LawsuitTopics.Add(lawsuitTopic);
    }
}
