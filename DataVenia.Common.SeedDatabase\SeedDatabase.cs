﻿using DataVenia.Common.SeedDatabase.UsersModule;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.SeedDatabase;

public sealed class SeedDatabase
{
    private readonly SeedUsersModule _seedUsersModule;

    public SeedDatabase(IIdentityProviderService identityProviderService, UsersDbContext usersDbContext)
    {
        var loggerFactory = new LoggerFactory();
        ILogger<SeedUsersModule> logger = loggerFactory.CreateLogger<SeedUsersModule>();

        loggerFactory.Dispose();

        _seedUsersModule = new SeedUsersModule(identityProviderService, usersDbContext, logger);

    }
    public async Task SeedDatabaseAsync()
    {
        await _seedUsersModule.Seed();
    }
}
