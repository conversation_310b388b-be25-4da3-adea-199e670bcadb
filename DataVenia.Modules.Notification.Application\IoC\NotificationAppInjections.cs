using DataVenia.Modules.Notification.Application.Factories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DataVenia.Modules.Notification.Application.IoC;

public static class NotificationAppInjections
{
    public static IServiceCollection AddNotificationApp(this IServiceCollection services)
    {
        services.AddScoped<NotificationStrategyFactory>();

        return services;
    }
}
