﻿using System.Linq.Expressions;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.SignUpToken;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Infrastructure.SignUpTokens;

public class SignUpTokenRepository(UsersDbContext context, ILogger<SignUpTokenRepository> logger) : ISignUpTokenRepository
{
    public void Insert(SignUpToken signUpToken)
    {
        context.SignUpTokens.Add(signUpToken);
    }

    public async Task<SignUpToken?> GetSingleByFilterAsync(Expression<Func<SignUpToken, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.GetSingleByFilterAsync<SignUpToken>(filter, cancellationToken);
    }
    
    public async Task<Result<SignUpToken>> GetSingleByTokenAsync(Guid token, CancellationToken cancellationToken = default)
    {
        try
        {
            
            SignUpToken? retrievedToken = await context.SignUpTokens
            .Include(s => s.Lawyer)
            .SingleOrDefaultAsync(s => s.Token == token, cancellationToken);
            
            return retrievedToken ?? Result.Failure<SignUpToken>(new Error("SignUpToken.NotFound", "Sign up token not found", ErrorType.NotFound));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting sign up token by token: {@Token}", token);
            return Result.Failure<SignUpToken>(Error.InternalServerError());
        }
    }
    
    public Result Update(SignUpToken token)
    {
        try
        {
            context.SignUpTokens.Update(token);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating token: {@Token}", token);
            return Result.Failure<SignUpToken>(Error.InternalServerError());
        }
    }
}
