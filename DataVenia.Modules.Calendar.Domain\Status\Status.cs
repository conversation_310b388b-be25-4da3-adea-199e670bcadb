﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Calendar.Domain.Status;
public sealed class Status : Entity
{
    public Guid Id { get; private set; }
    public string DisplayName { get; private set; }
    public string? Description { get; private set; }
    public int OrderIndex { get; private set; }
    public Guid OfficeId { get; private set; }
    public DateTime CreatedAt { get; private set; }

    private Status() { }

    public static Result<Status> Create(string displayName, string description, int orderIndex, Guid officeId)
    {
        return new Status
        {
            Id = Guid.CreateVersion7(),
            DisplayName = displayName,
            Description = description,
            OrderIndex = orderIndex,
            OfficeId = officeId,
            CreatedAt = DateTime.UtcNow
        };
    }
}
