using DataVenia.Modules.LawsuitSync.Domain.Enums;

namespace DataVenia.Modules.LawsuitSync.Domain.Helpers;

public static class InstanceMapperHelper
{
    public static LegalInstances? MapInstanceFromCourtName(string courtName)
    {
        if(courtName.Equals(LegalInstances.TST.ToString(), StringComparison.OrdinalIgnoreCase))
            return LegalInstances.TST;
        if(courtName.Equals(LegalInstances.TSE.ToString(), StringComparison.OrdinalIgnoreCase))
            return LegalInstances.TSE;
        if(courtName.Equals(LegalInstances.STM.ToString(), StringComparison.OrdinalIgnoreCase))
            return LegalInstances.STM;
        if(courtName.Equals(LegalInstances.STF.ToString(), StringComparison.OrdinalIgnoreCase))
            return LegalInstances.STF;
        if(courtName.Equals(LegalInstances.STJ.ToString(), StringComparison.OrdinalIgnoreCase))
            return LegalInstances.STJ;

        return LegalInstances.Other;
    }

    /// <summary>
    /// Only can be used for FirstInstance and SecondInstance
    /// </summary>
    public static LegalInstances? MapInstanceFromInstanceNumber(string instance)
    {
        if (!int.TryParse(instance, out var instanceNumber))
            return null;

        return instanceNumber switch
        {
            1 => LegalInstances.FirstInstance,
            2 => LegalInstances.SecondInstance,
            _ => LegalInstances.Other
        };
    }
}
