﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using LawsuitResponsiblesDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitResponsibles.LawsuitResponsible;

namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitResponsibles;
internal sealed class LawsuitResponsibleConfiguration : IEntityTypeConfiguration<LawsuitResponsiblesDomain>
{
    public void Configure(EntityTypeBuilder<LawsuitResponsiblesDomain> builder)
    {
        builder.ToTable("lawsuit_responsible"); // Nome da tabela no banco de dados

        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        // Configurando a chave primária
        builder.HasKey(lu => new { LawsuitId = lu.LawsuitDataId, lu.LawyerId });

        // Relacionamento com LawsuitData
        builder.HasOne(lr => lr.LawsuitData)
               .WithMany(l => l.Responsibles) // Não configuramos Lawsuit para navegar pelos LawsuitUser
               .HasForeignKey(lu => lu.LawsuitDataId)
               .OnDelete(DeleteBehavior.NoAction);

        // Relacionamento com SystemUser
        builder.Property(lr => lr.LawyerId)
            .IsRequired();
    }
}
