﻿using System.Text.Json.Serialization;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;

public sealed class MonitoringHistoryEntry
{
    public DateTime? StartedAt { get; init; }
    public DateTime? StoppedAt { get; set; }
    public Guid? StartedBy { get; init; }
    public Guid? StoppedBy { get; set; }

    public MonitoringHistoryEntry(DateTime startedAt, Guid startedBy, DateTime? stoppedAt, Guid? stoppedBy)
    {
        StartedAt = startedAt;
        StartedBy = startedBy;
        StoppedAt = stoppedAt;
        StoppedBy = stoppedBy;
    }

    public MonitoringHistoryEntry(DateTime startedAt, Guid startedBy) : this(startedAt, startedBy, null, null) { }

    public MonitoringHistoryEntry() {}

public void SetStoppedAt(DateTime stoppedAt, Guid stoppedBy)
    {
        StoppedAt = stoppedAt;
        StoppedBy = stoppedBy;
    }
}
