using DataVenia.Common.Contracts.Harvey;
using DataVenia.Modules.Harvey.Domain.Action;

namespace DataVenia.Modules.Harvey.Infrastructure.Facades;

public sealed class LawsuitTypeFacade(ILawsuitTypeRepository lawsuitTypeRepository) : ILawsuitTypeFacade
{
    public async Task<bool> ExistsAsync(string lawsuitTypeId, CancellationToken cancellationToken = default)
    {
        var lawsuitType = await lawsuitTypeRepository.GetByIdAsync(lawsuitTypeId, cancellationToken);
        return lawsuitType != null;
    }
}
