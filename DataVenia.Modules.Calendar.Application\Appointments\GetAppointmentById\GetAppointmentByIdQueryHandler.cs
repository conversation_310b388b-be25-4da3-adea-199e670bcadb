﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Application.Appointments.GetAppointment;
using DataVenia.Modules.Calendar.Domain.Appointments;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Calendar.Application.Appointments.GetAppointmentById;

internal sealed class GetAppointmentByIdQueryHandler(
    IAppointmentRepository appointmentRepository, 
    ILogger<GetAppointmentByIdQueryHandler> logger)
    : IQueryHandler<GetAppointmentByIdQuery, AppointmentResponse?>
{
    public async Task<Result<AppointmentResponse?>> Handle(GetAppointmentByIdQuery request, CancellationToken cancellationToken)
    {
        Domain.Appointments.Appointment? appointment = null;

        try
        {
            // pega o appointment pelo Id, validando escritório e se o lawyer que requisitou é vinculado ao appointment
            appointment = await appointmentRepository.GetSingleAsync(
                x => x.Id == request.appointmentId 
                     && x.OwnerOfficeId == request.officeId 
                     && (x.OwnerLawyerId == request.userId 
                         || x.Participants.Any(y => y.LawyerId == request.userId) 
                         || x.ResponsibleLawyerId == request.userId)
                , cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "an error occurred while retrieving the appointment with id {AppointmentId}.", request.appointmentId);
        }

        if (appointment == null)
            return Result.Failure<AppointmentResponse?>(new Error("Not.Found", "The appointment was not found.",
                ErrorType.NotFound));

        return new AppointmentResponse(
            appointment.Id, 
            appointment.Type,
            appointment.Name,
            appointment.Description, 
            appointment.ResponsibleLawyerId,
            appointment.Recurrence, 
            appointment.OwnerLawyerId,
            appointment.Participants,
            appointment.Alerts,
            appointment.Status);
    }
}
