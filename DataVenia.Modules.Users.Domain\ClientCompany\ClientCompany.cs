﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Users.Domain.ClientCompany;

public class ClientCompany: Entity
{
    public Guid Id { get; private set; }
    public Guid ClientId { get; private set; }
    public Client.Client Client { get; private set; }
    public Guid CompanyId { get; private set; }
    public Company.Company Company { get; private set; }
    public ClientCompanyRole Role { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }

    public static Result<ClientCompany> Create(Guid clientId, Guid companyId, ClientCompanyRole role)
    {
        var clientCompany = new ClientCompany
        {
            Id = Guid.CreateVersion7(),
            ClientId = clientId,
            CompanyId = companyId,
            Role = role,
            CreatedAt = DateTime.UtcNow
        };
        return clientCompany;
    }

    public void Update(ClientCompanyRole? role)
    {
        Role = role ?? Role;
        UpdatedAt = DateTime.UtcNow;
    }
}
