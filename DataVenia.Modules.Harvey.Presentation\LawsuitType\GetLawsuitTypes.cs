﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.Action;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Harvey.Presentation.Action;

internal sealed class GetLawsuitTypes : IEndpoint
{
    private readonly ILogger<GetLawsuitTypes> _logger;

    public GetLawsuitTypes(ILogger<GetLawsuitTypes> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/lawsuit-types", async (string? displayName, [FromServices] ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<GetLawsuitTypesResponse>> result = await sender.Send(new GetLawsuitTypesQuery(displayName));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
           {
                _logger.LogError(ex, "Error getting lawsuit types");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}
