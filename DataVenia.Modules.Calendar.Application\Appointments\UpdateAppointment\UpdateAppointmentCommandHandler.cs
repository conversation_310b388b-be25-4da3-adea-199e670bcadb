﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Application.Abstractions.Data;
using DataVenia.Modules.Calendar.Domain.Appointments;
using AppointmentDomain = DataVenia.Modules.Calendar.Domain.Appointments.Appointment;

namespace DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointment;
public sealed class UpdateAppointmentCommandHandler(
    IAppointmentRepository appointmentRepository,
    IUnitOfWork unitOfWork) : ICommandHandler<UpdateAppointmentCommand>
{
    public async Task<Result> Handle(UpdateAppointmentCommand request, CancellationToken cancellationToken)
    {
        Result<Recurrence> recurrenceResult = request.recurrence != null ? Recurrence.Create(
                request.recurrence.Frequency,
                request.recurrence.DaysOfWeek,
                request.recurrence.DaysOfMonth,
                request.recurrence.StartDate,
                request.recurrence.EndDate,
                request.recurrence.StartTime,
                request.recurrence.EndTime).Value : Result.Failure<Recurrence>(new Error("Invalid.Recurrence", "Recurrence cannot be null", ErrorType.Validation));

        if (recurrenceResult.IsFailure)
            return Result.Failure<Guid>(recurrenceResult.Error);

        AppointmentDomain? appointment = await appointmentRepository.GetSingleAsync(a => a.Id == request.id, cancellationToken);

        if (appointment is null)
            return Result.Failure(AppointmentErrors.NotFound(request.id));

        if (request.lawyerId != appointment.OwnerLawyerId)
            return Result.Failure(AppointmentErrors.Unauthorized);

        appointment.Update(
            request.type, 
            request.name, 
            request.description,
            request.responsibleLawyerId,
            recurrenceResult.Value,
            request.participantLawyersIds, 
            request.alerts,
            request.statusId);

        appointmentRepository.Update(appointment);

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}

public static class AppointmentErrors
{
    public static Error NotFound(Guid Id) =>
        Error.NotFound("Appointment.NotFound", $"The appointment with the identifier {Id} was not found");

    public static readonly Error Unauthorized = Error.Problem(
        "Appointment.Unauthorized",
        "This user cannot edit this appointment");

    public static readonly Error LaywerNotFound = Error.NotFound("Appointment.LawyerNotFound", "One or more of the specified lawyers are not present in the office");

}
