﻿using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Abstractions.Data;
using DataVenia.Modules.Calendar.Domain.Appointments;
using DataVenia.Modules.Calendar.Domain.Status;
using DataVenia.Modules.Calendar.Infrastructure.Appointments;
using DataVenia.Modules.Calendar.Infrastructure.Database;
using DataVenia.Modules.Calendar.Infrastructure.Status;
using DataVenia.Modules.Calendar.Presentation;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DataVenia.Modules.Calendar.Infrastructure;

public static class CalendarModule
{
    public static IServiceCollection AddCalendarModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddInfrastructure(configuration);

        services.AddEndpoints(AssemblyReference.Assembly);

        return services;
    }

    private static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<CalendarDbContext>((sp, options) =>
            options
                .UseNpgsql(
                    configuration.GetConnectionString("Database"),
                    npgsqlOptions => npgsqlOptions
                        .MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Calendar))
                .UseSnakeCaseNamingConvention());

        services.AddScoped<IAppointmentRepository, AppointmentRepository>();
        services.AddScoped<IStatusRepository, StatusRepository>();

        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<CalendarDbContext>());
    }
}
