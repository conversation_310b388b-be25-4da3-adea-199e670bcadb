﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DataVenia.Modules.Users.Domain.SharedModels;

namespace DataVenia.Modules.Users.Infrastructure.SharedModels;
internal sealed class ContactConfiguration : IEntityTypeConfiguration<Contact>
{
    public void Configure(EntityTypeBuilder<Contact> builder)
    {
        builder.ToTable("contact");

        builder.<PERSON><PERSON><PERSON>(u => u.Id);

        builder
        .HasOne(c => c.Office)
        .WithMany(lc => lc.Contacts)
        .HasForeignKey(c => c.OfficeId)
        .OnDelete(DeleteBehavior.NoAction);

        builder
           .HasOne(c => c.Lawyer)
        .WithMany(l => l.Contacts)
        .HasForeignKey(c => c.LawyerId)
        .OnDelete(DeleteBehavior.NoAction);

        // builder
        //     .HasOne(c => c.Client)
        //     .WithMany(l => l.Contacts)
        //     .HasForeign<PERSON>ey(c => c.ClientId)
        //     .OnDelete(DeleteBehavior.NoAction);

        builder
            .ToTable(tb => tb.HasCheckConstraint("CK_Contact_OneOwner",
                "(\"lawyer_id\" IS NOT NULL AND \"office_id\" IS NULL) OR " +
                "(\"lawyer_id\" IS NULL AND \"office_id\" IS NOT NULL)"));
    }
}
