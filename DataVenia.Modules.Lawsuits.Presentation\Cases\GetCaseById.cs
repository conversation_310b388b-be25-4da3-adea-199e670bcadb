﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Cases.GetCases;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;

public sealed class GetCaseById : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/cases/{caseId}", async (Guid officeId, Guid caseId, ClaimsPrincipal claims, [FromServices] ISender sender) =>
            {
                Result<EnrichedCaseResponse?> result = await sender.Send(new GetCaseByIdQuery(
                    claims.GetUserId(),
                    officeId,
                    caseId));

                return result.Match(@case => Results.Ok(new {item = @case}), ApiResults.Problem);
            })
            .RequireAuthorization("system:users:read")
            .WithTags(Tags.Cases);
    }
}
