using DataVenia.Modules.Notification.Application.IoC;
using DataVenia.Modules.Notification.Domain.Infrastructure.IoC;
using DataVenia.Modules.Notification.Worker.Workers;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DataVenia.Modules.Notification.Worker.IoC;

public static class NotificationInjections
{
    public static IBusRegistrationConfigurator RegisterNotificationModuleConsumer(
        this IBusRegistrationConfigurator configurator)
    {
        configurator = configurator ?? throw new ArgumentNullException(nameof(configurator));

        configurator.AddConsumer<MailNotificationConsumer>();
        configurator.AddConsumer<TelegramNotificationConsumer>();
        configurator.AddConsumer<LawsuitStepNotificationConsumer>();

        return configurator;
    }

    public static IServiceCollection AddNotificationModule(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddNotificationInfra(configuration)
            .AddNotificationApp();

        return services;
    }
}
