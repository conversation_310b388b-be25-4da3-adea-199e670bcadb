using Google.Cloud.Storage.V1;
using FluentResults;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.Infrastructure.Facades;

public class GcsStorageFacade : IBlobStorage
{
    private readonly StorageClient _client;
    private readonly ILogger<GcsStorageFacade> _logger;

    public GcsStorageFacade(ILogger<GcsStorageFacade> logger)
    {
        _logger = logger;

        try
        {
            _client = StorageClient.Create(); // GOOGLE_APPLICATION_CREDENTIALS must be set
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to initialize Google Cloud Storage client.", ex);
        }
    }

    public async Task<Result> UploadAsync(string bucketName, Stream inputStream, string objectName, string contentType = "application/octet-stream")
    {
        if (string.IsNullOrWhiteSpace(bucketName))
            return Result.Fail("Bucket Name is empty");

        try
        {
            if (inputStream == null)
                return Result.Fail("Input stream cannot be null.");

            await _client.UploadObjectAsync(new Google.Apis.Storage.v1.Data.Object
            {
                Bucket = bucketName,
                Name = objectName,
                ContentType = contentType
            }, inputStream);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to upload object");
            return Result.Fail($"Failed to upload object: {ex.Message}");
        }
    }

    public async Task<Result> DownloadAsync(string bucketName, string objectName, Stream outputStream, CancellationToken cancellationToken)
    {
        try
        {
            if (outputStream == null)
                return Result.Fail("Output stream cannot be null.");

            await _client.DownloadObjectAsync(bucketName, objectName, outputStream, cancellationToken: cancellationToken);
            return Result.Ok();
        }
        catch (Google.GoogleApiException ex) when (ex.Error.Code == 404)
        {
            _logger.LogDebug(ex, "Object  not found.");
            return Result.Fail($"Object '{objectName}' not found.");
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to download object");
            return Result.Fail($"Failed to download object: {ex.Message}");
        }
    }

    public async Task<Result> DeleteAsync(string bucketName, string objectName)
    {
        try
        {
            await _client.DeleteObjectAsync(bucketName, objectName);
            return Result.Ok();
        }
        catch (Google.GoogleApiException ex) when (ex.Error.Code == 404)
        {
            _logger.LogDebug(ex, "Object  not found.");
            return Result.Fail($"Object '{objectName}' not found.");
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to delete object");
            return Result.Fail($"Failed to delete object: {ex.Message}");
        }
    }

    public async Task<Result<bool>> ExistsAsync(string bucketName, string objectName)
    {
        try
        {
            Google.Apis.Storage.v1.Data.Object obj = await _client.GetObjectAsync(bucketName, objectName);
            return Result.Ok(obj != null);
        }
        catch (Google.GoogleApiException ex) when (ex.Error.Code == 404)
        {
            _logger.LogDebug(ex, "Object does not exist 404");
            return Result.Ok(false);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error checking existence");
            return Result.Fail<bool>($"Error checking existence: {ex.Message}");
        }
    }

    public async Task<Result<IList<string>>> ListAsync(string bucketName, string? prefix = null)
    {
        try
        {
            var result = new List<string>();
            await foreach (Google.Apis.Storage.v1.Data.Object obj in _client.ListObjectsAsync(bucketName, prefix))
            {
                result.Add(obj.Name);
            }
            return Result.Ok<IList<string>>(result);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to list objects");
            return Result.Fail<IList<string>>($"Failed to list objects: {ex.Message}");
        }
    }
}

