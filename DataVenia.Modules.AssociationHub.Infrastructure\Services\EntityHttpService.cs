using System.Text;
using System.Text.Json;
using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;
using DataVenia.Modules.AssociationHub.Domain.Services;
using FluentResults;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.AssociationHub.Infrastructure.Services;

internal sealed class EntityHttpService(
    HttpClient httpClient,
    EntityConfigurationRegistry configurationRegistry,
    ILogger<EntityHttpService> logger) : IEntityHttpService
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    public async Task<Result<EntityData>> GetEntityAsync(string entityType, Guid entityId, CancellationToken cancellationToken = default)
    {
        try
        {
            var configuration = configurationRegistry.GetConfiguration(entityType);
            if (configuration == null)
            {
                return Result.Fail<EntityData>(new Error($"Configuration not found for entity type '{entityType}'")
                    .WithMetadata("StatusCode", 404));
            }

            var endpoint = configuration.BuildGetEndpoint(entityId);
            logger.LogDebug("Getting entity {EntityType}:{EntityId} from endpoint: {Endpoint}",
                entityType, entityId, endpoint);

            var response = await httpClient.GetAsync(endpoint, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogWarning("Failed to get entity {EntityType}:{EntityId}. Status: {StatusCode}",
                    entityType, entityId, response.StatusCode);

                return Result.Fail<EntityData>(new Error($"Failed to get entity. Status: {response.StatusCode}")
                    .WithMetadata("StatusCode", (int)response.StatusCode));
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var jsonDocument = JsonDocument.Parse(content);
            var root = jsonDocument.RootElement;

            var entityData = new EntityData
            {
                Id = entityId,
                Associations = ExtractAssociations(root, configuration.AssociationPropertyName),
                AdditionalData = ExtractAdditionalData(root)
            };

            logger.LogDebug("Successfully retrieved entity {EntityType}:{EntityId} with {AssociationCount} association types",
                entityType, entityId, entityData.Associations.Count);

            return Result.Ok(entityData);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting entity {EntityType}:{EntityId}",
                entityType, entityId);

            return Result.Fail<EntityData>(new Error("An unexpected error occurred while getting the entity")
                .WithMetadata("StatusCode", 500));
        }
    }

    public async Task<Result> UpdateEntityAssociationsAsync(string entityType, Guid entityId, Dictionary<string, List<Guid>> associations, CancellationToken cancellationToken = default)
    {
        try
        {
            var configuration = configurationRegistry.GetConfiguration(entityType);
            if (configuration == null)
            {
                return Result.Fail(new Error($"Configuration not found for entity type '{entityType}'")
                    .WithMetadata("StatusCode", 404));
            }

            var endpoint = configuration.BuildPatchEndpoint(entityId);
            logger.LogDebug("Updating entity {EntityType}:{EntityId} associations at endpoint: {Endpoint}",
                entityType, entityId, endpoint);

            var patchData = new Dictionary<string, object>
            {
                [configuration.AssociationPropertyName] = associations
            };

            var json = JsonSerializer.Serialize(patchData, JsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PatchAsync(endpoint, content, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogWarning("Failed to update entity {EntityType}:{EntityId} associations. Status: {StatusCode}",
                    entityType, entityId, response.StatusCode);

                return Result.Fail(new Error($"Failed to update entity associations. Status: {response.StatusCode}")
                    .WithMetadata("StatusCode", (int)response.StatusCode));
            }

            logger.LogDebug("Successfully updated entity {EntityType}:{EntityId} associations",
                entityType, entityId);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating entity {EntityType}:{EntityId} associations",
                entityType, entityId);

            return Result.Fail(new Error("An unexpected error occurred while updating the entity associations")
                .WithMetadata("StatusCode", 500));
        }
    }

    private static Dictionary<string, List<Guid>> ExtractAssociations(JsonElement root, string associationPropertyName)
    {
        var associations = new Dictionary<string, List<Guid>>();

        if (root.TryGetProperty(associationPropertyName, out var associationsElement))
        {
            foreach (var property in associationsElement.EnumerateObject())
            {
                var ids = new List<Guid>();
                if (property.Value.ValueKind == JsonValueKind.Array)
                {
                    foreach (var item in property.Value.EnumerateArray())
                    {
                        if (item.TryGetGuid(out var guid))
                        {
                            ids.Add(guid);
                        }
                    }
                }
                associations[property.Name] = ids;
            }
        }

        return associations;
    }

    private static Dictionary<string, object> ExtractAdditionalData(JsonElement root)
    {
        var additionalData = new Dictionary<string, object>();

        foreach (var property in root.EnumerateObject())
        {
            if (property.Name.Equals("associations", StringComparison.OrdinalIgnoreCase))
                continue;

            additionalData[property.Name] = property.Value.Clone();
        }

        return additionalData;
    }
}
