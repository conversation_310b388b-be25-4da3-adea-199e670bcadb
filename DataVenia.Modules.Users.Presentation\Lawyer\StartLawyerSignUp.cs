﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyer.StartLawyerSignUp;
using DataVenia.Modules.Users.Application.Lawyers.RegisterLawyer;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.SharedModels;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Lawyer;

public class StartLawyerSignUp : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/offices/{officeId}/lawyers", async (Guid officeId, [FromBody] StartLawyerSignUpRequest request, ClaimsPrincipal claims, [FromServices] ISender sender) =>
            {
                Result<Guid> result = await sender.Send(new StartLawyerSignUpCommand(
                    officeId,
                    claims.GetUserId(),
                    request.Email,
                    request.FirstName,
                    request.LastName,
                    request.Contacts));

                return result.Match(id => Results.Ok(new { Id = id }), ApiResults.Problem);
            })
            .RequireAuthorization(Permission.CreateUser.Code)
            .WithTags(Tags.Users);
    }

}
public sealed record StartLawyerSignUpRequest
{
    public string Email { get; init; }
    public string FirstName { get; init; }
    public string LastName { get; init; }
    public List<Contact> Contacts { get; init; }
}
