﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Company.DeleteCompany;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Company;

public class DeleteCompany: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapDelete("offices/{officeId}/companies/{companyId}", async (Guid officeId, Guid companyId, [FromServices] ISender sender) =>
            {
                Result result = await sender.Send(new DeleteCompanyCommand(
                    companyId));
                
                return result.Match(Results.NoContent, ApiResults.Problem);
            })
            .RequireAuthorization("office:clients:delete")
            .WithTags(Tags.Company);
    }
}
