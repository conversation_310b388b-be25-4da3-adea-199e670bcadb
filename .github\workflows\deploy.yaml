name: Deploy latest tag to PRD

on:
  workflow_dispatch:
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    
     - name: executing remote ssh commands using ssh key
       uses: appleboy/ssh-action@v1
       continue-on-error: true
       with:
         host: ${{ vars.SSH_HOST }}
         username: ${{ vars.SSH_USER }}
         key: ${{ secrets.KEY }}
         port: ${{ secrets.PORT }}
         passphrase: ${{ secrets.PASSPHRASE }}
         script: |
            echo "Starting deployment..."
            echo "Entering data venia folder"
            cd /home/<USER>/repository/data-venia-api
            echo "Switching to latest tag on master"
            git fetch --tags --force
            git reset --hard origin/master
           
            LATEST_TAG=$(git describe --tags `git rev-list --tags --max-count=1`)
            echo "Latest tag is: $LATEST_TAG"
            echo "Checking out $LATEST_TAG..."
            git checkout $LATEST_TAG
            echo "Cleaning directory"
            git clean -fdx
  
            echo "Generating appsettings.json"
            echo '${{ secrets.APPSETTINGS_PRD }}' > DataVenia.Api/appsettings.json
            echo "Generating modules.user.json"
            echo '${{ secrets.MODULES_USER_PRD }}' > DataVenia.Api/modules.user.json
            echo "Generating modules.notification.json"
            echo '${{ secrets.MODULES_NOTIFICATION_PRD }}' > DataVenia.Api/modules.notification.json
            echo "Generating modules.lawsuitSync.json"
            echo '${{ secrets.MODULES_LAWSUIT_SYNC_PRD }}' > DataVenia.Api/modules.lawsuitSync.json
  
            # Check if dotnet ef is installed
            if ! dotnet ef --version &> /dev/null; then
                echo "dotnet-ef tools are not installed. Installing..."
                dotnet tool install --global dotnet-ef
            else
                echo "dotnet-ef tools are already installed."
            fi
            
            # bash migrations.sh $CONNECTION_STRING
            source migrations.sh "${{ secrets.CONN_STRING }}"
            bash deploy.sh
     
     - name: Notify Discord on Failure
       if: failure()
       env:
        DISCORD_WEBHOOK_URL: ${{ vars.DISCORD_WEBHOOK_URL }}
       run: |
        curl -X POST -H "Content-Type: application/json" \
          -d '{"content": "❌ Deployment failed for latest tag on PRD!"}' \
          "$DISCORD_WEBHOOK_URL"
