﻿using Microsoft.EntityFrameworkCore;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Infrastructure.Database;
using System.Threading;
using System.Linq.Expressions;

namespace DataVenia.Modules.Users.Infrastructure.IntermediateClasses;
public sealed class OfficeUserRepository(UsersDbContext context) : IOfficeUserRepository
{
    public async Task<OfficeUser?> GetSingleByFilterAsync(Expression<Func<OfficeUser, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.GetSingleByFilterAsync(filter, cancellationToken);
    }

    public async Task<IReadOnlyCollection<OfficeUser?>> GetManyByFilterAsync(Expression<Func<OfficeUser, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.GetManyByFilterAsync(filter, cancellationToken);
    }
    public void Insert(OfficeUser officeLawyer)
    {
        context.OfficeUsers.Add(officeLawyer);
    }

    public void Update(OfficeUser officeLawyer)
    {
        context.OfficeUsers.Update(officeLawyer);
    }
    
    public async Task<List<Guid>> GetBoundLawyerIdsAsync(Guid officeId, IEnumerable<Guid> lawyerIds, CancellationToken cancellationToken = default)
    {
        return await context.OfficeUsers
            .Where(ou => ou.OfficeId == officeId && lawyerIds.Contains(ou.UserId))
            .Select(ou => ou.UserId)
            .ToListAsync(cancellationToken);
    }
    
    public void InsertRange(IEnumerable<OfficeUser> officeUsers)
    {
        context.OfficeUsers.AddRange(officeUsers);
    }

}
