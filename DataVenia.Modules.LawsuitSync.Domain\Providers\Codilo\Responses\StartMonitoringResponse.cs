﻿namespace DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;

public class StartMonitoringResponse
{
    public bool Success { get; set; }
    public MonitoringData Data { get; set; }
}

public class MonitoringData
{
    public Guid Id { get; set; }
    public required string Cnj { get; set; }
    public object? Tag { get; set; }
    public bool Ignore { get; set; }
    public bool Credentials { get; set; }
    public required DateTime CreatedAt { get; set; }
    public required IEnumerable<Info> Info { get; set; }
}

public class Info
{
    public Guid Id { get; set; }
    public required string Source { get; set; }
    public required string Platform { get; set; }
    public required string Search { get; set; }
    public required string Query { get; set; }
    public required string SourceTag { get; set; }
    public required string PlatformTag { get; set; }
    public required string SearchTag { get; set; }
    public required string QueryTag { get; set; }
    public object? LastStatus { get; set; }
    public object? LastResponse { get; set; }
}
