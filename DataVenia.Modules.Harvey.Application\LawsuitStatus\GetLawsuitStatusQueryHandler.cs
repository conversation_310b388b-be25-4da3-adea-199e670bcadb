﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Harvey.Domain.Status;
using StatusDomain = DataVenia.Modules.Harvey.Domain.LawsuitStatus.LawsuitStatus;
namespace DataVenia.Modules.Harvey.Application.Status;
internal sealed class GetLawsuitStatusQueryHandler(
    ILawsuitStatusRepository statusRepository) : IQueryHandler<GetLawsuitStatusQuery, IReadOnlyCollection<GetLawsuitStatusResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetLawsuitStatusResponse>>> <PERSON>le(GetLawsuitStatusQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<StatusDomain> status = await statusRepository.GetAllAsync(request.displayName, cancellationToken);

        var statusResponse = status.Select(status => new GetLawsuitStatusResponse(
            status.Id,
            status.DisplayName)).ToList();

        return statusResponse;
    }
}
