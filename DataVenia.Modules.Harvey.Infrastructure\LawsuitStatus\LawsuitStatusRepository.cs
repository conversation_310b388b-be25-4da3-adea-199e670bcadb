﻿using DataVenia.Modules.Harvey.Domain.Action;
using DataVenia.Modules.Harvey.Domain.Status;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using StatusDomain = DataVenia.Modules.Harvey.Domain.LawsuitStatus.LawsuitStatus;
namespace DataVenia.Modules.Harvey.Infrastructure.Status;
public sealed class LawsuitStatusRepository(HarveyDbContext context) : ILawsuitStatusRepository
{
    public async Task<IReadOnlyCollection<StatusDomain>> GetAllAsync(string? displayName, CancellationToken cancellationToken = default)
    {
        IQueryable<StatusDomain> query = context.Status;

        if (!string.IsNullOrWhiteSpace(displayName))
            query = query.Where(c => c.DisplayName.Contains(displayName));

        return await query.ToListAsync(cancellationToken);
    }
}

