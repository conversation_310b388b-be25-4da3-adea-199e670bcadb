using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using FluentResults;

namespace DataVenia.Modules.LawsuitSync.Application.AppServices;

public sealed class StopLawsuitMonitoringAppService(
    ICodiloRepository codiloRepository,
    ILawsuitMonitoringRepository lawsuitMonitoringRepository) : IStopLawsuitMonitoringAppService
{
    public async Task<Result> ExecuteAsync(string cnj, Guid subscriberId)
    {
        var monitoring = await lawsuitMonitoringRepository.GetLawsuitMonitoring(cnj).ConfigureAwait(false);
        if (monitoring.IsFailed || monitoring.Value?.Any() != true)
            return Result.Fail(new Error($"There isn't any lawsuit being monitored with cnj {cnj}.").WithMetadata("StatusCode", 404));

        var monitoringList = monitoring.Value[0].Subscriptions;
        var newList = monitoringList.Where(x => x != subscriberId.ToString()).ToList();
        if (newList.Count == 0)
        {
            var monitoringId = monitoring.Value.FirstOrDefault()?.MonitorExternalId;
            if (monitoringId == null)
                return Result.Fail(new Error("Cannot stop monitoring without monitoring external id.").WithMetadata("StatusCode", 409));
            try
            {
                await codiloRepository.CancelLawsuitMonitoring(monitoringId.ToString() ?? string.Empty).ConfigureAwait(false);

                return await lawsuitMonitoringRepository.DeleteLawsuitMonitoringConfiguration(cnj);
            } catch(Exception ex)
            {
                return Result.Fail(new Error(ex.Message).WithMetadata("StatusCode", 409));
            }

        }

        foreach (var config in monitoring.Value)
        {
            config.Subscriptions = newList;
            await lawsuitMonitoringRepository.UpdateLawsuitMonitoringConfiguration(config);
        }

        return Result.Ok();
    }
}
