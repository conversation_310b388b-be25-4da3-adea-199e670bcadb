name: datavenia

x-compose-project:
  name: datavenia

networks:
  datavenia.network:
    driver: bridge

volumes:
  keycloak:
    driver: local
  database:
    driver: local

services:
  datavenia.api:
    image: ${DOCKER_REGISTRY-}dataveniaapi
    container_name: datavenia.Api
    networks: ["datavenia.network"]
    build:
      context: .
      dockerfile: datavenia.Api/Dockerfile
    ports:
      - 5001:80
      - 5002:443
    depends_on:
      - datavenia.database
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_Kestrel__Certificates__Default__Password=password
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
      - ASPNETCORE_URLS=https://+:443;http://+:80
    volumes:
      - ~/.aspnet/https:/https:ro

  datavenia.database:
    image: postgres:17.2-alpine3.20
    container_name: datavenia.Database
    networks: ["datavenia.network"]
    environment:
      - POSTGRES_DB=datavenia
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - database:/var/lib/postgresql/data
    ports:
      - 5432:5432

  datavenia.seq:
    image: datalust/seq:latest
    container_name: datavenia.Seq
    networks: ["datavenia.network"]
    environment:
      - ACCEPT_EULA=Y
    ports:
      - 5341:5341
      - 8081:80

  datavenia.identity:
    image: quay.io/keycloak/keycloak:latest
    container_name: datavenia.Identity
    command: start-dev #--import-realm
    networks: ["datavenia.network"]
    environment:
      - KC_HEALTH_ENABLED=true
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    volumes:
      - keycloak:/opt/keycloak/data
    ports:
      - 18080:8080
