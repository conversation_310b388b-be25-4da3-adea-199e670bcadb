﻿using System.Linq.Expressions;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.Users;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Users.Infrastructure.Users;

internal sealed class UserRepository(UsersDbContext context) : IUserRepository
{
    public async Task<User?> GetAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await context.Users.SingleOrDefaultAsync(u => u.Id == id, cancellationToken);
    }

    public async Task<User?> GetAsync(Expression<Func<User, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.GetSingleByFilterAsync(filter, cancellationToken);
    }
}
