﻿using DataVenia.Common.Domain;

namespace DataVenia.Common.Presentation.ApiResults;

public static class ResultExtensions
{
    public static TOut Match<TOut>(
        this Result result,
        Func<TOut> onSuccess,
        Func<Result, TOut> onFailure)
    {
        return result.IsSuccess ? onSuccess() : onFailure(result);
    }

    public static TOut Match<TIn, TOut>(
        this Result<TIn> result,
        Func<TIn, TOut> onSuccess,
        Func<Result<TIn>, TOut> onFailure)
    {
        return result.IsSuccess ? onSuccess(result.Value) : onFailure(result);
    }
    
    public static TOut Match<TOut>(
        this FluentResults.Result result,
        Func<TOut> onSuccess,
        Func<FluentResults.Result, TOut> onFailure)
    {
        return result.IsSuccess ? onSuccess() : onFailure(result);
    }
    
    public static TOut Match<TIn, TOut>(
        this FluentResults.Result<TIn> result,
        Func<TIn, TOut> onSuccess,
        Func<FluentResults.Result<TIn>, TOut> onFailure)
    {
        return result.IsSuccess ? onSuccess(result.Value) : onFailure(result);
    }
}
