﻿using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.SignUpToken;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Users.Infrastructure.SignUpTokens;

public class SignUpTokenConfiguration : IEntityTypeConfiguration<SignUpToken>
{
    public void Configure(EntityTypeBuilder<SignUpToken> builder)
    {
        builder.ToTable("sign_up_token");
        
        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Token)
            .IsRequired()
            .HasColumnType("uuid");

        builder.Property(e => e.OfficeId);

        builder.HasOne(e => e.Lawyer)
            .WithMany(l => l.SignUpToken)
            .HasForeignKey(e => e.LawyerId);
    }
}
