using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Requests;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;
using Refit;

namespace DataVenia.Modules.LawsuitSync.Infrastructure.LawsuitProviders.Codilo;

public interface ICodiloAuthClient
{
    [Post("/oauth/token")]
    [Headers("Content-Type: application/x-www-form-urlencoded")]
    Task<AuthResponse> GetTokenAsync([Body(BodySerializationMethod.UrlEncoded)] FormUrlEncodedContent request);
}
