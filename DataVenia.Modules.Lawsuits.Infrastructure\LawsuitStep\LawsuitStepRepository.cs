﻿using DataVenia.Modules.Lawsuits.Application.Lawsuits;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitStepsByCnj;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using FluentResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitStep;

public sealed class LawsuitStepRepository(LawsuitsDbContext context, ILogger<LawsuitStepRepository> logger) : ILawsuitStepRepository
{
    public async Task<Result<IReadOnlyCollection<LawsuitStepsResponse>>> GetLawsuitStepsByCnjAsync(string cnj, string? instance = null, DateTime? stoppedAt = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = context.LawsuitSteps
                .Where(ls => ls.Cnj == cnj);
            
            if(!string.IsNullOrEmpty(instance))
                query = query.Where(ls => ls.LegalInstanceId == instance);
            
            if(stoppedAt.HasValue)
                query = query.Where(ls => ls.OccurredAt <= stoppedAt);
            
            query = query.OrderByDescending(ls => ls.OccurredAt);

            var result = await query
                .Select(ls => new LawsuitStepsResponse(
                    ls.Id,
                    ls.Cnj,
                    ls.Title,
                    ls.Description,
                    ls.CreatedAt,
                    ls.OccurredAt
                ))
                .ToListAsync(cancellationToken);
            
                return Result.Ok((IReadOnlyCollection<LawsuitStepsResponse>)result);
            
        } catch (Exception ex)
        {
            logger.LogError(ex, "Error getting lawsuit steps by cnj: {@Cnj}", cnj);
            return Result.Fail<IReadOnlyCollection<LawsuitStepsResponse>>(new Error("Internal.Server.Error").WithMetadata("StatusCode", 500));
        }
    }
    
    public void InsertRange(IEnumerable<Domain.LawsuitSteps.LawsuitStep> lawsuitSteps)
    {
        context.LawsuitSteps.AddRange(lawsuitSteps);
    }
}
