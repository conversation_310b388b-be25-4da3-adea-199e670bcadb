﻿using System.Linq.Expressions;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;

namespace DataVenia.Modules.Users.Infrastructure.Client;
public sealed class ClientRepository(UsersDbContext context) : IClientRepository
{
    public Task<IReadOnlyCollection<ClientDomain>> GetManyAsync(Expression<Func<ClientDomain, bool>> filter, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public void Insert(ClientDomain client)
    {
        context.Clients.Add(client);
    }

    public async Task<IEnumerable<ClientDomain>> GetAllByOfficeIdAsync(Guid officeId, CancellationToken cancellationToken = default)
    {
        return await context.Clients
            .Where(l => l.OfficeId == officeId)
            .ToListAsync(cancellationToken);
    }

    public async Task<ClientDomain?> GetSingleAsync(Expression<Func<ClientDomain, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Clients
            .Include(l => l.ClientCompanies)
            .Where(filter)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
