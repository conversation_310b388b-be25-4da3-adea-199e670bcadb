﻿using DataVenia.Modules.Users.Domain.Users;

namespace DataVenia.Modules.Users.Domain.Authorization;
public sealed class UserGlobalRole
{
    public Guid Id { get; private set; }
    public Guid UserId { get; private set; }
    public Users.User User { get; private set; }
    public string RoleName { get; private set; }
    public Role Role { get; private set; }

    private UserGlobalRole() { }
    public static UserGlobalRole Create(Guid userId, string role)
    {
        var userGlobalRole = new UserGlobalRole()
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            RoleName = role,
        };

        return userGlobalRole;
    }
}
