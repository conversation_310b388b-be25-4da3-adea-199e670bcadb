﻿using DataVenia.Modules.Calendar.Application.Abstractions.Data;
using DataVenia.Modules.Calendar.Domain.Appointments;
using DataVenia.Modules.Calendar.Infrastructure.AppointmentParticipant;
using DataVenia.Modules.Calendar.Infrastructure.Appointments;
using DataVenia.Modules.Calendar.Infrastructure.Status;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using StatusDomain = DataVenia.Modules.Calendar.Domain.Status.Status;

namespace DataVenia.Modules.Calendar.Infrastructure.Database;

public sealed class CalendarDbContext(DbContextOptions<CalendarDbContext> options) : DbContext(options), IUnitOfWork
{
    public Guid InstanceId { get; } = Guid.NewGuid();
    internal DbSet<Appointment> Appointments { get; set; }
    internal DbSet<StatusDomain> Statuses { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema(Schemas.Calendar);

        modelBuilder.ApplyConfiguration(new AppointmentConfiguration());
        modelBuilder.ApplyConfiguration(new RecurrenceConfiguration());
        modelBuilder.ApplyConfiguration(new AppointmentParticipantConfiguration());
        modelBuilder.ApplyConfiguration(new StatusConfiguration());
    }
}
