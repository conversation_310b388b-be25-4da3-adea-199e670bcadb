using DataVenia.Modules.LawsuitSync.Domain.Entities;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using FluentResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.LawsuitSync.Infrastructure.Database.Repositories;

public class LawsuitMonitoringRepository : ILawsuitMonitoringRepository
{
    private readonly LawsuitSyncDbContext _dbContext;
    private readonly ILogger<LawsuitMonitoringRepository> _logger;

    public LawsuitMonitoringRepository(LawsuitSyncDbContext dbContext, ILogger<LawsuitMonitoringRepository> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<Result> AddLawsuitMonitoringConfiguration(
        LawsuitMonitoringConfiguration lawsuitMonitoringConfiguration)
    {
        using var _ = _logger.BeginScope("[AddLawsuitMonitoringConfiguration]");
        try
        {
            await _dbContext.LawsuitMonitoringConfiguration.AddAsync(lawsuitMonitoringConfiguration)
                .ConfigureAwait(false);
            var save = await _dbContext.SaveChangesAsync().ConfigureAwait(false);

            return save > 0 ? Result.Ok() : Result.Fail("Lawsuit Monitoring Configuration was not saved.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fail to save Lawsuit Monitoring Configuration.");
            return Result.Fail(ex.Message);
        }
    }

    public async Task<Result> AddLawsuitMonitoringConfigurationInBatch(
        IEnumerable<LawsuitMonitoringConfiguration> lawsuitMonitoringConfiguration)
    {
        using var _ = _logger.BeginScope("[AddLawsuitMonitoringConfiguration]");
        try
        {
            await _dbContext.LawsuitMonitoringConfiguration.AddRangeAsync(lawsuitMonitoringConfiguration)
                .ConfigureAwait(false);

            var save = await _dbContext.SaveChangesAsync().ConfigureAwait(false);

            return save > 0 ? Result.Ok() : Result.Fail("Lawsuit Monitoring Configuration was not saved.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fail to save Lawsuit Monitoring Configuration.");
            return Result.Fail(ex.Message);
        }
    }

    public async Task<Result<List<string>>> UpdateLawsuitMonitoringConfiguration(
        LawsuitMonitoringConfiguration updatedConfiguration)
    {
        using var _ = _logger.BeginScope("LawsuitMonitoringRepository.UpdateLawsuitMonitoringConfiguration");
        try
        {
            var lawsuitConfiguration = await _dbContext.LawsuitMonitoringConfiguration
                .FirstOrDefaultAsync(x => x.ExternalId == updatedConfiguration.ExternalId)
                .ConfigureAwait(false);

            if (lawsuitConfiguration == null)
                return Result.Fail("Lawsuit Monitoring Configuration was not found.");

            if (updatedConfiguration?.LastUpdate > lawsuitConfiguration.LastUpdate)
            {
                lawsuitConfiguration.LastUpdate = updatedConfiguration.LastUpdate;
                lawsuitConfiguration.CurrentStatus = updatedConfiguration.CurrentStatus;
                lawsuitConfiguration.CourtDivision = updatedConfiguration.CourtDivision;
                lawsuitConfiguration.Platform = updatedConfiguration.Platform;
                lawsuitConfiguration.LegalInstance = updatedConfiguration.LegalInstance;
            }

            _dbContext.LawsuitMonitoringConfiguration.Update(lawsuitConfiguration);
            await _dbContext.SaveChangesAsync().ConfigureAwait(false);

            return Result.Ok(lawsuitConfiguration.Subscriptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fail to save Lawsuit Monitoring Configuration.");
            return Result.Fail(ex.Message);
        }
    }

    public async Task<Result> DeleteLawsuitMonitoringConfiguration(string cnj)
    {
        using var _ = _logger.BeginScope("LawsuitMonitoringRepository.DeleteLawsuitMonitoringConfiguration");
        try
        {
            var lawsuitConfiguration = await _dbContext.LawsuitMonitoringConfiguration
                .Where(x => x.Cnj == cnj)
                .ToListAsync()
                .ConfigureAwait(false);

            if (lawsuitConfiguration?.Any() != true)
                return Result.Fail("Lawsuit Monitoring Configuration was not found.");

            foreach (var configuration in lawsuitConfiguration)
            {
                _dbContext.LawsuitMonitoringConfiguration.Remove(configuration);
            }

            await _dbContext.SaveChangesAsync().ConfigureAwait(false);

            return Result.Ok();
        }catch (Exception ex)
        {
            _logger.LogError(ex, "Fail to delete Lawsuit Monitoring Configuration.");
            return Result.Fail(ex.Message);
        }
    }

    public async Task<Result> AddLawsuitEventsHistory(
        LawsuitEventsHistory lawsuitEventsHistory)
    {
        using var _ = _logger.BeginScope("[AddLawsuitEventsHistory]");
        try
        {
            await _dbContext.LawsuitEventsHistory.AddAsync(lawsuitEventsHistory!).ConfigureAwait(false);

            var save = await _dbContext.SaveChangesAsync().ConfigureAwait(false);

            return save > 0 ? Result.Ok() : Result.Fail("Lawsuit Events were not saved.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fail to save Lawsuit Events History.");
            return Result.Fail(ex.Message);
        }
    }

    public async Task<Result<bool>> ExistsLawsuitMonitoring(string cnj)
    {
        try
        {
            var result = await GetLawsuitMonitoring(cnj);

            return result is { IsSuccess: true, Value: not null } && result.Value.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fail to get Lawsuit Monitoring Configuration.");
            return Result.Fail(ex.Message);
        }
    }

    public async Task<List<Guid>> GetSubscriptions(string cnj)
    {
        var result = await _dbContext.LawsuitMonitoringConfiguration.Where(x => x.Cnj == cnj)
            .Select(x => x.ExternalId)
            .ToListAsync()
            .ConfigureAwait(false);

        return result;
    }

    public async Task<Result<List<LawsuitMonitoringConfiguration>>> GetLawsuitMonitoring(string cnj)
    {
        try
        {
            var result = await _dbContext.LawsuitMonitoringConfiguration.Where(x => x.Cnj == cnj)
                .ToListAsync().ConfigureAwait(false);

            return Result.Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fail to get Lawsuit Monitoring Configuration.");
            return Result.Fail(ex.Message);
        }
    }
}
