# AssociationHub Module

The AssociationHub module is a generic, high-performance association management system that enables bidirectional relationships between entities across different modules without requiring code changes when new entities are added.

## Features

- **Generic Design**: Works with any entity type without requiring specific implementations
- **Bidirectional Associations**: Automatically creates associations in both directions
- **Bulk Operations**: Handle multiple associations in a single event for maximum efficiency
- **Message-Driven**: Uses MassTransit for asynchronous processing
- **HTTP-Based**: Makes REST API calls to entity endpoints for updates
- **High Performance**: Minimizes reflection usage through configuration-based approach
- **Fault Tolerant**: Includes retry mechanisms and error handling
- **Background Processing**: Processes association requests asynchronously
- **FluentResults Integration**: Uses FluentResults for robust error handling

## Architecture

### Components

1. **Domain Layer**
   - `AssociationRequest`: Entity representing an association operation
   - `EntityConfiguration`: Configuration for entity types and their endpoints
   - `IEntityHttpService`: Interface for HTTP operations on entities

2. **Application Layer**
   - `CreateAssociationCommand/Handler`: Creates new associations
   - `RemoveAssociationCommand/Handler`: Removes existing associations
   - `AssociationProcessingService`: Core business logic for processing requests

3. **Infrastructure Layer**
   - `EntityHttpService`: HTTP client implementation for entity operations
   - `AssociationRequestRepository`: Data access for association requests
   - `AssociationProcessingWorker`: Background service for processing requests

4. **Presentation Layer**
   - Message consumers for integration events
   - REST endpoints for direct API access

## Usage

### 1. Publishing Association Events

Other modules can create associations by publishing events:

```csharp
// Create association between Client and Company
var associations = new[]
{
    new AssociationRequest("Company", [companyId])
};

var createEvent = new CreateAssociationEvent(
    EntityType: "Client",
    EntityId: clientId,
    Associations: associations);

await publishEndpoint.Publish(createEvent);

// Create multiple associations at once (much more efficient!)
var multipleAssociations = new[]
{
    new AssociationRequest("Company", [companyId1, companyId2]),
    new AssociationRequest("Lawyer", [lawyerId1, lawyerId2, lawyerId3])
};

var bulkCreateEvent = new CreateAssociationEvent(
    EntityType: "Client",
    EntityId: clientId,
    Associations: multipleAssociations);

await publishEndpoint.Publish(bulkCreateEvent);

// Remove associations
var removeEvent = new RemoveAssociationEvent(
    EntityType: "Client",
    EntityId: clientId,
    Associations: associations);

await publishEndpoint.Publish(removeEvent);
```

### 2. Entity Requirements

For entities to work with AssociationHub, they need:

1. **GET Endpoint**: Returns entity data including associations
2. **PATCH Endpoint**: Accepts partial updates including associations
3. **Associations Property**: A jsonb field containing association arrays

Example entity structure:
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "John Doe",
  "email": "<EMAIL>",
  "associations": {
    "Company": ["456e7890-e89b-12d3-a456-426614174001"],
    "Lawsuit": ["789e0123-e89b-12d3-a456-426614174002", "012e3456-e89b-12d3-a456-426614174003"]
  }
}
```

### 3. Adding New Entity Types

To support a new entity type, add configuration in `EntityConfigurationSetup.cs`:

```csharp
registry.Register(new EntityConfiguration
{
    EntityType = "NewEntity",
    GetEndpointTemplate = "/new-entities/{0}",
    PatchEndpointTemplate = "/new-entities/{0}",
    AssociationPropertyName = "associations",
    IsEnabled = true,
    AssociationMode = AssociationMode.Whitelist,
    AllowedAssociations = ["Client", "Lawyer", "Case"]
});
```

### 4. Association Configuration Modes

The module supports three association modes:

#### **Whitelist Mode** (Recommended)
Only allows associations with entities in the `AllowedAssociations` list:

```csharp
AssociationMode = AssociationMode.Whitelist,
AllowedAssociations = ["Client", "Lawyer", "Case"]
// Can ONLY associate with Client, Lawyer, and Case
```

#### **Blacklist Mode**
Allows associations with all entities except those in the `AllowedAssociations` list:

```csharp
AssociationMode = AssociationMode.Blacklist,
AllowedAssociations = ["Office", "ArchivedDocument"]
// Can associate with ANY entity EXCEPT Office and ArchivedDocument
```

#### **Allow All Mode**
Allows associations with any supported entity type:

```csharp
AssociationMode = AssociationMode.AllowAll,
AllowedAssociations = [] // Not used in this mode
// Can associate with ANY supported entity
```

### 5. Association Validation

The module provides validation endpoints to check allowed associations:

#### **Get Allowed Associations**
```http
GET /associations/allowed/{entityType}
```

Response:
```json
{
  "entityType": "Client",
  "allowedAssociations": ["Company", "Lawyer", "Lawsuit"],
  "forbiddenAssociations": ["Office", "ArchivedDocument"],
  "associationMode": "Whitelist"
}
```

#### **Validate Specific Associations**
```http
POST /associations/validate
{
  "entityType": "Client",
  "targetEntityTypes": ["Company", "Office", "Lawyer"]
}
```

Response:
```json
{
  "entityType": "Client",
  "validTargets": ["Company", "Lawyer"],
  "invalidTargets": ["Office"],
  "unsupportedTargets": [],
  "isValid": false
}
```

### 6. Business Rules and Examples

The association configuration allows you to enforce complex business rules:

#### **Example 1: Hierarchical Restrictions**
```csharp
// Offices can only be associated with Lawyers
registry.Register(new EntityConfiguration
{
    EntityType = "Office",
    AssociationMode = AssociationMode.Whitelist,
    AllowedAssociations = ["Lawyer"]
});

// Lawyers can associate with most entities
registry.Register(new EntityConfiguration
{
    EntityType = "Lawyer",
    AssociationMode = AssociationMode.AllowAll
});
```

#### **Example 2: Security Restrictions**
```csharp
// Sensitive documents can only be associated with specific entities
registry.Register(new EntityConfiguration
{
    EntityType = "ConfidentialDocument",
    AssociationMode = AssociationMode.Whitelist,
    AllowedAssociations = ["SeniorLawyer", "Case"]
});

// Public entities cannot access sensitive data
registry.Register(new EntityConfiguration
{
    EntityType = "PublicClient",
    AssociationMode = AssociationMode.Blacklist,
    AllowedAssociations = ["ConfidentialDocument", "InternalMemo"]
});
```

#### **Example 3: Bidirectional Validation**
The system automatically validates both directions:
- If `Client` can associate with `Company`, it checks if `Company` can associate back with `Client`
- If not, only a unidirectional association is created
- This prevents orphaned or inconsistent associations

## Configuration

### Entity Endpoints

The module uses template-based endpoint configuration:
- `{0}` = Entity ID
- `{1}` = Additional parameter (e.g., Office ID)

Examples:
- Simple: `/clients/{0}` → `/clients/123e4567-e89b-12d3-a456-************`
- With context: `/offices/{1}/clients/{0}` → `/offices/office-id/clients/client-id`

### Application Settings

```json
{
  "AssociationHub": {
    "BaseUrl": "http://localhost:5163",
    "ProcessingInterval": "00:00:10",
    "MaxRetries": 3
  }
}
```

## Database Schema

The module creates the following table:

```sql
CREATE TABLE association_hub.association_requests (
    id UUID PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id UUID NOT NULL,
    target_entity_type VARCHAR(100) NOT NULL,
    target_entity_id UUID NOT NULL,
    operation VARCHAR(50) NOT NULL, -- 'Add' or 'Remove'
    status VARCHAR(50) NOT NULL,    -- 'Pending', 'Processing', 'Completed', 'Failed'
    error_message VARCHAR(1000),
    created_at TIMESTAMP NOT NULL,
    processed_at TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    deleted_at TIMESTAMP
);
```

## Performance Considerations

1. **Minimal Reflection**: Uses configuration-based approach instead of reflection
2. **Async Processing**: Background worker processes requests asynchronously
3. **Batch Operations**: Can process multiple requests efficiently
4. **HTTP Connection Pooling**: Uses HttpClient with connection pooling
5. **Database Indexing**: Optimized indexes for common query patterns

## Error Handling

- **Validation Errors**: Invalid entity types or configurations
- **HTTP Errors**: Failed API calls to entity endpoints
- **Retry Logic**: Automatic retry for failed requests (configurable)
- **Dead Letter**: Failed requests after max retries are marked as failed

## Monitoring

The module provides comprehensive logging:
- Request processing events
- HTTP call details
- Error conditions
- Performance metrics

## Integration Examples

See `AssociationUsageExample.cs` for complete usage examples including:
- Client-Company associations
- Lawyer-Lawsuit associations
- Multi-entity appointment associations
- Association removal patterns
