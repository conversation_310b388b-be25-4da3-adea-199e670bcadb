using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.AssociationHub.Application.Abstractions.Data;
using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;
using FluentResults;

namespace DataVenia.Modules.AssociationHub.Application.Associations.RemoveAssociation;

internal sealed class RemoveAssociationCommandHandler(
    IAssociationRequestRepository associationRequestRepository,
    EntityConfigurationRegistry configurationRegistry,
    IUnitOfWork unitOfWork) : ICommandHandlerFr<RemoveAssociationCommand>
{
    public async Task<Result> Handle(RemoveAssociationCommand request, CancellationToken cancellationToken)
    {
        // Validate that the source entity type is supported
        if (!configurationRegistry.IsEntitySupported(request.EntityType))
        {
            return Result.Fail(new Error($"Entity type '{request.EntityType}' is not supported for associations")
                .WithMetadata("StatusCode", 400));
        }

        // For removal, we're more lenient - we allow removing any association that exists,
        // even if it wouldn't be allowed to be created now (business rules may have changed)
        var unsupportedTargetTypes = request.Associations
            .Where(a => !configurationRegistry.IsEntitySupported(a.TargetEntityType))
            .Select(a => a.TargetEntityType)
            .ToList();

        if (unsupportedTargetTypes.Any())
        {
            return Result.Fail(new Error($"Target entity types are not supported: {string.Join(", ", unsupportedTargetTypes)}")
                .WithMetadata("StatusCode", 400));
        }

        var associationRequests = new List<AssociationRequest>();

        // Create association removal requests for all target entities
        foreach (var association in request.Associations)
        {
            foreach (var targetEntityId in association.TargetEntityIds)
            {
                // Create bidirectional association removal requests
                var sourceToTargetRequest = AssociationRequest.Create(
                    request.EntityType,
                    request.EntityId,
                    association.TargetEntityType,
                    targetEntityId,
                    AssociationOperation.Remove);

                var targetToSourceRequest = AssociationRequest.Create(
                    association.TargetEntityType,
                    targetEntityId,
                    request.EntityType,
                    request.EntityId,
                    AssociationOperation.Remove);

                associationRequests.Add(sourceToTargetRequest);
                associationRequests.Add(targetToSourceRequest);
            }
        }

        // Add all association requests in batch
        foreach (var associationRequest in associationRequests)
        {
            await associationRequestRepository.AddAsync(associationRequest, cancellationToken);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}