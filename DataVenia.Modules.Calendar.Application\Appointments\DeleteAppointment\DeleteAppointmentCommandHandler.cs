﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Application.Abstractions.Data;
using DataVenia.Modules.Calendar.Domain.Appointments;
using AppointmentDomain = DataVenia.Modules.Calendar.Domain.Appointments.Appointment;
namespace DataVenia.Modules.Calendar.Application.Appointments.DeleteAppointment;
public sealed class DeleteAppointmentCommandHandler(
    IAppointmentRepository appointmentRepository,
    IUnitOfWork unitOfWork) : ICommandHandler<DeleteAppointmentCommand>
{
    public async Task<Result> Handle(DeleteAppointmentCommand request, CancellationToken cancellationToken)
    {
        AppointmentDomain? appointment = await appointmentRepository.GetSingleAsync(x => x.Id == request.appointmentId, cancellationToken);

        if (appointment == null || appointment.OwnerLawyerId != request.lawyerId || appointment.OwnerOfficeId != request.officeId)
            return Result.Failure(Error.Problem("Unauthorized", "User is not allowed to do this"));

        appointmentRepository.Delete(appointment);

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
