﻿using System.Linq.Expressions;
using DataVenia.Common.Domain;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Calendar.Infrastructure.Database;
public static class ContextExtensions
{
    public static async Task<T?> GetSingleByFilterAsync<T>(this CalendarDbContext context, Expression<Func<T, bool>> filter, CancellationToken cancellation = default) where T : class
    {
        return await context.Set<T>().SingleOrDefaultAsync(filter, cancellation);
    }

    public static async Task<IReadOnlyCollection<T>> GetManyByFilterAsync<T>(this CalendarDbContext context, Expression<Func<T, bool>> filter, CancellationToken cancellationToken = default) where T : class
    {
        return await context.Set<T>().Where(filter).ToListAsync(cancellationToken);
    } 

    public static async Task<Result> DeleteAsync<T>(this CalendarDbContext context, Guid id, CancellationToken cancellation = default) where T : class
    {
        T? entity = await context.Set<T>().FindAsync(new object[] { id }, cancellation);

        if(entity == null)
        {
            return Result.Failure(Error.NotFound("Entity not found", "Could not find entity with specified id"));
        }

        context.Set<T>().Remove(entity);

        await context.SaveChangesAsync(cancellation);

        return Result.Success();
    }
}
