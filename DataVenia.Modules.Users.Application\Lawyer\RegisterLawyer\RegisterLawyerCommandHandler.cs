﻿using System.Text.Json;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Lawyers.RegisterLawyer;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.Oab;
using DataVenia.Modules.Users.Domain.Office;
using DataVenia.Modules.Users.Domain.Outbox;
using DataVenia.Modules.Users.IntegrationEvents;
using Microsoft.Extensions.Logging;
using LawyerDomain = DataVenia.Modules.Users.Domain.Lawyers.Lawyer;

namespace DataVenia.Modules.Users.Application.Lawyer.RegisterLawyer;
internal sealed class RegisterLawyerCommandHandler(
    ILawyerRepository lawyerRepository,
    IOfficeRepository officeRepository,
    IOfficeUserRepository officeUserRepository,
    IUnitOfWork unitOfWork, 
    IOabRepository oabRepository,
    IOutboxRepository outboxRepository,
    IUserGlobalRoleRepository userGlobalRoleRepository, 
    ILogger<RegisterLawyerCommandHandler> logger
    )
    : ICommandHandler<RegisterLawyerCommand, RegisterLawyersResponse>
{
    public async Task<Result<RegisterLawyersResponse>> Handle(RegisterLawyerCommand request, CancellationToken cancellationToken)
    {
         // Validate that at least an office or one lawyer is provided
            if (request.Office is null &&  (request.Lawyers is null || !request.Lawyers.Any()))
                return Result.Failure<RegisterLawyersResponse>(
                    new Error("Input.Invalid", "É necessário informar pelo menos um escritório ou um advogado", ErrorType.Validation));

            if (request.Lawyers.Any() && request.Office is null)
                return Result.Failure<RegisterLawyersResponse>(new Error("Input.Invalid",
                    "É necessário informar um escritório para o(s) advogado(s)", ErrorType.Validation));
            
            // --- Office processing ---
            Domain.Office.Office? officeEntity = null;
            bool officeAlreadyExists = false;
            if (request.Office is not null)
            {
                // Lookup office by CNPJ
                officeEntity = await officeRepository.GetAsync(x => x.Cnpj == request.Office.Cnpj, cancellationToken);
                
                if (officeEntity is not null)
                    officeAlreadyExists = true;
                else
                {
                    // Create new office entity using the provided OfficeRequest.
                    officeEntity = Domain.Office.Office.Create(
                        request.Office.Name,
                        request.Office.Website,
                        request.Office.Cnpj,
                        request.Office.Contacts?.ToList(),
                        request.Office.Addresses?.ToList());
                        
                    officeRepository.Insert(officeEntity);
                }
            }

            // --- Lawyers processing ---
            
            // 1. Obter os emails de todos os advogados na requisição
            var lawyersEmails = request.Lawyers.Select(lawyer => lawyer.Email).Distinct().ToList();
            
            // 2. Obter todos os advogados existentes de uma vez só
            List<LawyerDomain> existingLawyersFromDb = await lawyerRepository.GetByEmailsAsync(lawyersEmails, cancellationToken);
            var existingLawyersDict = existingLawyersFromDb.ToDictionary(lawyer => lawyer.Email, lawyer => lawyer);
            
            // Listas para controlar os advogados existentes e já vinculados
            var existingLawyersEmails = new List<string>();
            var alreadyBoundLawyers = new List<string>();
            
            // 3. Separar os advogados que já existem dos que precisam ser criados
            var newLawyers = new List<LawyerDomain>();

            foreach (LawyerRequest lawyerRequest in request.Lawyers)
            {
                if(existingLawyersDict.TryGetValue(lawyerRequest.Email, out LawyerDomain? existingLawyer))
                    existingLawyersEmails.Add(lawyerRequest.Email);
                else
                {
                    Result<LawyerDomain> lawyerResult = LawyerDomain.Create(
                        lawyerRequest.Email,
                        lawyerRequest.FirstName,
                        lawyerRequest.LastName,
                        lawyerRequest.Oabs,
                        lawyerRequest.Contacts,
                        null,
                        lawyerRequest.Cpf,
                        lawyerRequest.Rg,
                        lawyerRequest.Cnh,
                        lawyerRequest.Passport,
                        lawyerRequest.Ctps,
                        lawyerRequest.Pis,
                        lawyerRequest.VoterId);

                    if (lawyerResult.IsFailure)
                    {
                        logger.LogError("Falha ao criar advogado {Email}: {Error}", lawyerRequest.Email, lawyerResult.Error);
                        return Result.Failure<RegisterLawyersResponse>(lawyerResult.Error);
                    }
                    newLawyers.Add(lawyerResult.Value);
                }
            }
            
            // 4. Inserir em lote os novos advogados e suas OABs
            if (newLawyers.Count > 0)
            {
                lawyerRepository.InsertRange(newLawyers);
                oabRepository.InsertRange(newLawyers.Select(lawyer => lawyer.Oab));
                
                Result<Role?> systemMemberRole = await lawyerRepository.GetRoleByNameAsync(Role.SystemMember.Name, cancellationToken);
                
                if (systemMemberRole.IsFailure || systemMemberRole?.Value is null)
                {
                    return Result.Failure<RegisterLawyersResponse>(
                        new Error("Role.NotFound", "SystemMember role not found", ErrorType.InternalServerError));
                }
                
                foreach (LawyerDomain newLawyer in newLawyers)
                {
                    var userGlobalRole = UserGlobalRole.Create(newLawyer.Id, systemMemberRole.Value.Name);
                    userGlobalRoleRepository.Insert(userGlobalRole);
                }
                
                // Publish the lawyer sign-up event via the outbox.
                var newLawyersRequest = request.Lawyers
                    .Where(lawyer => !existingLawyersDict.ContainsKey(lawyer.Email))
                    .ToList();
                
                foreach (LawyerRequest lawyerRequest in newLawyersRequest)
                {
                    LawyerDomain? newLawyer = newLawyers.Find(lawyer => lawyer.Email == lawyerRequest.Email);
                    if (newLawyer is not null)
                    {
                        var lawyerSignedUpEvent = new LawyerSignedUpEvent(
                            newLawyer.Id,
                            newLawyer.Email,
                            lawyerRequest.FirstName,
                            lawyerRequest.LastName,
                            lawyerRequest.Password
                        );
                        string payloadJson = JsonSerializer.Serialize(lawyerSignedUpEvent);
                        var outboxMessage = new Outbox
                        {
                            MessageType = nameof(LawyerSignedUpEvent),
                            Payload = payloadJson,
                            CreatedAt = DateTime.UtcNow,
                            ProcessedAt = null
                        };
                        outboxRepository.Insert(outboxMessage);
                    }
                }
                
            }

            // --- Office Binding ---
            // If an office is present, bind lawyers to it.
            var boundLawyersEmails = new List<string>();
            if (officeEntity is not null)
            {
                
                // For each existing lawyer, check if it is bound to the office
                var existingLawyerIds = existingLawyersFromDb.Select(lawyer => lawyer.Id).ToList();
                List<Guid> boundLawyerIds = await officeUserRepository.GetBoundLawyerIdsAsync(officeEntity.Id, existingLawyerIds, cancellationToken);

                // Add previously bound lawyers to the bound list
                foreach (LawyerDomain existentLawyer in existingLawyersFromDb)
                {
                    if (boundLawyerIds.Contains(existentLawyer.Id))
                        alreadyBoundLawyers.Add(existentLawyer.Email);
                }

                var lawyersToBind = new List<Guid>();
                lawyersToBind.AddRange(newLawyers.Select(lawyer => lawyer.Id));

                foreach (LawyerDomain existentLawyer in existingLawyersFromDb)
                {
                    if(!boundLawyerIds.Contains(existentLawyer.Id))
                        lawyersToBind.Add(existentLawyer.Id);
                }
                
                // Now create office-user binding records using the role mapping.
                // To get the lawyer's email, combine both new and existing lawyers into one list.
                var allLawyers = newLawyers.Concat(existingLawyersFromDb).ToDictionary(l => l.Id, l => l.Email);

                
                if (lawyersToBind.Any())
                {
                    var lawyerRoleMapping = request.Lawyers
                        .ToDictionary(lawyerReq => lawyerReq.Email, lawyerReq => 
                            lawyerReq.IsOfficeAdmin 
                                ? Role.OfficeAdministrator.Name 
                                : Role.OfficeMember.Name);
                    
                    var officeUsers = lawyersToBind
                        .Select(lawyerId => {
                            // Look up the lawyer's email
                            if (!allLawyers.TryGetValue(lawyerId, out string lawyerEmail))
                            {
                                logger.LogError("Lawyer email not found for ID: {LawyerId}", lawyerId);
                                // This should not happen if the data is consistent.
                                return Result.Failure<OfficeUser>(new Error("Internal.Server.Error", "Something weird happened", ErrorType.InternalServerError));
                            }
                            // Use the mapping to determine the role.
                            if (!lawyerRoleMapping.TryGetValue(lawyerEmail, out string? officeRole))
                            {
                                officeRole = Role.OfficeMember.Name; // default if missing
                            }

                            var officeUser = OfficeUser.Create(officeEntity.Id, lawyerId, request.AdminId, officeRole);
                            
                            officeUser.Accept();

                            return officeUser;
                        })
                        .ToList();
                    
                    Result<OfficeUser>? failure = officeUsers.Find(ou => ou.IsFailure);
                    if (failure is not null)
                    {
                        logger.LogError("Falha ao criar advogado: {Error}", failure.Error);
                        return Result.Failure<RegisterLawyersResponse>(failure.Error);
                    }
                    
                    officeUserRepository.InsertRange(officeUsers.Where(ou => ou.IsSuccess).Select(ou => ou.Value).ToList());
                    
                    // For response: compute the emails of all lawyers that ended up bound by this operation,
                    // which is the union of those already bound and the ones we just bound.
                    var newlyBoundLawyersEmails = allLawyers
                        .Where(kvp => lawyersToBind.Contains(kvp.Key))
                        .Select(kvp => kvp.Value)
                        .ToList();
                    boundLawyersEmails = alreadyBoundLawyers.Union(newlyBoundLawyersEmails).ToList();
                }
            }
            
            // --- Save changes ---
            try
            {
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Erro ao salvar alterações no banco de dados.");
                return Result.Failure<RegisterLawyersResponse>(Error.InternalServerError());
            }
            
            // --- Build and return the response ---
            // Map the newLawyers to a list of emails for created lawyers.
            var createdLawyersEmails = newLawyers.Select(lawyer => lawyer.Email).ToList();
            
            var response = new RegisterLawyersResponse
            {
                OfficeAlreadyExists = officeEntity is not null ? officeAlreadyExists : null,
                ExistingLawyers = existingLawyersEmails,
                AlreadyBoundLawyers = alreadyBoundLawyers,
                NewlyCreatedLawyers = createdLawyersEmails,
                NewlyBoundLawyers = boundLawyersEmails
            };

            return Result.Success(response);
    }
}
