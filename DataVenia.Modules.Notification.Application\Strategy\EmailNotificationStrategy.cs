using System.Net.Mail;
using DataVenia.Common.Domain;
using DataVenia.Modules.Notification.Application.Factories;
using DataVenia.Modules.Notification.Application.Helpers;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Notification.Application.Strategy;

public sealed class EmailNotificationStrategy(
    SmtpClient client,
    string mailFrom,
    string groupName,
    ILogger<NotificationStrategyFactory> logger) : INotificationStrategy
{
    public async Task<Result> SendNotificationAsync(string message, string recipient, string? subject = null)
    {
        try
        {
            using var mailMessage = new MailMessage();
            mailMessage.From = new MailAddress(mailFrom, groupName);
            mailMessage.Subject = subject ?? "DataVenia Notification";
            mailMessage.Body = message;
            mailMessage.IsBodyHtml = MailMessageHelper.IsHtmlContent(message);

            mailMessage.To.Add(recipient);

            await client.SendMailAsync(mailMessage).ConfigureAwait(false);

            logger.LogTrace("Mail sent successfully to {Recipient}", recipient);

            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Fail to sent Mail to {Receipient} - error {Message}", recipient, ex.Message);

            return Result.Failure(new Error("500", "Fail to send email notification", ErrorType.InternalServerError));
        }
    }
}
