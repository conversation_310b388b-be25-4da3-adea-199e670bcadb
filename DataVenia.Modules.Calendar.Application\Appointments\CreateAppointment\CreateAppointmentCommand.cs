﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Calendar.Domain.Appointments;

namespace DataVenia.Modules.Calendar.Application.Appointment.CreateAppointment;

public sealed record CreateAppointmentCommand(
    Guid officeId,
    string Type, 
    string Name,
    string Description,

    Guid ResponsibleLawyerId,
    RecurrenceCommand? Recurrence,
    Guid OwnerLawyerId,
    Guid OwnerOfficeId,
    List<Guid> ParticipantLawyersIds,
    List<TimeSpan> Alerts,
    Guid? StatusId) : ICommand<Guid>;

public sealed record RecurrenceCommand(
    List<DayOfWeek>? DaysOfWeek,
    List<int>? DaysOfMonth,
    RecurrenceFrequency Frequency,
    DateOnly StartDate,
    DateOnly EndDate,
    TimeOnly StartTime,
    TimeOnly EndTime);
