<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1cda7fc0-4328-45aa-998a-07d107de4b92" name="Changes" comment="fix: allow credentials">
      <change beforePath="$PROJECT_DIR$/DataVenia.Common.Contracts/Events/LawsuitSync/LawsuitUpdateEvent.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Common.Contracts/Events/LawsuitSync/LawsuitUpdateEvent.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <excluded-from-favorite>
      <branch-storage>
        <map>
          <entry type="LOCAL">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="master" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </excluded-from-favorite>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/.nuget/packages/microsoft.net.test.sdk/17.12.0/build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/81f470d7433296f1f41327245ceeb713fe80ec1d53983d8b3755a93888d5c13e/HttpRequest.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/cb84b0c6366bb07e1e6831d31e321b5f2dee508d7317ddd6e8e62b6ce82b5d/Enum.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Common.Contracts/Events/LawsuitSync/LawsuitUpdateEvent.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250215191528_AddHarveyData.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250519035545_AddLawsuitClassAndTopic.Designer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250528010254_JudgeOrgans.Designer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250528010254_JudgeOrgans.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250603181833_AddClassesAndTopicsData.Designer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250603181833_AddClassesAndTopicsData.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250603181833_AddClassesAndTopicsData.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250603214657_RemoveItemType.Designer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/HarveyDbContextModelSnapshot.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/LawsuitClass/LawsuitClassConfiguration.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/source/repos/data-venia-api/DataVenia.Modules.Harvey.Infrastructure/LawsuitTopic/LawsuitTopicConfiguration.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Application/DataDivergences/UpdateDataDivergence/UpdateDataDivergenceCommandHandler.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2wnOeenlAlr9lpvskK0Vn1hk8Jb" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Docker.DataVenia.Api/Dockerfile.executor": "Debug",
    "Docker.docker-compose.yml: Compose Deployment.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "XThreadsFramesViewSplitterKey": "0.61276126",
    "git-widget-placeholder": "feat/lawsuit-cover-data",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Docker.docker-compose.yml: Compose Deployment">
    <configuration name="rest-api | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/rest-api.http" executionIdentifier="#1" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="DataVenia.Api: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Api/DataVenia.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Api: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Api/DataVenia.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Modules.LawsuitSync.Presentation: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Presentation/DataVenia.Modules.LawsuitSync.Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Modules.LawsuitSync.Presentation: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Presentation/DataVenia.Modules.LawsuitSync.Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Modules.LawsuitSync.Presentation: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Presentation/DataVenia.Modules.LawsuitSync.Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Api/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="datavenia.api" />
          <option name="contextFolderPath" value="C:\Users\<USER>\source\repos\data-venia-api" />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="DataVenia.Api/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="docker-compose.yml: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="composeProjectName" value="datavenia" />
          <option name="sourceFilePath" value="docker-compose.yml" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.rest-api | #1" />
        <item itemvalue="HTTP Request.rest-api | #1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1cda7fc0-4328-45aa-998a-07d107de4b92" name="Changes" comment="" />
      <created>1746673971126</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746673971126</updated>
      <workItem from="1746673972024" duration="8859000" />
      <workItem from="1747193161360" duration="33664000" />
      <workItem from="1747441182972" duration="4747000" />
      <workItem from="1747445964571" duration="57904000" />
      <workItem from="1747942754763" duration="16189000" />
      <workItem from="1748053274254" duration="59144000" />
      <workItem from="1748701292120" duration="57775000" />
      <workItem from="1748886866073" duration="36152000" />
    </task>
    <task id="LOCAL-00001" summary="feat: upgrade to .net 9 and change uuid to v7">
      <option name="closed" value="true" />
      <created>1747447450280</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1747447450280</updated>
    </task>
    <task id="LOCAL-00002" summary="fix: add version 9.0 to one last missing place in docker-compose">
      <option name="closed" value="true" />
      <created>1747447863609</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747447863609</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: add lawsuit classes and topics db tables">
      <option name="closed" value="true" />
      <created>1747581964276</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747581964276</updated>
    </task>
    <task id="LOCAL-00004" summary="feat: add lawsuit classes and topics seed endpoint">
      <option name="closed" value="true" />
      <created>1747627506303</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1747627506303</updated>
    </task>
    <task id="LOCAL-00005" summary="wip">
      <option name="closed" value="true" />
      <created>1748829720928</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1748829720928</updated>
    </task>
    <task id="LOCAL-00006" summary="feat: implement secure cookies with .datavenia.io">
      <option name="closed" value="true" />
      <created>1748830152619</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1748830152619</updated>
    </task>
    <task id="LOCAL-00007" summary="feat: implement cors for our frontend and landing page domains">
      <option name="closed" value="true" />
      <created>1748833036497</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1748833036497</updated>
    </task>
    <task id="LOCAL-00008" summary="fix: add headers and methods policy to cors">
      <option name="closed" value="true" />
      <created>1748834420668</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1748834420668</updated>
    </task>
    <task id="LOCAL-00009" summary="fix: change domain">
      <option name="closed" value="true" />
      <created>1748836308222</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1748836308222</updated>
    </task>
    <task id="LOCAL-00010" summary="fix: allow credentials">
      <option name="closed" value="true" />
      <created>1748840403411</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1748840403411</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: upgrade to .net 9 and change uuid to v7" />
    <MESSAGE value="fix: add version 9.0 to one last missing place in docker-compose" />
    <MESSAGE value="feat: add lawsuit classes and topics db tables" />
    <MESSAGE value="feat: add lawsuit classes and topics seed endpoint" />
    <MESSAGE value="wip" />
    <MESSAGE value="feat: implement secure cookies with .datavenia.io" />
    <MESSAGE value="feat: implement cors for our frontend and landing page domains" />
    <MESSAGE value="fix: add headers and methods policy to cors" />
    <MESSAGE value="fix: change domain" />
    <MESSAGE value="fix: allow credentials" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: allow credentials" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Application/DataImport/DataImportService.cs</url>
          <line>112</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Harvey.Application\DataImport\DataImportService.cs" containingFunctionPresentation="Method 'ImportLawsuitClassesAsync'">
            <startOffsets>
              <option value="4533" />
            </startOffsets>
            <endOffsets>
              <option value="4587" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Application/DataImport/DataImportService.cs</url>
          <line>203</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Harvey.Application\DataImport\DataImportService.cs" containingFunctionPresentation="Method 'ImportLawsuitTopicsAsync'">
            <startOffsets>
              <option value="8251" />
            </startOffsets>
            <endOffsets>
              <option value="8305" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Consumers/LawsuitUpdateEventConsumer.cs</url>
          <line>33</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Presentation\Consumers\LawsuitUpdateEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="1400" />
            </startOffsets>
            <endOffsets>
              <option value="1450" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/CreateAssociationEventConsumer.cs</url>
          <line>15</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.AssociationHub.Presentation\Consumers\CreateAssociationEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="579" />
            </startOffsets>
            <endOffsets>
              <option value="609" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/RemoveAssociationEventConsumer.cs</url>
          <line>13</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.AssociationHub.Presentation\Consumers\RemoveAssociationEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="564" />
            </startOffsets>
            <endOffsets>
              <option value="594" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/Workers/AssociationProcessingWorker.cs</url>
          <line>42</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.AssociationHub.Infrastructure\Workers\AssociationProcessingWorker.cs" containingFunctionPresentation="Method 'ProcessPendingRequestsAsync'">
            <startOffsets>
              <option value="1580" />
            </startOffsets>
            <endOffsets>
              <option value="1632" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Consumers/LawsuitUpdateEventConsumer.cs</url>
          <line>233</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Presentation\Consumers\LawsuitUpdateEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="10512" />
            </startOffsets>
            <endOffsets>
              <option value="10535" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/Lawsuits/Lawsuit.cs</url>
          <line>142</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Domain\Lawsuits\Lawsuit.cs" containingFunctionPresentation="Method 'CheckAndUpdateCoverData'">
            <startOffsets>
              <option value="5757" />
            </startOffsets>
            <endOffsets>
              <option value="5758" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Infrastructure/Lawsuit/LawsuitRepository.cs</url>
          <line>19</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Infrastructure\Lawsuit\LawsuitRepository.cs" containingFunctionPresentation="Method 'GetLawsuitsByCnjAsync'">
            <startOffsets>
              <option value="797" />
            </startOffsets>
            <endOffsets>
              <option value="973" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/Lawsuits/Lawsuit.cs</url>
          <line>169</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Domain\Lawsuits\Lawsuit.cs" containingFunctionPresentation="Method 'CheckAndUpdateCoverData'">
            <startOffsets>
              <option value="6836" />
            </startOffsets>
            <endOffsets>
              <option value="6892" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Application/DataDivergences/UpdateDataDivergence/UpdateDataDivergenceCommandHandler.cs</url>
          <line>23</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Application\DataDivergences\UpdateDataDivergence\UpdateDataDivergenceCommandHandler.cs" containingFunctionPresentation="Method 'Handle'">
            <startOffsets>
              <option value="1033" />
            </startOffsets>
            <endOffsets>
              <option value="1141" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="Type#DataVenia.Modules.Lawsuits.Domain.Lawsuits.DataDivergence" memberName="Fields" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
</project>