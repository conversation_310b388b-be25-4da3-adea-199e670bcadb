﻿using System.Text.Json;
using DataVenia.Common.Contracts.Events.Lawsuit;
using DataVenia.Common.Contracts.Events.LawsuitSync;
using DataVenia.Common.Contracts.Events.Notification;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Domain.Outbox;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Worker;

public sealed class OutboxDispatcher : BackgroundService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<OutboxDispatcher> _logger;

    public OutboxDispatcher(IServiceScopeFactory serviceScopeFactory,
        ILogger<OutboxDispatcher> logger)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            using (_logger.BeginScope(new
                   {
                       Class = $"Lawsuits {nameof(OutboxDispatcher)}"
                   }))
            {
                try
                {
                    _logger.LogInformation("Lawsuits OutboxDispatcher.ExecuteAsync");

                    // Create a new scope for each “batch” of outbox processing
                    using IServiceScope scope = _serviceScopeFactory.CreateScope();

                    // Resolve your scoped services from this scope
                    IOutboxRepository outboxRepository = scope.ServiceProvider.GetRequiredService<IOutboxRepository>();
                    IPublishEndpoint publishEndpoint = scope.ServiceProvider.GetRequiredService<IPublishEndpoint>();
                    IUnitOfWork unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                    IEnumerable<Outbox> messages =
                        await outboxRepository.GetUnprocessedMessagesAsync(stoppingToken);
                    foreach (Outbox message in messages)
                    {
                        switch (message.MessageType)
                        {
                            case nameof(DeactivateLawsuitSync):
                                _logger.LogInformation("DeactivateLawsuitSync {Cnj}", message.Payload);
                                var deactivatePayload =
                                    JsonSerializer.Deserialize<DeactivateLawsuitSync>(message.Payload);
                                if (deactivatePayload != null)
                                {
                                    await publishEndpoint.Publish(deactivatePayload, stoppingToken);
                                    outboxRepository.MarkAsProcessed(message);
                                    await unitOfWork.SaveChangesAsync(stoppingToken);
                                }

                                break;

                            case nameof(ActivateLawsuitSync):
                                _logger.LogInformation("ActivateLawsuitSync {Cnj}", message.Payload);
                                var activatePayload = JsonSerializer.Deserialize<ActivateLawsuitSync>(message.Payload);
                                if (activatePayload != null)
                                {
                                    await publishEndpoint.Publish(activatePayload, stoppingToken);
                                    outboxRepository.MarkAsProcessed(message);
                                    await unitOfWork.SaveChangesAsync(stoppingToken);
                                }

                                break;

                            case nameof(LawsuitStepsUpdate):
                                _logger.LogInformation("LawsuitStepsUpdate {Cnj}", message.Payload);
                                var updateStepsPayload =
                                    JsonSerializer.Deserialize<LawsuitStepsUpdate>(message.Payload);
                                if (updateStepsPayload != null)
                                {
                                    await publishEndpoint.Publish(updateStepsPayload, stoppingToken);
                                    outboxRepository.MarkAsProcessed(message);
                                    await unitOfWork.SaveChangesAsync(stoppingToken);
                                }

                                break;

                            default:
                                _logger.LogWarning("Unknown outbox message type: {MessageType}", message.MessageType);
                                break;
                        }
                    }

                    _logger.LogInformation("Finished Lawsuits OutboxDispatcher.ExecuteAsync with {Number} messages",
                        messages.Count());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error dispatching lawsuit outbox messages");
                }
            }


            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
}
