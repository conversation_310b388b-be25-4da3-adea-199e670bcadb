﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateCase;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.CasesData;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Application.Cases.CreateCase;

public sealed class CreateCaseCommandHandler(
    ICaseRepository caseRepository,
    ICaseDataRepository caseDataRepository,
    IOfficeLawyerFacade officeLawyerFacade,
    IUnitOfWork unitOfWork,
    ILogger<CreateCaseCommandHandler> logger) : ICommandHandler<CreateCaseCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateCaseCommand request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<OfficeLawyerDto> responsibles = await officeLawyerFacade.GetActiveByOfficeAndLawyers(request.OfficeId, request.ResponsibleIds);

        if (responsibles.Count != request.ResponsibleIds.Count)
            return Result.Failure<Guid>(Error.Conflict("NotAuthorized", "There are nonexistent users being attached to the case"));

        //var tags = new List<Tag>();

        // criar o lawsuit data tbm e as tags direito
        var @case = Case.Create(
            request.OfficeId
        );

        var caseData = CaseData.Create(
                request.Title,
                @case.Id,
                request.FolderId,
                request.CauseValue,
                request.ConvictionValue,
                request.Description,
                request.Observations,
                request.Access,
                // request.Parties,
                request.ResponsibleIds);

        caseRepository.Insert(@case);

        caseDataRepository.Insert(caseData);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving case to database: {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return @case.Id;
    }
}
