﻿using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.LawsuitSync.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddSubscriptions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<List<string>>(
                name: "subscriptions",
                schema: "lawsuit-sync",
                table: "tb_lawsuit_monitoring_configuration",
                type: "text[]",
                nullable: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "subscriptions",
                schema: "lawsuit-sync",
                table: "tb_lawsuit_monitoring_configuration");
        }
    }
}
