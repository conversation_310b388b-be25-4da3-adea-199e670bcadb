﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.User.Login;
public sealed class LoginCommandHandler(
    IIdentityProviderService identityProviderService, ILogger<LoginCommandHandler> logger
    ) : ICommandHandler<LoginCommand, LoginResponse>
{
    public async Task<Result<LoginResponse>> <PERSON>le(LoginCommand request, CancellationToken cancellationToken)
    {
        try
        {
            Result<LoginResponse> loginResponse = await identityProviderService.AuthenticateAsync(request.Username, request.Password, cancellationToken);
            return loginResponse;
        } catch(Exception ex)
        {
            logger.LogError(ex, "Error logging in user. {Username}", request.Username);
            return Result.Failure<LoginResponse>(Error.NotFound("Login.Not.Found", "User and password combination not found"));
        }

    }
}
