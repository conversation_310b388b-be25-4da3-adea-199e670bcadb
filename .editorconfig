root = true

# C# files
[*.cs]

#### Core EditorConfig Options ####

# Indentation and spacing
indent_size = 4
indent_style = space
tab_width = 4

# New line preferences
end_of_line = crlf
insert_final_newline = true

#### .NET Coding Conventions ####

# Organize usings
dotnet_separate_import_directive_groups = false
dotnet_sort_system_directives_first = true

# this. and Me. preferences
dotnet_style_qualification_for_event = false:error
dotnet_style_qualification_for_field = false:error
dotnet_style_qualification_for_method = false:error
dotnet_style_qualification_for_property = false:error

# Language keywords vs BCL types preferences
dotnet_style_predefined_type_for_locals_parameters_members = true:error
dotnet_style_predefined_type_for_member_access = true:error

# Parentheses preferences
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_other_binary_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:error

# Modifier preferences
dotnet_style_require_accessibility_modifiers = for_non_interface_members:error

# Expression-level preferences
dotnet_style_coalesce_expression = true:none
dotnet_style_collection_initializer = true:error
dotnet_style_explicit_tuple_names = true:error
dotnet_style_null_propagation = true:error
dotnet_style_object_initializer = true:error
dotnet_style_prefer_auto_properties = true:warning
dotnet_style_prefer_compound_assignment = true:error
dotnet_style_prefer_conditional_expression_over_assignment = true:error
dotnet_style_prefer_conditional_expression_over_return = true:none
dotnet_style_prefer_inferred_anonymous_type_member_names = true:error
dotnet_style_prefer_inferred_tuple_names = true:error
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:error
csharp_indent_labels = one_less_than_current
csharp_using_directive_placement = outside_namespace:error
csharp_style_namespace_declarations = file_scoped:error
csharp_style_prefer_method_group_conversion = true:silent
csharp_style_prefer_top_level_statements = true:silent
csharp_style_prefer_primary_constructors = true:none
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = true:error
csharp_style_expression_bodied_properties = true:error
csharp_style_expression_bodied_indexers = true:error
csharp_style_expression_bodied_accessors = true:error
csharp_style_expression_bodied_lambdas = true:none
csharp_style_expression_bodied_local_functions = true:error
csharp_prefer_simple_using_statement = true:suggestion
csharp_prefer_braces = true:silent
dotnet_diagnostic.IDE0161.severity = none
dotnet_diagnostic.S125.severity = none

[*.{cs,vb}]
dotnet_style_prefer_simplified_boolean_expressions = true:suggestion
dotnet_style_prefer_simplified_interpolation = true:suggestion
dotnet_style_namespace_match_folder = true:suggestion

# Field preferences
dotnet_style_readonly_field = true:error

# Parameter preferences
dotnet_code_quality_unused_parameters = all:error

#### C# Coding Conventions ####

# Namespace preferences
csharp_style_namespace_declarations= file_scoped:error

# var preferences
# csharp_style_var_elsewhere = false:error
# csharp_style_var_for_built_in_types = false:error
# csharp_style_var_when_type_is_apparent = true:error

# Expression-bodied members
csharp_style_expression_bodied_accessors = true:error
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_indexers = true:error
csharp_style_expression_bodied_lambdas = true:none
csharp_style_expression_bodied_local_functions = true:error
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_operators = true:error
csharp_style_expression_bodied_properties = true:error

# Pattern matching preferences
csharp_style_pattern_matching_over_as_with_null_check = true:error
csharp_style_pattern_matching_over_is_with_cast_check = true:error
csharp_style_prefer_switch_expression = true:error

# Null-checking preferences
csharp_style_conditional_delegate_call = true:error

# Modifier preferences
csharp_prefer_static_local_function = true:error
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async

# Expression-level preferences
csharp_prefer_simple_default_expression = true:error
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_style_inlined_variable_declaration = true:error
csharp_style_pattern_local_over_anonymous_function = true:error
csharp_style_prefer_index_operator = true:suggestion
csharp_style_prefer_range_operator = true:suggestion
csharp_style_throw_expression = true:suggestion
csharp_style_unused_value_assignment_preference = discard_variable:silent
csharp_style_unused_value_expression_statement_preference = discard_variable:none
csharp_style_prefer_method_group_conversion = true:silent
csharp_style_prefer_top_level_statements = true:silent

# 'using' directive preferences
csharp_using_directive_placement = outside_namespace:error

#### C# Formatting Rules ####

# New line preferences
csharp_new_line_before_catch =true
csharp_new_line_before_else =true
csharp_new_line_before_finally =true
csharp_new_line_before_members_in_anonymous_types = false
csharp_new_line_before_members_in_object_initializers = false
csharp_new_line_before_open_brace = all
csharp_new_line_between_query_expression_clauses = true

# Indentation preferences
csharp_indent_block_contents = true
csharp_indent_braces = false
csharp_indent_case_contents = true
csharp_indent_case_contents_when_block = true
csharp_indent_labels = one_less_than_current
csharp_indent_switch_labels = true

# Space preferences
csharp_space_after_cast = false
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_after_comma = true
csharp_space_after_dot = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_after_semicolon_in_for_statement = true
csharp_space_around_binary_operators = before_and_after
csharp_space_around_declaration_statements = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_before_comma = false
csharp_space_before_dot = false
csharp_space_before_open_square_brackets = false
csharp_space_before_semicolon_in_for_statement = false
csharp_space_between_empty_square_brackets = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_declaration_name_and_open_parenthesis = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_between_square_brackets = false

# Wrapping preferences
csharp_preserve_single_line_blocks = true
csharp_preserve_single_line_statements = false

#### Naming styles ####

# Naming rules

dotnet_naming_rule.interface_should_be_begins_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_begins_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_begins_with_i.style = begins_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

# Symbol specifications

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers = 

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers = 

# Naming styles

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.required_suffix = 
dotnet_naming_style.begins_with_i.word_separator = 
dotnet_naming_style.begins_with_i.capitalization = pascal_case

# Custom Rules - configure these as required

# .NET Code Analyzers rules

# CA1000: Do not declare static members on generic types
dotnet_diagnostic.CA1000.severity = none

# CA1002: Do not expose generic lists
dotnet_diagnostic.CA1002.severity = none

# CA1008: Enums should have zero value
dotnet_diagnostic.CA1008.severity = none

# CA1019: Define accessors for attribute arguments
dotnet_diagnostic.CA1019.severity = none

# CA1024: Use properties where appropriate
dotnet_diagnostic.CA1024.severity = none

# CA1030: Use events where appropriate
dotnet_diagnostic.CA1030.severity = none

# CA1031: Do not catch general exception types
dotnet_diagnostic.CA1031.severity = none

# CA1032: Implement standard exception constructors
dotnet_diagnostic.CA1032.severity = none

# CA1034: Nested types should not be visible
dotnet_diagnostic.CA1034.severity = none

# CA1040: Avoid empty interfaces
dotnet_diagnostic.CA1040.severity = none

# CA1051: Do not declare visible instance fields
dotnet_diagnostic.CA1051.severity = none

# CA1056: URI-like properties should not be strings
dotnet_diagnostic.CA1056.severity = none

# CA1062: Validate arguments of public methods
dotnet_diagnostic.CA1062.severity = none

# CA1063: Implement IDisposable Correctly
dotnet_diagnostic.CA1063.severity = none

# CA1307: Specify StringComparison for clarity
dotnet_diagnostic.CA1307.severity = none

# CA1308: Normalize strings to uppercase
dotnet_diagnostic.CA1308.severity = none

# CA1700: Do not name enum values 'Reserved'
dotnet_diagnostic.CA1700.severity = none

# CA1707: Identifiers should not contain underscores
dotnet_diagnostic.CA1707.severity = none

# CA1711: Identifiers should not have incorrect suffix
dotnet_diagnostic.CA1711.severity = none

# CA1716: Identifiers should not match keywords
dotnet_diagnostic.CA1716.severity = none

# CA1724: Type names should not match namespaces
dotnet_diagnostic.CA1724.severity = none

# CA1812: Avoid uninstantiated internal classes
dotnet_diagnostic.CA1812.severity = none

# CA1816: Dispose methods should call SuppressFinalize
dotnet_diagnostic.CA1816.severity = none

# CA1819: Properties should not return arrays
dotnet_diagnostic.CA1819.severity = none

# CA1822: Mark members as static
dotnet_diagnostic.CA1822.severity = none

# CA1848: Use the LoggerMessage delegates
dotnet_diagnostic.CA1848.severity = none

# CA1860: Avoid using 'Enumerable.Any()' extension method
dotnet_diagnostic.CA1860.severity = none

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = none

# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = none

# CA2211: Non-constant fields should not be visible
dotnet_diagnostic.CA2211.severity = none

# CA2213: Disposable fields should be disposed
dotnet_diagnostic.CA2213.severity = none

# CA2225: Operator overloads have named alternates
dotnet_diagnostic.CA2225.severity = none

# CA2227: Collection properties should be read only
dotnet_diagnostic.CA2227.severity = none

# CA2234: Pass system uri objects instead of strings
dotnet_diagnostic.CA2234.severity = none

# CA2326: Do not use TypeNameHandling values other than None
dotnet_diagnostic.CA2326.severity = none

# CA2326: Do not use insecure JsonSerializerSettings
dotnet_diagnostic.CA2327.severity = none

# CS8600: Converting null literal or possible null value to non-nullable type.
dotnet_diagnostic.CS8600.severity = none

# CS8603: Possible null reference return.
dotnet_diagnostic.CS8603.severity = none

# CS8618: Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
dotnet_diagnostic.CS8618.severity = none

# IDE Code Analyzers rules

# IDE0046: Convert to conditional expression
dotnet_diagnostic.IDE0046.severity = none

# IDE0053: Use expression body for lambda expression
dotnet_diagnostic.IDE0053.severity = none

# IDE0270: Use coalesce expression
dotnet_diagnostic.IDE0270.severity = none

# IDE0290: Use primary constructor
dotnet_diagnostic.IDE0290.severity = none

# SonarAnalyzer.CSharp rules

# S112: General or reserved exceptions should never be thrown
dotnet_diagnostic.S112.severity = none

# S1075: URIs should not be hardcoded
dotnet_diagnostic.S1075.severity = none

# S2094: Utility classes should not have public constructors
dotnet_diagnostic.S1118.severity = none

# S1144: Unused private types or members should be removed
dotnet_diagnostic.S1144.severity = none

# S2094: Classes should not be empty
dotnet_diagnostic.S2094.severity = none

# S2365: Properties should not make collection or array copies
dotnet_diagnostic.S2365.severity = none

# S3267: Loops should be simplified with "LINQ" expressions
dotnet_diagnostic.S3267.severity = none

# S3881: "IDisposable" should be implemented correctly
dotnet_diagnostic.S3881.severity = none

# S4136: Method overloads should be grouped together
dotnet_diagnostic.S4136.severity = none

# S6605: Collection-specific "Exists" method should be used instead of the "Any" extension
dotnet_diagnostic.S6605.severity = none
dotnet_style_operator_placement_when_wrapping = beginning_of_line
tab_width = 4
indent_size = 4
end_of_line = crlf
dotnet_style_coalesce_expression = true:none
dotnet_style_null_propagation = true:error
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:error
dotnet_style_prefer_auto_properties = true:warning
dotnet_style_object_initializer = true:error
dotnet_style_collection_initializer = true:error
dotnet_style_prefer_conditional_expression_over_assignment = true:error
dotnet_style_prefer_conditional_expression_over_return = true:none
dotnet_style_explicit_tuple_names = true:error
dotnet_style_prefer_inferred_tuple_names = true:error

[**/Migrations/*.cs]
# Desabilitar ou reduzir a severidade de erros em migrations geradas automaticamente
generated_code = true
# Ignorar avisos de estilo (exemplo de regra)
dotnet_diagnostic.IDE1006.severity = silent  # Ignora o aviso de convenção de nomeação

# Desabilitar todos os erros
dotnet_diagnostic.*.severity = none

# Desabilitar outros erros comuns que ocorrem em migrations
dotnet_diagnostic.CS1591.severity = none  # Ignora avisos de XML documentation (ex: CS1591)
dotnet_diagnostic.CS8019.severity = none  # Ignora avisos de usings desnecessários
dotnet_diagnostic.IDE0051.severity = none  # Ignora avisos de membros privados não utilizados
dotnet_diagnostic.IDE0059.severity = none  # Ignora avisos de variáveis atribuídas, mas não usadas

dotnet_diagnostic.CA1861.severity = none  # Ignora o erro CA1861 nas migrations
dotnet_diagnostic.CA1814.severity = none  # Ignora o uso de arrays multidimensionais, se aplicável
dotnet_diagnostic.CA1812.severity = none  # Ignora classes internas nunca instanciadas