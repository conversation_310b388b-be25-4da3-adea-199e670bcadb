﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Domain.Status;
using StatusDomain = DataVenia.Modules.Calendar.Domain.Status.Status;

namespace DataVenia.Modules.Calendar.Application.Status.GetAllStatus;
public sealed class GetAllStatusQueryHandler(
    IStatusRepository statusRepository) : IQueryHandler<GetAllStatusQuery, IReadOnlyCollection<StatusResponse>>
{
    public async Task<Result<IReadOnlyCollection<StatusResponse>>> Handle(GetAllStatusQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<StatusDomain> status = await statusRepository.GetManyAsync(x => x.OfficeId == request.OfficeId, cancellationToken);

        var statusResponse = status.Select(status => new StatusResponse(
            status.Id,
            status.DisplayName,
            status.Description,
            status.OrderIndex,
            status.OfficeId,
            status.CreatedAt,
            status.DeletedAt)).ToList();

        return statusResponse;
    }
}
