﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Calendar.Application.Appointment.CreateAppointment;
using DataVenia.Modules.Calendar.Domain.Appointments;

namespace DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointment;
public sealed record UpdateAppointmentCommand(
    Guid lawyerId,
    Guid officeId,
    Guid id, 
    string type,
    string name, 
    string description, 
    Guid responsibleLawyerId,
    RecurrenceCommand? recurrence, 
    List<Guid> participantLawyersIds,
    List<TimeSpan> alerts,
    Guid statusId) : ICommand;