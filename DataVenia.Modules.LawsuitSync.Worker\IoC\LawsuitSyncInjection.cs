using DataVenia.Modules.LawsuitSync.Worker.Workers;
using MassTransit;

namespace DataVenia.Modules.LawsuitSync.Worker.IoC;

public static class LawsuitSyncInjection
{
    public static IBusRegistrationConfigurator RegisterLawsuitSyncModuleConsumer(
        this IBusRegistrationConfigurator configurator)
    {
        configurator = configurator ?? throw new ArgumentNullException(nameof(configurator));
        
        configurator.AddConsumer<ActivateLawsuitSyncConsumer>();
        configurator.AddConsumer<RegisterLawsuitByCnjConsumer>();
        configurator.AddConsumer<InactivateLawsuitSyncConsumer>();
        
        return configurator;
    }
}
