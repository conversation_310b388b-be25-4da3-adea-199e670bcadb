﻿using System.Text.Json;
using DataVenia.Modules.Calendar.Domain.Appointments;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Calendar.Infrastructure.Appointments;

internal sealed class AppointmentConfiguration : IEntityTypeConfiguration<Appointment>
{
    public void Configure(EntityTypeBuilder<Appointment> builder)
    {
        builder.ToTable("appointment");

        builder.Property(u => u.Id).HasColumnType("uuid");
        builder.HasKey(u => u.Id);

        builder.Property(a => a.Type)
            .HasMaxLength(64)
            .IsRequired();
        builder.HasIndex(x => x.Type);

        builder.Property(a => a.Name) 
            .HasMaxLength(128)
            .IsRequired();

        builder.Property(a => a.Description)
            .HasMaxLength(500);

        builder.Property(a => a.ResponsibleLawyerId)
            .IsRequired();

        builder.Property(a => a.OwnerLawyerId)
                   .IsRequired();

        builder.Property(a => a.OwnerOfficeId)
               .IsRequired();

        // Configure AppointmentParticipant entity
        builder.HasMany(a => a.Participants)
           .WithOne(p => p.Appointment)
           .HasForeignKey(p => p.AppointmentId)
           .OnDelete(DeleteBehavior.NoAction);


        // Configure Alerts as JSON
        builder.Property(a => a.Alerts)
               .HasConversion(
                   v => JsonSerializer.Serialize(v.ToList(), (JsonSerializerOptions)null),
           v => JsonSerializer.Deserialize<List<TimeSpan>>(v, (JsonSerializerOptions)null) ?? new List<TimeSpan>())
               .HasColumnName("Alerts")
               .HasColumnType("jsonb"); // Use appropriate type based on your DB

        builder.HasOne(a => a.Status)
            .WithMany()
            .HasForeignKey(a => a.StatusId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
