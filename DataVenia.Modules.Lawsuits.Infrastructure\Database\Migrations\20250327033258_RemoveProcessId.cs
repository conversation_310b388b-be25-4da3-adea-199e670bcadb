﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemoveProcessId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "process_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.RenameColumn(
                name: "responsibles",
                schema: "lawsuit",
                table: "lawsuit_response",
                newName: "responsible_ids");

            migrationBuilder.AddColumn<string>(
                name: "access",
                schema: "lawsuit",
                table: "lawsuit_response",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "description",
                schema: "lawsuit",
                table: "lawsuit_response",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "observations",
                schema: "lawsuit",
                table: "lawsuit_response",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "access",
                schema: "lawsuit",
                table: "lawsuit_response");

            migrationBuilder.DropColumn(
                name: "description",
                schema: "lawsuit",
                table: "lawsuit_response");

            migrationBuilder.DropColumn(
                name: "observations",
                schema: "lawsuit",
                table: "lawsuit_response");

            migrationBuilder.RenameColumn(
                name: "responsible_ids",
                schema: "lawsuit",
                table: "lawsuit_response",
                newName: "responsibles");

            migrationBuilder.AddColumn<string>(
                name: "process_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");
        }
    }
}
