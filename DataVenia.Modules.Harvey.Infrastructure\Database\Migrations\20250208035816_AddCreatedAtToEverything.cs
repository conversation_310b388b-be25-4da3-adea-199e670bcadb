﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddCreatedAtToEverything : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "party_type",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "harvey",
                table: "party_type",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "legal_instance",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "harvey",
                table: "legal_instance",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "legal_category",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "harvey",
                table: "legal_category",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_type",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "harvey",
                table: "lawsuit_type",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_status",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "harvey",
                table: "lawsuit_status",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "forum",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "harvey",
                table: "forum",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "court_division",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "harvey",
                table: "court_division",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingAppeal",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3697), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingClient",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3708), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingDecision",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3711), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Cancelled",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3705), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Closed",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3702), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Completed",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3699), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "InProgress",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3683), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Pending",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 441, DateTimeKind.Utc).AddTicks(125), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Suspended",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3693), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "CollectionAction",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3456), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "DivorceAction",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3741), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "HabeasCorpus",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3746), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "LaborAction",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3742), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "Other",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3746), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "TaxEnforcementAction",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3743), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "WritOfMandamus",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3744), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "FirstInstance",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8413), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "Other",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8586), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "SecondInstance",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8583), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "STF",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8586), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "STJ",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8584), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Defendant",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3663), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Lawyer",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3501), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Other",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3664), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Plaintiff",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3661), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Witness",
                columns: new[] { "created_at", "updated_at" },
                values: new object[] { new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3664), null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "harvey",
                table: "party_type");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "harvey",
                table: "party_type");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "harvey",
                table: "legal_instance");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "harvey",
                table: "legal_instance");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "harvey",
                table: "legal_category");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "harvey",
                table: "legal_category");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_type");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "harvey",
                table: "lawsuit_type");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_status");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "harvey",
                table: "lawsuit_status");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "harvey",
                table: "forum");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "harvey",
                table: "forum");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "harvey",
                table: "court_division");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "harvey",
                table: "court_division");
        }
    }
}
