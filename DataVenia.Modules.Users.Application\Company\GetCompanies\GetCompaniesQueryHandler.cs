﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Client.GetClients;
using DataVenia.Modules.Users.Domain.Company;

namespace DataVenia.Modules.Users.Application.Company.GetCompanies;
public sealed class GetCompaniesQueryHandler(ICompanyRepository companyRepository) : IQueryHandler<GetCompaniesQuery, IReadOnlyCollection<GetCompaniesResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetCompaniesResponse>>> Handle(GetCompaniesQuery request, CancellationToken cancellationToken)
    {
        // buscar todos filtrando por office
        IReadOnlyCollection<Domain.Company.Company> companies = (await companyRepository.GetManyAsync(x => x.OfficeId == request.officeId, cancellationToken)).ToList();

        var companiesResponse = companies.Select(company =>
        {
            return new GetCompaniesResponse(
                Id: company.Id,
                Name: company.Name,
                Cnpj: company.Cnpj,
                Clients: company.ClientCompanies.Select(cc => cc.ClientId).ToList()
                );
        }).ToList();

        return companiesResponse;
    }
}
