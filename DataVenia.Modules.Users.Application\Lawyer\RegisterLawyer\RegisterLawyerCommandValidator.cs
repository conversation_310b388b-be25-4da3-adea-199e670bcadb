﻿using DataVenia.Modules.Users.Application.Lawyers.RegisterLawyer;
using FluentValidation;

namespace DataVenia.Modules.Users.Application.Lawyer.RegisterLawyer;

internal sealed class RegisterLawyerCommandValidator : AbstractValidator<RegisterLawyerCommand>
{
    public RegisterLawyerCommandValidator()
    {
        // RuleFor(c => c.FirstName).NotEmpty();
        // RuleFor(c => c.LastName).NotEmpty();
        // RuleFor(c => c.Email).EmailAddress().NotEmpty();
        // RuleFor(c => c.Password).MinimumLength(6);
    }
}
