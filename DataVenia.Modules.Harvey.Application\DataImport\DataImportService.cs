﻿using System.Text.Json;
using DataVenia.Modules.Harvey.Application.Abstractions.Data;
using DataVenia.Modules.Harvey.Domain.LawsuitClass;
using DataVenia.Modules.Harvey.Domain.LawsuitTopic;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Harvey.Application.DataImport;

public class DataImportService
{
    private readonly ILawsuitClassRepository _lawsuitClassRepository;
    private readonly ILawsuitTopicRepository _lawsuitTopicRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DataImportService> _logger;

    public DataImportService(
        ILawsuitClassRepository lawsuitClassRepository,
        ILawsuitTopicRepository lawsuitTopicRepository,
        IUnitOfWork unitOfWork,
        ILogger<DataImportService> logger)
    {
        _lawsuitClassRepository = lawsuitClassRepository;
        _lawsuitTopicRepository = lawsuitTopicRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<ImportResult> ImportLawsuitClassesAsync(string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting import of lawsuit classes from {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                _logger.LogError("File not found: {FilePath}", filePath);
                return new ImportResult(false, "File not found");
            }

            string jsonContent = await File.ReadAllTextAsync(filePath, cancellationToken);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            var lawsuitClassesData = JsonSerializer.Deserialize<List<LawsuitClassDto>>(jsonContent, options);

            if (lawsuitClassesData == null || !lawsuitClassesData.Any())
            {
                _logger.LogWarning("No lawsuit classes found in the file");
                return new ImportResult(false, "No lawsuit classes found in the file");
            }

            int importedCount = 0;
            foreach (var classDto in lawsuitClassesData)
            {
                var lawsuitClassResult = Domain.LawsuitClass.LawsuitClass.Create(
                    classDto.Id,
                    classDto.Type ?? string.Empty,
                    classDto.LegalProvision ?? string.Empty,
                    classDto.Article ?? string.Empty,
                    classDto.Acronym ?? string.Empty,
                    classDto.OldAcronym ?? string.Empty,
                    classDto.ActivePole ?? string.Empty,
                    classDto.PassivePole ?? string.Empty,
                    classDto.Glossary ?? string.Empty,
                    classDto.IsOwnNumbering,
                    classDto.IsFirstInstance,
                    classDto.IsSecondInstance,
                    classDto.JustEsJuizadoEs,
                    classDto.JustEsTurmas,
                    classDto.JustEs1grauMil,
                    classDto.JustEs2grauMil,
                    classDto.JustEsJuizadoEsFp,
                    classDto.JustTuEsUn,
                    classDto.JustFed1grau,
                    classDto.JustFed2grau,
                    classDto.JustFedJuizadoEs,
                    classDto.JustFedTurmas,
                    classDto.JustFedNacional,
                    classDto.JustFedRegional,
                    classDto.JustTrab1grau,
                    classDto.JustTrab2grau,
                    classDto.JustTrabTst,
                    classDto.JustTrabCsjt,
                    classDto.Stf,
                    classDto.Stj,
                    classDto.Cjf,
                    classDto.Cnj,
                    classDto.JustMilUniao1grau,
                    classDto.JustMilUniaoStm,
                    classDto.JustMilEst1grau,
                    classDto.JustMilEstTjm,
                    classDto.JustElei1grau,
                    classDto.JustElei2grau,
                    classDto.JustEleiTse,
                    classDto.IncludedBy,
                    classDto.IncludedAt,
                    classDto.UserIp,
                    classDto.UpdatedBy,
                    classDto.ProcedureId,
                    classDto.ProcedureOrigin,
                    classDto.IsCriminal ?? false);

                if (lawsuitClassResult.IsSuccess)
                {
                    _lawsuitClassRepository.Add(lawsuitClassResult.Value);
                    importedCount++;
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully imported {Count} lawsuit classes", importedCount);
            return new ImportResult(true, $"Successfully imported {importedCount} lawsuit classes");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing lawsuit classes");
            return new ImportResult(false, $"Error importing lawsuit classes: {ex.Message}");
        }
    }

    public async Task<ImportResult> ImportLawsuitTopicsAsync(string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting import of lawsuit topics from {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                _logger.LogError("File not found: {FilePath}", filePath);
                return new ImportResult(false, "File not found");
            }

            string jsonContent = await File.ReadAllTextAsync(filePath, cancellationToken);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            var lawsuitTopicsData = JsonSerializer.Deserialize<List<LawsuitTopicDto>>(jsonContent, options);

            if (lawsuitTopicsData == null || !lawsuitTopicsData.Any())
            {
                _logger.LogWarning("No lawsuit topics found in the file");
                return new ImportResult(false, "No lawsuit topics found in the file");
            }

            int importedCount = 0;
            foreach (var topicDto in lawsuitTopicsData)
            {
                var lawsuitTopicResult = Domain.LawsuitTopic.LawsuitTopic.Create(
                    topicDto.Id,
                    topicDto.LegalProvision ?? string.Empty,
                    topicDto.Article ?? string.Empty,
                    topicDto.Glossary ?? string.Empty,
                    topicDto.IsSecret ?? false,
                    topicDto.SecondaryTopic,
                    topicDto.PreviousCrime,
                    topicDto.IsFirstInstance,
                    topicDto.IsSecondInstance,
                    topicDto.JustEsJuizadoEs,
                    topicDto.JustEsTurmas,
                    topicDto.JustEs1grauMil,
                    topicDto.JustEs2grauMil,
                    topicDto.JustEsJuizadoEsFp,
                    topicDto.JustTuEsUn,
                    topicDto.JustFed1grau,
                    topicDto.JustFed2grau,
                    topicDto.JustFedJuizadoEs,
                    topicDto.JustFedTurmas,
                    topicDto.JustFedNacional,
                    topicDto.JustFedRegional,
                    topicDto.JustTrab1grau,
                    topicDto.JustTrab2grau,
                    topicDto.JustTrabTst,
                    topicDto.JustTrabCsjt,
                    topicDto.Stf,
                    topicDto.Stj,
                    topicDto.Cjf,
                    topicDto.Cnj,
                    topicDto.JustMilUniao1grau,
                    topicDto.JustMilUniaoStm,
                    topicDto.JustMilEst1grau,
                    topicDto.JustMilEstTjm,
                    topicDto.JustElei1grau,
                    topicDto.JustElei2grau,
                    topicDto.JustEleiTse,
                    topicDto.UpdatedBy,
                    topicDto.UpdatedAt,
                    topicDto.UserIp,
                    topicDto.UpdatedById);

                if (lawsuitTopicResult.IsSuccess)
                {
                    _lawsuitTopicRepository.Add(lawsuitTopicResult.Value);
                    importedCount++;
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully imported {Count} lawsuit topics", importedCount);
            return new ImportResult(true, $"Successfully imported {importedCount} lawsuit topics");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing lawsuit topics");
            return new ImportResult(false, $"Error importing lawsuit topics: {ex.Message}");
        }
    }
}
