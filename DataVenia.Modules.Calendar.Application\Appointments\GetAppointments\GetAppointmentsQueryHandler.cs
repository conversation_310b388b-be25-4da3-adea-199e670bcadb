﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Application.Appointments.GetAppointment;
using DataVenia.Modules.Calendar.Domain.Appointments;
using AppointmentDomain = DataVenia.Modules.Calendar.Domain.Appointments.Appointment;

namespace DataVenia.Modules.Calendar.Application.Appointments.GetAppointments;
public sealed class GetAppointmentsQueryHandler(
    IAppointmentRepository appointmentRepository) : IQueryHandler<GetAppointmentsQuery, IReadOnlyCollection<AppointmentResponse>>
{
    public async Task<Result<IReadOnlyCollection<AppointmentResponse>>> Handle(GetAppointmentsQuery request, CancellationToken cancellationToken)
    {
        // Get all appointments that the lawyer is related to (owner, responsible or participant)
        IReadOnlyCollection<AppointmentDomain> appointments = await appointmentRepository.GetManyAsync(
            x => x.OwnerLawyerId == request.LawyerId || 
            x.ResponsibleLawyerId == request.LawyerId || 
            x.Participants.Any(y => y.LawyerId == request.LawyerId), 
            cancellationToken);

        var appointmentsResponse = appointments.Select(appointment => new AppointmentResponse(
            appointment.Id,
            appointment.Type,
            appointment.Name,
            appointment.Description,
            appointment.ResponsibleLawyerId,
            appointment.Recurrence,
            appointment.OwnerLawyerId,
            appointment.Participants,
            appointment.Alerts,
            appointment.Status
            )).ToList();

        return appointmentsResponse;
    }
}
