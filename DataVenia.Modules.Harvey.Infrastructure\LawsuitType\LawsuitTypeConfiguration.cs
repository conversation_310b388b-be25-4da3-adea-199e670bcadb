﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ActionDomain = DataVenia.Modules.Harvey.Domain.Action.LawsuitType;
namespace DataVenia.Modules.Harvey.Infrastructure.Forum;

public sealed class LawsuitTypeConfiguration : IEntityTypeConfiguration<ActionDomain>
{
    public void Configure(EntityTypeBuilder<ActionDomain> builder)
    {
        builder.ToTable("lawsuit_type");

        builder.<PERSON><PERSON>ey(f => f.Id);

        builder.Property(f => f.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        
        builder.Property(f => f.DisplayName)
            .IsRequired()
            .HasMaxLength(512);
        builder.HasIndex(x => x.DisplayName).IsUnique();
    }
}
