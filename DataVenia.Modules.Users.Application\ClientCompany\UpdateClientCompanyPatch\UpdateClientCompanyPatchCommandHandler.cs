﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.ClientCompany.UpdateClientCompany;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.ClientCompany.UpdateClientCompanyPatch;

public class UpdateClientCompanyPatchCommandHandler(IClientCompanyRepository clientCompanyRepository, IUnitOfWork unitOfWork, ILogger<UpdateClientCompanyPatchCommandHandler> logger)
    : ICommandHandler<UpdateClientCompanyPatchCommand>
{
    public async Task<Result> Handle(UpdateClientCompanyPatchCommand request, CancellationToken cancellationToken)
    {
        Domain.ClientCompany.ClientCompany? clientCompany = await clientCompanyRepository.GetSingleAsync(x => x.Id == request.Id, cancellationToken);

        if (clientCompany is null)
        {
            logger.LogError("ClientCompany not found: {ClientCompany}", request.Id);
            return Result.Failure(Error.NotFound("Not.Found", $"ClientCompany with Id {request.Id} not found"));
        }

        clientCompany.Update(request.Role);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        } catch (Exception ex)
        {
            logger.LogError(ex, "Error saving clientCompany to database.");
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return Result.Success();
    }
}
