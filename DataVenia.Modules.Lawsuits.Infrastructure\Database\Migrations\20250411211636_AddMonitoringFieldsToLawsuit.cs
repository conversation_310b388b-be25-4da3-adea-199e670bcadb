﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddMonitoringFieldsToLawsuit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "monitoring_enabled",
                schema: "lawsuit",
                table: "lawsuit",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "monitoring_history",
                schema: "lawsuit",
                table: "lawsuit",
                type: "jsonb",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "outboxes",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    message_type = table.Column<string>(type: "text", nullable: false),
                    payload = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    processed = table.Column<bool>(type: "boolean", nullable: false),
                    processed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_outboxes", x => x.id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "outboxes",
                schema: "lawsuit");

            migrationBuilder.DropColumn(
                name: "monitoring_enabled",
                schema: "lawsuit",
                table: "lawsuit");

            migrationBuilder.DropColumn(
                name: "monitoring_history",
                schema: "lawsuit",
                table: "lawsuit");
        }
    }
}
