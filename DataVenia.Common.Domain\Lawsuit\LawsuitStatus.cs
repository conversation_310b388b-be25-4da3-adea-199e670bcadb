﻿namespace DataVenia.Common.Domain.Lawsuit;

public enum LawsuitStatus
{
    None = 0,
    /// <summary>
    /// Ativo (Em Tramitação)
    /// </summary>
    InProgress = 1,
    /// <summary>
    /// Recurso interposto ou apelação em andamento
    /// </summary>
    AwaitingAppeal = 2,
    /// <summary>
    /// Cumprimento de sentença ou fase de execução
    /// </summary>
    AwaitingDecision = 3,
    Suspended = 4,
    Archived = 5,
    /// <summary>
    ///  Processo encerrado com trânsito em julgado ou baixa
    /// </summary>
    Completed = 6,
    Cancelled = 7,
    /// <summary>
    /// Processo remetido a instância superior, como STJ, STF ou TST, para análise de recurso.
    /// </summary>
    RemittedToHigherCourt = 8
}
