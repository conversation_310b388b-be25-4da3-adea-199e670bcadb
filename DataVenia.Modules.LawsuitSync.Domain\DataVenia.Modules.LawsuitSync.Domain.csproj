﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\DataVenia.Common.Domain\DataVenia.Common.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="FluentResults">
      <HintPath>..\..\..\..\.nuget\packages\fluentresults\3.16.0\lib\netstandard2.1\FluentResults.dll</HintPath>
    </Reference>
    <Reference Include="Refit">
      <HintPath>..\..\..\..\.nuget\packages\refit\8.0.0\lib\net8.0\Refit.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Refit" Version="8.0.0" />
  </ItemGroup>

</Project>
