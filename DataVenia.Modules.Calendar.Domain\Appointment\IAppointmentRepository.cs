﻿using System.Linq.Expressions;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Calendar.Domain.Appointments;
public interface IAppointmentRepository
{
    Task<Appointment?> GetSingleAsync(Expression<Func<Appointment, bool>> filter, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<Appointment>> GetManyAsync(Expression<Func<Appointment, bool>> filter, CancellationToken cancellationToken = default);
    void Delete(Appointment appointment);
    void Insert(Appointment appointment);
    void Update(Appointment appointment);
}
