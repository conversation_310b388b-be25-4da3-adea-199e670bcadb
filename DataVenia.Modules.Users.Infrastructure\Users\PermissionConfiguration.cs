﻿using DataVenia.Modules.Users.Domain.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Users.Infrastructure.Users;

internal sealed class PermissionConfiguration : IEntityTypeConfiguration<Permission>
{
    public void Configure(EntityTypeBuilder<Permission> builder)
    {
        builder.ToTable("permission");

        builder.<PERSON><PERSON>ey(p => p.Code);

        builder.Property(p => p.Code).HasMaxLength(100);

        builder.HasData(
            Permission.GetUser,
            Permission.ModifyUser,
            Permission.CreateUser,

            Permission.GetUsers,

            Permission.GetStatus,

            Permission.CreateClient,
            Permission.DeleteClient,
            Permission.UpdateClient,
            Permission.GetClients,
            
            Permission.GetLawsuits,
            Permission.ModifyLawsuits,
            Permission.CreateLawsuits,
            
            Permission.GetAppointments,
            Permission.CreateAppointments,
            Permission.UpdateAppointments,

            Permission.CreateInvites,
            Permission.ReadInvites,
            Permission.UpdateInvites,

            Permission.GetHarvey,

            Permission.General,

            Permission.SystemAdministrator);

        builder
            .HasMany<Role>()
            .WithMany()
            .UsingEntity(joinBuilder =>
            {
            joinBuilder.ToTable("role_permission");

                joinBuilder.HasData(
                    // Office Member permissions
                    CreateRolePermission(Role.OfficeMember, Permission.CreateLawsuits),
                    CreateRolePermission(Role.OfficeMember, Permission.GetLawsuits),
                    CreateRolePermission(Role.OfficeMember, Permission.ModifyLawsuits),

                    CreateRolePermission(Role.OfficeMember, Permission.GetAppointments),
                    CreateRolePermission(Role.OfficeMember, Permission.CreateAppointments),
                    CreateRolePermission(Role.OfficeMember, Permission.UpdateAppointments),

                    CreateRolePermission(Role.OfficeMember, Permission.CreateClient),
                    CreateRolePermission(Role.OfficeMember, Permission.GetClients),
                    CreateRolePermission(Role.OfficeMember, Permission.UpdateClient),
                    CreateRolePermission(Role.OfficeMember, Permission.DeleteClient),

                    CreateRolePermission(Role.OfficeMember, Permission.GetStatus),

                    CreateRolePermission(Role.OfficeMember, Permission.GetUsers),

                    // Office Admin permissions
                    CreateRolePermission(Role.OfficeAdministrator, Permission.GetLawsuits),
                    CreateRolePermission(Role.OfficeAdministrator, Permission.ModifyLawsuits),
                    CreateRolePermission(Role.OfficeAdministrator, Permission.CreateLawsuits),

                    CreateRolePermission(Role.OfficeAdministrator, Permission.GetAppointments),
                    CreateRolePermission(Role.OfficeAdministrator, Permission.CreateAppointments),
                    CreateRolePermission(Role.OfficeAdministrator, Permission.UpdateAppointments),

                    CreateRolePermission(Role.OfficeAdministrator, Permission.CreateClient),
                    CreateRolePermission(Role.OfficeAdministrator, Permission.GetClients),
                    CreateRolePermission(Role.OfficeAdministrator, Permission.UpdateClient),
                    CreateRolePermission(Role.OfficeAdministrator, Permission.DeleteClient),

                    CreateRolePermission(Role.OfficeAdministrator, Permission.GetUsers),

                    CreateRolePermission(Role.OfficeAdministrator, Permission.GetStatus),

                    CreateRolePermission(Role.OfficeAdministrator, Permission.CreateInvites),

                    CreateRolePermission(Role.OfficeAdministrator, Permission.CreateUser),
                    
                    // System Member permissions
                    CreateRolePermission(Role.SystemMember, Permission.GetUser),
                    CreateRolePermission(Role.SystemMember, Permission.ModifyUser),
                    
                    CreateRolePermission(Role.SystemMember, Permission.ReadInvites),
                    CreateRolePermission(Role.SystemMember, Permission.UpdateInvites),

                    CreateRolePermission(Role.SystemMember, Permission.GetHarvey),
                    
                    CreateRolePermission(Role.SystemMember, Permission.General),

                    // System Admin Permission
                    CreateRolePermission(Role.SystemAdministrator, Permission.SystemAdministrator),
                    CreateRolePermission(Role.SystemAdministrator, Permission.CreateUser)
    ); });
    }

    private static object CreateRolePermission(Role role, Permission permission)
    {
        return new
        {
            RoleName = role.Name,
            PermissionCode = permission.Code
        };
    }
}
