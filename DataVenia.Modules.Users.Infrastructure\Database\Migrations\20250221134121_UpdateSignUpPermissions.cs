﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Users.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSignUpPermissions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "user",
                table: "role_permission",
                keyColumns: new[] { "permission_code", "role_name" },
                keyValues: new object[] { "system:users:write", "SystemMember" });

            migrationBuilder.DeleteData(
                schema: "user",
                table: "permission",
                keyColumn: "code",
                keyValue: "system:users:write");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                schema: "user",
                table: "permission",
                column: "code",
                value: "system:users:write");

            migrationBuilder.InsertData(
                schema: "user",
                table: "role_permission",
                columns: new[] { "permission_code", "role_name" },
                values: new object[] { "system:users:write", "SystemMember" });
        }
    }
}
