﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\DataVenia.Modules.Notification.Domain\DataVenia.Modules.Notification.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="Microsoft.Extensions.Configuration.Abstractions">
        <HintPath>..\..\..\..\..\.nuget\packages\microsoft.extensions.configuration.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
        <HintPath>..\..\..\..\..\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\8.0.2\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
      </Reference>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
      <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    </ItemGroup>

</Project>
