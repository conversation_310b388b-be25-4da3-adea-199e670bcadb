﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AppointmentParticipantDomain = DataVenia.Modules.Calendar.Domain.AppointmentParticipant.AppointmentParticipant;

namespace DataVenia.Modules.Calendar.Infrastructure.AppointmentParticipant;
internal sealed class AppointmentParticipantConfiguration : IEntityTypeConfiguration<AppointmentParticipantDomain>
{
    public void Configure(EntityTypeBuilder<AppointmentParticipantDomain> builder)
    {
        builder.ToTable("appointment_participant");

        builder.HasKey(ap => new { ap.AppointmentId, ap.LawyerId });

        builder.Property(ap => ap.LawyerId)
               .IsRequired();

        builder.HasOne(ap => ap.Appointment)
               .WithMany(a => a.Participants)
               .HasForeignKey(ap => ap.AppointmentId)
               .OnDelete(DeleteBehavior.NoAction); // Cascata opcional

        builder.Ignore(ap => ap.Appointment);
    }
}
