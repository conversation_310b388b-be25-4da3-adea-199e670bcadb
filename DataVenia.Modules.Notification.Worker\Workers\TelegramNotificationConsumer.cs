using DataVenia.Common.Contracts.Events.Notification;
using DataVenia.Modules.Notification.Application.Factories;
using DataVenia.Modules.Notification.Application.Strategy;
using DataVenia.Modules.Notification.Domain.Enums;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Notification.Worker.Workers;

public sealed class TelegramNotificationConsumer(NotificationStrategyFactory strategyFactory, ILogger<TelegramNotificationConsumer> logger) : IConsumer<TelegramNotificationEvent>
{
    public async Task Consume(ConsumeContext<TelegramNotificationEvent> context)
    {
        context = context ?? throw new ArgumentNullException(nameof(context));
        
        TelegramNotificationEvent notificationEvent = context.Message;

        INotificationStrategy strategy = strategyFactory.Create(NotificationEventType.Telegram);
        if(strategy != null)
        {
            await strategy.SendNotificationAsync(notificationEvent.Message, notificationEvent.Recipient).ConfigureAwait(false);
        }

        logger.LogTrace("Processed telegram notification for {Recipient}", notificationEvent.Recipient);
    }
}
