// using DataVenia.Common.Contracts.Events.AssociationHub;
// using MassTransit;
//
// namespace DataVenia.Modules.AssociationHub.Application.Examples;
//
// /// <summary>
// /// Example of how other modules can use the AssociationHub to create and remove multiple associations
// /// </summary>
// public class MultipleAssociationUsageExample
// {
//     private readonly IPublishEndpoint _publishEndpoint;
//
//     public MultipleAssociationUsageExample(IPublishEndpoint publishEndpoint)
//     {
//         _publishEndpoint = publishEndpoint;
//     }
//
//     /// <summary>
//     /// Example: Associate a Client with a Company
//     /// This will create bidirectional associations:
//     /// - Client will have Company ID in its associations.Company array
//     /// - Company will have Client ID in its associations.Client array
//     /// </summary>
//     public async Task AssociateClientWithCompanyAsync(Guid clientId, Guid companyId)
//     {
//         var associations = new[]
//         {
//             new AssociationRequest("Company", [companyId])
//         };
//
//         var createAssociationEvent = new CreateAssociationEvent(
//             EntityType: "Client",
//             EntityId: clientId,
//             Associations: associations);
//
//         await _publishEndpoint.Publish(createAssociationEvent);
//     }
//
//     /// <summary>
//     /// Example: Associate a Client with multiple Companies and Lawyers
//     /// This demonstrates the power of bulk associations in a single event
//     /// </summary>
//     public async Task AssociateClientWithMultipleEntitiesAsync(Guid clientId, Guid[] companyIds, Guid[] lawyerIds)
//     {
//         var associations = new[]
//         {
//             new AssociationRequest("Company", companyIds),
//             new AssociationRequest("Lawyer", lawyerIds)
//         };
//
//         var createAssociationEvent = new CreateAssociationEvent(
//             EntityType: "Client",
//             EntityId: clientId,
//             Associations: associations);
//
//         await _publishEndpoint.Publish(createAssociationEvent);
//     }
//
//     /// <summary>
//     /// Example: Associate a Lawyer with multiple Lawsuits at once
//     /// </summary>
//     public async Task AssociateLawyerWithLawsuitsAsync(Guid lawyerId, Guid[] lawsuitIds)
//     {
//         var associations = new[]
//         {
//             new AssociationRequest("Lawsuit", lawsuitIds)
//         };
//
//         var createAssociationEvent = new CreateAssociationEvent(
//             EntityType: "Lawyer",
//             EntityId: lawyerId,
//             Associations: associations);
//
//         await _publishEndpoint.Publish(createAssociationEvent);
//     }
//
//     /// <summary>
//     /// Example: Remove multiple associations between Client and Companies
//     /// </summary>
//     public async Task RemoveClientCompanyAssociationsAsync(Guid clientId, Guid[] companyIds)
//     {
//         var associations = new[]
//         {
//             new AssociationRequest("Company", companyIds)
//         };
//
//         var removeAssociationEvent = new RemoveAssociationEvent(
//             EntityType: "Client",
//             EntityId: clientId,
//             Associations: associations);
//
//         await _publishEndpoint.Publish(removeAssociationEvent);
//     }
//
//     /// <summary>
//     /// Example: Associate an Appointment with multiple entities in one call
//     /// This is much more efficient than multiple separate events
//     /// </summary>
//     public async Task AssociateAppointmentWithEntitiesAsync(Guid appointmentId, Guid[] lawyerIds, Guid[] clientIds)
//     {
//         var associations = new[]
//         {
//             new AssociationRequest("Lawyer", lawyerIds),
//             new AssociationRequest("Client", clientIds)
//         };
//
//         var createAssociationEvent = new CreateAssociationEvent(
//             EntityType: "Appointment",
//             EntityId: appointmentId,
//             Associations: associations);
//
//         await _publishEndpoint.Publish(createAssociationEvent);
//     }
//
//     /// <summary>
//     /// Example: Remove multiple associations at once
//     /// </summary>
//     public async Task RemoveMultipleAssociationsAsync(Guid clientId, Guid[] companyIds, Guid[] lawyerIds)
//     {
//         var associations = new[]
//         {
//             new AssociationRequest("Company", companyIds),
//             new AssociationRequest("Lawyer", lawyerIds)
//         };
//
//         var removeAssociationEvent = new RemoveAssociationEvent(
//             EntityType: "Client",
//             EntityId: clientId,
//             Associations: associations);
//
//         await _publishEndpoint.Publish(removeAssociationEvent);
//     }
//
//     /// <summary>
//     /// Example: Validate associations before creating them
//     /// This shows how to check if associations are allowed before attempting to create them
//     /// </summary>
//     public async Task ValidateAndCreateAssociationsAsync(Guid clientId, string[] targetEntityTypes)
//     {
//         // First, validate which associations are allowed
//         // This would typically be done via HTTP call to the validation endpoint
//         // For this example, we'll assume the validation passed
//
//         var validTargetTypes = targetEntityTypes; // In reality, filter based on validation result
//
//         var associations = validTargetTypes.Select(entityType =>
//             new AssociationRequest(entityType, [Guid.NewGuid()])).ToArray();
//
//         var createAssociationEvent = new CreateAssociationEvent(
//             EntityType: "Client",
//             EntityId: clientId,
//             Associations: associations);
//
//         await _publishEndpoint.Publish(createAssociationEvent);
//     }
//
//     /// <summary>
//     /// Example: Handle association validation errors gracefully
//     /// </summary>
//     public async Task CreateAssociationsWithValidationAsync(Guid officeId, Guid[] lawyerIds, Guid[] clientIds)
//     {
//         // Offices can only associate with Lawyers (based on configuration)
//         // Attempting to associate with Clients will be rejected
//         var associations = new[]
//         {
//             new AssociationRequest("Lawyer", lawyerIds), // This will succeed
//             new AssociationRequest("Client", clientIds)  // This will be rejected
//         };
//
//         var createAssociationEvent = new CreateAssociationEvent(
//             EntityType: "Office",
//             EntityId: officeId,
//             Associations: associations);
//
//         // The AssociationHub will process only valid associations (Lawyers)
//         // and log warnings about invalid ones (Clients)
//         await _publishEndpoint.Publish(createAssociationEvent);
//     }
// }
