﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyers.GetLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using LawyerDomain = DataVenia.Modules.Users.Domain.Lawyers.Lawyer;

namespace DataVenia.Modules.Users.Presentation.Lawyer;

public sealed class GetLawyerProfile : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("lawyers/profile", async (ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            Result<GetLawyerProfileResponse> result = await sender.Send(new GetLawyerProfileQuery(
                claims.GetUserId()));

            return result.Match(Results.Ok, ApiResults.Problem);
        })
        .RequireAuthorization("system:users:read")
        .WithTags(Tags.Users);
    }
}
