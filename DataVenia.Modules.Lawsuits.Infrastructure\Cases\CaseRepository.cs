﻿using System.Text;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Cases;
using DataVenia.Modules.Lawsuits.Application.Cases.GetCases;
using DataVenia.Modules.Lawsuits.Application.Lawsuits;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using DataVenia.Modules.Lawsuits.Infrastructure.Lawsuits;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Npgsql;

namespace DataVenia.Modules.Lawsuits.Infrastructure.Cases;

public class CaseRepository(LawsuitsDbContext context, ILogger<CaseRepository> logger) : ICaseRepository
{
    public async Task<Result<IReadOnlyCollection<CaseResponse>>> GetCasesAsync(Guid userId, Guid officeId, Guid? caseId = null,
        CancellationToken cancellationToken = default)
    {
        var sqlBuilder = new StringBuilder();
        sqlBuilder.AppendLine(@"
            SELECT 
    c.id AS Id,
    cd.title AS Title,
    cd.folder_id AS Folder_Id,
    f.name AS Folder_Name,
    cd.description,
    cd.observations,
    cd.cause_value,
    cd.conviction_value,
    cd.created_at,
    (
        SELECT ARRAY_AGG(cr.lawyer_id)
        FROM lawsuit.case_responsible cr
        WHERE cr.case_data_id = cd.id
    ) AS Responsible_Ids,
    cd.access
FROM lawsuit.case c
-- Retrieve the latest case_data per case using DISTINCT ON (PostgreSQL syntax)
INNER JOIN (
    SELECT DISTINCT ON (case_id) *
    FROM lawsuit.case_data
    ORDER BY case_id, created_at DESC
) cd ON cd.case_id = c.id
LEFT JOIN lawsuit.folder f ON cd.folder_id = f.id
-- Use a lateral join to aggregate responsibles from any case_data for the case
LEFT JOIN LATERAL (
    SELECT ARRAY_AGG(cr.lawyer_id) AS Responsibles
    FROM lawsuit.case_responsible cr
    INNER JOIN lawsuit.case_data cdd ON cdd.id = cr.case_data_id
    WHERE cdd.case_id = c.id
) responsibles ON TRUE
WHERE c.office_id = @officeId
        ");

        if (caseId.HasValue)
        {
            sqlBuilder.AppendLine("AND c.id = @caseId");
        }

        sqlBuilder.AppendLine(@"
            AND EXISTS (
    SELECT 1
    FROM lawsuit.case_responsible cr2
    INNER JOIN lawsuit.case_data cdd2 ON cdd2.id = cr2.case_data_id
    WHERE cdd2.case_id = c.id
      AND cr2.lawyer_id = @userId
)
        ");

        string sql = sqlBuilder.ToString();

        NpgsqlParameter[] parameters = new[]
        {
            new Npgsql.NpgsqlParameter("@officeId", officeId),
            new Npgsql.NpgsqlParameter("@userId", userId)
        };

        if (caseId.HasValue)
        {
            // If CaseId is provided, add the corresponding parameter
            Array.Resize(ref parameters, parameters.Length + 1);
            parameters[parameters.Length - 1] = new Npgsql.NpgsqlParameter("@caseId", caseId);
        }
        try
        {

            List<CaseResponse> cases = await context.Set<CaseResponse>()
                .FromSqlRaw(sql, parameters)
                .ToListAsync(cancellationToken);
        
            return cases;
        }
        catch (Exception ex)
        {
            string amountOfCases = caseId == null ? "collection" : $"unit: {caseId}";
            logger.LogError(ex, "Error occured while getting case. {AmountOfCases}", amountOfCases);
            return Result.Failure<IReadOnlyCollection<CaseResponse>>(new Error("Internal.Server.Error",
                "Something weird happened.", ErrorType.InternalServerError));
        }
    }

    public async Task<Case?> GetCaseByIdAsync(Guid caseId, CancellationToken cancellationToken = default)
    {
        Case? @case = await context.Cases
            .Include(l => l.CaseDatas
                .OrderByDescending(ld => ld.CreatedAt)
                .Take(1))
            .ThenInclude(ld =>ld.Responsibles)
            .Include(l => l.CaseDatas
                .OrderByDescending(ld => ld.CreatedAt)
                .Take(1))
            .ThenInclude(ld => ld.CaseParties)
            .Where(l => l.Id == caseId)
            .FirstOrDefaultAsync(cancellationToken);  // Fetch the first matching record or return null if none found

        return @case;
    }

    public void Insert(Case @case)
    {
        context.Cases.Add(@case);
    }

    public void Update(Case @case)
    {
        context.Cases.Update(@case);
    }
}
