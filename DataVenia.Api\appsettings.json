{"AllowedHosts": "*", "ConnectionStrings": {"Database": "Host=localhost;Port=5432;Database=datavenia;Username=datavenia;Password=datavenia;Include Error Detail=true", "Cache": "datavenia.redis:6379"}, "Authentication": {"Audience": "", "TokenValidationParameters": {"ValidIssuers": []}, "MetadataAddress": "", "RequireHttpsMetadata": false}, "KeyCloak": {"HealthUrl": ""}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "DataVenia.Api"}}}