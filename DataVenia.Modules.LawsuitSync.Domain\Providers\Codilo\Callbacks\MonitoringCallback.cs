﻿namespace DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Callbacks;

public class MonitoringCallback
{
    public Guid Id { get; set; }
    public required string Cnj { get; set; }
    public string? Tag { get; set; }
    public required bool Ignore { get; set; }
    public required bool Credentials { get; set; }
    public required DateTime CreatedAt { get; set; }
    public required IEnumerable<Info> Info { get; set; }
}

public class Info
{
    public Guid Id { get; set; }
    public required string Source { get; set; }
    public required string Platform { get; set; }
    public required string Search { get; set; }
    public required string Query { get; set; }
    public required string SourceTag { get; set; }
    public required string PlatformTag { get; set; }
    public required string SearchTag { get; set; }
    public required string QueryTag { get; set; }
    public required string LastStatus { get; set; }
    public required DateTime LastResponse { get; set; }
    public required bool Disabled { get; set; }
    public string? Message { get; set; }
    public required IEnumerable<Data> Data { get; set; }
}

public class Data
{
    public required IEnumerable<Cover> Cover { get; set; }
    public required Properties Properties { get; set; }
    public required IEnumerable<PropertiesArray> PropertiesArray { get; set; }
    public required IEnumerable<Person> People { get; set; }
    public required IEnumerable<LawsuitEvent> Steps { get; set; }
}

public class Cover
{
    public required string Id { get; set; }
    public required string Description { get; set; }
    public required string Value { get; set; }
    public required bool Secret { get; set; }
    public required string Index { get; set; }
    public required bool Confirmed { get; set; }
}

public class Properties
{
    public string? Number { get; set; }
    public string? Class { get; set; }
    public string? MainSubject { get; set; }
    public string? Degree { get; set; }
    public string? MainNumber { get; set; }
    public string? FreeLawsuit { get; set; }
    public string? Digital { get; set; }
    public DateTime? StartAt { get; set; }
    public string? Cnj { get; set; }
    public string? Origin { get; set; }
    public string? Value { get; set; }
}

public class PropertiesArray
{
    public required string Id { get; set; }
    public required string Key { get; set; }
    public required string Value { get; set; }
    public required bool Secret { get; set; }
    public required string Index { get; set; }
    public required DateTime CreatedAt { get; set; }
    public required bool Confirmed { get; set; }
}

public class Person
{
    public required string Id { get; set; }
    public required string Pole { get; set; }
    public string? Description { get; set; }
    public required string Name { get; set; }
    public string? Doc { get; set; }
    public required string Index { get; set; }
    public required bool Confirmed { get; set; }
    public required IEnumerable<Lawyer> Lawyers { get; set; }
}

public class Lawyer
{
    public required string Id { get; set; }
    public string? Description { get; set; }
    public required string Name { get; set; }
    public required string Uf { get; set; }
    public required string Oab { get; set; }
    public required string Index { get; set; }
    public required bool Confirmed { get; set; }
}

public class LawsuitEvent
{
    public required string Id { get; set; }
    public required DateTime Timestamp { get; set; }
    public string? Title { get; set; }
    public string? Description { get; set; }
    public string? ActionBy { get; set; }
    public required string Index { get; set; }
    public required bool Confirmed { get; set; }
    public required bool Secret { get; set; }
}
