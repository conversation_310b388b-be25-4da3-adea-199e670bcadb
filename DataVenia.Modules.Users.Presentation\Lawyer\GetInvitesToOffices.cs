﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyers.GetInvitesToOffices;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
namespace DataVenia.Modules.Users.Presentation.Lawyer;
internal sealed class GetInvitesToOffices : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("lawyers/invites", async (ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            Result<IReadOnlyCollection<OfficeUser?>> result = await sender.Send(new GetInvitesToOfficesQuery(
                claims.GetUserId()));

            return result.Match(
                success => {
                    var responseObject = new
                    {
                        items = success
                    };

                    return Results.Ok(responseObject);
                    },
                ApiResults.Problem);
        })
        .RequireAuthorization("system:invites:read")
        .WithTags(Tags.OfficeInvite);
    }
}
