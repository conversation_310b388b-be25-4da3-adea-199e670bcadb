﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Users.Domain.SharedModels;

namespace DataVenia.Modules.Users.Application.Lawyers.RegisterLawyer;
public sealed record RegisterLawyerCommand(List<LawyerRequest> Lawyers, OfficeRequest? Office, Guid AdminId)
    : ICommand<RegisterLawyersResponse>;

public sealed record LawyerRequest
{
    public string Email { get; init; }

    public string Password { get; init; }

    public string FirstName { get; init; }

    public string LastName { get; init; }
    public List<Contact> Contacts { get; init; }
    public List<string> Oabs { get; init; }
    public string? Cpf { get; init; }
    public string? Rg { get; init; }
    public string? Cnh { get; init; }
    public string? Passport { get; init; }
    public string? Ctps { get; init; }
    public string? Pis { get; init; }
    public string? VoterId { get; init; }
    public bool IsOfficeAdmin { get; init; }
}

public sealed record OfficeRequest
{
    public string Name { get; init; }
    public string Website { get; init; }
    public string Cnpj { get; init; }
    public IReadOnlyCollection<Contact> Contacts { get; init; }
    public IReadOnlyCollection<Address> Addresses { get; init; }
}

public sealed record RegisterLawyersResponse
{
    // Confere se o office já existia.
    public bool? OfficeAlreadyExists { get; init; }

    // Lista de advogados que já existiam.
    public List<string> ExistingLawyers { get; init; } = new();

    // Lista de advogados que já estavam vinculados ao office.
    public List<string> AlreadyBoundLawyers { get; init; } = new();
    public List<string> NewlyCreatedLawyers { get; init; } = new();
    public List<string> NewlyBoundLawyers { get; init; } = new();
}
