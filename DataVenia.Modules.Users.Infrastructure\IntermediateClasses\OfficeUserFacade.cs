﻿using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;

namespace DataVenia.Modules.Users.Infrastructure.IntermediateClasses;
public sealed class OfficeUserFacade(IOfficeUserRepository officeLawyerRepository) : IOfficeLawyerFacade
{   
    public async Task<IReadOnlyCollection<OfficeLawyerDto>> GetActiveByOfficeAndLawyers(Guid office, List<Guid> lawyersIds)
    {
        IReadOnlyCollection<OfficeUser?> officeLawyers = await officeLawyerRepository.GetManyByFilterAsync(
                x => x.OfficeId == office && lawyersIds.Contains(x.UserId) && x.InvitationStatus == InvitationStatus.ACTIVE);

        return officeLawyers
            .Select(lawyer => new OfficeLawyerDto(lawyer!.UserId, lawyer.OfficeId, lawyer.InvitationStatus))
            .ToList().AsReadOnly();
    }
}
