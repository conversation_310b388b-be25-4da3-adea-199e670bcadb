﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.SharedModels;
using DataVenia.Modules.Users.Domain.User;

namespace DataVenia.Modules.Users.Domain.Users;

public abstract class User : Entity
{
    protected readonly List<Contact> _contacts = [];
    private readonly List<OfficeUser> _officeUsers = [];
    protected User()
    {
    }

    public Guid Id { get; protected set; }
    public string Email { get; protected set; }
    public string FirstName { get; protected set; }
    public string LastName { get; protected set; }
    public Guid? IdentityId { get; protected set; }
    public string? Cpf { get; protected set; }
    public string? Rg { get; protected set; }
    public string? Cnh { get; protected set; }
    public string? Passport { get; protected set; }
    public string? Ctps { get; protected set; }
    public string? Pis { get; protected set; }
    public string? VoterId { get; protected set; }
    public TermsAndConditions TermsAndConditions { get; protected set; }
    public Preferences Preferences { get; protected set; }
    public IReadOnlyCollection<Contact> Contacts => _contacts.ToList();
    public IReadOnlyCollection<OfficeUser> OfficeUsers => _officeUsers.AsReadOnly();
    public DateTime CreatedAt { get; protected set; }
    public DateTime? UpdatedAt { get; protected set; }

    public void UpdatePartial(DateTime? acceptedAt = null, Guid? identityId = null)
    {
        if (identityId is not null)
            IdentityId = identityId;
        
        if (acceptedAt is null)
            return;

        TermsAndConditions.AcceptedAt = acceptedAt.Value;
        TermsAndConditions.Url = "urlTerm.ToBeSet";
        TermsAndConditions.Version = "1.0.0";
        
        UpdatedAt = DateTime.UtcNow;
    }
    
}
