﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DataVenia.Modules.Users.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class CreateDatabase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "user");

            migrationBuilder.CreateTable(
                name: "office",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    website = table.Column<string>(type: "text", nullable: false),
                    cnpj = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_office", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "permission",
                schema: "user",
                columns: table => new
                {
                    code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_permission", x => x.code);
                });

            migrationBuilder.CreateTable(
                name: "role",
                schema: "user",
                columns: table => new
                {
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    is_global = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_role", x => x.name);
                });

            migrationBuilder.CreateTable(
                name: "user",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    email = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: false),
                    first_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    last_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    identity_id = table.Column<Guid>(type: "uuid", nullable: true),
                    cpf = table.Column<string>(type: "character varying(11)", maxLength: 11, nullable: true),
                    rg = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: true),
                    cnh = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: true),
                    passport = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    ctps = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    pis = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    voter_id = table.Column<string>(type: "character varying(12)", maxLength: 12, nullable: true),
                    preferences = table.Column<string>(type: "jsonb", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    user_type = table.Column<string>(type: "character varying(8)", maxLength: 8, nullable: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    terms_and_conditions = table.Column<string>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "client",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    cpf = table.Column<string>(type: "character varying(11)", maxLength: 11, nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    email = table.Column<string>(type: "text", nullable: true),
                    office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_by = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_client", x => x.id);
                    table.ForeignKey(
                        name: "fk_client_office_office_id",
                        column: x => x.office_id,
                        principalSchema: "user",
                        principalTable: "office",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "company",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    cnpj = table.Column<string>(type: "character varying(18)", maxLength: 18, nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_by = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_company", x => x.id);
                    table.ForeignKey(
                        name: "fk_company_office_office_id",
                        column: x => x.office_id,
                        principalSchema: "user",
                        principalTable: "office",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "role_permission",
                schema: "user",
                columns: table => new
                {
                    permission_code = table.Column<string>(type: "character varying(100)", nullable: false),
                    role_name = table.Column<string>(type: "character varying(50)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_role_permission", x => new { x.permission_code, x.role_name });
                    table.ForeignKey(
                        name: "fk_role_permission_permission_permission_code",
                        column: x => x.permission_code,
                        principalSchema: "user",
                        principalTable: "permission",
                        principalColumn: "code",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_role_permission_role_role_name",
                        column: x => x.role_name,
                        principalSchema: "user",
                        principalTable: "role",
                        principalColumn: "name",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "oab",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    lawyer_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_oab", x => x.id);
                    table.ForeignKey(
                        name: "fk_oab_lawyers_lawyer_id",
                        column: x => x.lawyer_id,
                        principalSchema: "user",
                        principalTable: "user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "office_user",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    user_id = table.Column<Guid>(type: "uuid", nullable: false),
                    owner_id = table.Column<Guid>(type: "uuid", nullable: false),
                    role_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    invitation_status = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_office_user", x => x.id);
                    table.ForeignKey(
                        name: "fk_office_user_lawyers_owner_id",
                        column: x => x.owner_id,
                        principalSchema: "user",
                        principalTable: "user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_office_user_office_office_id",
                        column: x => x.office_id,
                        principalSchema: "user",
                        principalTable: "office",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_office_user_role_role_name",
                        column: x => x.role_name,
                        principalSchema: "user",
                        principalTable: "role",
                        principalColumn: "name",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_office_user_user_user_id",
                        column: x => x.user_id,
                        principalSchema: "user",
                        principalTable: "user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sign_up_token",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    lawyer_id = table.Column<Guid>(type: "uuid", nullable: false),
                    token = table.Column<Guid>(type: "uuid", nullable: false),
                    office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_by = table.Column<Guid>(type: "uuid", nullable: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sign_up_token", x => x.id);
                    table.ForeignKey(
                        name: "fk_sign_up_token_lawyers_lawyer_id",
                        column: x => x.lawyer_id,
                        principalSchema: "user",
                        principalTable: "user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "user_global_role",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    user_id = table.Column<Guid>(type: "uuid", nullable: false),
                    role_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user_global_role", x => x.id);
                    table.ForeignKey(
                        name: "fk_user_global_role_role_role_name",
                        column: x => x.role_name,
                        principalSchema: "user",
                        principalTable: "role",
                        principalColumn: "name",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_user_global_role_user_user_id",
                        column: x => x.user_id,
                        principalSchema: "user",
                        principalTable: "user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "address",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    neighborhood = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    state = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    complement = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    client_id = table.Column<Guid>(type: "uuid", nullable: true),
                    office_id = table.Column<Guid>(type: "uuid", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_address", x => x.id);
                    table.CheckConstraint("CK_Address_OneOwner", "(\"office_id\" IS NOT NULL)");
                    table.ForeignKey(
                        name: "fk_address_clients_client_id",
                        column: x => x.client_id,
                        principalSchema: "user",
                        principalTable: "client",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "fk_address_offices_office_id",
                        column: x => x.office_id,
                        principalSchema: "user",
                        principalTable: "office",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "contact",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    value = table.Column<string>(type: "text", nullable: false),
                    lawyer_id = table.Column<Guid>(type: "uuid", nullable: true),
                    office_id = table.Column<Guid>(type: "uuid", nullable: true),
                    client_id = table.Column<Guid>(type: "uuid", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_contact", x => x.id);
                    table.CheckConstraint("CK_Contact_OneOwner", "(\"lawyer_id\" IS NOT NULL AND \"office_id\" IS NULL) OR (\"lawyer_id\" IS NULL AND \"office_id\" IS NOT NULL)");
                    table.ForeignKey(
                        name: "fk_contact_clients_client_id",
                        column: x => x.client_id,
                        principalSchema: "user",
                        principalTable: "client",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "fk_contact_lawyers_lawyer_id",
                        column: x => x.lawyer_id,
                        principalSchema: "user",
                        principalTable: "user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_contact_offices_office_id",
                        column: x => x.office_id,
                        principalSchema: "user",
                        principalTable: "office",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "client_company",
                schema: "user",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    client_id = table.Column<Guid>(type: "uuid", nullable: false),
                    company_id = table.Column<Guid>(type: "uuid", nullable: false),
                    role = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_client_company", x => x.id);
                    table.ForeignKey(
                        name: "fk_client_company_client_client_id",
                        column: x => x.client_id,
                        principalSchema: "user",
                        principalTable: "client",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_client_company_company_company_id",
                        column: x => x.company_id,
                        principalSchema: "user",
                        principalTable: "company",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                schema: "user",
                table: "permission",
                column: "code",
                values: new object[]
                {
                    "office:appointments:read",
                    "office:appointments:update",
                    "office:appointments:write",
                    "office:clients:create",
                    "office:clients:delete",
                    "office:clients:read",
                    "office:clients:update",
                    "office:invites:create",
                    "office:lawsuits:create",
                    "office:lawsuits:read",
                    "office:lawsuits:update",
                    "office:status:read",
                    "office:users:read",
                    "office:users:write",
                    "system:administrator:all",
                    "system:general:all",
                    "system:harvey:read",
                    "system:invites:read",
                    "system:invites:update",
                    "system:users:read",
                    "system:users:update",
                    "system:users:write"
                });

            migrationBuilder.InsertData(
                schema: "user",
                table: "role",
                columns: new[] { "name", "is_global" },
                values: new object[,]
                {
                    { "OfficeAdministrator", false },
                    { "OfficeClient", false },
                    { "OfficeMember", false },
                    { "SystemAdministrator", true },
                    { "SystemMember", true }
                });

            migrationBuilder.InsertData(
                schema: "user",
                table: "role_permission",
                columns: new[] { "permission_code", "role_name" },
                values: new object[,]
                {
                    { "office:appointments:read", "OfficeAdministrator" },
                    { "office:appointments:read", "OfficeMember" },
                    { "office:appointments:update", "OfficeAdministrator" },
                    { "office:appointments:update", "OfficeMember" },
                    { "office:appointments:write", "OfficeAdministrator" },
                    { "office:appointments:write", "OfficeMember" },
                    { "office:clients:create", "OfficeAdministrator" },
                    { "office:clients:create", "OfficeMember" },
                    { "office:clients:delete", "OfficeAdministrator" },
                    { "office:clients:delete", "OfficeMember" },
                    { "office:clients:read", "OfficeAdministrator" },
                    { "office:clients:read", "OfficeMember" },
                    { "office:clients:update", "OfficeAdministrator" },
                    { "office:clients:update", "OfficeMember" },
                    { "office:invites:create", "OfficeAdministrator" },
                    { "office:lawsuits:create", "OfficeAdministrator" },
                    { "office:lawsuits:create", "OfficeMember" },
                    { "office:lawsuits:read", "OfficeAdministrator" },
                    { "office:lawsuits:read", "OfficeMember" },
                    { "office:lawsuits:update", "OfficeAdministrator" },
                    { "office:lawsuits:update", "OfficeMember" },
                    { "office:status:read", "OfficeAdministrator" },
                    { "office:status:read", "OfficeMember" },
                    { "office:users:read", "OfficeAdministrator" },
                    { "office:users:read", "OfficeMember" },
                    { "office:users:write", "OfficeAdministrator" },
                    { "office:users:write", "SystemAdministrator" },
                    { "system:administrator:all", "SystemAdministrator" },
                    { "system:general:all", "SystemMember" },
                    { "system:harvey:read", "SystemMember" },
                    { "system:invites:read", "SystemMember" },
                    { "system:invites:update", "SystemMember" },
                    { "system:users:read", "SystemMember" },
                    { "system:users:update", "SystemMember" },
                    { "system:users:write", "SystemMember" }
                });

            migrationBuilder.CreateIndex(
                name: "ix_address_client_id",
                schema: "user",
                table: "address",
                column: "client_id");

            migrationBuilder.CreateIndex(
                name: "ix_address_office_id",
                schema: "user",
                table: "address",
                column: "office_id");

            migrationBuilder.CreateIndex(
                name: "ix_client_office_id",
                schema: "user",
                table: "client",
                column: "office_id");

            migrationBuilder.CreateIndex(
                name: "ix_client_company_client_id",
                schema: "user",
                table: "client_company",
                column: "client_id");

            migrationBuilder.CreateIndex(
                name: "ix_client_company_company_id",
                schema: "user",
                table: "client_company",
                column: "company_id");

            migrationBuilder.CreateIndex(
                name: "ix_company_office_id",
                schema: "user",
                table: "company",
                column: "office_id");

            migrationBuilder.CreateIndex(
                name: "ix_contact_client_id",
                schema: "user",
                table: "contact",
                column: "client_id");

            migrationBuilder.CreateIndex(
                name: "ix_contact_lawyer_id",
                schema: "user",
                table: "contact",
                column: "lawyer_id");

            migrationBuilder.CreateIndex(
                name: "ix_contact_office_id",
                schema: "user",
                table: "contact",
                column: "office_id");

            migrationBuilder.CreateIndex(
                name: "ix_oab_lawyer_id",
                schema: "user",
                table: "oab",
                column: "lawyer_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_oab_value",
                schema: "user",
                table: "oab",
                column: "value",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_office_user_office_id_user_id",
                schema: "user",
                table: "office_user",
                columns: new[] { "office_id", "user_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_office_user_owner_id",
                schema: "user",
                table: "office_user",
                column: "owner_id");

            migrationBuilder.CreateIndex(
                name: "ix_office_user_role_name",
                schema: "user",
                table: "office_user",
                column: "role_name");

            migrationBuilder.CreateIndex(
                name: "ix_office_user_user_id",
                schema: "user",
                table: "office_user",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_permission_role_name",
                schema: "user",
                table: "role_permission",
                column: "role_name");

            migrationBuilder.CreateIndex(
                name: "ix_sign_up_token_lawyer_id",
                schema: "user",
                table: "sign_up_token",
                column: "lawyer_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_cpf",
                schema: "user",
                table: "user",
                column: "cpf");

            migrationBuilder.CreateIndex(
                name: "ix_user_email",
                schema: "user",
                table: "user",
                column: "email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_user_identity_id",
                schema: "user",
                table: "user",
                column: "identity_id",
                unique: true,
                filter: "identity_id IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "ix_user_global_role_role_name",
                schema: "user",
                table: "user_global_role",
                column: "role_name");

            migrationBuilder.CreateIndex(
                name: "IX_UserGlobalRole_UserId_RoleName",
                schema: "user",
                table: "user_global_role",
                columns: new[] { "user_id", "role_name" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "address",
                schema: "user");

            migrationBuilder.DropTable(
                name: "client_company",
                schema: "user");

            migrationBuilder.DropTable(
                name: "contact",
                schema: "user");

            migrationBuilder.DropTable(
                name: "oab",
                schema: "user");

            migrationBuilder.DropTable(
                name: "office_user",
                schema: "user");

            migrationBuilder.DropTable(
                name: "role_permission",
                schema: "user");

            migrationBuilder.DropTable(
                name: "sign_up_token",
                schema: "user");

            migrationBuilder.DropTable(
                name: "user_global_role",
                schema: "user");

            migrationBuilder.DropTable(
                name: "company",
                schema: "user");

            migrationBuilder.DropTable(
                name: "client",
                schema: "user");

            migrationBuilder.DropTable(
                name: "permission",
                schema: "user");

            migrationBuilder.DropTable(
                name: "role",
                schema: "user");

            migrationBuilder.DropTable(
                name: "user",
                schema: "user");

            migrationBuilder.DropTable(
                name: "office",
                schema: "user");
        }
    }
}
