using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Requests;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;
using FluentResults;

namespace DataVenia.Modules.LawsuitSync.Domain.Interfaces;

public interface ICodiloRepository
{
    Task<Result<StartMonitoringResponse>> StartLawsuitMonitoring(StartMonitoringRequest request);
    Task<Result> CancelLawsuitMonitoring(string lawsuitId);
    Task<Result<AutomaticSearchResponse>> StartLawsuitSearch(AutomaticSearchRequest request);
}
