﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Harvey.Domain.Action;
using LawsuitTypeDomain = DataVenia.Modules.Harvey.Domain.Action.LawsuitType;
namespace DataVenia.Modules.Harvey.Application.Action;
internal sealed class GetLawsuitTypesQueryHandler(
    ILawsuitTypeRepository lawsuitTypeRepository) : IQueryHandler<GetLawsuitTypesQuery, IReadOnlyCollection<GetLawsuitTypesResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetLawsuitTypesResponse>>> Handle(GetLawsuitTypesQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<LawsuitTypeDomain> lawsuitTypes = await lawsuitTypeRepository.GetAllAsync(request.displayName, cancellationToken);

        var lawsuitTypesResponse = lawsuitTypes.Select(lawsuitType => new GetLawsuitTypesResponse(
            lawsuitType.Id,
            lawsuitType.DisplayName)).ToList();

        return lawsuitTypesResponse;
    }
}
