﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Users.Infrastructure.ClientCompany;

internal sealed class ClientCompanyConfiguration : IEntityTypeConfiguration<Domain.ClientCompany.ClientCompany>
{
    public void Configure(EntityTypeBuilder<Domain.ClientCompany.ClientCompany> builder)
    {
        builder.ToTable("client_company");

        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.Has<PERSON>ey(ou => ou.Id);

        builder.Property(cc => cc.Role)
                .HasConversion<string>();
        
        // Configure the relationship between ClientCompany and Client
        builder.HasOne(ou => ou.Client)
            .WithMany(o => o.ClientCompanies)
            .HasForeignKey(ou => ou.ClientId);

        // Configure the relationship between ClientCompany and Company
        builder.HasOne(ou => ou.Company)
            .WithMany(l => l.ClientCompanies)
            .HasForeignKey(ou => ou.CompanyId);
        
        
        // // Relacionamento com Contacts
        // builder.HasMany(c => c.Contacts)
        //     .WithOne()
        //     .HasForeignKey("ClientId") // FK na tabela Contacts
        //     .OnDelete(DeleteBehavior.NoAction);
        //
        // // Relacionamento com Addresses
        // builder.HasMany(c => c.Addresses)
        //     .WithOne()
        //     .HasForeignKey("ClientId") // FK na tabela Addresses
        //     .OnDelete(DeleteBehavior.NoAction);
    }
}
