﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ForumDomain = DataVenia.Modules.Harvey.Domain.Forum.Forum;
namespace DataVenia.Modules.Harvey.Infrastructure.Forum;

public sealed class ForumConfiguration : IEntityTypeConfiguration<ForumDomain>
{
    public void Configure(EntityTypeBuilder<ForumDomain> builder)
    {
        builder.ToTable("forum");

        builder.Property(f => f.Id);
        builder.HasIndex(x => x.Id).IsUnique();
        builder.HasKey(f => f.Id);

        builder.Property(f => f.DisplayName)
            .IsRequired()
            .HasMaxLength(512);
        builder.HasIndex(x => x.DisplayName).IsUnique();
    }
}
