﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using CourtDivisionDomain = DataVenia.Modules.Harvey.Domain.CourtDivision.CourtDivision;
namespace DataVenia.Modules.Harvey.Infrastructure.Forum;
public sealed class CourtDivisionConfiguration : IEntityTypeConfiguration<CourtDivisionDomain>
{
    public void Configure(EntityTypeBuilder<CourtDivisionDomain> builder)
    {
        builder.ToTable("court_division");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.DisplayName)
               .IsRequired()
               .HasMaxLength(450);
    }
}
