﻿using System.Linq.Expressions;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Oab;
using DataVenia.Modules.Users.Infrastructure.Database;
using OabDomain = DataVenia.Modules.Users.Domain.Oab.Oab;

namespace DataVenia.Modules.Users.Infrastructure.Oab;
public sealed class OabRepository(UsersDbContext context) : IOabRepository
{
    public async Task<Result<OabDomain>> GetSingleByFilterAsync(Expression<Func<OabDomain, bool>> filter, CancellationToken cancellationToken = default)
    {
        OabDomain? oab = await context.GetSingleByFilterAsync(filter, cancellationToken);

        return oab is not null ? Result.Success(oab) : Result.None<OabDomain>();
    }
    
    public void Insert(OabDomain oab)
    {
        context.Oabs.Add(oab);
    }
    
    public void InsertRange(IEnumerable<OabDomain?> oabs)
    {
        var nonNullOabs = oabs.Where(o => o != null).Cast<OabDomain>().ToList();
        if (nonNullOabs.Any())
        {
            context.Oabs.AddRange(nonNullOabs);
        }
    }
}
