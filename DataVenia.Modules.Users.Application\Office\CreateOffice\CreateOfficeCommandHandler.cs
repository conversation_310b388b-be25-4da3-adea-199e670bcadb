﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Office;
using Microsoft.Extensions.Logging;
using OfficeDomain = DataVenia.Modules.Users.Domain.Office.Office;

namespace DataVenia.Modules.Users.Application.Office.CreateOffice;
public sealed class CreateOfficeCommandHandler(
    IOfficeRepository officeRepository,
    IUnitOfWork unitOfWork,
    ILogger<CreateOfficeCommandHandler> logger) : ICommandHandler<CreateOfficeCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateOfficeCommand request, CancellationToken cancellationToken)
    {

        var office = OfficeDomain.Create(request.Name, request.Website, request.Cnpj, request.Contacts, request.Addresses);

            officeRepository.Insert(office);
        
        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving office to database. {@Request}", request);

            return Result.Failure<Guid>(new Error("Office.Create", "There was an error while creating the office", ErrorType.InternalServerError));
        }

        return office.Id;
    }
}
