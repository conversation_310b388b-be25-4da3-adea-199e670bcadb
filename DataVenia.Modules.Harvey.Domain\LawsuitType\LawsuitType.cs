﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.Action;
public sealed class LawsuitType : Entity
{
    public string Id { get; private set; }
    public string DisplayName { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private LawsuitType() { }

    public static Result<LawsuitType> Create(string name, string displayName)
    {
        return new LawsuitType() { DisplayName = displayName, Id = name };
    }
}
