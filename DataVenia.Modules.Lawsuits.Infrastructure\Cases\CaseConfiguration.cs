﻿using DataVenia.Modules.Lawsuits.Domain.Cases;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.Cases;

internal sealed class CaseConfiguration : IEntityTypeConfiguration<Case>
{
    public void Configure(EntityTypeBuilder<Case> builder)
    {
        builder.ToTable("case");

        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(l => l.Id);

        builder.Property(l => l.CreatedAt)
            .HasDefaultValueSql("NOW()");

        // Relacionamento com LawsuitData (One-to-Many)
        builder.HasMany(l => l.CaseDatas)
            .WithOne(ld => ld.Case)
            .HasForeignKey(ld => ld.CaseId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
