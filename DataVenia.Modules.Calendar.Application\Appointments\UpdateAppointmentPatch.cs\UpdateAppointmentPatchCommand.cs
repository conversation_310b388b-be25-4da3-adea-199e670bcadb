﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Application.Appointment.CreateAppointment;

namespace DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointmentPartial.cs;

public sealed record UpdateAppointmentPatchCommand(
    Guid UserId,
    Guid OfficeId,
    Guid AppointmentId,
    string? Type,
    string? Name,
    string? Description,
    Guid? ResponsibleLawyerId,
    RecurrenceCommand? Recurrence,
    List<Guid>? ParticipantLawyersIds,
    List<TimeSpan>? Alerts,
    Guid? StatusId
) : ICommand;