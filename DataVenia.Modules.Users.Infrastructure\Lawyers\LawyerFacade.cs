﻿using DataVenia.Common.Contracts.Lawyer;
using DataVenia.Modules.Users.Domain.Lawyers;

namespace DataVenia.Modules.Users.Infrastructure.Lawyers;

public sealed class LawyerFacade(ILawyerRepository lawyerRepository) : ILawyerFacade
{   
    public async Task<IReadOnlyCollection<LawyerDto>> GetByIdsAsync(List<Guid> lawyersIds, CancellationToken cancellationToken = default)
    {
        IReadOnlyCollection<Lawyer> lawyers = await lawyerRepository.GetByIdsAsync(lawyersIds, cancellationToken);

        return lawyers
            .Select(lawyer => new LawyerDto(lawyer.Id, lawyer.Email, $"{lawyer.FirstName} {lawyer.LastName}"))
            .ToList().AsReadOnly();
    }
}
