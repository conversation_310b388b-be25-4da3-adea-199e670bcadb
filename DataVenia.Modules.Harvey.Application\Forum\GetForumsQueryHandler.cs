﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Harvey.Domain.Action;
using DataVenia.Modules.Harvey.Domain.Forum;
using ForumDomain = DataVenia.Modules.Harvey.Domain.Forum.Forum;
namespace DataVenia.Modules.Harvey.Application.Forum;
internal sealed class GetForumsQueryHandler(
    IForumRepository forumRepository) : IQueryHandler<GetForumsQuery, IReadOnlyCollection<GetForumsResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetForumsResponse>>> Handle(GetForumsQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<ForumDomain> forums = await forumRepository.GetAllAsync(request.displayName, cancellationToken);

        var forumsResponse = forums.Select(forum => new GetForumsResponse(
            forum.Id,
            forum.DisplayName)).ToList();

        return forumsResponse;
    }
}
