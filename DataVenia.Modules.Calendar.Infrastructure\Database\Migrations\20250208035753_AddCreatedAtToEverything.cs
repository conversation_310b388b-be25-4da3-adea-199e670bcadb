﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Calendar.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddCreatedAtToEverything : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "calendar",
                table: "recurrence",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "calendar",
                table: "recurrence",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "calendar",
                table: "appointment",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "calendar",
                table: "appointment",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "calendar",
                table: "recurrence");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "calendar",
                table: "recurrence");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "calendar",
                table: "appointment");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "calendar",
                table: "appointment");
        }
    }
}
