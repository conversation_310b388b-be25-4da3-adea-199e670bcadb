using DataVenia.Common.Contracts.Events.LawsuitSync;
using DataVenia.Common.Domain.Lawsuit;
using DataVenia.Modules.LawsuitSync.Application.Helpers;
using DataVenia.Modules.LawsuitSync.Domain.Entities;
using DataVenia.Modules.LawsuitSync.Domain.Enums;
using DataVenia.Modules.LawsuitSync.Domain.Helpers;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Callbacks;
using FluentResults;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Cover = DataVenia.Common.Contracts.Events.LawsuitSync.Cover;

namespace DataVenia.Modules.LawsuitSync.Application.AppServices;

public sealed class ReceiveLawsuitCallbackAppService(
    ILawsuitMonitoringRepository lawsuitMonitoringRepository,
    IPublishEndpoint massTransitPublisher,
    ILogger<ReceiveLawsuitCallbackAppService> logger)
    : IReceiveLawsuitCallbackAppService
{
    public async Task<Result> ExecuteAsync(MonitoringCallback callback)
    {
        using var _ = logger.BeginScope("ReceiveLawsuitCallbackAppService.ExecuteAsync");

        var validCallbacks = callback?.Info.Where(x => x.Data.Any()) ?? [];

        foreach (var validCallback in validCallbacks!)
        {
            if (!validCallback.Data.Any())
                continue;

            foreach (var callbackInstance in validCallback.Data)
            {
                var status = StatusMapperHelper
                    .MapMatterMovementsStatus(callbackInstance.Steps.OrderBy(x => x.Timestamp).LastOrDefault()?.Description);

                var lawsuitEvent = CreateLawsuitEventsHistory(callback, validCallback, status);

                var addLawsuitEventHistoryResult = await lawsuitMonitoringRepository.AddLawsuitEventsHistory(lawsuitEvent)
                    .ConfigureAwait(false);

                if (addLawsuitEventHistoryResult.IsFailed)
                {
                    logger.LogError("Failed to add Lawsuit Event history to LawsuitEventsHistory");
                    return addLawsuitEventHistoryResult;
                }

                var updateLawsuitMonitoring =
                    await UpdateLawsuitMonitoring(callback, validCallback, status).ConfigureAwait(false);

                if (updateLawsuitMonitoring.IsFailed)
                {
                    logger.LogError("Failed to update Lawsuit Monitoring configuration");
                    return updateLawsuitMonitoring.ToResult();
                }

                var instance = validCallback.PlatformTag.Equals("superiores", StringComparison.OrdinalIgnoreCase)
                                ? InstanceMapperHelper.MapInstanceFromCourtName(validCallback.SearchTag)
                                : InstanceMapperHelper.MapInstanceFromInstanceNumber(callbackInstance.Properties.Degree ?? string.Empty);

                if (instance is null)
                {
                    logger.LogCritical("Invalid instance {Instance} for cnj {Cnj} - degree {Degree}", validCallback.SearchTag, callback?.Cnj, callbackInstance.Properties.Degree);
                    continue;
                }

                var syncEvent = new LawsuitUpdateEvent(
                    callback?.Cnj?.Replace(".", "")?.Replace("-", "") ?? string.Empty,
                    updateLawsuitMonitoring.Value,
                    Instance: instance.ToString() ?? string.Empty,
                    Status: status.ToString(),
                    Cover: CreateCoverFromCodiloResponse(callbackInstance.Cover, validCallback.Search),
                    Parties: callbackInstance.People.Select(x =>
                    {
                        var pole = x.Pole.Equals("passive") ? PartyPole.PassivePole : PartyPole.ActivePole;
                        return new Party(x.Name, pole, x.Description ?? string.Empty, x.Doc ?? string.Empty);
                    }).ToList(),
                    Steps: callbackInstance.Steps.Select(x =>
                        new LawsuitStep(x.Id, x.Timestamp, x.Title, x.Description, x.ActionBy, x.Secret)).ToList()
                );

                await massTransitPublisher.Publish(syncEvent).ConfigureAwait(false);
            }
        }

        return Result.Ok();
    }

    private const string Class = "Classe";
    private const string Topic = "Assunto";
    private const string CauseValue = "Valor da Causa";
    private const string DistributedAt = "Data Distribuição";
    private static Cover CreateCoverFromCodiloResponse(IEnumerable<DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Callbacks.Cover> cover, string judgingOrgan)
    {
        if(cover?.Any() != true) return null;
        
        var coverClass = cover?.FirstOrDefault(x => x.Description == Class)?.Value ?? string.Empty;
        var causeValue = cover?.FirstOrDefault(x => x.Description == CauseValue)?.Value;
        if (!int.TryParse(causeValue, out var causeValueConverted))
            causeValueConverted = 0;
        
        var distributedAt = cover?.FirstOrDefault(x => x.Description == DistributedAt)?.Value;
        if (!DateTime.TryParse(distributedAt, out var distributedAtConverted))
            distributedAtConverted = default;

        var topics = cover.Where(x => x.Description == Topic).Select(x => x.Value).ToList();

        var lawsuitType = LawsuitTypeClassifier.ClassifyLawsuitType(coverClass, topics);
        
        return new Cover(
            Class: coverClass,
            Topics: topics,
            LawsuitType: lawsuitType,
            JudgingOrgan: judgingOrgan,
            CauseValue: causeValueConverted,
            DistributedAt: distributedAtConverted);
    }
    
    private async Task<Result<List<string>>> UpdateLawsuitMonitoring(MonitoringCallback? callback, Info validCallback, LawsuitStatus status)
    {
        var updatedConfiguration = new LawsuitMonitoringConfiguration()
        {
            Cnj = callback!.Cnj,
            MonitoringType = MonitoringType.Webhook,
            CourtDivision = validCallback.SearchTag,
            LegalInstance = validCallback.QueryTag,
            Platform = validCallback.PlatformTag,
            CurrentStatus = status,
            ExternalId = validCallback.Id,
            LastUpdate = validCallback.LastResponse
        };

        return await
            lawsuitMonitoringRepository.UpdateLawsuitMonitoringConfiguration(updatedConfiguration)
                .ConfigureAwait(false);
    }

    private static LawsuitEventsHistory CreateLawsuitEventsHistory(MonitoringCallback? callback, Info validCallback, LawsuitStatus status)
    {
        var lawsuitEvent = new LawsuitEventsHistory()
        {
            LawsuitMonitoringConfigurationExternalId = validCallback.Id,
            Cnj = callback?.Cnj ?? string.Empty,
            MonitoringType = MonitoringType.Webhook,
            LawsuitStatus = status,
            EventDate = validCallback.LastResponse,
            Event = JsonConvert.SerializeObject(validCallback.Data)
        };

        return lawsuitEvent;
    }
}
