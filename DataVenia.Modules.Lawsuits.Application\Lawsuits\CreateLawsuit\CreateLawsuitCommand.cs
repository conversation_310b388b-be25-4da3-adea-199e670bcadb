﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Lawsuits.Domain.DTOs;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateLawsuit;
public sealed record CreateLawsuitCommand(
    string Title,
    Guid? FolderId,
    List<PartyDto> Parties,
    string LawsuitTypeId,
    string ClassId,
    string LawsuitStatusId,
    string LegalInstanceId,
    List<int> TopicIds,
    string JudgingOrganId,
    string Cnj,
    string CourtHref,
    string Description,
    string Observations,
    decimal CauseValue,
    decimal ConvictionValue,
    DateTime DistributedAt,
    List<Guid> ResponsibleIds,
    string Access,
    Guid? EvolvedFromCaseId,
    Guid? GroupingCaseId,
    Guid UserId,
    Guid OfficeId
    ) : ICommand<Guid>;
