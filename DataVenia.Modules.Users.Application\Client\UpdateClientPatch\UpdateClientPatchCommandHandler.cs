﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Lawyer.UpdateLawyer;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Domain.Lawyers;
using Microsoft.Extensions.Logging;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;

namespace DataVenia.Modules.Users.Application.Client.UpdateClient;

public class UpdateClientPatchCommandHandler(IClientRepository clientRepository, IUnitOfWork unitOfWork, ILogger<UpdateClientPatchCommandHandler> logger)
: ICommandHandler<UpdateClientPatchCommand>
{
public async Task<Result> Handle(UpdateClientPatchCommand request, CancellationToken cancellationToken)
{
    ClientDomain? client = await clientRepository.GetSingleAsync(x => x.Id == request.ClientId, cancellationToken);

    if (client is null)
    {
        logger.LogError("Client not found: {ClientId}", request.ClientId);
        return Result.Failure(Error.NotFound("Not.Found", $"Client with Id {request.ClientId} not found"));
    }

    client.Update(request.Name, request.Cpf, request.Email);

    try
    {
        await unitOfWork.SaveChangesAsync(cancellationToken);
    } catch (Exception ex)
    {
        logger.LogError(ex, "Error saving client to database.");
        return Result.Failure<Guid>(Error.InternalServerError());
    }

    return Result.Success();
}
}
