﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Cases.UpdateCase;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Cases;

internal sealed class UpdateCase : IEndpoint
{
    private readonly ILogger<UpdateCase> _logger;

    public UpdateCase(ILogger<UpdateCase> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPut("/offices/{officeId}/cases/{caseId}", async (Guid officeId, Guid caseId, [FromBody] UpdateCaseRequest request, ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            try
            {
                Result result = await sender.Send(new UpdateCaseCommand(
                    caseId,
                    request.Title,
                    request.Id,
                    request.FolderId,
                    // request.Parties,
                    request.description,
                    request.observations,
                    request.causeValue,
                    request.convictionValue,
                    request.responsibleIds,
                    request.access,
                    claims.GetUserId(),
                    officeId));

                return result.Match(Results.NoContent, ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating case");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("office:lawsuits:update")
        .WithTags(Tags.Cases);
    }

}

public sealed class UpdateCaseRequest
{
    public Guid Id { get; init; }
    public string Title { get; init; }
    public Guid? FolderId { get; init; }
    // public List<PartyDto> Parties { get; init; }
    public decimal causeValue { get; init; }
    public decimal convictionValue { get; init; }
    public List<Guid> responsibleIds { get; init; }
    public string description { get; init; }
    public string observations { get; init; }
    public string access { get; init; }
}
