﻿﻿using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Outbox;
using DataVenia.Modules.Users.IntegrationEvents;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Outbox;

public sealed class OutboxDispatcher : BackgroundService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<OutboxDispatcher> _logger;
    
    public OutboxDispatcher(IServiceScopeFactory serviceScopeFactory,
        ILogger<OutboxDispatcher> logger)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // while (!stoppingToken.IsCancellationRequested)
        var i = 0;
        // ReSharper disable once ConditionIsAlwaysTrueOrFalse
#pragma warning disable CA1508
#pragma warning disable S2583
        while(i == 1)
#pragma warning restore S2583
#pragma warning restore CA1508
        {
            try
            {
                // Create a new scope for each “batch” of outbox processing
                using IServiceScope scope = _serviceScopeFactory.CreateScope();

                // Resolve your scoped services from this scope
                IOutboxRepository outboxRepository = scope.ServiceProvider.GetRequiredService<IOutboxRepository>();
                IPublishEndpoint publishEndpoint = scope.ServiceProvider.GetRequiredService<IPublishEndpoint>();
                IUnitOfWork unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                
                IEnumerable<Domain.Outbox.Outbox> messages =
                    await outboxRepository.GetUnprocessedMessagesAsync(stoppingToken);
                foreach (Domain.Outbox.Outbox message in messages)
                {
                    if (message.MessageType == nameof(LawyerSignedUpEvent))
                    {
                        LawyerSignedUpEvent? payload =
                            JsonSerializer.Deserialize<LawyerSignedUpEvent>(message.Payload);
                        if (payload != null)
                        {
                            var lawyerSignedUpEvent = new LawyerSignedUpEvent(
                                payload.LawyerId,
                                payload.Email,
                                payload.FirstName,
                                payload.LastName,
                                payload.Password
                            );
                            await publishEndpoint.Publish(lawyerSignedUpEvent, stoppingToken);
                            
                            outboxRepository.MarkAsProcessed(message);
                            
                            await unitOfWork.SaveChangesAsync(stoppingToken);
                        }
                    }
                }
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // swallow and exit loop cleanly
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error dispatching user outbox messages");
            }

        }
    }
}
