﻿using DataVenia.Modules.Lawsuits.Domain.DataDivergence;
using FluentResults;

namespace DataVenia.Modules.Lawsuits.Application.DataDivergences;

public interface IDataDivergenceRepository
{
    void Insert(DataDivergence dataDivergence);
    Task<Result<IReadOnlyCollection<DataDivergence>>> GetLatestForEachInstanceByLawsuitIdAsync(Guid officeId, CancellationToken cancellationToken = default);
    Task<Result<DataDivergence>> GetByIdAsync(Guid divergenceId, CancellationToken cancellationToken = default);
    void Update(DataDivergence dataDivergence);
}
