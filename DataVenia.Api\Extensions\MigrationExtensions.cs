﻿using DataVenia.Modules.Calendar.Infrastructure.Database;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using DataVenia.Modules.LawsuitSync.Infrastructure.Database;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
namespace DataVenia.Api.Extensions;

internal static class MigrationExtensions
{
    internal static void ApplyMigrations(this IApplicationBuilder app)
    {
        using IServiceScope scope = app.ApplicationServices.CreateScope();

        ApplyMigration<UsersDbContext>(scope);
        ApplyMigration<LawsuitsDbContext>(scope);
        ApplyMigration<CalendarDbContext>(scope);
        ApplyMigration<HarveyDbContext>(scope);
        ApplyMigration<LawsuitSyncDbContext>(scope);
    }

    internal static void SeedDatabase(this IServiceScope scope)
    {
        UsersDbContext usersDbContext = scope.ServiceProvider.GetRequiredService<UsersDbContext>();
        IIdentityProviderService identityProviderService = scope.ServiceProvider.GetRequiredService<IIdentityProviderService>();
        var seeder = new Common.SeedDatabase.SeedDatabase(identityProviderService, usersDbContext);
        seeder.SeedDatabaseAsync().Wait();

    }

    private static void ApplyMigration<TDbContext>(IServiceScope scope)
        where TDbContext : DbContext
    {
        using TDbContext context = scope.ServiceProvider.GetRequiredService<TDbContext>();

        context.Database.Migrate();
    }
}
