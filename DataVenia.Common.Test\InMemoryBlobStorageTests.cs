using DataVenia.Common.Infrastructure.Facades;
using Microsoft.Extensions.Logging;
using FakeItEasy;
using FluentResults;
using System.Text;
using Xunit;

namespace DataVenia.Common.Test;

public class InMemoryBlobStorageTests
{
    private readonly InMemoryBlobStorage _storage;

    public InMemoryBlobStorageTests()
    {
        ILogger<InMemoryBlobStorage> fakeLogger = A.Fake<ILogger<InMemoryBlobStorage>>();
        _storage = new InMemoryBlobStorage(fakeLogger);
    }

    [Fact]
    public async Task UploadAsync_ShouldStoreData()
    {
        string content = "Hello, world!";
        using var input = new MemoryStream(Encoding.UTF8.GetBytes(content));

        Result result = await _storage.UploadAsync("bucket", input, "file.txt");

        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task UploadAsync_WithNullInput_ShouldFail()
    {
        Result result = await _storage.UploadAsync("bucket", null!, "file.txt");

        Assert.True(result.IsFailed);
        Assert.Single(result.Errors);
        Assert.Contains("cannot be null", result.Errors[0].Message);
    }

    [Fact]
    public async Task DownloadAsync_ShouldReturnPreviouslyStoredData()
    {
        string content = "Stream me up, Scotty!";
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes(content)), "beam.txt");

        using var output = new MemoryStream();
        Result result = await _storage.DownloadAsync("bucket", "beam.txt", output, CancellationToken.None);

        Assert.True(result.IsSuccess);
        string downloaded = Encoding.UTF8.GetString(output.ToArray());
        Assert.Equal(content, downloaded);
    }

    [Fact]
    public async Task DownloadAsync_ObjectNotFound_ShouldFail()
    {
        using var output = new MemoryStream();
        Result result = await _storage.DownloadAsync("bucket", "ghost.txt", output, CancellationToken.None);

        Assert.True(result.IsFailed);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveObject()
    {
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes("Delete me")), "delete.txt");

        Result result = await _storage.DeleteAsync("bucket", "delete.txt");

        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task DeleteAsync_WhenObjectMissing_ShouldFail()
    {
        Result result = await _storage.DeleteAsync("bucket", "missing.txt");

        Assert.True(result.IsFailed);
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnTrueIfObjectExists()
    {
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes("Exists")), "exists.txt");

        Result<bool> result = await _storage.ExistsAsync("bucket", "exists.txt");

        Assert.True(result.Value);
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnFalseIfObjectDoesNotExist()
    {
        Result<bool> result = await _storage.ExistsAsync("bucket", "missing.txt");

        Assert.False(result.Value);
    }

    [Fact]
    public async Task ListAsync_ShouldReturnAllKeys_WhenPrefixIsNull()
    {
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes("A")), "a.txt");
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes("B")), "b.txt");

        Result<IList<string>> result = await _storage.ListAsync("bucket", null);

        Assert.True(result.IsSuccess);
        Assert.Contains("a.txt", result.Value);
        Assert.Contains("b.txt", result.Value);
        Assert.Equal(2, result.Value.Count);
    }

    [Fact]
    public async Task ListAsync_ShouldFilterKeysByPrefix()
    {
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes("One")), "prefix-one");
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes("Two")), "prefix-two");
        await _storage.UploadAsync("bucket", new MemoryStream(Encoding.UTF8.GetBytes("Other")), "other-file");

        Result<IList<string>> result = await _storage.ListAsync("bucket", "prefix");

        Assert.True(result.IsSuccess);
        Assert.Contains("prefix-one", result.Value);
        Assert.Contains("prefix-two", result.Value);
        Assert.DoesNotContain("other-file", result.Value);
        Assert.Equal(2, result.Value.Count);
    }
}

