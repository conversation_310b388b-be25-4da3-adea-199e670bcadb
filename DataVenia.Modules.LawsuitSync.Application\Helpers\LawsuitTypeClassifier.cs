using DataVenia.Common.Domain.Lawsuit;

namespace DataVenia.Modules.LawsuitSync.Application.Helpers;

public static class LawsuitTypeClassifier
{
    private static readonly Dictionary<string, LawsuitType> ClassToLawsuitType = new()
    {
        { "Ação Civil Pública", LawsuitType.Civel },
        { "Ação de Cobrança", LawsuitType.Civel },
        { "Execução de Título Extrajudicial", LawsuitType.Civel },

        { "Ação Penal", LawsuitType.Criminal },
        { "Inquérito Policial", LawsuitType.Criminal },

        { "Ação Trabalhista", LawsuitType.Trabalhista },
        { "Reclamação Trabalhista", LawsuitType.Trabalhista },

        { "Representação Eleitoral", LawsuitType.Eleitoral },
        { "Ação de Investigação Judicial Eleitoral", LawsuitType.Eleitoral },

        { "Ação Penal Militar", LawsuitType.Militar },
        { "Inquérito Policial Militar", LawsuitType.Militar },

        { "Mandado de Segurança", LawsuitType.Administrativo },
        { "Ação Popular", LawsuitType.Administrativo },

        { "Execução Fiscal", LawsuitType.Fiscal },

        { "Ação Civil Pública Ambiental", LawsuitType.Ambiental },

        { "Ação de Indenização por Danos Morais - Relação de Consumo", LawsuitType.Consumidor },

        { "Ação de Alimentos", LawsuitType.Familia },
        { "Divórcio Litigioso", LawsuitType.Familia },

        { "Inventário", LawsuitType.Sucessoes },
        { "Arrolamento", LawsuitType.Sucessoes },

        { "Recuperação Judicial", LawsuitType.Empresarial },
        { "Falência", LawsuitType.Empresarial },

        { "Ação de Destituição do Poder Familiar", LawsuitType.InfanciaEJuventude },
        { "Guarda", LawsuitType.InfanciaEJuventude },

        { "Ação Previdenciária", LawsuitType.Previdenciario },
        { "Revisão de Benefício Previdenciário", LawsuitType.Previdenciario },

        { "Ação Anulatória de Débito Fiscal", LawsuitType.Tributario },
        { "Mandado de Segurança Tributário", LawsuitType.Tributario }
    };

    private static readonly Dictionary<string, LawsuitType> TopicToLawsuitType = new()
    {
        { "Danos Morais", LawsuitType.Civel },
        { "Responsabilidade Civil", LawsuitType.Civel },

        { "Homicídio", LawsuitType.Criminal },
        { "Furto", LawsuitType.Criminal },

        { "Acidente de Trabalho", LawsuitType.Trabalhista },
        { "Horas Extras", LawsuitType.Trabalhista },

        { "Abuso de Poder Econômico", LawsuitType.Eleitoral },

        { "Crime Militar", LawsuitType.Militar },

        { "Improbidade Administrativa", LawsuitType.Administrativo },

        { "Dívida Ativa", LawsuitType.Fiscal },

        { "Dano Ambiental", LawsuitType.Ambiental },

        { "Relação de Consumo", LawsuitType.Consumidor },

        { "Pensão Alimentícia", LawsuitType.Familia },
        { "Guarda Compartilhada", LawsuitType.Familia },

        { "Partilha de Bens", LawsuitType.Sucessoes },

        { "Falência", LawsuitType.Empresarial },

        { "Adoção", LawsuitType.InfanciaEJuventude },

        { "Aposentadoria", LawsuitType.Previdenciario },

        { "Imposto de Renda", LawsuitType.Tributario }
    };

    public static LawsuitType ClassifyLawsuitType(string lawsuitClass, List<string> topics)
    {
        if (ClassToLawsuitType.TryGetValue(lawsuitClass, out var classLawsuitType))
        {
            return classLawsuitType;
        }

        foreach (var topic in topics)
        {
            if (TopicToLawsuitType.TryGetValue(topic, out var topicLawsuitType))
            {
                return topicLawsuitType;
            }
        }

        return LawsuitType.None;
    }
}
