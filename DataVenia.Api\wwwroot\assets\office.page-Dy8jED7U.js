import{e as c,r,d as o,j as e,h as u,U as v,O as j,l as g,a as n,$ as C,u as k,Q as w,c as b,m as z}from"./index-DxHSBLqJ.js";import{C as A}from"./card-header-title-MEld3mYM.js";import{L as x}from"./lawyers-487mkhkR.js";import{U as L}from"./user-round-plus-79Wfoby6.js";import"./schemas-CWY_3UB6.js";import"./brazilian-values-a8H3KUqb.js";/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=c("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=c("Inbox",[["polyline",{points:"22 12 16 12 14 15 10 15 8 12 2 12",key:"o97t9d"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=c("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=c("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var d;(s=>{s.copy=async t=>{try{await navigator.clipboard.writeText(t)}catch{const a=document.createElement("textarea");a.value=t,a.style.top="0",a.style.left="0",a.style.position="fixed",document.body.appendChild(a),a.focus(),a.select();try{document.execCommand("copy")}finally{document.body.removeChild(a)}}}})(d||(d={}));const F=[x.create({name:"Fulano Silva",oab:"RJ12345"}),x.create({name:"Beltrano Silveira",oab:"SP12345"})],I="/admin/escritorio?status=string",H=async()=>o.jsonResponse({items:F}),B=()=>({patch:async s=>o.redirectResponse(s.link(s.path,{status:"success"}))}),O=z(s=>{s.add("name","Advogado"),s.add("oab","OAB"),s.add("id","Ações",{Element:t=>e.jsxs("span",{className:"flex items-center gap-kilo",children:[e.jsxs(n,{theme:"info",size:"small",onClick:async()=>{await d.copy(t.row.name)},children:[e.jsx(S,{size:14})," Copiar convite"]}),e.jsxs(n,{theme:"warn",size:"small",children:[e.jsx(E,{size:14})," Cancelar convite"]})]})})});function X(){const[s,t]=r.useState(!1),[m,a]=o.useQueryStringState(I),i=o.useDataLoader(),h=(i==null?void 0:i.items)??[],p=o.useFormActions(),l=m.status==="success",y=()=>a(f=>({...f,status:void 0}));return r.useEffect(()=>{l&&t(!1)},[l]),e.jsxs(r.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 gap-mega lg:grid-cols-2",children:[e.jsx(u,{title:"Advogados",Icon:v,children:12}),e.jsx(u,{title:"Convites enviados",Icon:N,children:10})]}),e.jsxs(j,{title:e.jsx(A,{title:"Meu escritório",side:e.jsxs(r.Fragment,{children:[e.jsx(o.Link,{href:g.newLawyer,children:e.jsxs(n,{size:"small",children:[e.jsx(L,{size:14}),"Adicionar advogado"]})}),e.jsxs(n,{size:"small",onClick:()=>t(!0),children:[e.jsx(R,{size:14}),"Enviar convite"]})]})}),children:[e.jsx(C,{title:"Sucesso",open:l,theme:"success",onClose:y,children:"Convite enviado com sucesso! Peça para que o destinatário aceite o convite para ingressar no seu escritório."}),e.jsx(k,{cols:O,name:"my-office",loading:!(i!=null&&i.items),rows:h})]}),e.jsxs(w,{open:s,onChange:t,title:"Convidar advogado",children:[e.jsx("p",{className:"max-w-sm text-pretty",children:"Para adicionar advogados ao seu escritório, você deve enviar um convite para o email. Ao aceitar esse email, o advogado irá confirmar a solicitação e passará a fazer parte do seu time"}),e.jsxs(o.Form,{method:"patch",encType:"json",className:"my-kilo flex flex-col gap-kilo",onReset:()=>t(!1),children:[e.jsx(b,{required:!0,autoFocus:!0,name:"email",type:"email",title:"Email",placeholder:"<EMAIL>"}),e.jsxs("div",{className:"flex flex-row items-center justify-center gap-kilo",children:[e.jsx(n,{loading:p.loading,className:"w-full",theme:"warn",type:"reset",children:"Cancelar"}),e.jsx(n,{loading:p.loading,className:"w-full",type:"submit",children:"Convidar"})]})]})]})]})}export{B as actions,X as default,H as loader};
