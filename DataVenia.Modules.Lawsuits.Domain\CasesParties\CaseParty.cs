﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.CasesData;

namespace DataVenia.Modules.Lawsuits.Domain.CasesParties;

public sealed class CaseParty : Entity
{
    public Guid Id { get; set; }
    public Guid CaseDataId { get; private set; }
    public CaseData CaseData { get; private set; }

    public Guid PartyId { get; private set; }

    public string PartyType { get; private set; }
    public bool IsClient { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private CaseParty() { }

    public static CaseParty Create(Guid caseDataId, Guid partyId, string partyType, bool isClient)
    {
        var caseParty = new CaseParty()
        {
            Id = Guid.NewGuid(),
            CaseDataId = caseDataId,
            PartyId = partyId,
            PartyType = partyType,
            IsClient = isClient,
            CreatedAt = DateTime.UtcNow
        };

        return caseParty;
    }

    public void Update(string partyType, bool isClient)
    {
        PartyType = partyType;
        IsClient = isClient;
        UpdatedAt = DateTime.UtcNow;
    }
}
