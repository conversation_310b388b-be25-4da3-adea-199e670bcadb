﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Office.AcceptInviteToOffice;
using DataVenia.Modules.Users.Application.User.AcceptTermsAndConditions;
using DataVenia.Modules.Users.Domain.Authorization;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Users.Presentation.User;

internal sealed class UpdateUserPatch : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPatch("users/{userId}/legal-terms", async (Guid userId, [FromBody] UpdateUserPatchRequest request, ClaimsPrincipal claims, [FromServices] ISender sender) =>
            {
                Result result = await sender.Send(new UpdateUserPatchCommand(
                    claims.GetUserId(),
                    userId,
                    request.AcceptedAt));

                return result.Match(Results.NoContent, ApiResults.Problem);
            })
            .RequireAuthorization(Permission.General.Code)
            .WithTags(Tags.Users);
    }

}

public sealed class UpdateUserPatchRequest
{
    public DateTime AcceptedAt { get; init; }
}
