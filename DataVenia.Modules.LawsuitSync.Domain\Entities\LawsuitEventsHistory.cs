using DataVenia.Common.Domain.Lawsuit;
using DataVenia.Modules.LawsuitSync.Domain.Enums;

namespace DataVenia.Modules.LawsuitSync.Domain.Entities;

public sealed class LawsuitEventsHistory
{
    public long Id { get; set; }
    public Guid LawsuitMonitoringConfigurationExternalId { get; set; }
    public required string Cnj { get; set; }
    public required MonitoringType MonitoringType { get; set; }
    public required LawsuitStatus LawsuitStatus { get; set; }
    public required DateTimeOffset EventDate { get; set; }
    public required string Event { get; set; }
}
