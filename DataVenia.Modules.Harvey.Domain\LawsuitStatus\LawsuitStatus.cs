﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.LawsuitStatus;
public sealed class LawsuitStatus : Entity
{
    public static readonly LawsuitStatus Pending = Create("Pending", "Pendente").Value;
    public static readonly LawsuitStatus InProgress = Create("InProgress", "Em progresso").Value;
    public static readonly LawsuitStatus Suspended = Create("Suspended", "Suspendido").Value;
    public static readonly LawsuitStatus AwaitingAppeal = Create("AwaitingAppeal", "Aguardando Apelação").Value;
    public static readonly LawsuitStatus Completed = Create("Completed", "Completo").Value;
    public static readonly LawsuitStatus Closed = Create("Closed", "Fechado").Value;
    public static readonly LawsuitStatus Cancelled = Create("Cancelled", "Cancelado").Value;
    public static readonly LawsuitStatus AwaitingClient = Create("AwaitingClient", "Aguardando cliente").Value;
    public static readonly LawsuitStatus AwaitingDecision = Create("AwaitingDecision", "Aguardando decisão").Value;
    public string Id { get; private set; }
    public string DisplayName { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    private LawsuitStatus() { }

    public static Result<LawsuitStatus> Create(string name, string displayName)
    {
        return new LawsuitStatus() { DisplayName = displayName, Id = name };
    }
}
