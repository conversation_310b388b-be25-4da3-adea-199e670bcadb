using System.Text.Json;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Domain.SharedModels;
using AddressDomain = DataVenia.Modules.Users.Domain.SharedModels.Address;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
namespace DataVenia.Modules.Users.Infrastructure.Client;

internal sealed class ClientConfiguration : IEntityTypeConfiguration<ClientDomain>
{
    public void Configure(EntityTypeBuilder<ClientDomain> builder)
    {
        builder.ToTable("client");

        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.HasKey(ou => ou.Id);
        
        // Configure CPF as required
        builder.Property(c => c.Cpf)
            .HasMaxLength(11)
            .IsRequired();

        // Configure Name as required
        builder.Property(c => c.Name)
            .HasMaxLength(200)
            .IsRequired();
        
        var serializationOptions = new JsonSerializerOptions();
        
        builder.Property(l => l.Associations)
            .HasColumnName("associations")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, serializationOptions),
                v => JsonSerializer.Deserialize<ClientAssociations>(v, serializationOptions) ?? new ClientAssociations())
            .HasDefaultValueSql("'[]'::jsonb");
    }
}
