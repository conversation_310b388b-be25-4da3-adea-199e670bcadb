﻿using DataVenia.Common.Domain;

namespace DataVenia.Common.Application.Exceptions;

public sealed class DataVeniaException : Exception
{
    public DataVeniaException(string requestName, Error? error = default, Exception? innerException = default)
        : base("Application exception", innerException)
    {
        RequestName = requestName;
        Error = error;
    }

    public string RequestName { get; }

    public Error? Error { get; }
}
