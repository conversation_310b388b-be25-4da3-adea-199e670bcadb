﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RefactorLawsuitToSupportCoverData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "court",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropColumn(
                name: "court_division_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropColumn(
                name: "court_href",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropColumn(
                name: "forum_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropColumn(
                name: "legal_category_id",
                schema: "lawsuit",
                table: "lawsuit");

            migrationBuilder.AlterColumn<string>(
                name: "legal_instance_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "lawsuit_status_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<decimal>(
                name: "cause_value",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "numeric",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "numeric");

            migrationBuilder.AddColumn<string>(
                name: "judging_organ_href",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "judging_organ_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(150)",
                maxLength: 150,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "lawsuit_type_id",
                schema: "lawsuit",
                table: "lawsuit",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<DateTime>(
                name: "distributed_at",
                schema: "lawsuit",
                table: "lawsuit",
                type: "timestamp with time zone",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone");

            migrationBuilder.AddColumn<string>(
                name: "class_id",
                schema: "lawsuit",
                table: "lawsuit",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "topic_ids",
                schema: "lawsuit",
                table: "lawsuit",
                type: "jsonb",
                nullable: true,
                defaultValueSql: "'[]'::jsonb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "judging_organ_href",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropColumn(
                name: "judging_organ_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropColumn(
                name: "class_id",
                schema: "lawsuit",
                table: "lawsuit");

            migrationBuilder.DropColumn(
                name: "topic_ids",
                schema: "lawsuit",
                table: "lawsuit");

            migrationBuilder.AlterColumn<string>(
                name: "legal_instance_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "lawsuit_status_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "cause_value",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "numeric",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "numeric",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "court",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "court_division_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "court_href",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "forum_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "lawsuit_type_id",
                schema: "lawsuit",
                table: "lawsuit",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "distributed_at",
                schema: "lawsuit",
                table: "lawsuit",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "legal_category_id",
                schema: "lawsuit",
                table: "lawsuit",
                type: "text",
                nullable: false,
                defaultValue: "");
        }
    }
}
