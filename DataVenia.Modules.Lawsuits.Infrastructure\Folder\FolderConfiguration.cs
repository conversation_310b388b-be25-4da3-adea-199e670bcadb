﻿using FolderDomain = DataVenia.Modules.Lawsuits.Domain.Folder.Folder;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.Folder;
public sealed class FolderConfiguration : IEntityTypeConfiguration<FolderDomain>
{
    public void Configure(EntityTypeBuilder<FolderDomain> builder)
    {
        builder.ToTable("folder");

        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.<PERSON>Key(l => l.Id);

        builder.Property(x => x.Color).HasMaxLength(64);
        
        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.HasOne(f => f.ParentFolder)
            .WithMany()
            .HasForeignKey(f => f.ParentFolderId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
