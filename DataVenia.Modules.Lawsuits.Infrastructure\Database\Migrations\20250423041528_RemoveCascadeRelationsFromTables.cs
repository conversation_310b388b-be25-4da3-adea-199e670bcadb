﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemoveCascadeRelationsFromTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_case_data_case_case_id",
                schema: "lawsuit",
                table: "case_data");

            migrationBuilder.DropForeignKey(
                name: "fk_case_party_case_data_case_data_id",
                schema: "lawsuit",
                table: "case_party");

            migrationBuilder.DropForeignKey(
                name: "fk_case_responsible_case_datas_case_data_id",
                schema: "lawsuit",
                table: "case_responsible");

            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_data_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_party_lawsuit_data_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_party");

            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_responsible_lawsuit_datas_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_responsible");

            migrationBuilder.AddForeignKey(
                name: "fk_case_data_case_case_id",
                schema: "lawsuit",
                table: "case_data",
                column: "case_id",
                principalSchema: "lawsuit",
                principalTable: "case",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_case_party_case_data_case_data_id",
                schema: "lawsuit",
                table: "case_party",
                column: "case_data_id",
                principalSchema: "lawsuit",
                principalTable: "case_data",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_case_responsible_case_datas_case_data_id",
                schema: "lawsuit",
                table: "case_responsible",
                column: "case_data_id",
                principalSchema: "lawsuit",
                principalTable: "case_data",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_data_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                column: "lawsuit_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_party_lawsuit_data_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_party",
                column: "lawsuit_data_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit_data",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_responsible_lawsuit_datas_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_responsible",
                column: "lawsuit_data_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit_data",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_case_data_case_case_id",
                schema: "lawsuit",
                table: "case_data");

            migrationBuilder.DropForeignKey(
                name: "fk_case_party_case_data_case_data_id",
                schema: "lawsuit",
                table: "case_party");

            migrationBuilder.DropForeignKey(
                name: "fk_case_responsible_case_datas_case_data_id",
                schema: "lawsuit",
                table: "case_responsible");

            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_data_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_party_lawsuit_data_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_party");

            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_responsible_lawsuit_datas_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_responsible");

            migrationBuilder.AddForeignKey(
                name: "fk_case_data_case_case_id",
                schema: "lawsuit",
                table: "case_data",
                column: "case_id",
                principalSchema: "lawsuit",
                principalTable: "case",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_case_party_case_data_case_data_id",
                schema: "lawsuit",
                table: "case_party",
                column: "case_data_id",
                principalSchema: "lawsuit",
                principalTable: "case_data",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_case_responsible_case_datas_case_data_id",
                schema: "lawsuit",
                table: "case_responsible",
                column: "case_data_id",
                principalSchema: "lawsuit",
                principalTable: "case_data",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_data_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                column: "lawsuit_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_party_lawsuit_data_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_party",
                column: "lawsuit_data_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit_data",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_responsible_lawsuit_datas_lawsuit_data_id",
                schema: "lawsuit",
                table: "lawsuit_responsible",
                column: "lawsuit_data_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit_data",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
