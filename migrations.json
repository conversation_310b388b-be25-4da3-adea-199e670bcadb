{"projects": [{"path": "./DataVenia.Modules.Calendar.Infrastructure/DataVenia.Modules.Calendar.Infrastructure.csproj", "dbcontext": "CalendarDbContext"}, {"path": "./DataVenia.Modules.Harvey.Infrastructure/DataVenia.Modules.Harvey.Infrastructure.csproj", "dbcontext": "HarveyDbContext"}, {"path": "./DataVenia.Modules.Lawsuits.Infrastructure/DataVenia.Modules.Lawsuits.Infrastructure.csproj", "dbcontext": "LawsuitsDbContext"}, {"path": "./DataVenia.Modules.Users.Infrastructure/DataVenia.Modules.Users.Infrastructure.csproj", "dbcontext": "UsersDbContext"}, {"path": "./DataVenia.Modules.LawsuitSync.Infrastructure/DataVenia.Modules.LawsuitSync.Infrastructure.csproj", "dbcontext": "LawsuitSyncDbContext"}]}