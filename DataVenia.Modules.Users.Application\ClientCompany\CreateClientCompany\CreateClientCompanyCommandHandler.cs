﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Domain.ClientCompany;
using DataVenia.Modules.Users.Domain.Company;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.ClientCompany.CreateClientCompany;
public sealed class CreateClientCompanyCommandHandler(
    IClientCompanyRepository clientCompanyRepository,
    IClientRepository clientRepository,
    ICompanyRepository companyRepository,
    IUnitOfWork unitOfWork,
    ILogger<CreateClientCompanyCommandHandler> logger) : ICommandHandler<CreateClientCompanyCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateClientCompanyCommand request, CancellationToken cancellationToken)
    {
        // client a ser chamado existe?
        Domain.Client.Client? clientToBeAdded = await clientRepository.GetSingleAsync(lc => lc.Id == request.ClientId, cancellationToken);

        if (clientToBeAdded == null)
        {
            logger.LogError("Client to be bound to company does not exist. {ClientId}", request.ClientId);
            return Result.Failure<Guid>(new Error("Client.NotFound", "Client to be bound to company does not exist.", ErrorType.Validation));
        }

        // company a ser vinculado existe?
        Domain.Company.Company? existingCompany = await companyRepository.GetSingleAsync(lc => lc.Id == request.CompanyId, cancellationToken);

        if (existingCompany == null)
        {
            logger.LogError("Company does not exist. {CompanyId}", request.CompanyId);
            return Result.Failure<Guid>(new Error("Company.NotFound", "Company to be bound to client does not exist.", ErrorType.Validation));
        }

        // client já está vinculado a company?
        Domain.ClientCompany.ClientCompany? existingClientCompany = await clientCompanyRepository.GetSingleAsync(lc => lc.ClientId == clientToBeAdded.Id && lc.CompanyId == existingCompany.Id, cancellationToken);

        if (existingClientCompany != null)
        {
            logger.LogError("Client is already bound to the Company. {@ExistingClientCompany}", existingClientCompany);
            return Result.Failure<Guid>(new Error("Client.AlreadyBound", "Client is already bound to the Company.", ErrorType.Validation));
        }

        // cria vinculo
        Result<Domain.ClientCompany.ClientCompany> clientCompany = Domain.ClientCompany.ClientCompany.Create(request.ClientId, request.CompanyId, request.Role);

        if (clientCompany.IsFailure)
        {
            logger.LogError("It wasn't possible to create the ClientCompany {ClientCompanyFailure}", clientCompany.Error);
            return Result.Failure<Guid>(clientCompany.Error);
        }
        
        clientCompanyRepository.Insert(clientCompany.Value);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while saving the clientCompany entity. {@ClientCompany}", clientCompany);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return Result.Success(clientCompany.Value.Id);
    }
}
