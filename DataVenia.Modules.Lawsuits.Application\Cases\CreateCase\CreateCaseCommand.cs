﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Lawsuits.Domain.DTOs;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateCase;
public sealed record CreateCaseCommand(
    string Title,
    Guid? FolderId,
    List<PartyDto> Parties,
    string Description,
    string Observations,
    decimal CauseValue,
    decimal ConvictionValue,
    List<Guid> ResponsibleIds,
    string Access,
    Guid UserId,
    Guid OfficeId
    ) : ICommand<Guid>;
