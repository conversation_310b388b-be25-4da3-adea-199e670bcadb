﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Error = FluentResults.Error;
using Result = FluentResults.Result;

namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitData;
public sealed class LawsuitDataRepository(
    LawsuitsDbContext context,
    ILogger<LawsuitDataRepository> logger) : ILawsuitDataRepository
{
    public async Task<FluentResults.Result<Domain.LawsuitsData.LawsuitData>> GetLatestOfInstanceByLawsuitIdAsync(Guid lawsuitId, string instance, CancellationToken cancellationToken)
    {
        var lawsuitData = await context.LawsuitDatas
            .Where(l => l.LawsuitId == lawsuitId && l.LegalInstanceId == instance)
            .OrderByDescending(l => l.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);  

        return lawsuitData ?? Result.Fail<Domain.LawsuitsData.LawsuitData>("LawsuitData.Not.Found");
    }

    public Task<FluentResults.Result<List<Guid>>> GetLawyerIdsByLawsuitDataIdAsync(Guid lawsuitDataId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public void Insert(Domain.LawsuitsData.LawsuitData lawsuitData)
    {
        context.LawsuitDatas.Add(lawsuitData);
    }

    public async Task<FluentResults.Result<Domain.LawsuitsData.LawsuitData?>> GetLatestByLawsuitIdAsync(Guid lawsuitId, CancellationToken cancellationToken)
    {
        Domain.LawsuitsData.LawsuitData? lawsuit = await context.LawsuitDatas
            .Where(l => l.LawsuitId == lawsuitId)
            .Include(l => l.Responsibles)
            .OrderByDescending(l => l.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);  // Fetch the first matching record or return null if none found

        return lawsuit;
    }

    public async Task<FluentResults.Result<List<Domain.LawsuitsData.LawsuitData>>> GetLatestOfEachInstanceByLawsuitIdsAsync(List<Guid> lawsuitIds, CancellationToken cancellationToken)
    {
        try
        {
            
            var lawsuitData = await context.LawsuitDatas
                .Where(l => lawsuitIds.Contains(l.LawsuitId))
                .Include(l => l.Responsibles)
                .GroupBy(l => new { l.LawsuitId, l.LegalInstanceId })
                .Select(g => g.OrderByDescending(l => l.CreatedAt).First())
                .ToListAsync(cancellationToken);

            return lawsuitData;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occured while getting latest lawsuit_data for each instance. LawsuitId: {LawsuitId}", lawsuitIds);
            return Result.Fail<List<Domain.LawsuitsData.LawsuitData>>("Internal.Server.Error");
        }
    }

    
    public void Update(Domain.LawsuitsData.LawsuitData lawsuitData)
    {
        context.LawsuitDatas.Update(lawsuitData);
    }
}
