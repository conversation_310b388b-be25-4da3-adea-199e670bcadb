﻿using System.Linq.Expressions;

namespace DataVenia.Modules.Users.Domain.Company;

public interface ICompanyRepository
{
    Task<IReadOnlyCollection<Company>> GetManyAsync(Expression<Func<Company, bool>> filter, CancellationToken cancellationToken = default);
    Task<Domain.Company.Company?> GetSingleAsync(Expression<Func<Domain.Company.Company, bool>> filter,
        CancellationToken cancellationToken = default);
    void Insert(Company company);
    
    Task<IEnumerable<Domain.Company.Company>> GetAllByOfficeIdAsync(Guid officeId,
        CancellationToken cancellationToken = default);
    
}

