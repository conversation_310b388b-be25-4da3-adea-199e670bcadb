﻿﻿namespace DataVenia.Modules.Harvey.Application.LawsuitTopic;

public sealed record GetLawsuitTopicByIdResponse(
    int Id,
    string LegalProvision,
    string Article,
    string Glossary,
    bool IsSecret,
    bool SecondaryTopic,
    bool PreviousCrime,
    bool IsFirstInstance,
    bool IsSecondInstance,
    bool JustEsJuizadoEs,
    bool JustEsTurmas,
    bool JustEs1grauMil,
    bool JustEs2grauMil,
    bool JustEsJuizadoEsFp,
    bool JustTuEsUn,
    bool JustFed1grau,
    bool JustFed2grau,
    bool JustFedJuizadoEs,
    bool JustFedTurmas,
    bool JustFedNacional,
    bool JustFedRegional,
    bool JustTrab1grau,
    bool JustTrab2grau,
    bool JustTrabTst,
    bool JustTrabCsjt,
    bool Stf,
    bool Stj,
    bool Cjf,
    bool Cnj,
    bool JustMilUniao1grau,
    bool JustMilUniaoStm,
    bool JustMilEst1grau,
    bool JustMilEstTjm,
    bool JustElei1grau,
    bool JustElei2grau,
    bool JustEleiTse,
    string? UpdatedBy,
    DateTime? UpdatedAt);
