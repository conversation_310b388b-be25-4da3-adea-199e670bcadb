﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.LawsuitParty;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using LawsuitPartyDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitParties.LawsuitParty;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitParty;

public sealed class LawsuitPartyRepository(LawsuitsDbContext context, ILogger<LawsuitPartyRepository> logger) : ILawsuitPartyRepository
{
    public void Insert(LawsuitPartyDomain lawsuitParty)
    {
        context.LawsuitParties.Update(lawsuitParty);
    }

    public async Task<Result<IReadOnlyCollection<LawsuitPartyDomain>>> GetLawsuitPartiesAsync(Guid lawsuitId,
        CancellationToken cancellationToken = default)
    {
        try
        {

            List<LawsuitPartyDomain> lawsuitParties = await context.Set<LawsuitPartyDomain>().
                Where(lp => context.Set<Domain.LawsuitsData.LawsuitData>()
                    .Where(ld => ld.LawsuitId == lawsuitId)
                    .Select(ld => ld.Id)
                    .Contains(lp.LawsuitDataId))
                .ToListAsync(cancellationToken);

            return lawsuitParties;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting lawsuit parties");

            return Result.Failure<IReadOnlyCollection<LawsuitPartyDomain>>(new Error("Internal.Server.Error",
                "Something weird happened.", ErrorType.InternalServerError));
        }
    }
}
