﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Users.Infrastructure.Outbox;

internal sealed class OutboxConfiguration : IEntityTypeConfiguration<Domain.Outbox.Outbox>
{
    public void Configure(EntityTypeBuilder<Domain.Outbox.Outbox> builder)
    {
        builder.ToTable("outbox");
        builder.HasKey(m => m.Id);
        builder.Property(m => m.Id)
            .HasColumnType("uuid")
            .IsRequired();

        builder.Property(m => m.MessageType)
            .IsRequired();

        builder.Property(m => m.Payload)
            .IsRequired();

        builder.Property(m => m.CreatedAt)
            .IsRequired();

        builder.Property(m => m.Processed)
            .IsRequired();

        builder.Property(m => m.ProcessedAt);
            
        // Optional: Index to quickly query unprocessed messages
        builder.HasIndex(m => m.Processed);
    }
}
