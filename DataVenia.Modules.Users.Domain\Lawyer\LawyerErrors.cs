﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Users.Domain.Lawyers;
public static class LawyerErrors
{
    public static Error NotFound(Guid userId) =>
        Error.NotFound("Lawyers.NotFound", $"The Lawyer with the identifier {userId} not found");

    public static Error NotFound(string identityId) =>
        Error.NotFound("Lawyers.NotFound", $"The Lawyer with the IDP identifier {identityId} not found");
}
