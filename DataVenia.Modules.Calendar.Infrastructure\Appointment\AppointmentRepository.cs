﻿using System.Linq.Expressions;
using System.Threading;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Domain.Appointments;
using DataVenia.Modules.Calendar.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Calendar.Infrastructure.Appointments;

internal sealed class AppointmentRepository(CalendarDbContext context) : IAppointmentRepository
{
    public async Task<Appointment?> GetSingleAsync(Expression<Func<Appointment, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Appointments
            .Include(a => a.Participants)
            .Include(a => a.Status)
            .Include(a => a.Recurrence)
            .FirstOrDefaultAsync(filter, cancellationToken);

    }

    public async Task<IReadOnlyCollection<Appointment>> GetManyAsync(Expression<Func<Appointment, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Appointments
            .Include(a => a.Participants)
            .Include(a => a.Recurrence)
            .Include(a => a.Status)
            .Where(filter)
            .ToListAsync(cancellationToken);
    }

    public void Insert(Appointment appointment)
    {
        context.Appointments.Add(appointment);
    }

    public void Delete(Appointment appointment)
    {
        context.Appointments.Remove(appointment);
    }

    public void Update(Appointment appointment)
    {
        context.Appointments.Update(appointment);
    }
}
