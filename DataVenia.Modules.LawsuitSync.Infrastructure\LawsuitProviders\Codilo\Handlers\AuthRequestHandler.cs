﻿using System.Globalization;
using System.Net.Http.Headers;
using System.Text.Json;
using DataVenia.Common.Domain.Helpers;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Configs;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Requests;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Refit;

namespace DataVenia.Modules.LawsuitSync.Infrastructure.LawsuitProviders.Codilo.Handlers;

public class AuthRequestHandler : DelegatingHandler
{
    private readonly CodiloSettings _settings;
    private readonly HttpClient _httpClient;
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<AuthRequestHandler> _logger;

    public AuthRequestHandler(
        IOptions<CodiloSettings> settings,
        IHttpClientFactory httpClientFactory, IMemoryCache memoryCache,
        ILogger<AuthRequestHandler> logger)
    {
        _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        _httpClient = httpClientFactory.CreateClient("LawsuitSync");
        _memoryCache = memoryCache;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        var token = await GetAuthToken();
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);
        request.Headers.UserAgent.Add(new ProductInfoHeaderValue(new ProductHeaderValue("DataVenia", "1.0")));

        return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
    }

    private const string AuthTokenKey = "authToken";

    private async Task<AuthResponse> GetAuthToken()
    {
        if (_memoryCache.TryGetValue(AuthTokenKey, out AuthResponse authResponse))
        {
            return authResponse;
        }

        var test = new FormUrlEncodedContent(new Dictionary<string, string>
        {
            { "grant_type", "client_credentials" },
            { "id", _settings.AuthId },
            { "secret", _settings.AuthSecret },
        });

        using var request = new HttpRequestMessage(HttpMethod.Post, "oauth/token")
        {
            Content = test
        };
        request.Headers.Add("ContentType", "application/x-www-form-urlencoded");
        request.Headers.UserAgent.Add(new ProductInfoHeaderValue(new ProductHeaderValue("DataVenia", "1.0")));


        try
        {
            var response = await _httpClient.SendAsync(request);

            var responseString = await response.Content.ReadAsStringAsync();

            var result = JsonHelper.Deserialize<AuthResponse>(responseString);

            if (result.IsSuccess)
            {
                const int tenMinutesInSec = 600;
                var expiration = long.Parse(result.Value.ExpiresIn, CultureInfo.InvariantCulture) - tenMinutesInSec;
                var cacheConfig = new MemoryCacheEntryOptions()
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(expiration)
                };

                _memoryCache.Set(AuthTokenKey, result.Value, cacheConfig);
                return result.Value;
            }

            _logger.LogError("Failed to get auth token {@Result}", result);
            throw new AuthenticationFailureException("Could not authenticate request");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get auth token {@Result}", JsonSerializer.Serialize(ex));
            throw new AuthenticationFailureException("Could not authenticate request");
        }
    }
}
