import{s as m,f as d,d as r,r as u,j as s,O as h,c as o,Z as x,a as j,b as i}from"./index-DxHSBLqJ.js";import{l as f}from"./index-BjcY0-OB.js";import{z as n,S as w}from"./schemas-CWY_3UB6.js";import"./brazilian-values-a8H3KUqb.js";const g={duplicates:"last",allowDots:!0,allowSparse:!0,allowEmptyArrays:!0,depth:Number.MAX_SAFE_INTEGER,arrayLimit:Number.MAX_SAFE_INTEGER,strictNullHandling:!0,parseArrays:!0},y=a=>f.parse(a.reduce((t,e)=>m(t,e.path.join("."),e.message),{}),g),l=n.object({email:w.email,password:n.string()}),S=()=>({post:async a=>{const t=await a.request.json();await i(800);const e=l.safeParse(t);return e.success?(await i(800),r.redirectResponse(a.links.root)):r.jsonResponse({errors:y(e.error.issues)})}});function R(){const a=d(l,"login"),t=r.useFormActions(),[e,c]=u.useState("password");return s.jsx("div",{className:"flex h-screen w-full items-center justify-center px-kilo",children:s.jsx(h,{container:"lg:w-1/3 lg:container max-w-mobile",title:s.jsx("div",{className:"text-center",children:"Data Venia"}),children:s.jsxs(r.Form,{encType:"json",method:"post",className:"grid w-full grid-cols-1 gap-kilo",children:[s.jsx(o,{...a.input("email",{title:"E-mail",placeholder:"<EMAIL>"})}),s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsx(o,{...a.input("password",{type:e,title:"Senha",placeholder:"Não compartilhe sua senha"})}),s.jsx(x,{checked:e==="password",onChange:()=>c(p=>p==="password"?"text":"password"),children:"Mostrar senha"})]}),s.jsx(j,{loading:t.loading,type:"submit",children:"Entrar"})]})})})}export{S as actions,R as default};
