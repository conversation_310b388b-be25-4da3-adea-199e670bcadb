﻿using DataVenia.Modules.Harvey.Domain.LegalInstance;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using LegalInstanceDomain = DataVenia.Modules.Harvey.Domain.LegalInstance.LegalInstance;
namespace DataVenia.Modules.Harvey.Infrastructure.LegalInstance;
public sealed class LegalInstanceRepository(HarveyDbContext context) : ILegalInstanceRepository
{
    public async Task<IReadOnlyCollection<LegalInstanceDomain>> GetAllAsync(string? displayName, CancellationToken cancellationToken = default)
    {
        IQueryable<LegalInstanceDomain> query = context.LegalInstances;

        if (!string.IsNullOrWhiteSpace(displayName))
            query = query.Where(li => li.DisplayName.Contains(displayName));

        return await query.ToListAsync(cancellationToken);
    }
}
