﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddHarveyData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"INSERT INTO harvey.court_division (id, display_name) VALUES
                ('Camara', 'Camara'),
                ('CamaraCivel', 'Câmara Cível'),
                ('VaraCriminal', 'Vara Criminal'),
                ('VaraCivel', 'Vara Cível'),
                ('TribunaldeInstrucaoN3deMadrid', 'Tribunal de Instrução Nº3 de Madrid'),
                ('VaradeFamiliaSucessoes', 'Vara de Família/Sucessões'),
                ('VaradeFazendaPublica', 'Vara de Fazenda Publica'),
                ('VaradoTrabalho', 'Vara do Trabalho'),
                ('VaraEleitoral', 'Vara Eleitoral'),
                ('VaraEmpresarial', 'Vara Empresarial'),
                ('SupremoTribunalFederal', 'Supremo Tribunal Federal'),
                ('VaraFederal', 'Vara Federal'),
                ('SecretariaJudiciaria', 'Secretaria Judiciária'),
                ('TurmaRecursal', 'Turma Recursal'),
                ('VaraTrabalhista', 'Vara Trabalhista')
                ;");

            migrationBuilder.Sql(@"INSERT INTO harvey.lawsuit_type (id, display_name) VALUES
('AberturaRegistroeCumprimentodeTestamento', 'Abertura, Registro e Cumprimento de Testamento'),
('AcolhimentodeEmbargosdeDeclaracao', 'Acolhimento de Embargos de Declaração'),
('Adocao', 'Adoção'),
('AdocaoccDestituicaodoPoderFamiliar', 'Adoção c/c Destituição do Poder Familiar'),
('Advertencia', 'Advertência'),
('Agravo', 'Agravo'),
('AgravodeExecucaoPenal', 'Agravo de Execução Penal'),
('AgravodeInstrumento', 'Agravo de Instrumento'),
('AgravodeInstrumentoemAgravodePeticao', 'Agravo de Instrumento em Agravo de Petição'),
('AgravodeInstrumentoemRecursodeMandadodeSeguranca', 'Agravo de Instrumento em Recurso de Mandado de Segurança'),
('AgravodeInstrumentoemRecursodeRevista', 'Agravo de Instrumento em Recurso de Revista'),
('AgravodeInstrumentoemRecursoEspecial', 'Agravo de Instrumento em Recurso Especial'),
('AgravodeInstrumentoemRecursoExtraordinario', 'Agravo de Instrumento em Recurso Extraordinário'),
('AgravodeInstrumentoemRecursoOrdinario', 'Agravo de Instrumento em Recurso Ordinário'),
('AgravodeInstrumentoemRecursoOrdinarioparaoSTF', 'Agravo de Instrumento em Recurso Ordinário para o STF'),
('AgravodePeticao', 'Agravo de Petição'),
('AgravoRegimental', 'Agravo Regimental'),
('AlienacaodeBensdoAcusado', 'Alienação de Bens do Acusado'),
('AlienacaoJudicialdeBens', 'Alienação Judicial de Bens'),
('AlimentosLeiEspecialN547868', 'Alimentos - Lei Especial Nº 5.478/68'),
('AlimentosProvisionais', 'Alimentos - Provisionais'),
('AlteracaodoRegimedeBens', 'Alteração do Regime de Bens'),
('AlvaraJudicial', 'Alvará Judicial'),
('AlvaraJudicialLei685880', 'Alvará Judicial - Lei 6858/80'),
('Anistia', 'Anistia'),
('Antecipacaodetutela', 'Antecipação de tutela'),
('AnulacaoeSubstituicaodeTitulosaoPortador', 'Anulação e Substituição de Títulos ao Portador'),
('Apelacao', 'Apelação'),
('ApelacaoReexameNecessario', 'Apelação / Reexame Necessário'),
('ApelacaoCriminal', 'Apelação Criminal'),
('ApelacaoCivel', 'Apelação Cível'),
('ApelacaoemMandadodeSeguranca', 'Apelação em Mandado de Segurança'),
('Apensamento', 'Apensamento'),
('ApreensaodeTitulos', 'Apreensão de  Títulos'),
('ApreensaodeEmbarcacoes', 'Apreensão de Embarcações'),
('ApreensaoeDepositodeCoisaVendidacomReservadeDominio', 'Apreensão e Depósito de Coisa Vendida com Reserva de Domínio'),
('ApuracaodeInfracaoAdministrativaasNormasdeProtecaoaCriancaouAdolescente', 'Apuração de Infração Administrativa às Normas de Proteção à Criança ou Adolescente'),
('ApuracaodeIrregularidadesemEntidadesdeAtendimento', 'Apuração de Irregularidades em Entidades de Atendimento'),
('ArguicaodeDescumprimentodePreceitoFundamental', 'Arguição de Descumprimento de Preceito Fundamental'),
('ArguicaodeInconstitucionalidade', 'Arguição de Inconstitucionalidade'),
('ArguicaodeRelevancia', 'Arguição de Relevância'),
('ArguicaodeSuspeicao', 'Arguição de Suspeição'),
('ArrecadacaodasCoisasVagas', 'Arrecadação das Coisas Vagas'),
('Arresto', 'Arresto'),
('ArrestoHipotecaLegal', 'Arresto / Hipoteca Legal'),
('ArribadasForcadas', 'Arribadas Forçadas'),
('ArrolamentoComum', 'Arrolamento Comum'),
('ArrolamentodeBens', 'Arrolamento de Bens'),
('ArrolamentoSumario', 'Arrolamento Sumário'),
('AssedioMoral', 'Assédio Moral'),
('Atentado', 'Atentado'),
('AutodeApreensaoemFlagrante', 'Auto de Apreensão em Flagrante'),
('AutodePrisaoemFlagrante', 'Auto de Prisão em Flagrante'),
('Autorizacaojudicial', 'Autorização judicial'),
('Avaliacaoparaatestardependenciadedrogas', 'Avaliação para atestar dependência de drogas'),
('AvariaaCargodoSegurador', 'Avaria a Cargo do Segurador'),
('Avarias', 'Avarias'),
('AveriguacaodePaternidade', 'Averiguação de Paternidade'),
('Avocatoria', 'Avocatória'),
('ACAODEDISSOLUCAODEUNIAOESTAVEL', 'AÇAO DE DISSOLUÇÃO DE UNIÃO ESTÁVEL'),
('AcaoAnulatoriadeClausulasConvencionais', 'Ação Anulatória de Cláusulas Convencionais'),
('AcaoCautelar', 'Ação Cautelar'),
('AcaoCivilColetiva', 'Ação Civil Coletiva'),
('AcaoCivildeImprobidadeAdministrativa', 'Ação Civil de Improbidade Administrativa'),
('AcaoCivilOriginaria', 'Ação Civil Originária'),
('AcaoCivilPublica', 'Ação Civil Pública'),
('ACAODEREVISAODEFGTS', 'AÇÃO DE  REVISÃO DE  FGTS'),
('AcaodeAlimentos', 'Ação de Alimentos'),
('AcaodeCumprimento', 'Ação de Cumprimento'),
('AcaodeImprobidadeAdministrativa', 'Ação de Improbidade Administrativa'),
('AcaodeImpugnacaodeMandatoEletivo', 'Ação de Impugnação de Mandato Eletivo'),
('AcaoDeclaratoriadeConstitucionalidade', 'Ação Declaratória de Constitucionalidade'),
('AcaoDiretadeInconstitucionalidade', 'Ação Direta de Inconstitucionalidade'),
('AcaoOrdinariaRegressiva', 'Ação Ordinária Regressiva'),
('AcaoOriginaria', 'Ação Originária'),
('AcaoOriginariaEspecial', 'Ação Originária Especial'),
('AcaoPenal', 'Ação Penal'),
('AcaoPenalProcedimentoOrdinario', 'Ação Penal - Procedimento Ordinário'),
('AcaoPenalProcedimentoSumarissimo', 'Ação Penal - Procedimento Sumaríssimo'),
('AcaoPenalProcedimentoSumario', 'Ação Penal - Procedimento Sumário'),
('AcaoPenaldeCompetenciadoJuri', 'Ação Penal de Competência do Júri'),
('AcaoPenalEleitoral', 'Ação Penal Eleitoral'),
('AcaoPenalMilitarProcedimentoOrdinario', 'Ação Penal Militar - Procedimento Ordinário'),
('AcaoRegressiva', 'Ação Regressiva'),
('AcaoRescisoria', 'Ação Rescisória'),
('AcaoTrabalhistaRitoOrdinario', 'Ação Trabalhista - Rito Ordinário'),
('AcaoTrabalhistaRitoSumarissimo', 'Ação Trabalhista - Rito Sumaríssimo'),
('AcaoTrabalhistaRitoSumarioAlcada', 'Ação Trabalhista - Rito Sumário (Alçada)'),
('BuscaeApreensao', 'Busca e Apreensão'),
('BuscaeApreensaoemAlienacaoFiduciaria', 'Busca e Apreensão em Alienação Fiduciária'),
('CompromissoArbitral', 'Compromisso Arbitral'),
('ConflitodeAtribuicao', 'Conflito de Atribuição'),
('Conflitodeatribuicao', 'Conflito de atribuição'),
('ConflitodeAtribuicoes', 'Conflito de Atribuições'),
('ConsignatoriadeAlugueis', 'Consignatória de Aluguéis'),
('ContribuicaoTaxaAssistencial', 'Contribuição / Taxa Assistencial'),
('ConversaodeAgravodeInstrumentoemAgravoRetido', 'Conversão de Agravo de Instrumento em Agravo Retido'),
('CrimesAmbientais', 'Crimes Ambientais'),
('CrimesContraaPropriedadeImaterial', 'Crimes Contra a Propriedade Imaterial'),
('CrimesContraaPropriedadeIndustrial', 'Crimes Contra a Propriedade Industrial'),
('CrimesContraaPropriedadeIntelectual', 'Crimes Contra a Propriedade Intelectual'),
('DeclaracaodeAusencia', 'Declaração de Ausência'),
('Determinacaodearquivamentodeprocedimentosinvestigatorios', 'Determinação de arquivamento de procedimentos investigatórios'),
('EmbargosdoAcusado', 'Embargos do Acusado'),
('EmbargosemAgravodeInstrumento', 'Embargos em Agravo de Instrumento'),
('EmbargosemAcaoPenalMilitar', 'Embargos em Ação Penal Militar'),
('EmbargosInfringentesemApelacaoCivel', 'Embargos Infringentes em Apelação Cível'),
('EmbargosInfringentesemAcaoRescisoria', 'Embargos Infringentes em Ação Rescisória'),
('EmbargosaAdjudicacao', 'Embargos à Adjudicação'),
('EmbargosaArrematacao', 'Embargos à Arrematação'),
('EmbargosaExecucaoemAcaoRecisoria', 'Embargos à Execução em Ação Recisoria'),
('ExecucaoContraaFazendaPublica', 'Execução Contra a Fazenda Pública'),
('ExecucaodeAlimentos', 'Execução de Alimentos'),
('ExecucaodeTermodeAjustedeConduta', 'Execução de Termo de Ajuste de Conduta'),
('ExecucaoemAcaoRescisoria', 'Execução em Ação Rescisória'),
('ExecucaoProvisoriaemAutosSuplementares', 'Execução Provisória em Autos Suplementares'),
('HabilitacaoparaAdocao', 'Habilitação para Adoção'),
('ImpugnacaoaoCumprimentodeDecisao', 'Impugnação ao Cumprimento  de Decisão'),
('ImpugnacaoaoCumprimentodeSentenca', 'Impugnação ao Cumprimento de Sentença'),
('ImpugnacaoaoPedidodeAssistenciaLitisconsorcialouSimples', 'Impugnação ao Pedido de Assistência Litisconsorcial ou Simples'),
('ImpugnacaoaoValordaCausa', 'Impugnação ao Valor da Causa'),
('ImpugnacaodeAssistenciaJudiciaria', 'Impugnação de Assistência Judiciária'),
('ImpugunacaoaoValordaCausa', 'Impugunação ao Valor da Causa'),
('InqueritoAdministrativo', 'Inquérito Administrativo'),
('InqueritoparaApuracaodeFaltaGrave', 'Inquérito para Apuração de Falta Grave'),
('InsanidadeMentaldoAcusado', 'Insanidade Mental do Acusado'),
('InternacaocomAtividadesExternas', 'Internação com Atividades Externas'),
('InternacaosemAtividadesExternas', 'Internação sem Atividades Externas'),
('JustificacaodeDinheiroaRisco', 'Justificação de Dinheiro a Risco'),
('LiberdadeAssistida', 'Liberdade Assistida'),
('LIMITADOAOTETO', 'LIMITADO AO TETO'),
('LiquidacaoporArtigos', 'Liquidação por Artigos'),
('LiquidacaoProvisoriaporArbitramento', 'Liquidação Provisória por Arbitramento'),
('LiquidacaoProvisoriaporArtigos', 'Liquidação Provisória por Artigos'),
('MedidasdeProtecaoaCriancaeAdolescente', 'Medidas de Proteção à Criança e Adolescente'),
('Mortedoagente', 'Morte do agente'),
('NomeacaodeAdvogado', 'Nomeação de Advogado'),
('Ordenacaodeentregadeautos', 'Ordenação de entrega de autos'),
('PedidodeArquivamentoemRepresentacaoCriminal', 'Pedido de Arquivamento em Representação Criminal'),
('PedidodeAvocacao', 'Pedido de Avocação'),
('PedidodeBuscaeApreensaoCriminal', 'Pedido de Busca e Apreensão Criminal'),
('PrestacaodeServicosaComunidade', 'Prestação de Serviços a Comunidade'),
('ProcedimentoEspecialdaLeiAntitoxicos', 'Procedimento Especial da Lei Antitóxicos'),
('ProcedimentoEspecialdosCrimesdeAbusodeAutoridade', 'Procedimento Especial dos Crimes de Abuso de Autoridade'),
('ProcessoAdministrativo', 'Processo Administrativo'),
('ProcessoAdministrativoDisciplinaremfacedeMagistrado', 'Processo Administrativo Disciplinar em face de Magistrado'),
('ProcessoAdministrativoDisciplinaremfacedeServidor', 'Processo Administrativo Disciplinar em face de Servidor'),
('ProcessodeApuracaodeAtoInfracional', 'Processo de Apuração de Ato Infracional'),
('ProtestoFormadoaBordo', 'Protesto Formado a Bordo'),
('RecursoAdministrativo', 'Recurso Administrativo'),
('RecursodeApreensaodeLivro', 'Recurso de Apreensão de Livro'),
('RecursoemProcessoAdministrativoDisciplinaremfacedeServidor', 'Recurso em Processo Administrativo Disciplinar em face de Servidor'),
('Reformadedecisaoanterior', 'Reforma de decisão anterior'),
('Renunciadoqueixosoouperdaoaceito', 'Renúncia do queixoso ou perdão aceito'),
('RestauracaodeAutos', 'Restauração de Autos'),
('RestituicaodeCoisasApreendidas', 'Restituição de Coisas Apreendidas'),
('Retratacaodoagente', 'Retratação do agente'),
('RevisionaldeAluguel', 'Revisional de Aluguel'),
('SentencaArbitral', 'Sentença Arbitral'),
('SuspensaodeLiminarouAntecipacaodeTutela', 'Suspensão de Liminar ou Antecipação de Tutela'),
('SuspensaodeTutelaAntecipada', 'Suspensão de Tutela Antecipada'),
('BaixaDefinitiva', 'Baixa Definitiva'),
('BoletimdeOcorrenciaCircunstanciada', 'Boletim de Ocorrência Circunstanciada'),
('EmbargosdeRetencaoporBenfeitorias', 'Embargos de Retenção por Benfeitorias'),
('Cancelamentodadistribuicao', 'Cancelamento da distribuição'),
('CancelamentodeNaturalizacao', 'Cancelamento de Naturalização'),
('CancelamentodeRegistrodePartidoPolitico', 'Cancelamento de Registro de Partido Político'),
('Cartadeordem', 'Carta de ordem'),
('CartadeOrdem', 'Carta de Ordem'),
('CartaPrecatoria', 'Carta Precatória'),
('CartaRogatoria', 'Carta Rogatória'),
('CartaTestemunhavel', 'Carta Testemunhável'),
('CautelarFiscal', 'Cautelar Fiscal'),
('CautelarInominada', 'Cautelar Inominada'),
('Caucao', 'Caução'),
('CIVILObrigacoesInadimplemento', 'CIVIL > Obrigações > Inadimplemento'),
('CobrancadeCeduladeCreditoIndustrial', 'Cobrança de Cédula de Crédito Industrial'),
('Comunicacao', 'Comunicação'),
('ComutacaodePena', 'Comutação de Pena'),
('Conclusao', 'Conclusão'),
('ConfirmacaodeTestamento', 'Confirmação de Testamento'),
('ConflitodeCompetencia', 'Conflito de Competência'),
('Conflitodecompetencia', 'Conflito de competência'),
('ConflitodeJurisdicao', 'Conflito de Jurisdição'),
('ConhecimentoemParteeNaoProvimento', 'Conhecimento em Parte e Não-Provimento'),
('ConhecimentoemParteeProvimento', 'Conhecimento em Parte e Provimento'),
('ConhecimentoemParteeProvimentoemParte', 'Conhecimento em Parte e Provimento em Parte'),
('ConselhodeJustificacao', 'Conselho de Justificação'),
('ConsignacaoemPagamento', 'Consignação em Pagamento'),
('Consulta', 'Consulta'),
('ContraprotestoJudicial', 'Contraprotesto Judicial'),
('ContribuicaoConfederativa', 'Contribuição Confederativa'),
('ConvencaodasPartes', 'Convenção das Partes'),
('ConversaodePena', 'Conversão de Pena'),
('ConversaodeSeparacaoJudicialemDivorcio', 'Conversão de Separação Judicial em Divórcio'),
('Correicao', 'Correição'),
('CorreicaoExtraordinaria', 'Correição Extraordinária'),
('CorreicaoOrdinaria', 'Correição Ordinária'),
('CorreicaoParcial', 'Correição Parcial'),
('CorreicaoParcialouReclamacaoCorreicional', 'Correição Parcial ou Reclamação Correicional'),
('CriacaodeZonaEleitoralouRemanejamento', 'Criação de Zona Eleitoral ou Remanejamento'),
('CrimesdeCaluniaInjuriaeDifamacaodeCompetenciadoJuizSingular', 'Crimes de Calúnia, Injúria e Difamação de Competência do Juiz Singular'),
('CrimesdeImprensa', 'Crimes de Imprensa'),
('Cumprimentodesentenca', 'Cumprimento de sentença'),
('CumprimentoProvisoriodeDecisao', 'Cumprimento Provisório de Decisão'),
('DeclaratoriadeConstitucionalidade', 'Declaratória de Constitucionalidade'),
('DespejoporFaltadePagamentoCumuladoComCobranca', 'Despejo por Falta de Pagamento Cumulado Com Cobrança'),
('DissidioColetivo', 'Dissídio Coletivo'),
('DissidioColetivodeGreve', 'Dissídio Coletivo de Greve'),
('DivorcioConsensual', 'Divórcio Consensual'),
('EmbargosaExecucaoemMedidaCautelar', 'Embargos à Execução em Medida Cautelar'),
('ExcecaodeCoisaJulgada', 'Exceção de Coisa Julgada'),
('ExecucaodeCertidaodeCreditoJudicial', 'Execução de Certidão de Crédito Judicial'),
('ExecucaodeTermodeConciliacaodeCCP', 'Execução de Termo de Conciliação de CCP'),
('ExecucaoemMedidaCautelar', 'Execução em Medida Cautelar'),
('ExecucaoemSentencaEstrangeiraContestada', 'Execução em Sentença Estrangeira Contestada'),
('ExibicaodeDocumentoouCoisa', 'Exibição de Documento ou Coisa'),
('Extincaodaexecucaooudocumprimentodasentenca', 'Extinção da execução ou do cumprimento da sentença'),
('HabeasCorpus', 'Habeas Corpus'),
('HabilitacaodeCredito', 'Habilitação de Crédito'),
('HabilitacaoparaCasamento', 'Habilitação para Casamento'),
('ImpugnacaodeCredito', 'Impugnação de Crédito'),
('IncidentedeDeslocamentodeCompetencia', 'Incidente de Deslocamento de Competência'),
('InsolvenciaRequeridapeloCredor', 'Insolvência Requerida pelo Credor'),
('Investigacaocontramagistrado', 'Investigação contra magistrado'),
('LiberdadeProvisoriacomousemfianca', 'Liberdade Provisória com ou sem fiança'),
('LivramentoCondicional', 'Livramento Condicional'),
('MandadodeSegurancaColetivo', 'Mandado de Segurança Coletivo'),
('MedidaCautelar', 'Medida Cautelar'),
('MedidasInvestigatoriasSobreOrganizacoesCriminosas', 'Medidas Investigatórias Sobre Organizações Criminosas'),
('PedidodeRevisaodoValordaCausa', 'Pedido de Revisão do Valor da Causa'),
('Perempcaolitispendenciaoucoisajulgada', 'Perempção, litispendência ou coisa julgada'),
('PrestacaodeContas', 'Prestação de Contas'),
('PrestacaodeContasExigidas', 'Prestação de Contas - Exigidas'),
('PrestacaodeContasOferecidas', 'Prestação de Contas - Oferecidas'),
('ProcedimentodoJuizadoEspecialCriminalSumariissimo', 'Procedimento do Juizado Especial Criminal - Sumariíssimo'),
('ProcedimentodoJuizadoEspecialCivel', 'Procedimento do Juizado Especial Cível'),
('ProcessoCrime', 'Processo Crime'),
('QueixaCrime', 'Queixa Crime'),
('RecursocontraExpedicaodeDiploma', 'Recurso contra Expedição de Diploma'),
('RecursoCriminal', 'Recurso Criminal'),
('RecursodeMedidaCautelar', 'Recurso de Medida Cautelar'),
('RecursodeSentencaCriminal', 'Recurso de Sentença Criminal'),
('RecursoemHabeasCorpus', 'Recurso em Habeas Corpus'),
('RecursoOrdinarioemHabeasCorpus', 'Recurso Ordinário em Habeas Corpus'),
('RegistrodeCasamentoNuncupativo', 'Registro de Casamento Nuncupativo'),
('RegistrodeComiteFinanceiro', 'Registro de Comitê Financeiro'),
('RegularizacaodeRegistroCivil', 'Regularização de Registro Civil'),
('Remocaomodificacaoedispensadetutoroucurador', 'Remoção, modificação  e dispensa de tutor ou curador'),
('RepresentacaoCriminal', 'Representação Criminal'),
('RetificacaoouSuprimentoouRestauracaodeRegistroCivil', 'Retificação ou Suprimento ou Restauração de Registro Civil'),
('RevisaoCriminal', 'Revisão Criminal'),
('RevisaoJudicialdeDecisaodoConselhoTutelar', 'Revisão Judicial de Decisão do Conselho Tutelar'),
('SentencaEstrangeiraContestada', 'Sentença Estrangeira Contestada'),
('SeparacaoConsensual', 'Separação Consensual'),
('SuprimentodeCapacidadeoudeConsentimentoparaCasar', 'Suprimento de Capacidade ou de Consentimento para Casar'),
('SuprimentodeIdadeeouConsentimento', 'Suprimento de Idade e/ou Consentimento'),
('SuspensaoCondicionaldaPena', 'Suspensão Condicional da Pena'),
('SuspensaoCondicionaldoProcesso', 'Suspensão Condicional do Processo'),
('TermoCircunstanciado', 'Termo Circunstanciado'),
('TutelaccDestituicaodoPoderFamiliar', 'Tutela c/c Destituição do Poder Familiar'),
('TutelaeCuratelaNomeacao', 'Tutela e Curatela -  Nomeação'),
('TutelaeCuratelaRemocaoeDispensa', 'Tutela e Curatela - Remoção e Dispensa'),
('depreexecutividade', 'de pré-executividade'),
('Decretacaodefalencia', 'Decretação de falência'),
('Deliberacaodapartilha', 'Deliberação da partilha'),
('DemarcacaoDivisao', 'Demarcação / Divisão'),
('Denuncia', 'Denúncia'),
('Depositarioinfiel', 'Depositário infiel'),
('Deposito', 'Depósito'),
('DepositodaLei886694', 'Depósito da Lei 8. 866/94'),
('DesaforamentodeJulgamento', 'Desaforamento de Julgamento'),
('Desapensamento', 'Desapensamento'),
('Desaposentacao', 'Desaposentação'),
('Desapropriacao', 'Desapropriação'),
('DesapropriacaoImovelRuralporInteresseSocial', 'Desapropriação Imóvel Rural por Interesse Social'),
('DesercaodeOficial', 'Deserção de Oficial'),
('DesercaodePraca', 'Deserção de Praça'),
('Despejo', 'Despejo'),
('DespejoporFaltadePagamento', 'Despejo por Falta de Pagamento'),
('Destinacao', 'Destinação'),
('DestinacaoParcial', 'Destinação Parcial'),
('Direitodevisita', 'Direito de visita'),
('DiretadeInconstitucionalidade', 'Direta de Inconstitucionalidade'),
('Discriminatoria', 'Discriminatória'),
('DissolucaoeLiquidacaodeSociedade', 'Dissolução e Liquidação de Sociedade'),
('DivorcioLitigioso', 'Divórcio Litigioso'),
('Duvida', 'Dúvida'),
('EmbargosdeDeclaracao', 'Embargos de Declaração'),
('EmbargosdeDivergencia', 'Embargos de Divergência'),
('EmbargosdeDivergenciaemRecursodeMandadodeSeguranca', 'Embargos de Divergência em Recurso de Mandado de Segurança'),
('EmbargosdeDivergenciaemRecursoEspecial', 'Embargos de Divergência em Recurso Especial'),
('EmbargosdeTerceiro', 'Embargos de Terceiro'),
('EmbargosInfringentesedeNulidade', 'Embargos Infringentes e de Nulidade'),
('EmbargosaExecucaoemMandadodeSeguranca', 'Embargos à Execução em Mandado de Segurança'),
('EspecializacaodeHipotecaLegal', 'Especialização de Hipoteca Legal'),
('ExcessoouDesvio', 'Excesso ou Desvio'),
('ExcecaodaVerdade', 'Exceção da Verdade'),
('ExcecaodeIlegitimidadedeParte', 'Exceção de Ilegitimidade de Parte'),
('ExcecaodeImpedimento', 'Exceção de Impedimento'),
('ExcecaodeIncompetencia', 'Exceção de Incompetência'),
('ExcecaodeIncompetenciadeJuizo', 'Exceção de Incompetência de Juízo'),
('ExcecaodeLitispendencia', 'Exceção de Litispendência'),
('ExcecaodeSuspeicao', 'Exceção de Suspeição'),
('ExecucaodaPena', 'Execução da Pena'),
('ExecucaodeMedidadeSeguranca', 'Execução de Medida de Segurança'),
('ExecucaodeMulta', 'Execução de Multa'),
('ExecucaodeTituloExtrajudicial', 'Execução de Título Extrajudicial'),
('ExecucaoemMandadodeSeguranca', 'Execução em Mandado de Segurança'),
('ExecucaoHipotecariadoSistemaFinanceirodaHabitacao', 'Execução Hipotecária do Sistema Financeiro da Habitação'),
('Expedicaodedocumento', 'Expedição de documento'),
('ExpropriacaodaLei825791', 'Expropriação da Lei 8.257/91'),
('Extincaodasobrigacoesdofalido', 'Extinção das obrigações do falido'),
('HabeasData', 'Habeas Data'),
('HomologacaodeTransacaoExtrajudicial', 'Homologação de Transação Extrajudicial'),
('HomologacaodoPenhorLegal', 'Homologação do Penhor Legal'),
('IncidentedeFalsidade', 'Incidente de Falsidade'),
('IncidentedeInsanidadeMental', 'Incidente de Insanidade Mental'),
('IncidentedeSanidadeMental', 'Incidente de Sanidade Mental'),
('IncidentedeUniformizacaodeJurisprudencia', 'Incidente de Uniformização de Jurisprudência'),
('InsolvenciaRequeridapeloDevedoroupeloEspolio', 'Insolvência Requerida pelo Devedor ou pelo Espólio'),
('InstrucaodeRescisoria', 'Instrução de Rescisória'),
('IntrucaoProvisoriadeDesercao', 'Intrução Provisória de Deserção'),
('LiquidacaodeSentenca', 'Liquidação de Sentença'),
('MandadodeInjuncao', 'Mandado de Injunção'),
('MandadodeSeguranca', 'Mandado de Segurança'),
('MedidasProtetivasEstatutodoIdoso', 'Medidas Protetivas - Estatuto do Idoso'),
('MedidasProtetivasdeurgenciaLeiMariadaPenha', 'Medidas Protetivas de urgência (Lei Maria da Penha)'),
('NegacaodeSeguimento', 'Negação de Seguimento'),
('NotificacaoparaExplicacoesLeideImprensa', 'Notificação para Explicações (Lei de Imprensa)'),
('NunciacaodeObraNova', 'Nunciação de Obra Nova'),
('NaoDecretacaodeFalencia', 'Não-Decretação de Falência'),
('ObrigacaodeRepararoDano', 'Obrigação de Reparar o Dano'),
('OrganizacaoeFiscalizacaodeFundacao', 'Organização e Fiscalização de Fundação'),
('Outrosprocedimentosdejurisdicaovoluntaria', 'Outros procedimentos de jurisdição voluntária'),
('Pagamentointegraldodebito', 'Pagamento integral do débito'),
('Paralisacaopornegligenciadaspartes', 'Paralisação por negligência das partes'),
('PedidodeDesaforamento', 'Pedido de Desaforamento'),
('PedidodePrisaoPreventiva', 'Pedido de Prisão Preventiva'),
('PedidodePrisaoTemporaria', 'Pedido de Prisão Temporária'),
('PedidodePrisaoLiberdadeVigiadaparaFinsdeExpulsao', 'Pedido de Prisão/ Liberdade Vigiada para Fins de Expulsão'),
('PedidodeProvidencias', 'Pedido de Providências'),
('PedidodeQuebradeSigilodeDadoseouTelefonico', 'Pedido de Quebra de Sigilo de Dados e/ou Telefônico'),
('PedidodeRespostaouRetificacaodaLeideImprensa', 'Pedido de Resposta ou Retificação da Lei de Imprensa'),
('PedidodeUniformizacaodeInterpretacaodeLeiFederal', 'Pedido de Uniformização de Interpretação de Lei Federal'),
('PerdaouSuspensaoouRestabelecimentodoPoderFamiliar', 'Perda ou Suspensão ou Restabelecimento do Poder Familiar'),
('Permissaodesaida', 'Permissão de saída'),
('PosseemNomedoNascituro', 'Posse em Nome do Nascituro'),
('Prescricaodecadenciaouperempcao', 'Prescrição, decadência ou perempção'),
('ProcedimentoInvestigatoriodoMPPecasdeInformacao', 'Procedimento Investigatório do MP (Peças de Informação)'),
('Progressaoderegime', 'Progressão de regime'),
('ProtocolodePeticao', 'Protocolo de Petição'),
('QuestaodeOrdem', 'Questão de Ordem'),
('RecebimentodeEmbargosaExecucao', 'Recebimento de Embargos à Execução'),
('ReclamacaoDisciplinar', 'Reclamação Disciplinar'),
('RecursodeLiquidacaodeSentenca', 'Recurso de Liquidação de Sentença'),
('RecursodeMulta', 'Recurso de Multa'),
('RecursodeRevista', 'Recurso de Revista'),
('RecursoemHabeasData', 'Recurso em Habeas Data'),
('RecursoemMandadodeInjuncao', 'Recurso em Mandado de Injunção'),
('RecursoemMandadodeSeguranca', 'Recurso em Mandado de Segurança'),
('RecursoOrdinarioemMandadodeSeguranca', 'Recurso Ordinário em Mandado de Segurança'),
('RedisponibilizacaonoDiariodaJusticaEletronico', 'Redisponibilização no Diário da Justiça Eletrônico'),
('RegistrodeEntidadeSindical', 'Registro de Entidade Sindical'),
('RegistrodeOrgaodePartidoPoliticoemFormacao', 'Registro de Órgão de Partido Político em Formação'),
('RegulamentacaodeVisitas', 'Regulamentação de Visitas'),
('ReintegracaoManutencaodePosse', 'Reintegração / Manutenção de Posse'),
('RelatoriodeInvestigacoes', 'Relatório de Investigações'),
('RelaxamentodePrisao', 'Relaxamento de Prisão'),
('RemicaodoImovelHipotecado', 'Remição do Imóvel Hipotecado'),
('RemocaodeInventariante', 'Remoção de Inventariante'),
('RenovatoriadeLocacao', 'Renovatória de Locação'),
('RepresentacaopDeclaracaodeIndignidadeIncompatibilidade', 'Representação p/ Declaração de Indignidade/Incompatibilidade'),
('RepresentacaopPerdadaGraduacao', 'Representação p/ Perda da Graduação'),
('RepresentacaoporExcessodePrazo', 'Representação por Excesso de Prazo'),
('Requisicaodeinformacoes', 'Requisição de informações'),
('RequisicaodePequenoValor', 'Requisição de Pequeno Valor'),
('RetificacaodeNomeEstrangeiro', 'Retificação de Nome Estrangeiro'),
('RetificacaodeRegistrodeImovel', 'Retificação de Registro de Imóvel'),
('Retroatividadedelei', 'Retroatividade de lei'),
('RevisaodeEleitorado', 'Revisão de Eleitorado'),
('SessaodoTribunaldoJuri', 'Sessão do Tribunal do Júri'),
('Supervenienciadedoencamental', 'Superveniência de doença mental'),
('SuspensaodeDireitos', 'Suspensão de Direitos'),
('SuspensaodeExecucaodeSentenca', 'Suspensão de Execução de Sentença'),
('SuspensaodeLiminar', 'Suspensão de Liminar'),
('SuspensaodeLiminaredeSentenca', 'Suspensão de Liminar e de Sentença'),
('SuspensaodeSeguranca', 'Suspensão de Segurança'),
('SuspensaodoProcesso', 'Suspensão do Processo'),
('TransferenciadaExecucaodaPena', 'Transferência da Execução da Pena'),
('Unificacaodepenas', 'Unificação de penas'),
('EfeitoSuspensivo', 'Efeito Suspensivo'),
('Eleicao', 'Eleição'),
('Emancipacao', 'Emancipação'),
('Embargos', 'Embargos'),
('EmbargosInfringentes', 'Embargos Infringentes'),
('EmbargosRemetidos', 'Embargos Remetidos'),
('EmbargosaExecucao', 'Embargos à Execução'),
('EmbargosaExecucaoFiscal', 'Embargos à Execução Fiscal'),
('ExecucaoemSentencaEstrangeira', 'Execução em Sentença Estrangeira'),
('ExecucaoFiscal', 'Execução Fiscal'),
('ExecucaoFrustrada', 'Execução Frustrada'),
('ExecucaoProvisoria', 'Execução Provisória'),
('Exibicao', 'Exibição'),
('Extradicao', 'Extradição'),
('Inclusaoempauta', 'Inclusão em pauta'),
('InqueritoPolicialEspecial', 'Inquérito Policial Especial'),
('IntervencaoemMunicipio', 'Intervenção em Município'),
('NotificacaoparaExplicacoes', 'Notificação para Explicações'),
('PrisaoPreventivaparaExtradicao', 'Prisão Preventiva para Extradição'),
('ProcedenciaemParte', 'Procedência em Parte'),
('ProvimentoemParte', 'Provimento em Parte'),
('RecuperacaoExtrajudicial', 'Recuperação Extrajudicial'),
('RecursoemSentidoEstrito', 'Recurso em Sentido Estrito'),
('RecursoemsentidoestritoRecursoexofficio', 'Recurso em sentido estrito/Recurso ex officio'),
('RecursoEspecial', 'Recurso Especial'),
('RecursoEspecialEleitoral', 'Recurso Especial Eleitoral'),
('RecursoExtraordinario', 'Recurso Extraordinário'),
('SentencaEstrangeira', 'Sentença Estrangeira'),
('TrabalhoExterno', 'Trabalho Externo'),
('Transferenciaentreestabelecimentospenais', 'Transferência entre estabelecimentos penais'),
('TransferenciaparaoutroEstabelecimentoPenal', 'Transferência para outro Estabelecimento Penal'),
('Forcamaior', 'Força maior'),
('IntervencaoFederal', 'Intervenção Federal'),
('RelatorioFalimentar', 'Relatório Falimentar'),
('Guarda', 'Guarda'),
('Habilitacao', 'Habilitação'),
('HerancaJacente', 'Herança Jacente'),
('Indulto', 'Indulto'),
('Inquerito', 'Inquerito'),
('InqueritoPolicial', 'Inquérito Policial'),
('Inspecao', 'Inspeção'),
('Instrucao', 'Instrução'),
('Insubmissao', 'Insubmissão'),
('InterditoProibitorio', 'Interdito Proibitório'),
('Interdicao', 'Interdição'),
('Interpelacao', 'Interpelação'),
('InterpelacaoJudicial', 'Interpelação Judicial'),
('Inventario', 'Inventário'),
('RecursoInominado', 'Recurso Inominado'),
('SalarioInNatura', 'Salário In Natura'),
('Justificacao', 'Justificação'),
('ProcessoJudicial', 'Processo Judicial'),
('ProtestoporNovoJuri', 'Protesto por Novo Júri'),
('RecuperacaoJudicial', 'Recuperação Judicial'),
('LeilaoouPraca', 'Leilão ou Praça'),
('ListaTriplice', 'Lista Tríplice'),
('SeparacaoLitigiosa', 'Separação Litigiosa'),
('Mandado', 'Mandado'),
('Monitoria', 'Monitória'),
('Outrasmedidasprovisionais', 'Outras medidas provisionais'),
('Naturalizacao', 'Naturalização'),
('Notificacao', 'Notificação'),
('NoticiaCrime', 'Notícia-Crime'),
('NaoProvimento', 'Não-Provimento'),
('ReexameNecessario', 'Reexame Necessário'),
('ReexameNecessarioRecursoOrdinario', 'Reexame Necessário / Recurso Ordinário'),
('Obrigacoes', 'Obrigações'),
('Oficio', 'Ofício'),
('Oposicao', 'Oposição'),
('Procedimentoordinario', 'Procedimento ordinário'),
('RecursoOrdinario', 'Recurso Ordinário'),
('Peticao', 'Petição'),
('Precatorio', 'Precatório'),
('Prisao', 'Prisão'),
('ProcedimentoSumario', 'Procedimento Sumário'),
('ProcessoSumarioDetencao', 'Processo Sumário (Detenção)'),
('Protesto', 'Protesto'),
('Providencia', 'Providência'),
('Publicacao', 'Publicação'),
('Recursoprejudicado', 'Recurso prejudicado'),
('Reabilitacao', 'Reabilitação'),
('Reclamacao', 'Reclamação'),
('RegistroTorrens', 'Registro Torrens'),
('Remessa', 'Remessa'),
('Remicao', 'Remição'),
('Representacao', 'Representação'),
('Semiliberdade', 'Semiliberdade'),
('Sequestro', 'Seqüestro'),
('Sindicancia', 'Sindicância'),
('Sonegados', 'Sonegados'),
('Tutela', 'Tutela'),
('Usucapiao', 'Usucapião')
;
");

            migrationBuilder.Sql(@"INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumAzariasMenescaldeVasconcelosManaus', 'Fórum Azarias Menescal de Vasconcelos - Manaus'),
                                   ('ForumCriminaldeRioBrancoAC', 'Fórum Criminal de Rio Branco - AC'),
                                   ('ForumCiveldeRioBrancoAC', 'Fórum Cível de Rio Branco - AC'),
                                   ('ForumdeAbadiania', 'Fórum de Abadiânia'),
                                   ('ForumdeAbaetetuba', 'Fórum de Abaetetuba'),
                                   ('ForumdeAbaete', 'Fórum de Abaeté'),
                                   ('ForumdeAbare', 'Fórum de Abaré'),
                                   ('ForumdeAbelardoLuz', 'Fórum de Abelardo Luz'),
                                   ('ForumdeAbreCampo', 'Fórum de Abre Campo'),
                                   ('ForumdeAbreueLima', 'Fórum de Abreu e Lima'),
                                   ('ForumdeAcajutiba', 'Fórum de Acajutiba'),
                                   ('ForumdeAcaraci', 'Fórum de Acaraci'),
                                   ('ForumdeAcarau', 'Fórum de Acarau'),
                                   ('ForumdeAcari', 'Fórum de Acari'),
                                   ('ForumdeAcara', 'Fórum de Acará'),
                                   ('ForumdeAcopiara', 'Fórum de Acopiara'),
                                   ('ForumdeAcrelandia', 'Fórum de Acrelândia'),
                                   ('ForumdeAcreuna', 'Fórum de Acreúna'),
                                   ('ForumdeAdamantina', 'Fórum de Adamantina'),
                                   ('ForumdeAfogadosdaIngazeira', 'Fórum de Afogados da Ingazeira'),
                                   ('ForumdeAfonsoBezerra', 'Fórum de Afonso Bezerra'),
                                   ('ForumdeAfonsoClaudio', 'Fórum de Afonso Cláudio'),
                                   ('ForumdeAfranio', 'Fórum de Afrânio'),
                                   ('ForumdeAfua', 'Fórum de Afuá'),
                                   ('ForumdeAgrestina', 'Fórum de Agrestina'),
                                   ('ForumdeAguai', 'Fórum de Aguaí'),
                                   ('ForumdeAgudo', 'Fórum de Agudo'),
                                   ('ForumdeAimores', 'Fórum de Aimorés'),
                                   ('ForumdeAiuaba', 'Fórum de Aiuaba'),
                                   ('ForumdeAiuruoca', 'Fórum de Aiuruoca'),
                                   ('ForumdeAlagoaGrande', 'Fórum de Alagoa Grande'),
                                   ('ForumdeAlagoaNova', 'Fórum de Alagoa Nova'),
                                   ('ForumdeAlagoinha', 'Fórum de Alagoinha'),
                                   ('ForumdeAlagoinhaPB', 'Fórum de Alagoinha - PB'),
                                   ('ForumdeAlagoinhas', 'Fórum de Alagoinhas'),
                                   ('ForumdeAlcobaca', 'Fórum de Alcobaça'),
                                   ('ForumdeAlcantara', 'Fórum de Alcântara'),
                                   ('ForumdeAldeiasAltas', 'Fórum de Aldeias Altas'),
                                   ('ForumdeAlegre', 'Fórum de Alegre'),
                                   ('ForumdeAlegrete', 'Fórum de Alegrete'),
                                   ('ForumdeAlenquer', 'Fórum de Alenquer'),
                                   ('ForumdeAlexandria', 'Fórum de Alexandria'),
                                   ('ForumdeAlexania', 'Fórum de Alexânia'),
                                   ('ForumdeAlfenas', 'Fórum de Alfenas'),
                                   ('ForumdeAlfredoChaves', 'Fórum de Alfredo Chaves'),
                                   ('ForumdeAlhandra', 'Fórum de Alhandra'),
                                   ('ForumdeAlianca', 'Fórum de Aliança'),
                                   ('ForumdeAlmas', 'Fórum de Almas'),
                                   ('ForumdeAlmeirim', 'Fórum de Almeirim'),
                                   ('ForumdeAlmenara', 'Fórum de Almenara'),
                                   ('ForumdeAlminoAfonso', 'Fórum de Almino Afonso'),
                                   ('ForumdeAlmiranteTamandare', 'Fórum de Almirante Tamandaré'),
                                   ('ForumdeAlpinopolis', 'Fórum de Alpinópolis'),
                                   ('ForumdeAltaFlorestaMT', 'Fórum de Alta Floresta - MT'),
                                   ('ForumdeAltaFlorestaRO', 'Fórum de Alta Floresta - RO'),
                                   ('ForumdeAltamira', 'Fórum de Altamira'),
                                   ('ForumdeAltinho', 'Fórum de Altinho'),
                                   ('ForumdeAltinopolis', 'Fórum de Altinópolis'),
                                   ('ForumdeAltoAlegre', 'Fórum de Alto Alegre'),
                                   ('ForumdeAltoAlegredoMaranhao', 'Fórum de Alto Alegre do Maranhão'),
                                   ('ForumdeAltoAraguaia', 'Fórum de Alto Araguaia'),
                                   ('ForumdeAltoGarcas', 'Fórum de Alto Garças'),
                                   ('ForumdeAltoLonga', 'Fórum de Alto Longá'),
                                   ('ForumdeAltoParana', 'Fórum de Alto Paraná'),
                                   ('ForumdeAltoParaisoDeGoias', 'Fórum de Alto Paraíso De Goiás'),
                                   ('ForumdeAltoParnaiba', 'Fórum de Alto Parnaíba'),
                                   ('ForumdeAltoPiquiri', 'Fórum de Alto Piquiri'),
                                   ('ForumdeAltoRioDoce', 'Fórum de Alto Rio Doce'),
                                   ('ForumdeAltoRioNovo', 'Fórum de Alto Rio Novo'),
                                   ('ForumdeAltoSanto', 'Fórum de Alto Santo'),
                                   ('ForumdeAltoTaquari', 'Fórum de Alto Taquari'),
                                   ('ForumdeAltos', 'Fórum de Altos'),
                                   ('ForumdeAltonia', 'Fórum de Altônia'),
                                   ('ForumdeAlvaraes', 'Fórum de Alvarães'),
                                   ('ForumdeAlvinopolis', 'Fórum de Alvinópolis'),
                                   ('ForumdeAlvorada', 'Fórum de Alvorada'),
                                   ('ForumdeAlvoradaTO', 'Fórum de Alvorada - TO'),
                                   ('ForumdeAlvoradaDoNorte', 'Fórum de Alvorada Do Norte'),
                                   ('ForumdeAlvoradaDoOeste', 'Fórum de Alvorada Do Oeste'),
                                   ('ForumdeAlemParaiba', 'Fórum de Além Paraíba'),
                                   ('ForumdeAmambai', 'Fórum de Amambai'),
                                   ('ForumdeAmapa', 'Fórum de Amapá'),
                                   ('ForumdeAmaraji', 'Fórum de Amaraji'),
                                   ('ForumdeAmarante', 'Fórum de Amarante'),
                                   ('ForumdeAmarantedoMaranhao', 'Fórum de Amarante do Maranhão'),
                                   ('ForumdeAmargosa', 'Fórum de Amargosa'),
                                   ('ForumdeAmericana', 'Fórum de Americana'),
                                   ('ForumdeAmontada', 'Fórum de Amontada'),
                                   ('ForumdeAmparo', 'Fórum de Amparo'),
                                   ('ForumdeAmparodoSaoFrancisco', 'Fórum de Amparo do São Francisco'),
                                   ('ForumdeAmeliaRodrigues', 'Fórum de Amélia Rodrigues'),
                                   ('ForumdeAmericaDourada', 'Fórum de América Dourada'),
                                   ('ForumdeAmericoBrasiliense', 'Fórum de Américo Brasiliense'),
                                   ('ForumdeAnadia', 'Fórum de Anadia'),
                                   ('ForumdeAnage', 'Fórum de Anagé'),
                                   ('ForumdeAnajatuba', 'Fórum de Anajatuba'),
                                   ('ForumdeAnajas', 'Fórum de Anajás'),
                                   ('ForumdeAnama', 'Fórum de Anamã'),
                                   ('ForumdeAnanindeua', 'Fórum de Ananindeua'),
                                   ('ForumdeAnanas', 'Fórum de Ananás'),
                                   ('ForumdeAnastacio', 'Fórum de Anastácio'),
                                   ('ForumdeAnaurilandia', 'Fórum de Anaurilândia'),
                                   ('ForumdeAnchieta', 'Fórum de Anchieta'),
                                   ('ForumdeAnchietaSC', 'Fórum de Anchieta - SC'),
                                   ('ForumdeAndarai', 'Fórum de Andaraí'),
                                   ('ForumdeAndira', 'Fórum de Andirá'),
                                   ('ForumdeAndradas', 'Fórum de Andradas'),
                                   ('ForumdeAndradina', 'Fórum de Andradina'),
                                   ('ForumdeAndrelandia', 'Fórum de Andrelândia'),
                                   ('ForumdeAngatuba', 'Fórum de Angatuba'),
                                   ('ForumdeAngelim', 'Fórum de Angelim'),
                                   ('ForumdeAngical', 'Fórum de Angical'),
                                   ('ForumdeAngicaldoPiaui', 'Fórum de Angical do Piauí'),
                                   ('ForumdeAngicos', 'Fórum de Angicos'),
                                   ('ForumdeAngradosReis', 'Fórum de Angra dos Reis'),
                                   ('ForumdeAngelica', 'Fórum de Angélica'),
                                   ('ForumdeAnicuns', 'Fórum de Anicuns'),
                                   ('ForumdeAnitaGaribaldi', 'Fórum de Anita Garibaldi'),
                                   ('ForumdeAnori', 'Fórum de Anori'),
                                   ('ForumdeAntas', 'Fórum de Antas'),
                                   ('ForumdeAntonina', 'Fórum de Antonina'),
                                   ('ForumdeAntonioAlmeida', 'Fórum de Antônio Almeida'),
                                   ('ForumdeAntonioPrado', 'Fórum de Antônio Prado'),
                                   ('ForumdeAnapolis', 'Fórum de Anápolis'),
                                   ('ForumdeAnisiodeAbreu', 'Fórum de Anísio de Abreu'),
                                   ('ForumdeAparecida', 'Fórum de Aparecida'),
                                   ('ForumdeAparecidaDeGoiania', 'Fórum de Aparecida De Goiânia'),
                                   ('ForumdeAparecidaDoTaboado', 'Fórum de Aparecida Do Taboado'),
                                   ('ForumdeApiaca', 'Fórum de Apiacá'),
                                   ('ForumdeApiacas', 'Fórum de Apiacás'),
                                   ('ForumdeApiai', 'Fórum de Apiaí'),
                                   ('ForumdeApodi', 'Fórum de Apodi'),
                                   ('ForumdeApora', 'Fórum de Aporá'),
                                   ('ForumdeApucarana', 'Fórum de Apucarana'),
                                   ('ForumdeApui', 'Fórum de Apuí'),
                                   ('ForumdeAquidaba', 'Fórum de Aquidabã'),
                                   ('ForumdeAquidauana', 'Fórum de Aquidauana'),
                                   ('ForumdeAquiraz', 'Fórum de Aquiraz'),
                                   ('ForumdeAracati', 'Fórum de Aracati'),
                                   ('ForumdeAraci', 'Fórum de Araci'),
                                   ('ForumdeAracoiaba', 'Fórum de Aracoiaba'),
                                   ('ForumdeAracruz', 'Fórum de Aracruz'),
                                   ('ForumdeAragarcas', 'Fórum de Aragarças'),
                                   ('ForumdeAraguacema', 'Fórum de Araguacema'),
                                   ('ForumdeAraguari', 'Fórum de Araguari'),
                                   ('ForumdeAraguatins', 'Fórum de Araguatins'),
                                   ('ForumdeAraguacu', 'Fórum de Araguaçu'),
                                   ('ForumdeAraguaina', 'Fórum de Araguaína'),
                                   ('ForumdeAraioses', 'Fórum de Araioses'),
                                   ('ForumdeArame', 'Fórum de Arame'),
                                   ('ForumdeArapiraca', 'Fórum de Arapiraca'),
                                   ('ForumdeArapoema', 'Fórum de Arapoema'),
                                   ('ForumdeArapongas', 'Fórum de Arapongas'),
                                   ('ForumdeArapoti', 'Fórum de Arapoti'),
                                   ('ForumdeAraputanga', 'Fórum de Araputanga'),
                                   ('ForumdeAraquari', 'Fórum de Araquari'),
                                   ('ForumdeArara', 'Fórum de Arara'),
                                   ('ForumdeArarangua', 'Fórum de Ararangua'),
                                   ('ForumdeAraraquara', 'Fórum de Araraquara'),
                                   ('ForumdeAraras', 'Fórum de Araras'),
                                   ('ForumdeArari', 'Fórum de Arari'),
                                   ('ForumdeAraripe', 'Fórum de Araripe'),
                                   ('ForumdeAraripina', 'Fórum de Araripina'),
                                   ('ForumdeAraruama', 'Fórum de Araruama'),
                                   ('ForumdeAraruna', 'Fórum de Araruna'),
                                   ('ForumdeAraucaria', 'Fórum de Araucária'),
                                   ('ForumdeAraua', 'Fórum de Arauá'),
                                   ('ForumdeAraxa', 'Fórum de Araxá'),
                                   ('ForumdeAracagi', 'Fórum de Araçagi'),
                                   ('ForumdeAracatuba', 'Fórum de Araçatuba'),
                                   ('ForumdeAracu', 'Fórum de Araçu'),
                                   ('ForumdeAracuai', 'Fórum de Araçuaí'),
                                   ('ForumdeArcos', 'Fórum de Arcos'),
                                   ('ForumdeArcoverde', 'Fórum de Arcoverde'),
                                   ('ForumdeAreado', 'Fórum de Areado'),
                                   ('ForumdeAreia', 'Fórum de Areia'),
                                   ('ForumdeAreiaBranca', 'Fórum de Areia Branca'),
                                   ('ForumdeAreiaBrancaRN', 'Fórum de Areia Branca - RN'),
                                   ('ForumdeArenapolis', 'Fórum de Arenápolis'),
                                   ('ForumdeArinos', 'Fórum de Arinos'),
                                   ('ForumdeAripuana', 'Fórum de Aripuanã'),
                                   ('ForumdeAriquemes', 'Fórum de Ariquemes'),
                                   ('ForumdeArmazem', 'Fórum de Armazem'),
                                   ('ForumdeAroazes', 'Fórum de Aroazes'),
                                   ('ForumdeAroeiras', 'Fórum de Aroeiras'),
                                   ('ForumdeArraialdoCabo', 'Fórum de Arraial do Cabo'),
                                   ('ForumdeArraialdoPiaui', 'Fórum de Arraial do Piauí'),
                                   ('ForumdeArraias', 'Fórum de Arraias'),
                                   ('ForumdeArroiodoMeio', 'Fórum de Arroio do Meio'),
                                   ('ForumdeArroiodoTigre', 'Fórum de Arroio do Tigre'),
                                   ('ForumdeArroioGrande', 'Fórum de Arroio Grande'),
                                   ('ForumdeAruja', 'Fórum de Arujá'),
                                   ('ForumdeArvorezinha', 'Fórum de Arvorezinha'),
                                   ('ForumdeAres', 'Fórum de Arês'),
                                   ('ForumdeAscurra', 'Fórum de Ascurra'),
                                   ('ForumdeAssare', 'Fórum de Assare'),
                                   ('ForumdeAssai', 'Fórum de Assaí'),
                                   ('ForumdeAssis', 'Fórum de Assis'),
                                   ('ForumdeAssisBrasil', 'Fórum de Assis Brasil'),
                                   ('ForumdeAssisChateaubriand', 'Fórum de Assis Chateaubriand'),
                                   ('ForumdeAstorga', 'Fórum de Astorga'),
                                   ('ForumdeAtalaia', 'Fórum de Atalaia'),
                                   ('ForumdeAtalaiaDoNorte', 'Fórum de Atalaia Do Norte'),
                                   ('ForumdeAtibaia', 'Fórum de Atibaia'),
                                   ('ForumdeAtiloVivacqua', 'Fórum de Atílo Vivacqua'),
                                   ('ForumdeAugustinopolis', 'Fórum de Augustinópolis'),
                                   ('ForumdeAugustoCorrea', 'Fórum de Augusto Corrêa'),
                                   ('ForumdeAugustoPestana', 'Fórum de Augusto Pestana'),
                                   ('ForumdeAurelinoLeal', 'Fórum de Aurelino Leal'),
                                   ('ForumdeAuriflama', 'Fórum de Auriflama'),
                                   ('ForumdeAurilandia', 'Fórum de Aurilândia'),
                                   ('ForumdeAurora', 'Fórum de Aurora'),
                                   ('ForumdeAuroraDoPara', 'Fórum de Aurora Do Pará'),
                                   ('ForumdeAuroraDoTocantins', 'Fórum de Aurora Do Tocantins'),
                                   ('ForumdeAutazes', 'Fórum de Autazes'),
                                   ('ForumdeAvare', 'Fórum de Avaré'),
                                   ('ForumdeAvelinoLopes', 'Fórum de Avelino Lopes'),
                                   ('ForumdeAxixaDoTocantins', 'Fórum de Axixá Do Tocantins'),
                                   ('ForumdeAcailandia', 'Fórum de Açailândia'),
                                   ('ForumdeAcu', 'Fórum de Açu'),
                                   ('ForumdeAcucena', 'Fórum de Açucena'),
                                   ('ForumdeBarroAlto', 'Fórum de Barro Alto'),
                                   ('ForumdeBocaDoAcre', 'Fórum de Boca Do Acre'),
                                   ('ForumdeBuenosAires', 'Fórum de Buenos Aires'),
                                   ('ForumdeBuritiAlegre', 'Fórum de Buriti Alegre'),
                                   ('ForumdeCabodeSantoAgostinho', 'Fórum de Cabo de Santo Agostinho'),
                                   ('ForumdeCachoeiraAlta', 'Fórum de Cachoeira Alta'),
                                   ('ForumdeCachoeiraDoArari', 'Fórum de Cachoeira Do Arari'),
                                   ('ForumdeCampoAlegre', 'Fórum de Campo Alegre'),
                                   ('ForumdeCampoAlegredeLourdes', 'Fórum de Campo Alegre de Lourdes'),
                                   ('ForumdeCamposAltos', 'Fórum de Campos Altos'),
                                   ('ForumdeCapelaAL', 'Fórum de Capela - AL'),
                                   ('ForumdeCapeladoAltoAlegre', 'Fórum de Capela do Alto Alegre'),
                                   ('ForumdeCariacicaJardimAmerica', 'Fórum de Cariacica - Jardim América'),
                                   ('ForumdeCasimirodeAbreu', 'Fórum de Casimiro de Abreu'),
                                   ('ForumdeCastroAlves', 'Fórum de Castro Alves'),
                                   ('ForumdeCerroAzul', 'Fórum de Cerro Azul'),
                                   ('ForumdeConceicaodasAlagoas', 'Fórum de Conceição das Alagoas'),
                                   ('ForumdeConceicaodoAlmeida', 'Fórum de Conceição do Almeida'),
                                   ('ForumdeConceicaoDoAraguaia', 'Fórum de Conceição Do Araguaia'),
                                   ('ForumdeCruzAlta', 'Fórum de Cruz Alta'),
                                   ('ForumdeCruzdasAlmas', 'Fórum de Cruz das Almas'),
                                   ('ForumdeCandidodeAbreu', 'Fórum de Cândido de Abreu'),
                                   ('ForumdeDomAquino', 'Fórum de Dom Aquino'),
                                   ('ForumdeFormosoDoAraguaia', 'Fórum de Formoso Do Araguaia'),
                                   ('ForumdeItapirangaAM', 'Fórum de Itapiranga - AM'),
                                   ('ForumdeJoaoAlfredo', 'Fórum de João Alfredo'),
                                   ('ForumdeLiciniodeAlmeida', 'Fórum de Licínio de Almeida'),
                                   ('ForumdeLimoeirodeAnadia', 'Fórum de Limoeiro de Anadia'),
                                   ('ForumdeLimoeiroDoAjuru', 'Fórum de Limoeiro Do Ajurú'),
                                   ('ForumdeMagalhaesdeAlmeida', 'Fórum de Magalhães de Almeida'),
                                   ('ForumdeMaravilhaAL', 'Fórum de Maravilha - AL'),
                                   ('ForumdeMiguelAlves', 'Fórum de Miguel Alves'),
                                   ('ForumdeMonteAlegre', 'Fórum de Monte Alegre'),
                                   ('ForumdeMonteAlegredeMinas', 'Fórum de Monte Alegre de Minas'),
                                   ('ForumdeMonteAlegredeSergipe', 'Fórum de Monte Alegre de Sergipe'),
                                   ('ForumdeMonteAlegredoPiaui', 'Fórum de Monte Alegre do Piauí'),
                                   ('ForumdeMonteAlto', 'Fórum de Monte Alto'),
                                   ('ForumdeMonteAprazivel', 'Fórum de Monte Aprazível'),
                                   ('ForumdeMonteAzul', 'Fórum de Monte Azul'),
                                   ('ForumdeMonteAzulPaulista', 'Fórum de Monte Azul Paulista'),
                                   ('ForumdeMontesAltos', 'Fórum de Montes Altos'),
                                   ('ForumdeMorroAgudo', 'Fórum de Morro Agudo'),
                                   ('ForumdeNossaSenhoraAparecida', 'Fórum de Nossa Senhora Aparecida'),
                                   ('ForumdeNovaAlvoradaDoSul', 'Fórum de Nova Alvorada Do Sul'),
                                   ('ForumdeNovoAcordo', 'Fórum de Novo Acordo'),
                                   ('ForumdeNovoAirao', 'Fórum de Novo Airão'),
                                   ('ForumdeNovoAripuana', 'Fórum de Novo Aripuanã'),
                                   ('ForumdePalmasdeMonteAlto', 'Fórum de Palmas de Monte Alto'),
                                   ('ForumdePatydoAlferes', 'Fórum de Paty do Alferes'),
                                   ('ForumdePauloAfonso', 'Fórum de Paulo Afonso'),
                                   ('ForumdePedraAzul', 'Fórum de Pedra Azul'),
                                   ('ForumdePedroAfonso', 'Fórum de Pedro Afonso'),
                                   ('ForumdePedroAvelino', 'Fórum de Pedro Avelino'),
                                   ('ForumdePeixotoDeAzevedo', 'Fórum de Peixoto De Azevedo'),
                                   ('ForumdePilaoArcado', 'Fórum de Pilão Arcado'),
                                   ('ForumdePonteAltaDoTocantins', 'Fórum de Ponte Alta Do Tocantins'),
                                   ('ForumdePortoAcre', 'Fórum de Porto Acre'),
                                   ('ForumdePortoAlegre', 'Fórum de Porto Alegre'),
                                   ('ForumdePortoAlegreDoNorte', 'Fórum de Porto Alegre Do Norte'),
                                   ('ForumdePousoAlegre', 'Fórum de Pouso Alegre'),
                                   ('ForumdePaodeAcucar', 'Fórum de Pão de Açúcar'),
                                   ('ForumdeRiachodasAlmas', 'Fórum de Riacho das Almas'),
                                   ('ForumdeRibeiradoAmparo', 'Fórum de Ribeira do Amparo'),
                                   ('ForumdeRiodoAntonio', 'Fórum de Rio do Antônio'),
                                   ('ForumdeRodriguesAlves', 'Fórum de Rodrigues Alves'),
                                   ('ForumdeRondaAlta', 'Fórum de Ronda Alta'),
                                   ('ForumdeSantaAdelia', 'Fórum de Santa Adélia'),
                                   ('ForumdeSantanaAP', 'Fórum de Santana - AP'),
                                   ('ForumdeSantanaDoAraguaia', 'Fórum de Santana Do Araguaia'),
                                   ('ForumdeSantantaDoAcarau', 'Fórum de Santanta Do Acarau'),
                                   ('ForumdeSantoAmaro', 'Fórum de Santo Amaro'),
                                   ('ForumdeSantoAmarodaImperatriz', 'Fórum de Santo Amaro da Imperatriz'),
                                   ('ForumdeSantoAmarodasBrotas', 'Fórum de Santo Amaro das Brotas'),
                                   ('ForumdeSantoAmarodoMaranhao', 'Fórum de Santo Amaro do Maranhão'),
                                   ('ForumdeSantoAnastacio', 'Fórum de Santo Anastácio'),
                                   ('ForumdeSantoAntoniodaPlatina', 'Fórum de Santo Antonio da Platina'),
                                   ('ForumdeSantoAntoniodePadua', 'Fórum de Santo Antonio de Padua'),
                                   ('ForumdeSantoAntoniodoSudoeste', 'Fórum de Santo Antonio do Sudoeste'),
                                   ('ForumdeSantoAntonio', 'Fórum de Santo Antônio'),
                                   ('ForumdeSantoAntoniodaPatrulha', 'Fórum de Santo Antônio da Patrulha'),
                                   ('ForumdeSantoAntoniodasMissoes', 'Fórum de Santo Antônio das Missões'),
                                   ('ForumdeSantoAntoniodeJesus', 'Fórum de Santo Antônio de Jesus'),
                                   ('ForumdeSantoAntonioDeLeverger', 'Fórum de Santo Antônio De Leverger'),
                                   ('ForumdeSantoAntonioDoDescoberto', 'Fórum de Santo Antônio Do Descoberto'),
                                   ('ForumdeSantoAntonioDoIca', 'Fórum de Santo Antônio Do Içá'),
                                   ('ForumdeSantoAntoniodoMonte', 'Fórum de Santo Antônio do Monte'),
                                   ('ForumdeSantoAntonioDoTaua', 'Fórum de Santo Antônio Do Tauá'),
                                   ('ForumdeSantoAntoniodosLopes', 'Fórum de Santo Antônio dos Lopes'),
                                   ('ForumdeSantoAugusto', 'Fórum de Santo Augusto'),
                                   ('ForumdeSaoDomingosdoAzeitao', 'Fórum de São Domingos do Azeitão'),
                                   ('ForumdeSaoFranciscodeAssis', 'Fórum de São Francisco de Assis'),
                                   ('ForumdeSaoFelixDoAraguaia', 'Fórum de São Félix Do Araguaia'),
                                   ('ForumdeSaoGeraldoDoAraguaia', 'Fórum de São Geraldo Do Araguaia'),
                                   ('ForumdeSaoGoncalodoAmaranteCE', 'Fórum de São Goncalo do Amarante - CE'),
                                   ('ForumdeSaoGoncalodoAmaranteRN', 'Fórum de São Gonçalo do Amarante - RN'),
                                   ('ForumdeSaoLuizDoAnaua', 'Fórum de São Luiz Do Anauá'),
                                   ('ForumdeSaoMateusSantoAntonio', 'Fórum de São Mateus - Santo Antônio'),
                                   ('ForumdeSaoMiguelArcanjo', 'Fórum de São Miguel Arcanjo'),
                                   ('ForumdeSaoMigueldoAleixo', 'Fórum de São Miguel do Aleixo'),
                                   ('ForumdeSaoMiguelDoAraguaia', 'Fórum de São Miguel Do Araguaia'),
                                   ('ForumdeSaoPedrodaAldeia', 'Fórum de São Pedro da Aldeia'),
                                   ('ForumdeSaoSebastiaodoAlto', 'Fórum de São Sebastiao do Alto'),
                                   ('ForumdeSaoSebastiaoAL', 'Fórum de São Sebastião - AL'),
                                   ('ForumdeTerradeAreia', 'Fórum de Terra de Areia'),
                                   ('ForumdeVargemAlta', 'Fórum de Vargem Alta'),
                                   ('ForumdeVarzeaAlegre', 'Fórum de Varzea Alegre'),
                                   ('ForumdeVenancioAires', 'Fórum de Venâncio Aires'),
                                   ('ForumdeVitoriaDesJoseMathiasdeAlmeidaNe', 'Fórum de Vitória - Des. José Mathias de Almeida Ne'),
                                   ('ForumdeVitoriadeSantoAntao', 'Fórum de Vitória de Santo Antão'),
                                   ('ForumdeVicosaAL', 'Fórum de Viçosa - AL'),
                                   ('ForumFederaldeAltamira', 'Fórum Federal de Altamira'),
                                   ('ForumFederaldeAngraDosReis', 'Fórum Federal de Angra Dos Reis'),
                                   ('ForumFederaldeAnapolis', 'Fórum Federal de Anápolis'),
                                   ('ForumFederaldeAparecidaDeGoiania', 'Fórum Federal de Aparecida De Goiânia'),
                                   ('ForumFederaldeApucarana', 'Fórum Federal de Apucarana'),
                                   ('ForumFederaldeAracajuI', 'Fórum Federal de Aracaju I'),
                                   ('ForumFederaldeAracajuII', 'Fórum Federal de Aracaju II'),
                                   ('ForumFederaldeArapiraca', 'Fórum Federal de Arapiraca'),
                                   ('ForumFederaldeAraraquara', 'Fórum Federal de Araraquara'),
                                   ('ForumFederaldeAracatuba', 'Fórum Federal de Araçatuba'),
                                   ('ForumFederaldeAssis', 'Fórum Federal de Assis'),
                                   ('ForumFederaldeBHAntonioFernandoPinheiro', 'Fórum Federal de BH - Antônio Fernando Pinheiro'),
                                   ('ForumFederaldeBHEuclydesReisAguiar', 'Fórum Federal de BH - Euclydes Reis Aguiar'),
                                   ('ForumFederaldeCruzAlta', 'Fórum Federal de Cruz Alta'),
                                   ('ForumFederaldePauloAfonso', 'Fórum Federal de Paulo Afonso'),
                                   ('ForumFederaldePortoAlegre', 'Fórum Federal de Porto Alegre'),
                                   ('ForumFederaldePousoAlegre', 'Fórum Federal de Pouso Alegre'),
                                   ('ForumFederaldeSantoAndre', 'Fórum Federal de Santo André'),
                                   ('ForumFederaldeSaoPedroDaAldeia', 'Fórum Federal de São Pedro Da Aldeia'),
                                   ('ForumGumersindoBessaAracaju', 'Fórum Gumersindo Bessa - Aracaju'),
                                   ('ForumIntegradode18doForteAracaju', 'Fórum Integrado de 18 do Forte - Aracaju'),
                                   ('ForumIntegradodeInacioBarbosaAracaju', 'Fórum Integrado de Inácio Barbosa - Aracaju'),
                                   ('ForumIntegradodeSantaMariaAracaju', 'Fórum Integrado de Santa Maria - Aracaju'),
                                   ('ForumIntegradoMariaVirginiaAracaju', 'Fórum Integrado Maria Virginia - Aracaju'),
                                   ('ForumMinAbelardodeAraujoJurema', 'Fórum Min. Abelardo de Araújo Jurema'),
                                   ('ForumNovaAndradina', 'Fórum Nova Andradina'),
                                   ('ForumRegionaldeSantoAmaroCriminal', 'Fórum Regional de Santo Amaro - Criminal'),
                                   ('ForumRegionaldeSantoAmaroCivel', 'Fórum Regional de Santo Amaro - Cível'),
                                   ('ForumRegionaldoAltoPetropolis', 'Fórum Regional do Alto Petrópolis'),
                                   ('JuizadoEspecialAdjuntodeRioBrilhante', 'Juizado Especial Adjunto de Rio Brilhante'),
                                   ('JuizadoEspecialFederalAdjuntodeUberaba', 'Juizado Especial Federal Adjunto de Uberaba'),
                                   ('JuizadoEspecialFederalCiveldeAmericana', 'Juizado Especial Federal Cível de Americana'),
                                   ('JuizadoEspecialFederalCiveldeAndradina', 'Juizado Especial Federal Cível de Andradina'),
                                   ('JuizadoEspecialFederalCiveldeAvare', 'Juizado Especial Federal Cível de Avaré'),
                                   ('TrabalhistadeAbaetuba', 'Trabalhista de Abaetuba'),
                                   ('TrabalhistadeAdamantina', 'Trabalhista de Adamantina'),
                                   ('TrabalhistadeAfonsoClaudio', 'Trabalhista de Afonso Cláudio'),
                                   ('TrabalhistadeAimores', 'Trabalhista de Aimorés'),
                                   ('TrabalhistadeAlagoinhas', 'Trabalhista de Alagoinhas'),
                                   ('TrabalhistadeAlegre', 'Trabalhista de Alegre'),
                                   ('TrabalhistadeAlegrete', 'Trabalhista de Alegrete'),
                                   ('TrabalhistadeAlfenas', 'Trabalhista de Alfenas'),
                                   ('TrabalhistadeAlmenara', 'Trabalhista de Almenara'),
                                   ('TrabalhistadeAltaFloresta', 'Trabalhista de Alta Floresta'),
                                   ('TrabalhistadeAltamira', 'Trabalhista de Altamira'),
                                   ('TrabalhistadeAlvorada', 'Trabalhista de Alvorada'),
                                   ('TrabalhistadeAmambai', 'Trabalhista de Amambaí'),
                                   ('TrabalhistadeAmericana', 'Trabalhista de Americana'),
                                   ('TrabalhistadeAmparo', 'Trabalhista de Amparo'),
                                   ('TrabalhistadeAnanindeua', 'Trabalhista de Ananindeua'),
                                   ('TrabalhistadeAndradina', 'Trabalhista de Andradina'),
                                   ('TrabalhistadeAngradosReis', 'Trabalhista de Angra dos Reis'),
                                   ('TrabalhistadeAnapolis', 'Trabalhista de Anápolis'),
                                   ('TrabalhistadeAparecida', 'Trabalhista de Aparecida'),
                                   ('TrabalhistadeAparecidadeGoiania', 'Trabalhista de Aparecida de Goiânia'),
                                   ('TrabalhistadeApucarana', 'Trabalhista de Apucarana'),
                                   ('TrabalhistadeAquidauana', 'Trabalhista de Aquidauana'),
                                   ('TrabalhistadeAracaju', 'Trabalhista de Aracaju'),
                                   ('TrabalhistadeAracruz', 'Trabalhista de Aracruz'),
                                   ('TrabalhistadeAraguari', 'Trabalhista de Araguari'),
                                   ('TrabalhistadeAraguaina', 'Trabalhista de Araguaína'),
                                   ('TrabalhistadeArapiraca', 'Trabalhista de Arapiraca'),
                                   ('TrabalhistadeArapongas', 'Trabalhista de Arapongas'),
                                   ('TrabalhistadeArarangua', 'Trabalhista de Araranguá'),
                                   ('TrabalhistadeAraraquara', 'Trabalhista de Araraquara'),
                                   ('TrabalhistadeAraras', 'Trabalhista de Araras'),
                                   ('TrabalhistadeAraripina', 'Trabalhista de Araripina'),
                                   ('TrabalhistadeAraruama', 'Trabalhista de Araruama'),
                                   ('TrabalhistadeAraucaria', 'Trabalhista de Araucária'),
                                   ('TrabalhistadeAraxa', 'Trabalhista de Araxá'),
                                   ('TrabalhistadeAracatuba', 'Trabalhista de Araçatuba'),
                                   ('TrabalhistadeAreia', 'Trabalhista de Areia'),
                                   ('TrabalhistadeAriquemes', 'Trabalhista de Ariquemes'),
                                   ('TrabalhistadeArroioGrande', 'Trabalhista de Arroio Grande'),
                                   ('TrabalhistadeAssis', 'Trabalhista de Assis'),
                                   ('TrabalhistadeAssisChateaubriand', 'Trabalhista de Assis Chateaubriand'),
                                   ('TrabalhistadeAtalaia', 'Trabalhista de Atalaia'),
                                   ('TrabalhistadeAtibaia', 'Trabalhista de Atibaia'),
                                   ('TrabalhistadeAvare', 'Trabalhista de Avaré'),
                                   ('TrabalhistadeAcailandia', 'Trabalhista de Açailândia'),
                                   ('TrabalhistadeAcu', 'Trabalhista de Açu'),
                                   ('TrabalhistadeCabodeSantoAgostinho', 'Trabalhista de Cabo de Santo Agostinho'),
                                   ('TrabalhistadeCaruaruAgamenonMagalhaes', 'Trabalhista de Caruaru - Agamenon Magalhães'),
                                   ('TrabalhistadeCaruaruRodriguesdeAbreu', 'Trabalhista de Caruaru - Rodrigues de Abreu'),
                                   ('TrabalhistadeCruzAlta', 'Trabalhista de Cruz Alta'),
                                   ('TrabalhistadeCruzdasAlmas', 'Trabalhista de Cruz das Almas'),
                                   ('TrabalhistadeMonteAzul', 'Trabalhista de Monte Azul'),
                                   ('TrabalhistadeNovaAndradina', 'Trabalhista de Nova Andradina'),
                                   ('TrabalhistadePauloAfonso', 'Trabalhista de Paulo Afonso'),
                                   ('TrabalhistadePortoAlegre', 'Trabalhista de Porto Alegre'),
                                   ('TrabalhistadePousoAlegre', 'Trabalhista de Pouso Alegre'),
                                   ('TrabalhistadeSantoAmaro', 'Trabalhista de Santo Amaro'),
                                   ('TrabalhistadeSantoAndre', 'Trabalhista de Santo André'),
                                   ('TrabalhistadeSantoAntoniodeJesus', 'Trabalhista de Santo Antônio de Jesus'),
                                   ('TrabalhistadeSaoFelixdoAraguaia', 'Trabalhista de São Félix do Araguaia'),
                                   ('TrabalhistadeVitoriadeSantoAntao', 'Trabalhista de Vitória de Santo Antão'),
                                   ('TribunaldeJusticadeAlagoas', 'Tribunal de Justiça de Alagoas'),
                                   ('TribunaldeJusticadoAcre', 'Tribunal de Justiça do Acre'),
                                   ('TribunaldeJusticadoAmapa', 'Tribunal de Justiça do Amapá'),
                                   ('TribunaldeJusticadoAmazonas', 'Tribunal de Justiça do Amazonas'),
                                   ('UnidadeJudiciariaAvancadadePortoUniao', 'Unidade Judiciária Avançada de Porto União'),
                                   ('ForumBarreiro', 'Fórum Barreiro'),
                                   ('ForumBonito', 'Fórum Bonito'),
                                   ('ForumCriminaldeBelem', 'Fórum Criminal de Belém'),
                                   ('ForumCiveldeBelem', 'Fórum Cível de Belém'),
                                   ('ForumdeBacabal', 'Fórum de Bacabal'),
                                   ('ForumdeBacuri', 'Fórum de Bacuri'),
                                   ('ForumdeBaependi', 'Fórum de Baependi'),
                                   ('ForumdeBage', 'Fórum de Bagé'),
                                   ('ForumdeBaianopolis', 'Fórum de Baianópolis'),
                                   ('ForumdeBaixaGrande', 'Fórum de Baixa Grande'),
                                   ('ForumdeBaixoGuandu', 'Fórum de Baixo Guandú'),
                                   ('ForumdeBaiao', 'Fórum de Baião'),
                                   ('ForumdeBalnearioCamboriu', 'Fórum de Balneario Camboriu'),
                                   ('ForumdeBalnearioPicarras', 'Fórum de Balneario Picarras'),
                                   ('ForumdeBalsas', 'Fórum de Balsas'),
                                   ('ForumdeBambui', 'Fórum de Bambuí'),
                                   ('ForumdeBananal', 'Fórum de Bananal'),
                                   ('ForumdeBananeiras', 'Fórum de Bananeiras'),
                                   ('ForumdeBandeirantesMS', 'Fórum de Bandeirantes - MS'),
                                   ('ForumdeBandeirantesPR', 'Fórum de Bandeirantes - PR'),
                                   ('ForumdeBarauna', 'Fórum de Baraúna'),
                                   ('ForumdeBarbacena', 'Fórum de Barbacena'),
                                   ('ForumdeBarbalha', 'Fórum de Barbalha'),
                                   ('ForumdeBarbosaFerraz', 'Fórum de Barbosa Ferraz'),
                                   ('ForumdeBarcarena', 'Fórum de Barcarena'),
                                   ('ForumdeBarcelos', 'Fórum de Barcelos'),
                                   ('ForumdeBariri', 'Fórum de Bariri'),
                                   ('ForumdeBarra', 'Fórum de Barra'),
                                   ('ForumdeBarraBonita', 'Fórum de Barra Bonita'),
                                   ('ForumdeBarradaEstiva', 'Fórum de Barra da Estiva'),
                                   ('ForumdeBarradeSantaRosa', 'Fórum de Barra de Santa Rosa'),
                                   ('ForumdeBarradeSaoFrancisco', 'Fórum de Barra de São Francisco'),
                                   ('ForumdeBarraDoBugres', 'Fórum de Barra Do Bugres'),
                                   ('ForumdeBarradoChoca', 'Fórum de Barra do Choça'),
                                   ('ForumdeBarradoCorda', 'Fórum de Barra do Corda'),
                                   ('ForumdeBarraDoGarcas', 'Fórum de Barra Do Garças'),
                                   ('ForumdeBarradoMendes', 'Fórum de Barra do Mendes'),
                                   ('ForumdeBarradoPirai', 'Fórum de Barra do Pirai'),
                                   ('ForumdeBarradoRibeiro', 'Fórum de Barra do Ribeiro'),
                                   ('ForumdeBarradosCoqueiros', 'Fórum de Barra dos Coqueiros'),
                                   ('ForumdeBarraMansa', 'Fórum de Barra Mansa'),
                                   ('ForumdeBarraVelha', 'Fórum de Barra Velha'),
                                   ('ForumdeBarracao', 'Fórum de Barracão'),
                                   ('ForumdeBarras', 'Fórum de Barras'),
                                   ('ForumdeBarreiras', 'Fórum de Barreiras'),
                                   ('ForumdeBarreirinha', 'Fórum de Barreirinha'),
                                   ('ForumdeBarreirinhas', 'Fórum de Barreirinhas'),
                                   ('ForumdeBarreiros', 'Fórum de Barreiros'),
                                   ('ForumdeBarretos', 'Fórum de Barretos'),
                                   ('ForumdeBarroDuro', 'Fórum de Barro Duro'),
                                   ('ForumdeBarroso', 'Fórum de Barroso'),
                                   ('ForumdeBaraodeCocais', 'Fórum de Barão de Cocais'),
                                   ('ForumdeBaraodeGrajau', 'Fórum de Barão de Grajaú'),
                                   ('ForumdeBastos', 'Fórum de Bastos'),
                                   ('ForumdeBataguassu', 'Fórum de Bataguassu'),
                                   ('ForumdeBataipora', 'Fórum de Bataiporã'),
                                   ('ForumdeBatalha', 'Fórum de Batalha'),
                                   ('ForumdeBatalhaPI', 'Fórum de Batalha - PI'),
                                   ('ForumdeBatatais', 'Fórum de Batatais'),
                                   ('ForumdeBaturite', 'Fórum de Baturité'),
                                   ('ForumdeBayeux', 'Fórum de Bayeux'),
                                   ('ForumdeBebedouro', 'Fórum de Bebedouro'),
                                   ('ForumdeBeberibe', 'Fórum de Beberibe'),
                                   ('ForumdeBelaCruz', 'Fórum de Bela Cruz'),
                                   ('ForumdeBelaVista', 'Fórum de Bela Vista'),
                                   ('ForumdeBelaVistaDeGoias', 'Fórum de Bela Vista De Goiás'),
                                   ('ForumdeBelaVistadoParaiso', 'Fórum de Bela Vista do Paraíso'),
                                   ('ForumdeBelfordRoxo', 'Fórum de Belford Roxo')
                                   ;
                                   INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumdeBelmonte', 'Fórum de Belmonte'),
                                   ('ForumdeBeloCampo', 'Fórum de Belo Campo'),
                                   ('ForumdeBeloJardim', 'Fórum de Belo Jardim'),
                                   ('ForumdeBeloVale', 'Fórum de Belo Vale'),
                                   ('ForumdeBelemdeMaria', 'Fórum de Belém de Maria'),
                                   ('ForumdeBelemdoBrejodoCruz', 'Fórum de Belém do Brejo do Cruz'),
                                   ('ForumdeBelemdoSaoFrancisco', 'Fórum de Belém do São Francisco'),
                                   ('ForumdeBeneditinos', 'Fórum de Beneditinos'),
                                   ('ForumdeBenevides', 'Fórum de Benevides'),
                                   ('ForumdeBenjaminConstant', 'Fórum de Benjamin Constant'),
                                   ('ForumdeBentoGoncalves', 'Fórum de Bento Gonçalves'),
                                   ('ForumdeBequimao', 'Fórum de Bequimão'),
                                   ('ForumdeBertioga', 'Fórum de Bertioga'),
                                   ('ForumdeBertolinea', 'Fórum de Bertolínea'),
                                   ('ForumdeBeruri', 'Fórum de Beruri'),
                                   ('ForumdeBetim', 'Fórum de Betim'),
                                   ('ForumdeBetania', 'Fórum de Betânia'),
                                   ('ForumdeBezerros', 'Fórum de Bezerros'),
                                   ('ForumdeBicas', 'Fórum de Bicas'),
                                   ('ForumdeBiguacu', 'Fórum de Biguacu'),
                                   ('ForumdeBilac', 'Fórum de Bilac'),
                                   ('ForumdeBirigui', 'Fórum de Birigüi'),
                                   ('ForumdeBlumenau', 'Fórum de Blumenau'),
                                   ('ForumdeBoaEsperanca', 'Fórum de Boa Esperança'),
                                   ('ForumdeBoaEsperancaES', 'Fórum de Boa Esperança - ES'),
                                   ('ForumdeBoaNova', 'Fórum de Boa Nova'),
                                   ('ForumdeBoaViagem', 'Fórum de Boa Viagem'),
                                   ('ForumdeBoaVista', 'Fórum de Boa Vista'),
                                   ('ForumdeBoaVistadoTupim', 'Fórum de Boa Vista do Tupim'),
                                   ('ForumdeBoaVistaDosRamos', 'Fórum de Boa Vista Dos Ramos'),
                                   ('ForumdeBocadaMata', 'Fórum de Boca da Mata'),
                                   ('ForumdeBocaina', 'Fórum de Bocaina'),
                                   ('ForumdeBocaiuva', 'Fórum de Bocaiúva'),
                                   ('ForumdeBocaiuvadoSul', 'Fórum de Bocaíuva do Sul'),
                                   ('ForumdeBodoco', 'Fórum de Bodocó'),
                                   ('ForumdeBoituva', 'Fórum de Boituva'),
                                   ('ForumdeBomConselho', 'Fórum de Bom Conselho'),
                                   ('ForumdeBomDespacho', 'Fórum de Bom Despacho'),
                                   ('ForumdeBomJardim', 'Fórum de Bom Jardim'),
                                   ('ForumdeBomJardimPE', 'Fórum de Bom Jardim - PE'),
                                   ('ForumdeBomJardimRJ', 'Fórum de Bom Jardim - RJ'),
                                   ('ForumdeBomJesus', 'Fórum de Bom Jesus'),
                                   ('ForumdeBomJesusPI', 'Fórum de Bom Jesus - PI'),
                                   ('ForumdeBomJesusdaLapa', 'Fórum de Bom Jesus da Lapa'),
                                   ('ForumdeBomJesusDeGoias', 'Fórum de Bom Jesus De Goiás'),
                                   ('ForumdeBomJesusdeItabapoana', 'Fórum de Bom Jesus de Itabapoana'),
                                   ('ForumdeBomJesusdoNorte', 'Fórum de Bom Jesus do Norte'),
                                   ('ForumdeBomRetiro', 'Fórum de Bom Retiro'),
                                   ('ForumdeBomSucesso', 'Fórum de Bom Sucesso'),
                                   ('ForumdeBonfim', 'Fórum de Bonfim'),
                                   ('ForumdeBonfinopolisdeMinas', 'Fórum de Bonfinópolis de Minas'),
                                   ('ForumdeBonito', 'Fórum de Bonito'),
                                   ('ForumdeBonitoPA', 'Fórum de Bonito - PA'),
                                   ('ForumdeBonitodeSantaFe', 'Fórum de Bonito de Santa Fé'),
                                   ('ForumdeBoqueirao', 'Fórum de Boqueirão'),
                                   ('ForumdeBoquim', 'Fórum de Boquim'),
                                   ('ForumdeBoquira', 'Fórum de Boquira'),
                                   ('ForumdeBorba', 'Fórum de Borba'),
                                   ('ForumdeBorborema', 'Fórum de Borborema'),
                                   ('ForumdeBordadaMata', 'Fórum de Borda da Mata'),
                                   ('ForumdeBotelhos', 'Fórum de Botelhos'),
                                   ('ForumdeBotucatu', 'Fórum de Botucatu'),
                                   ('ForumdeBotupora', 'Fórum de Botuporã'),
                                   ('ForumdeBracodoNorte', 'Fórum de Braco do Norte'),
                                   ('ForumdeBraganca', 'Fórum de Bragança'),
                                   ('ForumdeBragancaPaulista', 'Fórum de Bragança Paulista'),
                                   ('ForumdeBrasilNovo', 'Fórum de Brasil Novo'),
                                   ('ForumdeBrasilandia', 'Fórum de Brasilândia'),
                                   ('ForumdeBrasileia', 'Fórum de Brasiléia'),
                                   ('ForumdeBrasnorte', 'Fórum de Brasnorte'),
                                   ('ForumdeBrasilia', 'Fórum de Brasília'),
                                   ('ForumdeBrasiliadeMinas', 'Fórum de Brasília de Minas'),
                                   ('ForumdeBrasopolis', 'Fórum de Brasópolis'),
                                   ('ForumdeBrejo', 'Fórum de Brejo'),
                                   ('ForumdeBrejodaMadredeDeus', 'Fórum de Brejo da Madre de Deus'),
                                   ('ForumdeBrejodoCruz', 'Fórum de Brejo do Cruz'),
                                   ('ForumdeBrejoGrande', 'Fórum de Brejo Grande'),
                                   ('ForumdeBrejoSanto', 'Fórum de Brejo Santo'),
                                   ('ForumdeBrejao', 'Fórum de Brejão'),
                                   ('ForumdeBrejoes', 'Fórum de Brejões'),
                                   ('ForumdeBreuBranco', 'Fórum de Breu Branco'),
                                   ('ForumdeBreves', 'Fórum de Breves'),
                                   ('ForumdeBrodowski', 'Fórum de Brodowski'),
                                   ('ForumdeBrotas', 'Fórum de Brotas'),
                                   ('ForumdeBrotasdeMacaubas', 'Fórum de Brotas de Macaúbas'),
                                   ('ForumdeBrumadinho', 'Fórum de Brumadinho'),
                                   ('ForumdeBrumado', 'Fórum de Brumado'),
                                   ('ForumdeBrusque', 'Fórum de Brusque'),
                                   ('ForumdeBuenoBrandao', 'Fórum de Bueno Brandão'),
                                   ('ForumdeBuenopolis', 'Fórum de Buenópolis'),
                                   ('ForumdeBuerarema', 'Fórum de Buerarema'),
                                   ('ForumdeBujari', 'Fórum de Bujari'),
                                   ('ForumdeBujaru', 'Fórum de Bujarú'),
                                   ('ForumdeBuritama', 'Fórum de Buritama'),
                                   ('ForumdeBuriti', 'Fórum de Buriti'),
                                   ('ForumdeBuritiBravo', 'Fórum de Buriti Bravo'),
                                   ('ForumdeBuritidosLopes', 'Fórum de Buriti dos Lopes'),
                                   ('ForumdeBuriticupu', 'Fórum de Buriticupu'),
                                   ('ForumdeBuritis', 'Fórum de Buritis'),
                                   ('ForumdeBuritisRO', 'Fórum de Buritis - RO'),
                                   ('ForumdeButia', 'Fórum de Butiá'),
                                   ('ForumdeBuique', 'Fórum de Buíque'),
                                   ('ForumdeBuzios', 'Fórum de Búzios'),
                                   ('ForumdeCampoBelo', 'Fórum de Campo Belo'),
                                   ('ForumdeCampoBelodoSul', 'Fórum de Campo Belo do Sul'),
                                   ('ForumdeCampoBom', 'Fórum de Campo Bom'),
                                   ('ForumdeCampodoBrito', 'Fórum de Campo do Brito'),
                                   ('ForumdeCamposBelos', 'Fórum de Campos Belos'),
                                   ('ForumdeCandeiasBA', 'Fórum de Candeias - BA'),
                                   ('ForumdeCantodoBuriti', 'Fórum de Canto do Buriti'),
                                   ('ForumdeCapivarideBaixo', 'Fórum de Capivari de Baixo'),
                                   ('ForumdeCapaoBonito', 'Fórum de Capão Bonito'),
                                   ('ForumdeCarlosBarbosa', 'Fórum de Carlos Barbosa'),
                                   ('ForumdeCasaBranca', 'Fórum de Casa Branca'),
                                   ('ForumdeConceicaodaBarra', 'Fórum de Conceição da Barra'),
                                   ('ForumdeCoronelBicaco', 'Fórum de Coronel Bicaco'),
                                   ('ForumdeDuasBarras', 'Fórum de Duas Barras'),
                                   ('ForumdeEngenheiroBeltrao', 'Fórum de Engenheiro Beltrão'),
                                   ('ForumdeFariasBrito', 'Fórum de Farias Brito'),
                                   ('ForumdeFonteBoa', 'Fórum de Fonte Boa'),
                                   ('ForumdeFranciscoBeltrao', 'Fórum de Francisco Beltrão'),
                                   ('ForumdeGovernadorEugenioBarros', 'Fórum de Governador Eugênio Barros'),
                                   ('ForumdeJoseBonifacio', 'Fórum de José Bonifácio'),
                                   ('ForumdeLeopoldoDeBulhoes', 'Fórum de Leopoldo De Bulhões'),
                                   ('ForumdeMalhadadosBois', 'Fórum de Malhada dos Bois'),
                                   ('ForumdeMatiasBarbosa', 'Fórum de Matias Barbosa'),
                                   ('ForumdeMogidasCruzesBrasCubas', 'Fórum de Mogi das Cruzes - Brás Cubas'),
                                   ('ForumdeMoitaBonita', 'Fórum de Moita Bonita'),
                                   ('ForumdeMonteBelo', 'Fórum de Monte Belo'),
                                   ('ForumdeMundoNovoBA', 'Fórum de Mundo Novo - BA'),
                                   ('ForumdeNossaSenhoradoSocorroPedroBarreto', 'Fórum de Nossa Senhora do Socorro - Pedro Barreto'),
                                   ('ForumdeNovaBrasilandia', 'Fórum de Nova Brasilândia'),
                                   ('ForumdeNovaFatimaBA', 'Fórum de Nova Fátima - BA'),
                                   ('ForumdeOliveiradosBrejinhos', 'Fórum de Oliveira dos Brejinhos'),
                                   ('ForumdeOuroBranco', 'Fórum de Ouro Branco'),
                                   ('ForumdePadreBernardo', 'Fórum de Padre Bernardo'),
                                   ('ForumdePastosBons', 'Fórum de Pastos Bons'),
                                   ('ForumdePatoBranco', 'Fórum de Pato Branco'),
                                   ('ForumdePauBrasil', 'Fórum de Pau Brasil'),
                                   ('ForumdePedraBranca', 'Fórum de Pedra Branca'),
                                   ('ForumdePeixeBoi', 'Fórum de Peixe Boi'),
                                   ('ForumdePereiraBarreto', 'Fórum de Pereira Barreto'),
                                   ('ForumdePimentaBueno', 'Fórum de Pimenta Bueno'),
                                   ('ForumdePlanaltoBA', 'Fórum de Planalto - BA'),
                                   ('ForumdePortoBelo', 'Fórum de Porto Belo'),
                                   ('ForumdePocoBranco', 'Fórum de Poço Branco'),
                                   ('ForumdePresidenteBernardes', 'Fórum de Presidente Bernardes'),
                                   ('ForumdeRibeiraoBonito', 'Fórum de Ribeirão Bonito'),
                                   ('ForumdeRioBananal', 'Fórum de Rio Bananal'),
                                   ('ForumdeRioBonito', 'Fórum de Rio Bonito'),
                                   ('ForumdeRioBrancoMT', 'Fórum de Rio Branco - MT'),
                                   ('ForumdeRioBrancodoSul', 'Fórum de Rio Branco do Sul'),
                                   ('ForumdeRodeioBonito', 'Fórum de Rodeio Bonito'),
                                   ('ForumdeRuyBarbosa', 'Fórum de Ruy Barbosa'),
                                   ('ForumdeSantaBranca', 'Fórum de Santa Branca'),
                                   ('ForumdeSantaBarbaraBA', 'Fórum de Santa Bárbara - BA'),
                                   ('ForumdeSantaBarbaraMG', 'Fórum de Santa Bárbara - MG'),
                                   ('ForumdeSantaBarbaraDOeste', 'Fórum de Santa Bárbara D''Oeste'),
                                   ('ForumdeSantaBarbaradoSul', 'Fórum de Santa Bárbara do Sul'),
                                   ('ForumdeSantaLuziaBA', 'Fórum de Santa Luzia - BA'),
                                   ('ForumdeSantaMariadaBoaVista', 'Fórum de Santa Maria da Boa Vista'),
                                   ('ForumdeSenhordoBonfim', 'Fórum de Senhor do Bonfim'),
                                   ('ForumdeSerraBranca', 'Fórum de Serra Branca'),
                                   ('ForumdeSobradinhoBA', 'Fórum de Sobradinho - BA'),
                                   ('ForumdeSaoBenedito', 'Fórum de São Benedito'),
                                   ('ForumdeSaoBeneditodoRioPreto', 'Fórum de São Benedito do Rio Preto'),
                                   ('ForumdeSaoBento', 'Fórum de São Bento'),
                                   ('ForumdeSaoBentoMA', 'Fórum de São Bento - MA'),
                                   ('ForumdeSaoBentodoNorte', 'Fórum de São Bento do Norte'),
                                   ('ForumdeSaoBentodoSapucai', 'Fórum de São Bento do Sapucaí'),
                                   ('ForumdeSaoBentodoSul', 'Fórum de São Bento do Sul'),
                                   ('ForumdeSaoBentodoUna', 'Fórum de São Bento do Una'),
                                   ('ForumdeSaoBernardo', 'Fórum de São Bernardo'),
                                   ('ForumdeSaoBorja', 'Fórum de São Borja'),
                                   ('ForumdeSaoBras', 'Fórum de São Brás'),
                                   ('ForumdeSaoDomingosBA', 'Fórum de São Domingos - BA'),
                                   ('ForumdeSaoGabrielBA', 'Fórum de São Gabriel - BA'),
                                   ('ForumdeSaoJoaquimdaBarra', 'Fórum de São Joaquim da Barra'),
                                   ('ForumdeSaoJosedoBelmonte', 'Fórum de São José do Belmonte'),
                                   ('ForumdeSaoJoaoBatistaMA', 'Fórum de São João Batista - MA'),
                                   ('ForumdeSaoJoaoBatistaSC', 'Fórum de São João Batista - SC'),
                                   ('ForumdeSaoJoaodaBarra', 'Fórum de São João da Barra'),
                                   ('ForumdeSaoJoaodaBoaVista', 'Fórum de São João da Boa Vista'),
                                   ('ForumdeSaoLuisdeMontesBelos', 'Fórum de São Luís de Montes Belos'),
                                   ('ForumdeSaoPedrodaAguaBranca', 'Fórum de São Pedro da Água Branca'),
                                   ('ForumdeSaoSebastiaoDaBoaVista', 'Fórum de São Sebastião Da Boa Vista'),
                                   ('ForumdeTelemacoBorba', 'Fórum de Telêmaco Borba'),
                                   ('ForumdeTeodoroSampaioBA', 'Fórum de Teodoro Sampaio - BA'),
                                   ('ForumdeTerraBoa', 'Fórum de Terra Boa'),
                                   ('ForumdeTobiasBarreto', 'Fórum de Tobias Barreto'),
                                   ('ForumdeVilaBelaDaSantissimaTrindade', 'Fórum de Vila Bela Da Santíssima Trindade'),
                                   ('ForumdeViscondedoRioBranco', 'Fórum de Visconde do Rio Branco'),
                                   ('ForumdeWenceslauBraz', 'Fórum de Wenceslau Braz'),
                                   ('ForumdeAguaBoa', 'Fórum de Água Boa'),
                                   ('ForumdeAguaBranca', 'Fórum de Água Branca'),
                                   ('ForumdeAguaBrancaPB', 'Fórum de Água Branca - PB'),
                                   ('ForumdeAguaBrancaPI', 'Fórum de Água Branca - PI'),
                                   ('ForumdeAguasBelas', 'Fórum de Águas Belas'),
                                   ('ForumdeAguiaBranca', 'Fórum de Águia Branca'),
                                   ('ForumDistritalVarellaBarcaNatal', 'Fórum Distrital Varella Barca - Natal'),
                                   ('ForumDoisIrmaosDoBuriti', 'Fórum Dois Irmãos Do Buriti'),
                                   ('ForumFederaldeBage', 'Fórum Federal de Bagé'),
                                   ('ForumFederaldeBarraDoPirai', 'Fórum Federal de Barra Do Piraí'),
                                   ('ForumFederaldeBarreiras', 'Fórum Federal de Barreiras'),
                                   ('ForumFederaldeBauru', 'Fórum Federal de Bauru'),
                                   ('ForumFederaldeBelem', 'Fórum Federal de Belém'),
                                   ('ForumFederaldeBentoGoncalves', 'Fórum Federal de Bento Gonçalves'),
                                   ('ForumFederaldeBHOscarDiasCorrea', 'Fórum Federal de BH - Oscar Dias Corrêa'),
                                   ('ForumFederaldeBlumenau', 'Fórum Federal de Blumenau'),
                                   ('ForumFederaldeBoaVista', 'Fórum Federal de Boa Vista'),
                                   ('ForumFederaldeBragancaPaulista', 'Fórum Federal de Bragança Paulista'),
                                   ('ForumFederaldeBrasiliaCaboFrio', 'Fórum Federal de Brasília - Cabo Frio'),
                                   ('ForumFederaldeBrasiliaI', 'Fórum Federal de Brasília I'),
                                   ('ForumFederaldeBrasiliaII', 'Fórum Federal de Brasília II'),
                                   ('ForumFederaldeBrusque', 'Fórum Federal de Brusque'),
                                   ('ForumFederaldeFranciscoBeltrao', 'Fórum Federal de Francisco Beltrão'),
                                   ('ForumFederaldePatoBranco', 'Fórum Federal de Pato Branco'),
                                   ('ForumFederaldeRioBranco', 'Fórum Federal de Rio Branco'),
                                   ('ForumFederaldeSaoBernardodoCampo', 'Fórum Federal de São Bernardo do Campo'),
                                   ('ForumFederaldeSaoJoaodaBoaVista', 'Fórum Federal de São João da Boa Vista'),
                                   ('ForumLafayetteBeloHorizonte', 'Fórum Lafayette – Belo Horizonte'),
                                   ('ForumRegionaldaBarradaTijuca', 'Fórum Regional da Barra da Tijuca'),
                                   ('ForumRegionaldeBangu', 'Fórum Regional de Bangu'),
                                   ('ForumRuyBarbosaSalvador', 'Fórum Ruy Barbosa - Salvador'),
                                   ('JEFSAOBERNARDO', 'JEF SÃO BERNARDO'),
                                   ('JuizadodeBarraDoBugres', 'Juizado de Barra Do Bugres'),
                                   ('JuizadoEspecialFederalCiveldeBotucatu', 'Juizado Especial Federal Cível de Botucatu'),
                                   ('JuizadosEspeciaisCiveisdeRioBranco', 'Juizados Especiais Cíveis de Rio Branco'),
                                   ('TrabalhistadeBacabal', 'Trabalhista de Bacabal'),
                                   ('TrabalhistadeBage', 'Trabalhista de Bagé'),
                                   ('TrabalhistadeBalnearioCamboriu', 'Trabalhista de Balneário Camboriú'),
                                   ('TrabalhistadeBalsas', 'Trabalhista de Balsas'),
                                   ('TrabalhistadeBandeirantes', 'Trabalhista de Bandeirantes'),
                                   ('TrabalhistadeBarbacena', 'Trabalhista de Barbacena'),
                                   ('TrabalhistadeBarradoCorda', 'Trabalhista de Barra do Corda'),
                                   ('TrabalhistadeBarradoGarcas', 'Trabalhista de Barra do Garças'),
                                   ('TrabalhistadeBarradoPirai', 'Trabalhista de Barra do Piraí'),
                                   ('TrabalhistadeBarreiras', 'Trabalhista de Barreiras'),
                                   ('TrabalhistadeBarreirinhas', 'Trabalhista de Barreirinhas'),
                                   ('TrabalhistadeBarreiros', 'Trabalhista de Barreiros'),
                                   ('TrabalhistadeBarretos', 'Trabalhista de Barretos'),
                                   ('TrabalhistadeBarueri', 'Trabalhista de Barueri'),
                                   ('TrabalhistadeBataguassu', 'Trabalhista de Bataguassu'),
                                   ('TrabalhistadeBatatais', 'Trabalhista de Batatais'),
                                   ('TrabalhistadeBaturite', 'Trabalhista de Baturité'),
                                   ('TrabalhistadeBauru', 'Trabalhista de Bauru'),
                                   ('TrabalhistadeBebedouro', 'Trabalhista de Bebedouro'),
                                   ('TrabalhistadeBeloHorizonte', 'Trabalhista de Belo Horizonte'),
                                   ('TrabalhistadeBeloJardim', 'Trabalhista de Belo Jardim'),
                                   ('TrabalhistadeBelem', 'Trabalhista de Belém'),
                                   ('TrabalhistadeBentoGoncalves', 'Trabalhista de Bento Gonçalves'),
                                   ('TrabalhistadeBetim', 'Trabalhista de Betim'),
                                   ('TrabalhistadeBirigui', 'Trabalhista de Birigui'),
                                   ('TrabalhistadeBlumenau', 'Trabalhista de Blumenau'),
                                   ('TrabalhistadeBoaVista', 'Trabalhista de Boa Vista'),
                                   ('TrabalhistadeBomDespacho', 'Trabalhista de Bom Despacho'),
                                   ('TrabalhistadeBomJesusdaLapa', 'Trabalhista de Bom Jesus da Lapa'),
                                   ('TrabalhistadeBotucatu', 'Trabalhista de Botucatu'),
                                   ('TrabalhistadeBragancaPaulista', 'Trabalhista de Bragança Paulista'),
                                   ('TrabalhistadeBrasilia', 'Trabalhista de Brasília'),
                                   ('TrabalhistadeBreves', 'Trabalhista de Breves'),
                                   ('TrabalhistadeBrumado', 'Trabalhista de Brumado'),
                                   ('TrabalhistadeBrusque', 'Trabalhista de Brusque'),
                                   ('TrabalhistadeBuritis', 'Trabalhista de Buritis'),
                                   ('TrabalhistadeCapaoBonito', 'Trabalhista de Capão Bonito'),
                                   ('TrabalhistadeFranciscoBeltrao', 'Trabalhista de Francisco Beltrão'),
                                   ('TrabalhistadeJoseBonifacio', 'Trabalhista de José Bonifácio'),
                                   ('TrabalhistadePatoBranco', 'Trabalhista de Pato Branco'),
                                   ('TrabalhistadePimentaBueno', 'Trabalhista de Pimenta Bueno'),
                                   ('TrabalhistadeRioBranco', 'Trabalhista de Rio Branco'),
                                   ('TrabalhistadeRioBrilhante', 'Trabalhista de Rio Brilhante'),
                                   ('TrabalhistadeSantaBarbaraDOeste', 'Trabalhista de Santa Bárbara D''Oeste'),
                                   ('TrabalhistadeSenhordoBonfim', 'Trabalhista de Senhor do Bonfim'),
                                   ('TrabalhistadeSaoBentodoSul', 'Trabalhista de São Bento do Sul'),
                                   ('TrabalhistadeSaoBernardodoCampo', 'Trabalhista de São Bernardo do Campo'),
                                   ('TrabalhistadeSaoBorja', 'Trabalhista de São Borja'),
                                   ('TrabalhistadeSaoJoaquimdaBarra', 'Trabalhista de São Joaquim da Barra'),
                                   ('TrabalhistadeSaoJoaodaBoaVista', 'Trabalhista de São João da Boa Vista'),
                                   ('TrabalhistadeSaoLuizdeMontesBelos', 'Trabalhista de São Luiz de Montes Belos'),
                                   ('TrabalhistadeTelemacoBorba', 'Trabalhista de Telêmaco Borba'),
                                   ('TrabalhistadeWenceslauBraz', 'Trabalhista de Wenceslau Braz'),
                                   ('TrabalhistadeAguaBoa', 'Trabalhista de Água Boa'),
                                   ('TribunaldeJusticadaBahia', 'Tribunal de Justiça da Bahia'),
                                   ('ForumCentraldeJoaoPessoa', 'Fórum Central de João Pessoa'),
                                   ('ForumCentraldeManaus', 'Fórum Central de Manaus'),
                                   ('ForumCentraldeTeresina', 'Fórum Central de Teresina'),
                                   ('ForumCentraldoRiodeJaneiro', 'Fórum Central do Rio de Janeiro'),
                                   ('ForumCentraldosJuizadosEspeciais', 'Fórum Central dos Juizados Especiais'),
                                   ('ForumCriminaldeCruzeirodoSul', 'Fórum Criminal de Cruzeiro do Sul'),
                                   ('ForumCriminaldeCuritiba', 'Fórum Criminal de Curitiba'),
                                   ('ForumCriminaldeJoaoPessoa', 'Fórum Criminal de João Pessoa'),
                                   ('ForumCriminaldeLaurodeFreitas', 'Fórum Criminal de Lauro de Freitas'),
                                   ('ForumCriminaldeParanagua', 'Fórum Criminal de Paranaguá'),
                                   ('ForumCriminaldePortoVelho', 'Fórum Criminal de Porto Velho'),
                                   ('ForumCriminaldeSaoCristovao', 'Fórum Criminal de São Cristovão'),
                                   ('ForumCriminaldeTeresina', 'Fórum Criminal de Teresina'),
                                   ('ForumCiveldeCruzeirodoSul', 'Fórum Cível de Cruzeiro do Sul'),
                                   ('ForumCiveldeCuritiba', 'Fórum Cível de Curitiba'),
                                   ('ForumCiveldeLaurodeFreitas', 'Fórum Cível de Lauro de Freitas'),
                                   ('ForumCiveldeParanagua', 'Fórum Cível de Paranaguá'),
                                   ('ForumCiveldePortoVelho', 'Fórum Cível de Porto Velho'),
                                   ('ForumdeCaapiranga', 'Fórum de Caapiranga'),
                                   ('ForumdeCaapora', 'Fórum de Caaporã'),
                                   ('ForumdeCaarapo', 'Fórum de Caarapó'),
                                   ('ForumdeCabaceiras', 'Fórum de Cabaceiras'),
                                   ('ForumdeCabedelo', 'Fórum de Cabedelo'),
                                   ('ForumdeCaboFrio', 'Fórum de Cabo Frio'),
                                   ('ForumdeCaboVerde', 'Fórum de Cabo Verde'),
                                   ('ForumdeCabreuva', 'Fórum de Cabreúva'),
                                   ('ForumdeCabrobo', 'Fórum de Cabrobó'),
                                   ('ForumdeCacador', 'Fórum de Cacador'),
                                   ('ForumdeCacequi', 'Fórum de Cacequi'),
                                   ('ForumdeCachoeira', 'Fórum de Cachoeira'),
                                   ('ForumdeCachoeiradeMinas', 'Fórum de Cachoeira de Minas'),
                                   ('ForumdeCachoeiradoSul', 'Fórum de Cachoeira do Sul'),
                                   ('ForumdeCachoeiraPaulista', 'Fórum de Cachoeira Paulista'),
                                   ('ForumdeCachoeirasdeMacacu', 'Fórum de Cachoeiras de Macacu'),
                                   ('ForumdeCachoeirinhaPE', 'Fórum de Cachoeirinha - PE'),
                                   ('ForumdeCachoeirinhaRS', 'Fórum de Cachoeirinha - RS'),
                                   ('ForumdeCachoeirodeItapemirim', 'Fórum de Cachoeiro de Itapemirim'),
                                   ('ForumdeCacimbadeDentro', 'Fórum de Cacimba de Dentro'),
                                   ('ForumdeCacimbinhas', 'Fórum de Cacimbinhas'),
                                   ('ForumdeCacoal', 'Fórum de Cacoal'),
                                   ('ForumdeCaconde', 'Fórum de Caconde'),
                                   ('ForumdeCacule', 'Fórum de Caculé'),
                                   ('ForumdeCaetite', 'Fórum de Caetité'),
                                   ('ForumdeCaete', 'Fórum de Caeté'),
                                   ('ForumdeCaetes', 'Fórum de Caetés'),
                                   ('ForumdeCafelandia', 'Fórum de Cafelândia'),
                                   ('ForumdeCaiaponia', 'Fórum de Caiapônia'),
                                   ('ForumdeCaico', 'Fórum de Caicó'),
                                   ('ForumdeCaieiras', 'Fórum de Caieiras'),
                                   ('ForumdeCaicara', 'Fórum de Caiçara'),
                                   ('ForumdeCajamar', 'Fórum de Cajamar'),
                                   ('ForumdeCajazeiras', 'Fórum de Cajazeiras'),
                                   ('ForumdeCajueiro', 'Fórum de Cajueiro'),
                                   ('ForumdeCajuru', 'Fórum de Cajuru'),
                                   ('ForumdeCaldas', 'Fórum de Caldas'),
                                   ('ForumdeCaldasNovas', 'Fórum de Caldas Novas'),
                                   ('ForumdeCaldeiraoGrande', 'Fórum de Caldeirão Grande'),
                                   ('ForumdeCalcado', 'Fórum de Calçado'),
                                   ('ForumdeCalcoene', 'Fórum de Calçoene'),
                                   ('ForumdeCamaca', 'Fórum de Camacã'),
                                   ('ForumdeCamamu', 'Fórum de Camamu'),
                                   ('ForumdeCamanducaia', 'Fórum de Camanducaia'),
                                   ('ForumdeCamapua', 'Fórum de Camapuã'),
                                   ('ForumdeCamaqua', 'Fórum de Camaquã'),
                                   ('ForumdeCamaragibe', 'Fórum de Camaragibe'),
                                   ('ForumdeCamacari', 'Fórum de Camaçari'),
                                   ('ForumdeCambara', 'Fórum de Cambará'),
                                   ('ForumdeCamboriu', 'Fórum de Camboriu'),
                                   ('ForumdeCambuci', 'Fórum de Cambuci'),
                                   ('ForumdeCambuquira', 'Fórum de Cambuquira'),
                                   ('ForumdeCambui', 'Fórum de Cambuí'),
                                   ('ForumdeCambe', 'Fórum de Cambé'),
                                   ('ForumdeCameta', 'Fórum de Cametá'),
                                   ('ForumdeCamocim', 'Fórum de Camocim'),
                                   ('ForumdeCamocimdeSaoFelix', 'Fórum de Camocim de São Félix'),
                                   ('ForumdeCampanha', 'Fórum de Campanha'),
                                   ('ForumdeCampestre', 'Fórum de Campestre'),
                                   ('ForumdeCampinadaLagoa', 'Fórum de Campina da Lagoa'),
                                   ('ForumdeCampinaGrande', 'Fórum de Campina Grande'),
                                   ('ForumdeCampinaGrandedoSul', 'Fórum de Campina Grande do Sul'),
                                   ('ForumdeCampinaVerde', 'Fórum de Campina Verde'),
                                   ('ForumdeCampinas', 'Fórum de Campinas'),
                                   ('ForumdeCampinasdasMissoes', 'Fórum de Campinas das Missões'),
                                   ('ForumdeCampinasdoPiaui', 'Fórum de Campinas do Piauí'),
                                   ('ForumdeCampinasVilaMimosa', 'Fórum de Campinas – Vila Mimosa'),
                                   ('ForumdeCampinorte', 'Fórum de Campinorte'),
                                   ('ForumdeCampinapolis', 'Fórum de Campinápolis'),
                                   ('ForumdeCampoEre', 'Fórum de Campo Ere'),
                                   ('ForumdeCampoFormoso', 'Fórum de Campo Formoso'),
                                   ('ForumdeCampoGrande', 'Fórum de Campo Grande'),
                                   ('ForumdeCampoGrandeMS', 'Fórum de Campo Grande - MS'),
                                   ('ForumdeCampoLargo', 'Fórum de Campo Largo'),
                                   ('ForumdeCampoLimpoPaulista', 'Fórum de Campo Limpo Paulista'),
                                   ('ForumdeCampoMaior', 'Fórum de Campo Maior'),
                                   ('ForumdeCampoMourao', 'Fórum de Campo Mourão'),
                                   ('ForumdeCampoNovo', 'Fórum de Campo Novo'),
                                   ('ForumdeCampoNovoDoParecis', 'Fórum de Campo Novo Do Parecis'),
                                   ('ForumdeCampoVerde', 'Fórum de Campo Verde'),
                                   ('ForumdeCamposdoJordao', 'Fórum de Campos do Jordão'),
                                   ('ForumdeCamposdosGoytacazes', 'Fórum de Campos dos Goytacazes'),
                                   ('ForumdeCamposGerais', 'Fórum de Campos Gerais'),
                                   ('ForumdeCamposNovos', 'Fórum de Campos Novos'),
                                   ('ForumdeCamposSales', 'Fórum de Campos Sales'),
                                   ('ForumdeCananeia', 'Fórum de Cananéia'),
                                   ('ForumdeCanapi', 'Fórum de Canapi'),
                                   ('ForumdeCanarana', 'Fórum de Canarana'),
                                   ('ForumdeCanaranaMT', 'Fórum de Canarana - MT'),
                                   ('ForumdeCanavieiras', 'Fórum de Canavieiras'),
                                   ('ForumdeCanaaDosCarajas', 'Fórum de Canaã Dos Carajás'),
                                   ('ForumdeCandeiasMG', 'Fórum de Candeias - MG'),
                                   ('ForumdeCandelaria', 'Fórum de Candelária'),
                                   ('ForumdeCanela', 'Fórum de Canela'),
                                   ('ForumdeCanguaretama', 'Fórum de Canguaretama'),
                                   ('ForumdeCangucu', 'Fórum de Canguçu'),
                                   ('ForumdeCanhoba', 'Fórum de Canhoba'),
                                   ('ForumdeCanhotinho', 'Fórum de Canhotinho'),
                                   ('ForumdeCaninde', 'Fórum de Canindé'),
                                   ('ForumdeCanindedoSaoFrancisco', 'Fórum de Canindé do São Francisco'),
                                   ('ForumdeCanoas', 'Fórum de Canoas'),
                                   ('ForumdeCanoinhas', 'Fórum de Canoinhas'),
                                   ('ForumdeCansancao', 'Fórum de Cansanção'),
                                   ('ForumdeCantagalo', 'Fórum de Cantagalo'),
                                   ('ForumdeCantagaloPR', 'Fórum de Cantagalo - PR'),
                                   ('ForumdeCantanhede', 'Fórum de Cantanhede'),
                                   ('ForumdeCanutama', 'Fórum de Canutama'),
                                   ('ForumdeCanapolis', 'Fórum de Canápolis'),
                                   ('ForumdeCapanema', 'Fórum de Capanema'),
                                   ('ForumdeCapanemaPA', 'Fórum de Capanema - PA'),
                                   ('ForumdeCapela', 'Fórum de Capela'),
                                   ('ForumdeCapelinha', 'Fórum de Capelinha'),
                                   ('ForumdeCapimGrosso', 'Fórum de Capim Grosso'),
                                   ('ForumdeCapinzal', 'Fórum de Capinzal'),
                                   ('ForumdeCapinopolis', 'Fórum de Capinópolis'),
                                   ('ForumdeCapistrano', 'Fórum de Capistrano'),
                                   ('ForumdeCapitaodeCampos', 'Fórum de Capitão de Campos'),
                                   ('ForumdeCapitaoLeonidasMarques', 'Fórum de Capitão Leônidas Marques'),
                                   ('ForumdeCapitaoPoco', 'Fórum de Capitão Poço'),
                                   ('ForumdeCapivari', 'Fórum de Capivari'),
                                   ('ForumdeCapixaba', 'Fórum de Capixaba'),
                                   ('ForumdeCapoeiras', 'Fórum de Capoeiras'),
                                   ('ForumdeCapaodaCanoa', 'Fórum de Capão da Canoa'),
                                   ('ForumdeCaracarai', 'Fórum de Caracaraí'),
                                   ('ForumdeCaracol', 'Fórum de Caracol'),
                                   ('ForumdeCaraguatatuba', 'Fórum de Caraguatatuba'),
                                   ('ForumdeCarandai', 'Fórum de Carandaí'),
                                   ('ForumdeCarangola', 'Fórum de Carangola'),
                                   ('ForumdeCaratinga', 'Fórum de Caratinga'),
                                   ('ForumdeCarauari', 'Fórum de Carauari'),
                                   ('ForumdeCaravelas', 'Fórum de Caravelas'),
                                   ('ForumdeCarazinho', 'Fórum de Carazinho'),
                                   ('ForumdeCaraubas', 'Fórum de Caraúbas'),
                                   ('ForumdeCardoso', 'Fórum de Cardoso'),
                                   ('ForumdeCareiro', 'Fórum de Careiro'),
                                   ('ForumdeCareiroDaVarzea', 'Fórum de Careiro Da Várzea'),
                                   ('ForumdeCariacicaCampoGrande', 'Fórum de Cariacica - Campo Grande'),
                                   ('ForumdeCaridade', 'Fórum de Caridade'),
                                   ('ForumdeCarinhanha', 'Fórum de Carinhanha'),
                                   ('ForumdeCarira', 'Fórum de Carira'),
                                   ('ForumdeCarire', 'Fórum de Cariré'),
                                   ('ForumdeCarlosChagas', 'Fórum de Carlos Chagas'),
                                   ('ForumdeCarlopolis', 'Fórum de Carlópolis'),
                                   ('ForumdeCarmo', 'Fórum de Carmo'),
                                   ('ForumdeCarmodaMata', 'Fórum de Carmo da Mata'),
                                   ('ForumdeCarmodeMinas', 'Fórum de Carmo de Minas'),
                                   ('ForumdeCarmodoCajuru', 'Fórum de Carmo do Cajuru'),
                                   ('ForumdeCarmodoParanaiba', 'Fórum de Carmo do Paranaíba'),
                                   ('ForumdeCarmodoRioClaro', 'Fórum de Carmo do Rio Claro'),
                                   ('ForumdeCarmoDoRioVerde', 'Fórum de Carmo Do Rio Verde'),
                                   ('ForumdeCarmopolis', 'Fórum de Carmópolis'),
                                   ('ForumdeCarnaubal', 'Fórum de Carnaubal'),
                                   ('ForumdeCarnaiba', 'Fórum de Carnaíba'),
                                   ('ForumdeCarolina', 'Fórum de Carolina'),
                                   ('ForumdeCarpina', 'Fórum de Carpina'),
                                   ('ForumdeCaruaru', 'Fórum de Caruaru'),
                                   ('ForumdeCarutapera', 'Fórum de Carutapera'),
                                   ('ForumdeCasaNova', 'Fórum de Casa Nova'),
                                   ('ForumdeCasca', 'Fórum de Casca'),
                                   ('ForumdeCascavel', 'Fórum de Cascavel'),
                                   ('ForumdeCascavelCE', 'Fórum de Cascavel - CE'),
                                   ('ForumdeCassilandia', 'Fórum de Cassilândia'),
                                   ('ForumdeCastanhal', 'Fórum de Castanhal'),
                                   ('ForumdeCastelo', 'Fórum de Castelo'),
                                   ('ForumdeCastelodoPiaui', 'Fórum de Castelo do Piauí'),
                                   ('ForumdeCastro', 'Fórum de Castro'),
                                   ('ForumdeCataguases', 'Fórum de Cataguases'),
                                   ('ForumdeCatalao', 'Fórum de Catalão'),
                                   ('ForumdeCatanduva', 'Fórum de Catanduva'),
                                   ('ForumdeCatanduvas', 'Fórum de Catanduvas'),
                                   ('ForumdeCatanduvasPR', 'Fórum de Catanduvas - PR'),
                                   ('ForumdeCatarina', 'Fórum de Catarina'),
                                   ('ForumdeCatende', 'Fórum de Catende'),
                                   ('ForumdeCatoledoRocha', 'Fórum de Catolé do Rocha'),
                                   ('ForumdeCatu', 'Fórum de Catu'),
                                   ('ForumdeCatuipe', 'Fórum de Catuípe'),
                                   ('ForumdeCaucaia', 'Fórum de Caucaia'),
                                   ('ForumdeCavalcante', 'Fórum de Cavalcante'),
                                   ('ForumdeCaxambu', 'Fórum de Caxambu'),
                                   ('ForumdeCaxias', 'Fórum de Caxias'),
                                   ('ForumdeCaxiasdoSul', 'Fórum de Caxias do Sul'),
                                   ('ForumdeCacapava', 'Fórum de Caçapava'),
                                   ('ForumdeCacapavadoSul', 'Fórum de Caçapava do Sul'),
                                   ('ForumdeCacu', 'Fórum de Caçu'),
                                   ('ForumdeCearaMirim', 'Fórum de Ceará-Mirim'),
                                   ('ForumdeCedral', 'Fórum de Cedral'),
                                   ('ForumdeCedro', 'Fórum de Cedro'),
                                   ('ForumdeCedrodeSaoJoao', 'Fórum de Cedro de São João'),
                                   ('ForumdeCeilandia', 'Fórum de Ceilândia'),
                                   ('ForumdeCentenariodoSul', 'Fórum de Centenário do Sul'),
                                   ('ForumdeCentral', 'Fórum de Central'),
                                   ('ForumdeCerejeiras', 'Fórum de Cerejeiras'),
                                   ('ForumdeCeres', 'Fórum de Ceres'),
                                   ('ForumdeCerqueiraCesar', 'Fórum de Cerqueira César'),
                                   ('ForumdeCerquilho', 'Fórum de Cerquilho'),
                                   ('ForumdeCerroLargo', 'Fórum de Cerro Largo'),
                                   ('ForumdeChapadaDosGuimaraes', 'Fórum de Chapada Dos Guimarães'),
                                   ('ForumdeChapadinha', 'Fórum de Chapadinha'),
                                   ('ForumdeChapadaoDoSul', 'Fórum de Chapadão Do Sul')
                                   ;
                                   INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumdeChapeco', 'Fórum de Chapeco'),
                                   ('ForumdeCharqueadas', 'Fórum de Charqueadas'),
                                   ('ForumdeChaval', 'Fórum de Chaval'),
                                   ('ForumdeChavantes', 'Fórum de Chavantes'),
                                   ('ForumdeChaves', 'Fórum de Chaves'),
                                   ('ForumdeChopinzinho', 'Fórum de Chopinzinho'),
                                   ('ForumdeChorrocho', 'Fórum de Chorrochó'),
                                   ('ForumdeChaGrande', 'Fórum de Chã Grande'),
                                   ('ForumdeChaPreta', 'Fórum de Chã Preta'),
                                   ('ForumdeCianorte', 'Fórum de Cianorte'),
                                   ('ForumdeCidadeGaucha', 'Fórum de Cidade Gaúcha'),
                                   ('ForumdeCidadeOcidental', 'Fórum de Cidade Ocidental'),
                                   ('ForumdeCipo', 'Fórum de Cipó'),
                                   ('ForumdeClevelandia', 'Fórum de Clevelândia'),
                                   ('ForumdeClaudia', 'Fórum de Cláudia'),
                                   ('ForumdeClaudio', 'Fórum de Cláudio'),
                                   ('ForumdeCoaraci', 'Fórum de Coaraci'),
                                   ('ForumdeCoari', 'Fórum de Coari'),
                                   ('ForumdeCocal', 'Fórum de Cocal'),
                                   ('ForumdeCodajas', 'Fórum de Codajás'),
                                   ('ForumdeCodo', 'Fórum de Codó'),
                                   ('ForumdeCoelhoNeto', 'Fórum de Coelho Neto'),
                                   ('ForumdeColatina', 'Fórum de Colatina'),
                                   ('ForumdeColina', 'Fórum de Colina'),
                                   ('ForumdeColinas', 'Fórum de Colinas'),
                                   ('ForumdeColinasDoTocantins', 'Fórum de Colinas Do Tocantins'),
                                   ('ForumdeColmeia', 'Fórum de Colméia'),
                                   ('ForumdeColniza', 'Fórum de Colniza'),
                                   ('ForumdeColombo', 'Fórum de Colombo'),
                                   ('ForumdeColorado', 'Fórum de Colorado'),
                                   ('ForumdeColoradoDoOeste', 'Fórum de Colorado Do Oeste'),
                                   ('ForumdeColider', 'Fórum de Colíder'),
                                   ('ForumdeColoniaLeopoldina', 'Fórum de Colônia Leopoldina'),
                                   ('ForumdeComodoro', 'Fórum de Comodoro'),
                                   ('ForumdeConceicaodeMacabu', 'Fórum de Conceicao de Macabu'),
                                   ('ForumdeConceicao', 'Fórum de Conceição'),
                                   ('ForumdeConceicaodaFeira', 'Fórum de Conceição da Feira'),
                                   ('ForumdeConceicaodoCaninde', 'Fórum de Conceição do Canindé'),
                                   ('ForumdeConceicaodoCastelo', 'Fórum de Conceição do Castelo'),
                                   ('ForumdeConceicaodoCoite', 'Fórum de Conceição do Coité'),
                                   ('ForumdeConceicaodoJacuipe', 'Fórum de Conceição do Jacuípe'),
                                   ('ForumdeConceicaodoMatoDentro', 'Fórum de Conceição do Mato Dentro'),
                                   ('ForumdeConceicaodoRioVerde', 'Fórum de Conceição do Rio Verde'),
                                   ('ForumdeConchal', 'Fórum de Conchal'),
                                   ('ForumdeConchas', 'Fórum de Conchas'),
                                   ('ForumdeConcordia', 'Fórum de Concordia'),
                                   ('ForumdeConcordiaDoPara', 'Fórum de Concórdia Do Pará'),
                                   ('ForumdeCondado', 'Fórum de Condado'),
                                   ('ForumdeConde', 'Fórum de Conde'),
                                   ('ForumdeCondeuba', 'Fórum de Condeúba'),
                                   ('ForumdeCongonhas', 'Fórum de Congonhas'),
                                   ('ForumdeCongonhinhas', 'Fórum de Congonhinhas'),
                                   ('ForumdeConquista', 'Fórum de Conquista'),
                                   ('ForumdeConselheiroLafaiete', 'Fórum de Conselheiro Lafaiete'),
                                   ('ForumdeConselheiroPena', 'Fórum de Conselheiro Pena'),
                                   ('ForumdeConstantina', 'Fórum de Constantina'),
                                   ('ForumdeContagem', 'Fórum de Contagem'),
                                   ('ForumdeCoracaodeJesus', 'Fórum de Coração de Jesus'),
                                   ('ForumdeCoracaodeMaria', 'Fórum de Coração de Maria'),
                                   ('ForumdeCorbelia', 'Fórum de Corbélia'),
                                   ('ForumdeCordeiro', 'Fórum de Cordeiro'),
                                   ('ForumdeCordeiropolis', 'Fórum de Cordeirópolis'),
                                   ('ForumdeCoreau', 'Fórum de Coreau'),
                                   ('ForumdeCoremas', 'Fórum de Coremas'),
                                   ('ForumdeCoribe', 'Fórum de Coribe'),
                                   ('ForumdeCorinto', 'Fórum de Corinto'),
                                   ('ForumdeCornelioProcopio', 'Fórum de Cornélio Procópio'),
                                   ('ForumdeCoroata', 'Fórum de Coroatá'),
                                   ('ForumdeCoromandel', 'Fórum de Coromandel'),
                                   ('ForumdeCoronelFabriciano', 'Fórum de Coronel Fabriciano'),
                                   ('ForumdeCoronelFreitas', 'Fórum de Coronel Freitas'),
                                   ('ForumdeCoronelVivida', 'Fórum de Coronel Vivida'),
                                   ('ForumdeCorreiaPinto', 'Fórum de Correia Pinto'),
                                   ('ForumdeCorrente', 'Fórum de Corrente'),
                                   ('ForumdeCorrentes', 'Fórum de Correntes'),
                                   ('ForumdeCorrentina', 'Fórum de Correntina'),
                                   ('ForumdeCortes', 'Fórum de Cortês'),
                                   ('ForumdeCorumbaiba', 'Fórum de Corumbaíba'),
                                   ('ForumdeCorumba', 'Fórum de Corumbá'),
                                   ('ForumdeCorumbaDeGoias', 'Fórum de Corumbá De Goiás'),
                                   ('ForumdeCoruripe', 'Fórum de Coruripe'),
                                   ('ForumdeCosmopolis', 'Fórum de Cosmópolis'),
                                   ('ForumdeCostaMarques', 'Fórum de Costa Marques'),
                                   ('ForumdeCostaRica', 'Fórum de Costa Rica'),
                                   ('ForumdeCotegipe', 'Fórum de Cotegipe'),
                                   ('ForumdeCotia', 'Fórum de Cotia'),
                                   ('ForumdeCotriguacu', 'Fórum de Cotriguaçu'),
                                   ('ForumdeCoxim', 'Fórum de Coxim'),
                                   ('ForumdeCrateus', 'Fórum de Crateús'),
                                   ('ForumdeCrato', 'Fórum de Crato'),
                                   ('ForumdeCravinhos', 'Fórum de Cravinhos'),
                                   ('ForumdeCriciuma', 'Fórum de Criciuma'),
                                   ('ForumdeCrissiumal', 'Fórum de Crissiumal'),
                                   ('ForumdeCristalina', 'Fórum de Cristalina'),
                                   ('ForumdeCristalandia', 'Fórum de Cristalândia'),
                                   ('ForumdeCristalandiadoPiaui', 'Fórum de Cristalândia do Piauí'),
                                   ('ForumdeCristina', 'Fórum de Cristina'),
                                   ('ForumdeCristinoCastro', 'Fórum de Cristino Castro'),
                                   ('ForumdeCristinapolis', 'Fórum de Cristinápolis'),
                                   ('ForumdeCristopolis', 'Fórum de Cristópolis'),
                                   ('ForumdeCrixas', 'Fórum de Crixás'),
                                   ('ForumdeCrominia', 'Fórum de Cromínia'),
                                   ('ForumdeCruz', 'Fórum de Cruz'),
                                   ('ForumdeCruzdoEspiritoSanto', 'Fórum de Cruz do Espírito Santo'),
                                   ('ForumdeCruzeiro', 'Fórum de Cruzeiro'),
                                   ('ForumdeCruzeirodoOeste', 'Fórum de Cruzeiro do Oeste'),
                                   ('ForumdeCruzeta', 'Fórum de Cruzeta'),
                                   ('ForumdeCruzilia', 'Fórum de Cruzília'),
                                   ('ForumdeCubatao', 'Fórum de Cubatão'),
                                   ('ForumdeCuiaba', 'Fórum de Cuiabá'),
                                   ('ForumdeCuite', 'Fórum de Cuité'),
                                   ('ForumdeCumari', 'Fórum de Cumari'),
                                   ('ForumdeCumaru', 'Fórum de Cumaru'),
                                   ('ForumdeCumbe', 'Fórum de Cumbe'),
                                   ('ForumdeCunha', 'Fórum de Cunha'),
                                   ('ForumdeCunhaPora', 'Fórum de Cunha Pora'),
                                   ('ForumdeCupira', 'Fórum de Cupira'),
                                   ('ForumdeCuraca', 'Fórum de Curaçá'),
                                   ('ForumdeCurimata', 'Fórum de Curimatá'),
                                   ('ForumdeCurionopolis', 'Fórum de Curionópolis'),
                                   ('ForumdeCuritibaSedeMaua', 'Fórum de Curitiba - Sede Mauá'),
                                   ('ForumdeCuritibanos', 'Fórum de Curitibanos'),
                                   ('ForumdeCuriuva', 'Fórum de Curiúva'),
                                   ('ForumdeCurraisNovos', 'Fórum de Currais Novos'),
                                   ('ForumdeCurralinho', 'Fórum de Curralinho'),
                                   ('ForumdeCururupu', 'Fórum de Cururupu'),
                                   ('ForumdeCuruca', 'Fórum de Curuça'),
                                   ('ForumdeCurvelo', 'Fórum de Curvelo'),
                                   ('ForumdeCustodia', 'Fórum de Custódia'),
                                   ('ForumdeCaceres', 'Fórum de Cáceres'),
                                   ('ForumdeCassia', 'Fórum de Cássia'),
                                   ('ForumdeCandidoMendes', 'Fórum de Cândido Mendes'),
                                   ('ForumdeCandidoMota', 'Fórum de Cândido Mota'),
                                   ('ForumdeCandidoSales', 'Fórum de Cândido Sales'),
                                   ('ForumdeCiceroDantas', 'Fórum de Cícero Dantas'),
                                   ('ForumdeCocos', 'Fórum de Côcos'),
                                   ('ForumdeDoisCorregos', 'Fórum de Dois Córregos'),
                                   ('ForumdeDuquedeCaxias', 'Fórum de Duque de Caxias'),
                                   ('ForumdeEuclidesdaCunha', 'Fórum de Euclides da Cunha'),
                                   ('ForumdeFloresdaCunha', 'Fórum de Flores da Cunha'),
                                   ('ForumdeGeneralCamara', 'Fórum de General Câmara'),
                                   ('ForumdeGracchoCardoso', 'Fórum de Graccho Cardoso'),
                                   ('ForumdeHumbertodeCampos', 'Fórum de Humberto de Campos'),
                                   ('ForumdeIsaiasCoelho', 'Fórum de Isaias Coelho'),
                                   ('ForumdeJardimCE', 'Fórum de Jardim - CE'),
                                   ('ForumdeJoaoCamara', 'Fórum de João Câmara'),
                                   ('ForumdeJuliodeCastilhos', 'Fórum de Júlio de Castilhos'),
                                   ('ForumdeLuizCorreia', 'Fórum de Luiz Correia'),
                                   ('ForumdeMaceioCentral', 'Fórum de Maceió - Central'),
                                   ('ForumdeMarechalCandidoRondon', 'Fórum de Marechal Cândido Rondon'),
                                   ('ForumdeMartinhoCampos', 'Fórum de Martinho Campos'),
                                   ('ForumdeMatrizdoCamaragibe', 'Fórum de Matriz do Camaragibe'),
                                   ('ForumdeMiguelCalmon', 'Fórum de Miguel Calmon'),
                                   ('ForumdeMogidasCruzes', 'Fórum de Mogi das Cruzes'),
                                   ('ForumdeMonteCarmelo', 'Fórum de Monte Carmelo'),
                                   ('ForumdeMontesClaros', 'Fórum de Montes Claros'),
                                   ('ForumdeMontesClarosDeGoias', 'Fórum de Montes Claros De Goiás'),
                                   ('ForumdeMorrodoChapeu', 'Fórum de Morro do Chapéu'),
                                   ('ForumdeNovaCanaa', 'Fórum de Nova Canaã'),
                                   ('ForumdeNovaCanaaDoNorte', 'Fórum de Nova Canaã Do Norte'),
                                   ('ForumdeNovaCruz', 'Fórum de Nova Cruz'),
                                   ('ForumdeNovoCruzeiro', 'Fórum de Novo Cruzeiro'),
                                   ('ForumdeOlhoDaguadasCunhas', 'Fórum de Olho D’água das Cunhãs'),
                                   ('ForumdeOsvaldoCruz', 'Fórum de Osvaldo Cruz'),
                                   ('ForumdeOtacilioCosta', 'Fórum de Otacilio Costa'),
                                   ('ForumdePacatubaCE', 'Fórum de Pacatuba - CE'),
                                   ('ForumdePassodeCamaragibe', 'Fórum de Passo de Camaragibe'),
                                   ('ForumdePedroCanario', 'Fórum de Pedro Canário'),
                                   ('ForumdePlacidoDeCastro', 'Fórum de Plácido De Castro'),
                                   ('ForumdePortoCalvo', 'Fórum de Porto Calvo'),
                                   ('ForumdePortoRealdoColegio', 'Fórum de Porto Real do Colégio'),
                                   ('ForumdePocosdeCaldas', 'Fórum de Poços de Caldas'),
                                   ('ForumdePrimeiraCruz', 'Fórum de Primeira Cruz'),
                                   ('ForumdeResendeCosta', 'Fórum de Resende Costa'),
                                   ('ForumdeRibeiraoCascalheira', 'Fórum de Ribeirão Cascalheira'),
                                   ('ForumdeRibeiraoClaro', 'Fórum de Ribeirão Claro'),
                                   ('ForumdeRioCasca', 'Fórum de Rio Casca'),
                                   ('ForumdeRioClaro', 'Fórum de Rio Claro'),
                                   ('ForumdeRioClaroRJ', 'Fórum de Rio Claro - RJ'),
                                   ('ForumdeRiodeContas', 'Fórum de Rio de Contas'),
                                   ('ForumdeRiodoCampo', 'Fórum de Rio do Campo'),
                                   ('ForumdeRosariodoCatete', 'Fórum de Rosário do Catete'),
                                   ('ForumdeSantaCecilia', 'Fórum de Santa Cecilia'),
                                   ('ForumdeSantaCruz', 'Fórum de Santa Cruz'),
                                   ('ForumdeSantaCruzdasPalmeiras', 'Fórum de Santa Cruz das Palmeiras'),
                                   ('ForumdeSantaCruzdeCabralia', 'Fórum de Santa Cruz de Cabrália'),
                                   ('ForumdeSantaCruzDeGoias', 'Fórum de Santa Cruz De Goiás'),
                                   ('ForumdeSantaCruzdoCapibaribe', 'Fórum de Santa Cruz do Capibaribe'),
                                   ('ForumdeSantaCruzdoPiaui', 'Fórum de Santa Cruz do Piauí'),
                                   ('ForumdeSantaCruzdoRioPardo', 'Fórum de Santa Cruz do Rio Pardo'),
                                   ('ForumdeSantaCruzdoSul', 'Fórum de Santa Cruz do Sul'),
                                   ('ForumdeSantaMariadoCambuca', 'Fórum de Santa Maria do Cambucá'),
                                   ('ForumdeSantaRitadeCaldas', 'Fórum de Santa Rita de Caldas'),
                                   ('ForumdeSantaRitadeCassia', 'Fórum de Santa Rita de Cássia'),
                                   ('ForumdeSantanaDoCariri', 'Fórum de Santana Do Cariri'),
                                   ('ForumdeSantoCristo', 'Fórum de Santo Cristo'),
                                   ('ForumdeSenadorCanedo', 'Fórum de Senador Canedo'),
                                   ('ForumdeSerraDesJoaoManoeldeCarvalho', 'Fórum de Serra - Des. João Manoel de Carvalho'),
                                   ('ForumdeSiqueiraCampos', 'Fórum de Siqueira Campos'),
                                   ('ForumdeSaoCaetano', 'Fórum de São Caetano'),
                                   ('ForumdeSaoCaetanoDeOdivelas', 'Fórum de São Caetano De Odivelas'),
                                   ('ForumdeSaoCarlosSC', 'Fórum de São Carlos - SC'),
                                   ('ForumdeSaoCarlosSP', 'Fórum de São Carlos - SP'),
                                   ('ForumdeSaoCristovao', 'Fórum de São Cristovão'),
                                   ('ForumdeSaoDomingosDoCapim', 'Fórum de São Domingos Do Capim'),
                                   ('ForumdeSaoFranciscodoConde', 'Fórum de São Francisco do Conde'),
                                   ('ForumdeSaoGabrieldaCachoeira', 'Fórum de São Gabriel da Cachoeira'),
                                   ('ForumdeSaoGoncalodosCampos', 'Fórum de São Gonçalo dos Campos'),
                                   ('ForumdeSaoJosedaCoroaGrande', 'Fórum de São José da Coroa Grande'),
                                   ('ForumdeSaoJosedoCalcado', 'Fórum de São José do Calçado'),
                                   ('ForumdeSaoJosedoCampestre', 'Fórum de São José do Campestre'),
                                   ('ForumdeSaoJosedoCedro', 'Fórum de São José do Cedro'),
                                   ('ForumdeSaoJoseDoRioClaro', 'Fórum de São José Do Rio Claro'),
                                   ('ForumdeSaoJosedosCampos', 'Fórum de São José dos Campos'),
                                   ('ForumdeSaoJoaodoCariri', 'Fórum de São João do Cariri'),
                                   ('ForumdeSaoLuisDoCuru', 'Fórum de São Luis Do Curu'),
                                   ('ForumdeSaoMateusCentro', 'Fórum de São Mateus - Centro'),
                                   ('ForumdeSaoMigueldosCampos', 'Fórum de São Miguel dos Campos'),
                                   ('ForumdeSaoSebastiaodoCai', 'Fórum de São Sebastião do Caí'),
                                   ('ForumdeTrombudoCentral', 'Fórum de Trombudo Central'),
                                   ('ForumdeTresCoracoes', 'Fórum de Três Corações'),
                                   ('ForumdeTresCoroas', 'Fórum de Três Coroas'),
                                   ('ForumdeVeraCruz', 'Fórum de Vera Cruz'),
                                   ('ForumdeVicosaDoCeara', 'Fórum de Vicosa Do Ceara'),
                                   ('ForumdeVitoriadaConquista', 'Fórum de Vitória da Conquista'),
                                   ('ForumdeAguaClara', 'Fórum de Água Clara'),
                                   ('ForumDesCarlosSoutoSalvador', 'Fórum Des. Carlos Souto - Salvador'),
                                   ('ForumFederalCriminalPrevidenciariodeSaoPaulo', 'Fórum Federal Criminal/Previdenciário de São Paulo'),
                                   ('ForumFederalCiveldeSaoPaulo', 'Fórum Federal Cível de São Paulo'),
                                   ('ForumFederaldeCachoeiraDoSul', 'Fórum Federal de Cachoeira Do Sul'),
                                   ('ForumFederaldeCachoeirodeItapemirim', 'Fórum Federal de Cachoeiro de Itapemirim'),
                                   ('ForumFederaldeCaico', 'Fórum Federal de Caicó'),
                                   ('ForumFederaldeCampinaGrande', 'Fórum Federal de Campina Grande'),
                                   ('ForumFederaldeCampinas', 'Fórum Federal de Campinas'),
                                   ('ForumFederaldeCampoFormoso', 'Fórum Federal de Campo Formoso'),
                                   ('ForumFederaldeCampoGrande', 'Fórum Federal de Campo Grande'),
                                   ('ForumFederaldeCampoMourao', 'Fórum Federal de Campo Mourão'),
                                   ('ForumFederaldeCamposDosGoytacazes', 'Fórum Federal de Campos Dos Goytacazes'),
                                   ('ForumFederaldeCanoas', 'Fórum Federal de Canoas'),
                                   ('ForumFederaldeCarazinho', 'Fórum Federal de Carazinho'),
                                   ('ForumFederaldeCaruaru', 'Fórum Federal de Caruaru'),
                                   ('ForumFederaldeCascavel', 'Fórum Federal de Cascavel'),
                                   ('ForumFederaldeCastanhal', 'Fórum Federal de Castanhal'),
                                   ('ForumFederaldeCaxias', 'Fórum Federal de Caxias'),
                                   ('ForumFederaldeCaxiasDoSul', 'Fórum Federal de Caxias Do Sul'),
                                   ('ForumFederaldeCacador', 'Fórum Federal de Caçador'),
                                   ('ForumFederaldeChapeco', 'Fórum Federal de Chapecó'),
                                   ('ForumFederaldeColatina', 'Fórum Federal de Colatina'),
                                   ('ForumFederaldeConcordia', 'Fórum Federal de Concórdia'),
                                   ('ForumFederaldeCorumba', 'Fórum Federal de Corumbá'),
                                   ('ForumFederaldeCoxim', 'Fórum Federal de Coxim'),
                                   ('ForumFederaldeCriciuma', 'Fórum Federal de Criciúma'),
                                   ('ForumFederaldeCuiaba', 'Fórum Federal de Cuiabá'),
                                   ('ForumFederaldeCuritiba', 'Fórum Federal de Curitiba'),
                                   ('ForumFederaldeCarceres', 'Fórum Federal de Cárceres'),
                                   ('ForumFederaldeMontesClaros', 'Fórum Federal de Montes Claros'),
                                   ('ForumFederaldeSantaCruzDoSul', 'Fórum Federal de Santa Cruz Do Sul'),
                                   ('ForumFederaldeSaoCarlos', 'Fórum Federal de São Carlos'),
                                   ('ForumFederaldeSaoJosedosCampos', 'Fórum Federal de São José dos Campos'),
                                   ('ForumFederaldeVitoriaDaConquista', 'Fórum Federal de Vitória Da Conquista'),
                                   ('ForumRegionaldeCampoGrande', 'Fórum Regional de Campo Grande'),
                                   ('ForumRegionaldeSantaCruz', 'Fórum Regional de Santa Cruz'),
                                   ('JeccdeSaoMigueldosCampos', 'Jecc de São Miguel dos Campos'),
                                   ('JEFdeCuritiba', 'JEF de Curitiba'),
                                   ('JuizadoCristoRei', 'Juizado - Cristo Rei'),
                                   ('JuizadodeCanarana', 'Juizado de Canarana'),
                                   ('JuizadoEspecialCentro', 'Juizado Especial - Centro'),
                                   ('JuizadoEspecialCivelMoradadaSerra', 'Juizado Especial Cível - Morada da Serra'),
                                   ('JuizadoEspecialCivelParqueCuiaba', 'Juizado Especial Cível - Parque Cuiabá'),
                                   ('JuizadoEspecialCivelPlanalto', 'Juizado Especial Cível - Planalto'),
                                   ('JuizadoEspecialCivelPortoUnic', 'Juizado Especial Cível - Porto (Unic)'),
                                   ('JuizadoEspecialCivelTijucal', 'Juizado Especial Cível - Tijucal'),
                                   ('JuizadoEspecialCiveldeCruzeirodoSul', 'Juizado Especial Cível de Cruzeiro do Sul'),
                                   ('JuizadoEspecialdeCampoLargo', 'Juizado Especial de Campo Largo'),
                                   ('JuizadoEspecialdoConsumidor', 'Juizado Especial do Consumidor'),
                                   ('JuizadoEspecialFederalCivel', 'Juizado Especial Federal Cível'),
                                   ('JuizadoEspecialFederalCiveldeCaraguatatuba', 'Juizado Especial Federal Cível de Caraguatatuba'),
                                   ('JuizadoEspecialFederalCiveldeLins', 'Juizado Especial Federal Cível de Lins'),
                                   ('JuizadoEspecialFederalCiveldeMogidasCruzes', 'Juizado Especial Federal Cível de Mogi das Cruzes'),
                                   ('JuizadoEspecialFederalCiveldeOsasco', 'Juizado Especial Federal Cível de Osasco'),
                                   ('JuizadoEspecialFederalCiveldeRegistro', 'Juizado Especial Federal Cível de Registro'),
                                   ('JuizadoEspecialFederaldeCampinas', 'Juizado Especial Federal de Campinas'),
                                   ('JuizadoEspecialFederaldeCampoGrande', 'Juizado Especial Federal de Campo Grande'),
                                   ('JuizadosEspeciaisdeCuritiba', 'Juizados Especiais de Curitiba'),
                                   ('PRIMEIRACAMARACRIMINAL', 'PRIMEIRA CÂMARA CRIMINAL'),
                                   ('TrabalhistadeCaboFrio', 'Trabalhista de Cabo Frio'),
                                   ('TrabalhistadeCachoeiradoSul', 'Trabalhista de Cachoeira do Sul'),
                                   ('TrabalhistadeCachoeirinha', 'Trabalhista de Cachoeirinha'),
                                   ('TrabalhistadeCachoeirodoItapemirim', 'Trabalhista de Cachoeiro do Itapemirim'),
                                   ('TrabalhistadeCacoal', 'Trabalhista de Cacoal'),
                                   ('TrabalhistadeCaico', 'Trabalhista de Caicó'),
                                   ('TrabalhistadeCaieiras', 'Trabalhista de Caieiras'),
                                   ('TrabalhistadeCajamar', 'Trabalhista de Cajamar'),
                                   ('TrabalhistadeCajazeiras', 'Trabalhista de Cajazeiras'),
                                   ('TrabalhistadeCajuru', 'Trabalhista de Cajuru'),
                                   ('TrabalhistadeCaldasNovas', 'Trabalhista de Caldas Novas'),
                                   ('TrabalhistadeCamacan', 'Trabalhista de Camacan'),
                                   ('TrabalhistadeCamaqua', 'Trabalhista de Camaquã'),
                                   ('TrabalhistadeCamacari', 'Trabalhista de Camaçari'),
                                   ('TrabalhistadeCambe', 'Trabalhista de Cambé'),
                                   ('TrabalhistadeCampinaGrande', 'Trabalhista de Campina Grande'),
                                   ('TrabalhistadeCampinas', 'Trabalhista de Campinas'),
                                   ('TrabalhistadeCampoGrande', 'Trabalhista de Campo Grande'),
                                   ('TrabalhistadeCampoLimpoPaulista', 'Trabalhista de Campo Limpo Paulista'),
                                   ('TrabalhistadeCampoMourao', 'Trabalhista de Campo Mourão'),
                                   ('TrabalhistadeCamposdosGoytacazes', 'Trabalhista de Campos dos Goytacazes'),
                                   ('TrabalhistadeCandeias', 'Trabalhista de Candeias'),
                                   ('TrabalhistadeCanoas', 'Trabalhista de Canoas'),
                                   ('TrabalhistadeCanoinhas', 'Trabalhista de Canoinhas'),
                                   ('TrabalhistadeCapanema', 'Trabalhista de Capanema'),
                                   ('TrabalhistadeCapivari', 'Trabalhista de Capivari'),
                                   ('TrabalhistadeCaraguatatuba', 'Trabalhista de Caraguatatuba'),
                                   ('TrabalhistadeCarapicuiba', 'Trabalhista de Carapicuíba'),
                                   ('TrabalhistadeCaratinga', 'Trabalhista de Caratinga'),
                                   ('TrabalhistadeCarazinho', 'Trabalhista de Carazinho'),
                                   ('TrabalhistadeCarpina', 'Trabalhista de Carpina'),
                                   ('TrabalhistadeCascavel', 'Trabalhista de Cascavel'),
                                   ('TrabalhistadeCassilandia', 'Trabalhista de Cassilândia'),
                                   ('TrabalhistadeCastanhal', 'Trabalhista de Castanhal'),
                                   ('TrabalhistadeCastro', 'Trabalhista de Castro'),
                                   ('TrabalhistadeCataguases', 'Trabalhista de Cataguases'),
                                   ('TrabalhistadeCatalao', 'Trabalhista de Catalão'),
                                   ('TrabalhistadeCatanduva', 'Trabalhista de Catanduva'),
                                   ('TrabalhistadeCatente', 'Trabalhista de Catente'),
                                   ('TrabalhistadeCatoledoRocha', 'Trabalhista de Catolé do Rocha'),
                                   ('TrabalhistadeCaucaia', 'Trabalhista de Caucaia'),
                                   ('TrabalhistadeCaxambu', 'Trabalhista de Caxambu'),
                                   ('TrabalhistadeCaxias', 'Trabalhista de Caxias'),
                                   ('TrabalhistadeCaxiasdoSul', 'Trabalhista de Caxias do Sul'),
                                   ('TrabalhistadeCacador', 'Trabalhista de Caçador'),
                                   ('TrabalhistadeCacapava', 'Trabalhista de Caçapava'),
                                   ('TrabalhistadeCearaMirim', 'Trabalhista de Ceará-Mirim'),
                                   ('TrabalhistadeCeres', 'Trabalhista de Ceres'),
                                   ('TrabalhistadeChapadinha', 'Trabalhista de Chapadinha'),
                                   ('TrabalhistadeChapeco', 'Trabalhista de Chapecó'),
                                   ('TrabalhistadeCianorte', 'Trabalhista de Cianorte'),
                                   ('TrabalhistadeCoari', 'Trabalhista de Coari'),
                                   ('TrabalhistadeColatina', 'Trabalhista de Colatina'),
                                   ('TrabalhistadeColombo', 'Trabalhista de Colombo'),
                                   ('TrabalhistadeColoradodoOeste', 'Trabalhista de Colorado do Oeste'),
                                   ('TrabalhistadeColider', 'Trabalhista de Colíder'),
                                   ('TrabalhistadeConceicaodoCoite', 'Trabalhista de Conceição do Coité'),
                                   ('TrabalhistadeConcordia', 'Trabalhista de Concórdia'),
                                   ('TrabalhistadeCongonhas', 'Trabalhista de Congonhas'),
                                   ('TrabalhistadeConselheiroLafaiete', 'Trabalhista de Conselheiro Lafaiete'),
                                   ('TrabalhistadeContagem', 'Trabalhista de Contagem'),
                                   ('TrabalhistadeCordeiro', 'Trabalhista de Cordeiro'),
                                   ('TrabalhistadeCornelioProcopio', 'Trabalhista de Cornélio Procópio'),
                                   ('TrabalhistadeCoronelFabriciano', 'Trabalhista de Coronel Fabriciano'),
                                   ('TrabalhistadeCorrente', 'Trabalhista de Corrente'),
                                   ('TrabalhistadeCorumba', 'Trabalhista de Corumbá'),
                                   ('TrabalhistadeCotia', 'Trabalhista de Cotia'),
                                   ('TrabalhistadeCoxim', 'Trabalhista de Coxim'),
                                   ('TrabalhistadeCrateus', 'Trabalhista de Crateús'),
                                   ('TrabalhistadeCrato', 'Trabalhista de Crato'),
                                   ('TrabalhistadeCravinhos', 'Trabalhista de Cravinhos'),
                                   ('TrabalhistadeCriciuma', 'Trabalhista de Criciúma'),
                                   ('TrabalhistadeCruzeiro', 'Trabalhista de Cruzeiro'),
                                   ('TrabalhistadeCruzeirodoSul', 'Trabalhista de Cruzeiro do Sul'),
                                   ('TrabalhistadeCubatao', 'Trabalhista de Cubatão'),
                                   ('TrabalhistadeCuiaba', 'Trabalhista de Cuiabá'),
                                   ('TrabalhistadeCuritiba', 'Trabalhista de Curitiba'),
                                   ('TrabalhistadeCuritibanos', 'Trabalhista de Curitibanos'),
                                   ('TrabalhistadeCurraisNovos', 'Trabalhista de Currais Novos'),
                                   ('TrabalhistadeCurvelo', 'Trabalhista de Curvelo'),
                                   ('TrabalhistadeCaceres', 'Trabalhista de Cáceres'),
                                   ('TrabalhistadeDuquedeCaxias', 'Trabalhista de Duque de Caxias'),
                                   ('TrabalhistadeEuclidesdaCunha', 'Trabalhista de Euclides da Cunha'),
                                   ('TrabalhistadeJaboataodosGuararapesCentro', 'Trabalhista de Jaboatão dos Guararapes - Centro'),
                                   ('TrabalhistadeManausCentro', 'Trabalhista de Manaus - Centro'),
                                   ('TrabalhistadeMarechalCandidoRondon', 'Trabalhista de Marechal Cândido Rondon'),
                                   ('TrabalhistadeMogidasCruzes', 'Trabalhista de Mogi das Cruzes'),
                                   ('TrabalhistadeMontesClaros', 'Trabalhista de Montes Claros'),
                                   ('TrabalhistadeMossoroCentro', 'Trabalhista de Mossoró - Centro'),
                                   ('TrabalhistadePlacidodeCastro', 'Trabalhista de Plácido de Castro'),
                                   ('TrabalhistadePortoCalvo', 'Trabalhista de Porto Calvo'),
                                   ('TrabalhistadePocosdeCaldas', 'Trabalhista de Poços de Caldas'),
                                   ('TrabalhistadeRioClaro', 'Trabalhista de Rio Claro'),
                                   ('TrabalhistadeSantaCruzdoRioPardo', 'Trabalhista de Santa Cruz do Rio Pardo'),
                                   ('TrabalhistadeSantaCruzdoSul', 'Trabalhista de Santa Cruz do Sul'),
                                   ('TrabalhistadeSaoCaetanodoSul', 'Trabalhista de São Caetano do Sul'),
                                   ('TrabalhistadeSaoCarlos', 'Trabalhista de São Carlos'),
                                   ('TrabalhistadeSaoJosedosCampos', 'Trabalhista de São José dos Campos'),
                                   ('TrabalhistadeSaoMigueldosCampos', 'Trabalhista de São Miguel dos Campos'),
                                   ('TrabalhistadeTresCoracoes', 'Trabalhista de Três Corações'),
                                   ('TrabalhistadeVitoriadaConquista', 'Trabalhista de Vitória da Conquista'),
                                   ('TrabalhistadoRiodeJaneiroCentro', 'Trabalhista do Rio de Janeiro - Centro'),
                                   ('TribunaldeJusticadeSantaCatarina', 'Tribunal de Justiça de Santa Catarina'),
                                   ('TribunaldeJusticadoCeara', 'Tribunal de Justiça do Ceará'),
                                   ('TribunaldoJurideCuritiba', 'Tribunal do Júri de Curitiba'),
                                   ('VaradaInfanciaedaJuventudedeCuritiba', 'Vara da Infância e da Juventude de Curitiba'),
                                   ('VaradeExecucoesPenaisdeCuritiba', 'Vara de Execuções Penais de Curitiba'),
                                   ('VarasdaFamiliadeCuritiba', 'Varas da Família de Curitiba'),
                                   ('VarasdeDelitosdeTransitodeCuritiba', 'Varas de Delitos de Trânsito de Curitiba'),
                                   ('1VaradaInfanciaedaJuventudedeTeresina', '1ª Vara da Infância e da Juventude de Teresina'),
                                   ('2VaradaInfanciaedaJuventudedeTeresina', '2ª Vara da Infância e da Juventude de Teresina'),
                                   ('ForumdaInfanciaeJuventudedeJoaoPessoa', 'Fórum da Infância e Juventude de João Pessoa'),
                                   ('ForumdeDelmiroGouveia', 'Fórum de Delmiro Gouveia'),
                                   ('ForumdeDemervalLobao', 'Fórum de Demerval Lobão'),
                                   ('ForumdeDeodapolis', 'Fórum de Deodápolis'),
                                   ('ForumdeDescalvado', 'Fórum de Descalvado'),
                                   ('ForumdeDescanso', 'Fórum de Descanso'),
                                   ('ForumdeDiamantina', 'Fórum de Diamantina'),
                                   ('ForumdeDiamantino', 'Fórum de Diamantino'),
                                   ('ForumdeDianopolis', 'Fórum de Dianópolis'),
                                   ('ForumdeDiasDAvila', 'Fórum de Dias D''Ávila'),
                                   ('ForumdeDivinaPastora', 'Fórum de Divina Pastora'),
                                   ('ForumdeDivino', 'Fórum de Divino'),
                                   ('ForumdeDivinopolis', 'Fórum de Divinópolis'),
                                   ('ForumdeDoisIrmaos', 'Fórum de Dois Irmãos'),
                                   ('ForumdeDoisVizinhos', 'Fórum de Dois Vizinhos'),
                                   ('ForumdeDomEliseu', 'Fórum de Dom Eliseu'),
                                   ('ForumdeDomPedrito', 'Fórum de Dom Pedrito'),
                                   ('ForumdeDomPedro', 'Fórum de Dom Pedro'),
                                   ('ForumdeDomingosMartins', 'Fórum de Domingos Martins'),
                                   ('ForumdeDoresdoIndaia', 'Fórum de Dores do Indaiá'),
                                   ('ForumdeDoresdoRioPreto', 'Fórum de Dores do Rio Preto'),
                                   ('ForumdeDourados', 'Fórum de Dourados'),
                                   ('ForumdeDracena', 'Fórum de Dracena'),
                                   ('ForumdeEcoporanga', 'Fórum de Ecoporanga'),
                                   ('ForumdeEdeia', 'Fórum de Edéia'),
                                   ('ForumdeEirunepe', 'Fórum de Eirunepé'),
                                   ('ForumdeEldorado', 'Fórum de Eldorado'),
                                   ('ForumdeEldoradodoSul', 'Fórum de Eldorado do Sul'),
                                   ('ForumdeEldoradoPaulista', 'Fórum de Eldorado Paulista'),
                                   ('ForumdeElesbaoVeloso', 'Fórum de Elesbão Veloso'),
                                   ('ForumdeElizeuMartins', 'Fórum de Elizeu Martins'),
                                   ('ForumdeEloiMendes', 'Fórum de Elói Mendes'),
                                   ('ForumdeEmbu', 'Fórum de Embu'),
                                   ('ForumdeEmbuGuacu', 'Fórum de Embu-Guaçu'),
                                   ('ForumdeEncantado', 'Fórum de Encantado'),
                                   ('ForumdeEncruzilhada', 'Fórum de Encruzilhada'),
                                   ('ForumdeEncruzilhadadoSul', 'Fórum de Encruzilhada do Sul'),
                                   ('ForumdeEngenheiroPaulodeFrontin', 'Fórum de Engenheiro Paulo de Frontin'),
                                   ('ForumdeEntreRios', 'Fórum de Entre Rios'),
                                   ('ForumdeEntreRiosdeMinas', 'Fórum de Entre Rios de Minas'),
                                   ('ForumdeEnvira', 'Fórum de Envira'),
                                   ('ForumdeEpitaciolandia', 'Fórum de Epitaciolândia'),
                                   ('ForumdeErechim', 'Fórum de Erechim'),
                                   ('ForumdeErvalia', 'Fórum de Ervália'),
                                   ('ForumdeEscada', 'Fórum de Escada'),
                                   ('ForumdeEsmeraldas', 'Fórum de Esmeraldas'),
                                   ('ForumdeEsperaFeliz', 'Fórum de Espera Feliz'),
                                   ('ForumdeEsperantina', 'Fórum de Esperantina'),
                                   ('ForumdeEsperantinopolis', 'Fórum de Esperantinópolis'),
                                   ('ForumdeEsperanca', 'Fórum de Esperança'),
                                   ('ForumdeEspigaoDoOeste', 'Fórum de Espigão Do Oeste'),
                                   ('ForumdeEspinosa', 'Fórum de Espinosa'),
                                   ('ForumdeEsplanada', 'Fórum de Esplanada'),
                                   ('ForumdeEspumoso', 'Fórum de Espumoso'),
                                   ('ForumdeEspiritoSantodoPinhal', 'Fórum de Espírito Santo do Pinhal'),
                                   ('ForumdeEsteio', 'Fórum de Esteio'),
                                   ('ForumdeEstreito', 'Fórum de Estreito'),
                                   ('ForumdeEstrela', 'Fórum de Estrela'),
                                   ('ForumdeEstrelaDoNorte', 'Fórum de Estrela Do Norte'),
                                   ('ForumdeEstreladoSul', 'Fórum de Estrela do Sul'),
                                   ('ForumdeEstrelaDOeste', 'Fórum de Estrela D´Oeste'),
                                   ('ForumdeEstancia', 'Fórum de Estância'),
                                   ('ForumdeEstanciaVelha', 'Fórum de Estância Velha'),
                                   ('ForumdeEugenopolis', 'Fórum de Eugenópolis'),
                                   ('ForumdeEunapolis', 'Fórum de Eunápolis'),
                                   ('ForumdeEusebio', 'Fórum de Eusebio'),
                                   ('ForumdeExecucoesFiscaisFederaisdeSaoPaulo', 'Fórum de Execuções Fiscais Federais de São Paulo'),
                                   ('ForumdeExtrema', 'Fórum de Extrema'),
                                   ('ForumdeExtremoz', 'Fórum de Extremoz'),
                                   ('ForumdeExu', 'Fórum de Exu'),
                                   ('ForumdeFaro', 'Fórum de Faro'),
                                   ('ForumdeFarroupilha', 'Fórum de Farroupilha'),
                                   ('ForumdeFartura', 'Fórum de Fartura'),
                                   ('ForumdeFaxinal', 'Fórum de Faxinal'),
                                   ('ForumdeFaxinaldoSoturno', 'Fórum de Faxinal do Soturno'),
                                   ('ForumdeFazendaNova', 'Fórum de Fazenda Nova'),
                                   ('ForumdeFazendaRioGrande', 'Fórum de Fazenda Rio Grande'),
                                   ('ForumdeFeijo', 'Fórum de Feijó'),
                                   ('ForumdeFeiradeSantana', 'Fórum de Feira de Santana'),
                                   ('ForumdeFeiraGrande', 'Fórum de Feira Grande'),
                                   ('ForumdeFeiraNova', 'Fórum de Feira Nova'),
                                   ('ForumdeFeiraNovaPE', 'Fórum de Feira Nova - PE'),
                                   ('ForumdeFeliz', 'Fórum de Feliz'),
                                   ('ForumdeFelizNatal', 'Fórum de Feliz Natal'),
                                   ('ForumdeFernandoFalcao', 'Fórum de Fernando Falcão'),
                                   ('ForumdeFernandopolis', 'Fórum de Fernandópolis'),
                                   ('ForumdeFerrazdeVasconcelos', 'Fórum de Ferraz de Vasconcelos'),
                                   ('ForumdeFerreiraGomes', 'Fórum de Ferreira Gomes'),
                                   ('ForumdeFerreiros', 'Fórum de Ferreiros'),
                                   ('ForumdeFerros', 'Fórum de Ferros'),
                                   ('ForumdeFigueiropolis', 'Fórum de Figueirópolis'),
                                   ('ForumdeFiladelfia', 'Fórum de Filadélfia'),
                                   ('ForumdeFirminopolis', 'Fórum de Firminópolis'),
                                   ('ForumdeFlexeiras', 'Fórum de Flexeiras'),
                                   ('ForumdeFlores', 'Fórum de Flores'),
                                   ('ForumdeFloresta', 'Fórum de Floresta'),
                                   ('ForumdeFloriano', 'Fórum de Floriano'),
                                   ('ForumdeFlorianopolis', 'Fórum de Florianopolis'),
                                   ('ForumdeFlorania', 'Fórum de Florânia'),
                                   ('ForumdeFormiga', 'Fórum de Formiga'),
                                   ('ForumdeFormosa', 'Fórum de Formosa'),
                                   ('ForumdeFormosadoOeste', 'Fórum de Formosa do Oeste'),
                                   ('ForumdeFormosadoRioPreto', 'Fórum de Formosa do Rio Preto'),
                                   ('ForumdeFormoso', 'Fórum de Formoso')
                                   ;
                                   INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumdeForquilha', 'Fórum de Forquilha'),
                                   ('ForumdeForquilhinha', 'Fórum de Forquilhinha'),
                                   ('ForumdeFortaleza', 'Fórum de Fortaleza'),
                                   ('ForumdeFortalezadosNogueiras', 'Fórum de Fortaleza dos Nogueiras'),
                                   ('ForumdeFortim', 'Fórum de Fortim'),
                                   ('ForumdeFortuna', 'Fórum de Fortuna'),
                                   ('ForumdeFozdoIguacu', 'Fórum de Foz do Iguaçu'),
                                   ('ForumdeFraiburgo', 'Fórum de Fraiburgo'),
                                   ('ForumdeFranca', 'Fórum de Franca'),
                                   ('ForumdeFrancinopolis', 'Fórum de Francinópolis'),
                                   ('ForumdeFranciscoMorato', 'Fórum de Francisco Morato'),
                                   ('ForumdeFranciscoSantos', 'Fórum de Francisco Santos'),
                                   ('ForumdeFranciscoSa', 'Fórum de Francisco Sá'),
                                   ('ForumdeFrancodaRocha', 'Fórum de Franco da Rocha'),
                                   ('ForumdeFrecheirinha', 'Fórum de Frecheirinha'),
                                   ('ForumdeFredericoWestphalen', 'Fórum de Frederico Westphalen'),
                                   ('ForumdeFreiPaulo', 'Fórum de Frei Paulo'),
                                   ('ForumdeFronteiras', 'Fórum de Fronteiras'),
                                   ('ForumdeFrutal', 'Fórum de Frutal'),
                                   ('ForumdeFundao', 'Fórum de Fundão'),
                                   ('ForumdeGalileia', 'Fórum de Galiléia'),
                                   ('ForumdeGameleira', 'Fórum de Gameleira'),
                                   ('ForumdeGandu', 'Fórum de Gandu'),
                                   ('ForumdeGaranhuns', 'Fórum de Garanhuns'),
                                   ('ForumdeGararu', 'Fórum de Gararu'),
                                   ('ForumdeGaribaldi', 'Fórum de Garibaldi'),
                                   ('ForumdeGaropaba', 'Fórum de Garopaba'),
                                   ('ForumdeGarrafaoDoNorte', 'Fórum de Garrafão Do Norte'),
                                   ('ForumdeGaruva', 'Fórum de Garuva'),
                                   ('ForumdeGarca', 'Fórum de Garça'),
                                   ('ForumdeGaspar', 'Fórum de Gaspar'),
                                   ('ForumdeGaurama', 'Fórum de Gaurama'),
                                   ('ForumdeGaviao', 'Fórum de Gavião'),
                                   ('ForumdeGeneralMaynard', 'Fórum de General Maynard'),
                                   ('ForumdeGeneralSalgado', 'Fórum de General Salgado'),
                                   ('ForumdeGentiodoOuro', 'Fórum de Gentio do Ouro'),
                                   ('ForumdeGetulina', 'Fórum de Getulina'),
                                   ('ForumdeGetulioVargas', 'Fórum de Getúlio Vargas'),
                                   ('ForumdeGilbues', 'Fórum de Gilbués'),
                                   ('ForumdeGiraudoPonciano', 'Fórum de Girau do Ponciano'),
                                   ('ForumdeGirua', 'Fórum de Giruá'),
                                   ('ForumdeGloria', 'Fórum de Glória'),
                                   ('ForumdeGloriaDeDourados', 'Fórum de Glória De Dourados'),
                                   ('ForumdeGloriadoGoita', 'Fórum de Glória do Goitá'),
                                   ('ForumdeGoiana', 'Fórum de Goiana'),
                                   ('ForumdeGoiandira', 'Fórum de Goiandira'),
                                   ('ForumdeGoianinha', 'Fórum de Goianinha'),
                                   ('ForumdeGoianira', 'Fórum de Goianira'),
                                   ('ForumdeGoianapolis', 'Fórum de Goianápolis'),
                                   ('ForumdeGoianesia', 'Fórum de Goianésia'),
                                   ('ForumdeGoiatins', 'Fórum de Goiatins'),
                                   ('ForumdeGoiatuba', 'Fórum de Goiatuba'),
                                   ('ForumdeGoioere', 'Fórum de Goioerê'),
                                   ('ForumdeGoias', 'Fórum de Goiás'),
                                   ('ForumdeGoiania', 'Fórum de Goiânia'),
                                   ('ForumdeGoncalvesDias', 'Fórum de Gonçalves Dias'),
                                   ('ForumdeGovernadorDixSeptRosado', 'Fórum de Governador Dix-Sept Rosado'),
                                   ('ForumdeGovernadorLomantoJunior', 'Fórum de Governador Lomanto Júnior'),
                                   ('ForumdeGovernadorMangabeira', 'Fórum de Governador Mangabeira'),
                                   ('ForumdeGovernadorNunesFreire', 'Fórum de Governador Nunes Freire'),
                                   ('ForumdeGovernadorValadares', 'Fórum de Governador Valadares'),
                                   ('ForumdeGraca', 'Fórum de Graca'),
                                   ('ForumdeGrajau', 'Fórum de Grajaú'),
                                   ('ForumdeGramado', 'Fórum de Gramado'),
                                   ('ForumdeGrandesRios', 'Fórum de Grandes Rios'),
                                   ('ForumdeGranja', 'Fórum de Granja'),
                                   ('ForumdeGravatai', 'Fórum de Gravataí'),
                                   ('ForumdeGravata', 'Fórum de Gravatá'),
                                   ('ForumdeGroairas', 'Fórum de Groairas'),
                                   ('ForumdeGraoMogol', 'Fórum de Grão-Mogol'),
                                   ('ForumdeGuadalupe', 'Fórum de Guadalupe'),
                                   ('ForumdeGuajaraMirim', 'Fórum de Guajará Mirim'),
                                   ('ForumdeGuanambi', 'Fórum de Guanambi'),
                                   ('ForumdeGuanhaes', 'Fórum de Guanhães'),
                                   ('ForumdeGuapimirim', 'Fórum de Guapimirim'),
                                   ('ForumdeGuapore', 'Fórum de Guaporé'),
                                   ('ForumdeGuape', 'Fórum de Guapé'),
                                   ('ForumdeGuapo', 'Fórum de Guapó'),
                                   ('ForumdeGuarabira', 'Fórum de Guarabira'),
                                   ('ForumdeGuaramirim', 'Fórum de Guaramirim'),
                                   ('ForumdeGuarani', 'Fórum de Guarani'),
                                   ('ForumdeGuaranidasMissoes', 'Fórum de Guarani das Missões'),
                                   ('ForumdeGuaraniacu', 'Fórum de Guaraniaçu'),
                                   ('ForumdeGuarantaDoNorte', 'Fórum de Guarantã Do Norte'),
                                   ('ForumdeGuaranesia', 'Fórum de Guaranésia'),
                                   ('ForumdeGuarapari', 'Fórum de Guarapari'),
                                   ('ForumdeGuarapuava', 'Fórum de Guarapuava'),
                                   ('ForumdeGuararapes', 'Fórum de Guararapes'),
                                   ('ForumdeGuararema', 'Fórum de Guararema'),
                                   ('ForumdeGuaratinga', 'Fórum de Guaratinga'),
                                   ('ForumdeGuaratingueta', 'Fórum de Guaratinguetá'),
                                   ('ForumdeGuaratuba', 'Fórum de Guaratuba'),
                                   ('ForumdeGuarai', 'Fórum de Guaraí'),
                                   ('ForumdeGuariba', 'Fórum de Guariba'),
                                   ('ForumdeGuarulhos', 'Fórum de Guarulhos'),
                                   ('ForumdeGuara', 'Fórum de Guará'),
                                   ('ForumdeGuaxupe', 'Fórum de Guaxupé'),
                                   ('ESCRITORIO', 'ESCRITORIO'),
                                   ('ForumdeManoelEmidio', 'Fórum de Manoel Emídio'),
                                   ('ForumdeMardeEspanha', 'Fórum de Mar de Espanha'),
                                   ('ForumdeNovaEra', 'Fórum de Nova Era'),
                                   ('ForumdeNovaEsperanca', 'Fórum de Nova Esperança'),
                                   ('ForumdePontesELacerda', 'Fórum de Pontes E Lacerda'),
                                   ('ForumdePortoEsperidiao', 'Fórum de Porto Esperidião'),
                                   ('ForumdePresidenteEpitacio', 'Fórum de Presidente Epitácio'),
                                   ('ForumdeRioPretoDaEva', 'Fórum de Rio Preto Da Eva'),
                                   ('ForumdeSantoEstevao', 'Fórum de Santo Estevão'),
                                   ('ForumdeSaoJosedoEgito', 'Fórum de São José do Egito'),
                                   ('ForumdeSaoJoaoEvangelista', 'Fórum de São João Evangelista'),
                                   ('ForumFederaldeErechim', 'Fórum Federal de Erechim'),
                                   ('ForumFederaldeEstancia', 'Fórum Federal de Estância'),
                                   ('ForumFederaldeEunapolis', 'Fórum Federal de Eunápolis'),
                                   ('ForumVaradaInfanciaeJuventude', 'Fórum/Vara da Infância e Juventude'),
                                   ('JuizadoEspecialdePontaGrossa', 'Juizado Especial de Ponta Grossa'),
                                   ('JuizadoEspecialdeSaoJosedosPinhais', 'Juizado Especial de São José dos Pinhais'),
                                   ('JuizadoEspecialFederaldeJundiai', 'Juizado Especial Federal de Jundiaí'),
                                   ('JuizadoEspecialFederaldeManaus', 'Juizado Especial Federal de Manaus'),
                                   ('JuizadoEspecialFederaldeSaoPaulo', 'Juizado Especial Federal de São Paulo'),
                                   ('JuizadoEspecialFederaldeVitoria', 'Juizado Especial Federal de Vitória'),
                                   ('JuizadoEspecialGeiselJoaoPessoa', 'Juizado Especial Geisel - João Pessoa'),
                                   ('JuizadosEspeciaisFederaisdeSalvador', 'Juizados Especiais Federais de Salvador'),
                                   ('TrabalhistadeEirunepe', 'Trabalhista de Eirunepé'),
                                   ('TrabalhistadeEmbu', 'Trabalhista de Embu'),
                                   ('TrabalhistadeEncantado', 'Trabalhista de Encantado'),
                                   ('TrabalhistadeEpitaciolandia', 'Trabalhista de Epitaciolândia'),
                                   ('TrabalhistadeErechim', 'Trabalhista de Erechim'),
                                   ('TrabalhistadeEscada', 'Trabalhista de Escada'),
                                   ('TrabalhistadeEsteio', 'Trabalhista de Esteio'),
                                   ('TrabalhistadeEstreito', 'Trabalhista de Estreito'),
                                   ('TrabalhistadeEstrela', 'Trabalhista de Estrela'),
                                   ('TrabalhistadeEstancia', 'Trabalhista de Estância'),
                                   ('TrabalhistadeEstanciaVelha', 'Trabalhista de Estância Velha'),
                                   ('TrabalhistadeEunapolis', 'Trabalhista de Eunápolis'),
                                   ('TrabalhistadeNovaEsperanca', 'Trabalhista de Nova Esperança'),
                                   ('TrabalhistadePonteseLacerda', 'Trabalhista de Pontes e Lacerda'),
                                   ('TribunaldeJusticadoEspiritoSanto', 'Tribunal de Justiça do Espírito Santo'),
                                   ('TribunaldoEstadodoRiodeJaneiro', 'Tribunal do Estado do Rio de Janeiro'),
                                   ('VarasdasExecucoesFiscaisSP', 'Varas das Execuções Fiscais - SP'),
                                   ('VarasEspInfanciaeJuventude', 'Varas Esp Infância e Juventude'),
                                   ('ForoRegionalV', 'Foro Regional V'),
                                   ('ForumdeGuacui', 'Fórum de Guaçui'),
                                   ('ForumdeGuaiba', 'Fórum de Guaíba'),
                                   ('ForumdeGuaira', 'Fórum de Guaíra'),
                                   ('ForumdeGuairaPR', 'Fórum de Guaíra - PR'),
                                   ('ForumdeGuimaraes', 'Fórum de Guimarães'),
                                   ('ForumdeGuiratinga', 'Fórum de Guiratinga'),
                                   ('ForumdeGurinhem', 'Fórum de Gurinhém'),
                                   ('ForumdeGurupi', 'Fórum de Gurupi'),
                                   ('ForumdeGurupa', 'Fórum de Gurupá'),
                                   ('ForumdeGalia', 'Fórum de Gália'),
                                   ('ForumdeIgarapeGrande', 'Fórum de Igarapé Grande'),
                                   ('ForumdeIguabaGrande', 'Fórum de Iguaba Grande'),
                                   ('ForumdeJaboataodosGuararapes', 'Fórum de Jaboatão dos Guararapes'),
                                   ('ForumdeJoaquimGomes', 'Fórum de Joaquim Gomes'),
                                   ('ForumdeJussaraGO', 'Fórum de Jussara - GO'),
                                   ('ForumdeLagoadosGatos', 'Fórum de Lagoa dos Gatos'),
                                   ('ForumdeLuisGomes', 'Fórum de Luís Gomes'),
                                   ('ForumdeMataGrande', 'Fórum de Mata Grande'),
                                   ('ForumdeMonsenhorGil', 'Fórum de Monsenhor Gil'),
                                   ('ForumdeNossaSenhoradaGloria', 'Fórum de Nossa Senhora da Glória'),
                                   ('ForumdeNovaGranada', 'Fórum de Nova Granada'),
                                   ('ForumdeNovoGama', 'Fórum de Novo Gama'),
                                   ('ForumdePalmeirasDeGoias', 'Fórum de Palmeiras De Goiás'),
                                   ('ForumdePedroGomes', 'Fórum de Pedro Gomes'),
                                   ('ForumdePetrolinaDeGoias', 'Fórum de Petrolina De Goiás'),
                                   ('ForumdePiranhasGO', 'Fórum de Piranhas - GO'),
                                   ('ForumdePlanaltinaGO', 'Fórum de Planaltina - GO'),
                                   ('ForumdePontaGrossa', 'Fórum de Ponta Grossa'),
                                   ('ForumdePortoDosGauchos', 'Fórum de Porto Dos Gaúchos'),
                                   ('ForumdePortoGrande', 'Fórum de Porto Grande'),
                                   ('ForumdePraiaGrande', 'Fórum de Praia Grande'),
                                   ('ForumdePresidenteGetulio', 'Fórum de Presidente Getulio'),
                                   ('ForumdeRibeiroGoncalves', 'Fórum de Ribeiro Gonçalves'),
                                   ('ForumdeRioGrande', 'Fórum de Rio Grande'),
                                   ('ForumdeSantaHelenaDeGoias', 'Fórum de Santa Helena De Goiás'),
                                   ('ForumdeSantaTerezinhaDeGoias', 'Fórum de Santa Terezinha De Goiás'),
                                   ('ForumdeSantanadosGarrotes', 'Fórum de Santana dos Garrotes'),
                                   ('ForumdeSenadorGuiomard', 'Fórum de Senador Guiomard'),
                                   ('ForumdeSaoDomingosGO', 'Fórum de São Domingos - GO'),
                                   ('ForumdeSaoGabrielRS', 'Fórum de São Gabriel - RS'),
                                   ('ForumdeSaoGabrieldaPalha', 'Fórum de São Gabriel da Palha'),
                                   ('ForumdeSaoGoncalo', 'Fórum de São Goncalo'),
                                   ('ForumdeSaoGoncalodoPiaui', 'Fórum de São Gonçalo do Piauí'),
                                   ('ForumdeSaoGoncalodoSapucai', 'Fórum de São Gonçalo do Sapucaí'),
                                   ('ForumdeSaoGotardo', 'Fórum de São Gotardo'),
                                   ('ForumdeSaoLuizGonzaga', 'Fórum de São Luiz Gonzaga'),
                                   ('ForumdeSaoLuisGonzagadoMaranhao', 'Fórum de São Luís Gonzaga do Maranhão'),
                                   ('ForumdeSaoMiguelDoGuama', 'Fórum de São Miguel Do Guamá'),
                                   ('ForumdeSaoMiguelDoGuapore', 'Fórum de São Miguel Do Guaporé'),
                                   ('ForumdeSaoSebastiaodaGrama', 'Fórum de São Sebastião da Grama'),
                                   ('ForumdeSaoSimaoGO', 'Fórum de São Simão - GO'),
                                   ('ForumdeTaquaralDeGoias', 'Fórum de Taquaral De Goiás'),
                                   ('ForumdeTomardoGeru', 'Fórum de Tomar do Geru'),
                                   ('ForumdeTrindadeGO', 'Fórum de Trindade - GO'),
                                   ('ForumdeValparaisoDeGoias', 'Fórum de Valparaíso De Goiás'),
                                   ('ForumdeVargemGrande', 'Fórum de Vargem Grande'),
                                   ('ForumdeVargemGrandedoSul', 'Fórum de Vargem Grande do Sul'),
                                   ('ForumdeVargemGrandePaulista', 'Fórum de Vargem Grande Paulista'),
                                   ('ForumdeVarzeaGrande', 'Fórum de Várzea Grande'),
                                   ('ForumdeWenceslauGuimaraes', 'Fórum de Wenceslau Guimarães'),
                                   ('ForumdeAguasLindasDeGoias', 'Fórum de Águas Lindas De Goiás'),
                                   ('ForumdoGama', 'Fórum do Gama'),
                                   ('ForumFederaldeGaranhuns', 'Fórum Federal de Garanhuns'),
                                   ('ForumFederaldeGoiania', 'Fórum Federal de Goiânia'),
                                   ('ForumFederaldeGovernadorValadares', 'Fórum Federal de Governador Valadares'),
                                   ('ForumFederaldeGuanambi', 'Fórum Federal de Guanambi'),
                                   ('ForumFederaldeGuarapuava', 'Fórum Federal de Guarapuava'),
                                   ('ForumFederaldeGuaratingueta', 'Fórum Federal de Guaratinguetá'),
                                   ('ForumFederaldeGuarulhos', 'Fórum Federal de Guarulhos'),
                                   ('ForumFederaldePontaGrossa', 'Fórum Federal de Ponta Grossa'),
                                   ('ForumFederaldeRioGrande', 'Fórum Federal de Rio Grande'),
                                   ('ForumFederaldeSaoGoncalo', 'Fórum Federal de São Gonçalo'),
                                   ('ForumRegionaldaIlhadoGovernador', 'Fórum Regional da Ilha do Governador'),
                                   ('ForumRioVerdeDeMatoGrosso', 'Fórum Rio Verde De Mato Grosso'),
                                   ('ForumSaoGabrielDoOeste', 'Fórum São Gabriel Do Oeste'),
                                   ('JeccdeDelmiroGouveia', 'Jecc de Delmiro Gouveia'),
                                   ('JuizadoJdGloria', 'Juizado - Jd Glória'),
                                   ('TrabalhistadeGaranhuns', 'Trabalhista de Garanhuns'),
                                   ('TrabalhistadeGarca', 'Trabalhista de Garça'),
                                   ('TrabalhistadeGoiana', 'Trabalhista de Goiana'),
                                   ('TrabalhistadeGoianinha', 'Trabalhista de Goianinha'),
                                   ('TrabalhistadeGoias', 'Trabalhista de Goiás'),
                                   ('TrabalhistadeGoiania', 'Trabalhista de Goiânia'),
                                   ('TrabalhistadeGovernadorValadares', 'Trabalhista de Governador Valadares'),
                                   ('TrabalhistadeGramado', 'Trabalhista de Gramado'),
                                   ('TrabalhistadeGravatai', 'Trabalhista de Gravataí'),
                                   ('TrabalhistadeGuajaraMirim', 'Trabalhista de Guajará Mirim'),
                                   ('TrabalhistadeGuanambi', 'Trabalhista de Guanambi'),
                                   ('TrabalhistadeGuanhaes', 'Trabalhista de Guanhães'),
                                   ('TrabalhistadeGuarabira', 'Trabalhista de Guarabira'),
                                   ('TrabalhistadeGuarapari', 'Trabalhista de Guarapari'),
                                   ('TrabalhistadeGuarapuava', 'Trabalhista de Guarapuava'),
                                   ('TrabalhistadeGuaratingueta', 'Trabalhista de Guaratingueta'),
                                   ('TrabalhistadeGuarai', 'Trabalhista de Guaraí'),
                                   ('TrabalhistadeGuaruja', 'Trabalhista de Guarujá'),
                                   ('TrabalhistadeGuarulhos', 'Trabalhista de Guarulhos'),
                                   ('TrabalhistadeGuaxupe', 'Trabalhista de Guaxupé'),
                                   ('TrabalhistadeGuaiba', 'Trabalhista de Guaíba'),
                                   ('TrabalhistadeGurupi', 'Trabalhista de Gurupi'),
                                   ('TrabalhistadeJaboataodosGuararapesPrazeres', 'Trabalhista de Jaboatão dos Guararapes - Prazeres'),
                                   ('TrabalhistadeNossaSenhoradaGloria', 'Trabalhista de Nossa Senhora da Glória'),
                                   ('TrabalhistadePontaGrossa', 'Trabalhista de Ponta Grossa'),
                                   ('TrabalhistadePraiaGrande', 'Trabalhista de Praia Grande'),
                                   ('TrabalhistadeRioGrande', 'Trabalhista de Rio Grande'),
                                   ('TrabalhistadeSaoGabriel', 'Trabalhista de São Gabriel'),
                                   ('TrabalhistadeSaoGabrielDOeste', 'Trabalhista de São Gabriel D''Oeste'),
                                   ('TrabalhistadeSaoGoncalo', 'Trabalhista de São Gonçalo'),
                                   ('TrabalhistadeSaoMigueldoGuapore', 'Trabalhista de São Miguel do Guaporé'),
                                   ('TrabalhistadeValparaisodeGoias', 'Trabalhista de Valparaíso de Goiás'),
                                   ('TribunaldeJusticadeGoias', 'Tribunal de Justiça de Goiás'),
                                   ('TribunaldeJusticadeMatoGrosso', 'Tribunal de Justiça de Mato Grosso'),
                                   ('TribunaldeJusticadeMinasGerais', 'Tribunal de Justiça de Minas Gerais'),
                                   ('TribunaldeJusticadeMinasGeraisFranSales', 'Tribunal de Justiça de Minas Gerais - Fran. Sales'),
                                   ('TribunaldeJusticadoMatoGrossodoSul', 'Tribunal de Justiça do Mato Grosso do Sul'),
                                   ('TribunaldeJusticadoRioGrandedoNorte', 'Tribunal de Justiça do Rio Grande do Norte'),
                                   ('TribunaldeJusticadoRioGrandedoSul', 'Tribunal de Justiça do Rio Grande do Sul'),
                                   ('ForumdeHerval', 'Fórum de Herval'),
                                   ('ForumdeHervalDOeste', 'Fórum de Herval D''Oeste'),
                                   ('ForumdeHidrolandia', 'Fórum de Hidrolandia'),
                                   ('ForumdeHorizonte', 'Fórum de Horizonte'),
                                   ('ForumdeHorizontina', 'Fórum de Horizontina'),
                                   ('ForumdeHortolandia', 'Fórum de Hortolândia'),
                                   ('ForumdeHumaita', 'Fórum de Humaitá'),
                                   ('ForumdeNovoHamburgo', 'Fórum de Novo Hamburgo'),
                                   ('ForumdeNovoHorizonte', 'Fórum de Novo Horizonte'),
                                   ('ForumdeSantaHelena', 'Fórum de Santa Helena'),
                                   ('ForumdeSantaHelenaMA', 'Fórum de Santa Helena - MA'),
                                   ('ForumFederaldeNovoHamburgo', 'Fórum Federal de Novo Hamburgo'),
                                   ('ForumHelyLopesMeirelles', 'Fórum Hely Lopes Meirelles'),
                                   ('TrabalhistadeHortolandia', 'Trabalhista de Hortolândia'),
                                   ('TrabalhistadeHumaita', 'Trabalhista de Humaitá'),
                                   ('TrabalhistadeNovoHamburgo', 'Trabalhista de Novo Hamburgo'),
                                   ('ForumdeIacanga', 'Fórum de Iacanga'),
                                   ('ForumdeIaciara', 'Fórum de Iaciara'),
                                   ('ForumdeIati', 'Fórum de Iati'),
                                   ('ForumdeIacu', 'Fórum de Iaçu'),
                                   ('ForumdeIbaiti', 'Fórum de Ibaiti'),
                                   ('ForumdeIbatiba', 'Fórum de Ibatiba'),
                                   ('ForumdeIbate', 'Fórum de Ibaté'),
                                   ('ForumdeIbiapina', 'Fórum de Ibiapina'),
                                   ('ForumdeIbicarai', 'Fórum de Ibicaraí'),
                                   ('ForumdeIbicui', 'Fórum de Ibicuí'),
                                   ('ForumdeIbimirim', 'Fórum de Ibimirim'),
                                   ('ForumdeIbipora', 'Fórum de Ibiporâ'),
                                   ('ForumdeIbiquera', 'Fórum de Ibiquera'),
                                   ('ForumdeIbiraci', 'Fórum de Ibiraci'),
                                   ('ForumdeIbirajuba', 'Fórum de Ibirajuba'),
                                   ('ForumdeIbirama', 'Fórum de Ibirama'),
                                   ('ForumdeIbirapitanga', 'Fórum de Ibirapitanga'),
                                   ('ForumdeIbirapua', 'Fórum de Ibirapuã'),
                                   ('ForumdeIbirataia', 'Fórum de Ibirataia'),
                                   ('ForumdeIbiracu', 'Fórum de Ibiraçu'),
                                   ('ForumdeIbirite', 'Fórum de Ibirité'),
                                   ('ForumdeIbiruba', 'Fórum de Ibirubá'),
                                   ('ForumdeIbitiara', 'Fórum de Ibitiara'),
                                   ('ForumdeIbitinga', 'Fórum de Ibitinga'),
                                   ('ForumdeIbitirama', 'Fórum de Ibitirama'),
                                   ('ForumdeIbitita', 'Fórum de Ibititá'),
                                   ('ForumdeIbia', 'Fórum de Ibiá'),
                                   ('ForumdeIbiuna', 'Fórum de Ibiúna'),
                                   ('ForumdeIbotirama', 'Fórum de Ibotirama'),
                                   ('ForumdeIcapui', 'Fórum de Icapui'),
                                   ('ForumdeIcara', 'Fórum de Icara'),
                                   ('ForumdeIcaraima', 'Fórum de Icaraíma'),
                                   ('ForumdeIcatu', 'Fórum de Icatu'),
                                   ('ForumdeIchu', 'Fórum de Ichu'),
                                   ('ForumdeIco', 'Fórum de Ico'),
                                   ('ForumdeIconha', 'Fórum de Iconha'),
                                   ('ForumdeIepe', 'Fórum de Iepê'),
                                   ('ForumdeIgaci', 'Fórum de Igaci'),
                                   ('ForumdeIgapora', 'Fórum de Igaporã'),
                                   ('ForumdeIgarapava', 'Fórum de Igarapava'),
                                   ('ForumdeIgarape', 'Fórum de Igarapé'),
                                   ('ForumdeIgarapeMirim', 'Fórum de Igarapé Mirim'),
                                   ('ForumdeIgarapeAcu', 'Fórum de Igarapé-Açú'),
                                   ('ForumdeIgarassu', 'Fórum de Igarassu'),
                                   ('ForumdeIgrejaNova', 'Fórum de Igreja Nova'),
                                   ('ForumdeIgrejinha', 'Fórum de Igrejinha'),
                                   ('ForumdeIguape', 'Fórum de Iguape'),
                                   ('ForumdeIguatama', 'Fórum de Iguatama'),
                                   ('ForumdeIguatemi', 'Fórum de Iguatemi'),
                                   ('ForumdeIguatu', 'Fórum de Iguatu'),
                                   ('ForumdeIguai', 'Fórum de Iguaí'),
                                   ('ForumdeIjui', 'Fórum de Ijuí'),
                                   ('ForumdeIlhadasFlores', 'Fórum de Ilha das Flores'),
                                   ('ForumdeIlhaSolteira', 'Fórum de Ilha Solteira'),
                                   ('ForumdeIlhabela', 'Fórum de Ilhabela'),
                                   ('ForumdeIlheus', 'Fórum de Ilhéus'),
                                   ('ForumdeImarui', 'Fórum de Imarui'),
                                   ('ForumdeImbituba', 'Fórum de Imbituba'),
                                   ('ForumdeImbituva', 'Fórum de Imbituva'),
                                   ('ForumdeImperatriz', 'Fórum de Imperatriz'),
                                   ('ForumdeInaja', 'Fórum de Inajá'),
                                   ('ForumdeIndaial', 'Fórum de Indaial'),
                                   ('ForumdeIndaiatuba', 'Fórum de Indaiatuba'),
                                   ('ForumdeIndependencia', 'Fórum de Independencia'),
                                   ('ForumdeIndiaroba', 'Fórum de Indiaroba'),
                                   ('ForumdeInga', 'Fórum de Ingá'),
                                   ('ForumdeInhambupe', 'Fórum de Inhambupe'),
                                   ('ForumdeInhapim', 'Fórum de Inhapim'),
                                   ('ForumdeInhuma', 'Fórum de Inhuma'),
                                   ('ForumdeInhumas', 'Fórum de Inhumas'),
                                   ('ForumdeInocencia', 'Fórum de Inocência'),
                                   ('ForumdeIpanema', 'Fórum de Ipanema'),
                                   ('ForumdeIpanguacu', 'Fórum de Ipanguaçu'),
                                   ('ForumdeIpaporanga', 'Fórum de Ipaporanga'),
                                   ('ForumdeIpatinga', 'Fórum de Ipatinga'),
                                   ('ForumdeIpaucu', 'Fórum de Ipauçu'),
                                   ('ForumdeIpiau', 'Fórum de Ipiaú'),
                                   ('ForumdeIpiranga', 'Fórum de Ipiranga'),
                                   ('ForumdeIpirangadoPiaui', 'Fórum de Ipiranga do Piauí'),
                                   ('ForumdeIpira', 'Fórum de Ipirá'),
                                   ('ForumdeIpixuna', 'Fórum de Ipixuna'),
                                   ('ForumdeIpojuca', 'Fórum de Ipojuca'),
                                   ('ForumdeIpora', 'Fórum de Iporâ'),
                                   ('ForumdeIpu', 'Fórum de Ipu'),
                                   ('ForumdeIpubi', 'Fórum de Ipubi'),
                                   ('ForumdeIpueiras', 'Fórum de Ipueiras'),
                                   ('ForumdeIpumirim', 'Fórum de Ipumirim'),
                                   ('ForumdeIpua', 'Fórum de Ipuã'),
                                   ('ForumdeIramaia', 'Fórum de Iramaia'),
                                   ('ForumdeIranduba', 'Fórum de Iranduba'),
                                   ('ForumdeIraquara', 'Fórum de Iraquara'),
                                   ('ForumdeIrara', 'Fórum de Irará'),
                                   ('ForumdeIrati', 'Fórum de Irati'),
                                   ('ForumdeIraucuba', 'Fórum de Iraucuba'),
                                   ('ForumdeIrai', 'Fórum de Iraí'),
                                   ('ForumdeIrece', 'Fórum de Irecê'),
                                   ('ForumdeIretama', 'Fórum de Iretama'),
                                   ('ForumdeIrituia', 'Fórum de Irituia'),
                                   ('ForumdeIsraelandia', 'Fórum de Israelândia'),
                                   ('ForumdeIta', 'Fórum de Ita'),
                                   ('ForumdeItabaiana', 'Fórum de Itabaiana'),
                                   ('ForumdeItabaianaPB', 'Fórum de Itabaiana - PB'),
                                   ('ForumdeItabaianinha', 'Fórum de Itabaianinha'),
                                   ('ForumdeItabela', 'Fórum de Itabela'),
                                   ('ForumdeItaberaba', 'Fórum de Itaberaba'),
                                   ('ForumdeItaberai', 'Fórum de Itaberaí'),
                                   ('ForumdeItabera', 'Fórum de Itaberá'),
                                   ('ForumdeItabi', 'Fórum de Itabi'),
                                   ('ForumdeItabira', 'Fórum de Itabira'),
                                   ('ForumdeItabirito', 'Fórum de Itabirito'),
                                   ('ForumdeItaborai', 'Fórum de Itaborai'),
                                   ('ForumdeItabuna', 'Fórum de Itabuna'),
                                   ('ForumdeItacaja', 'Fórum de Itacajá'),
                                   ('ForumdeItacare', 'Fórum de Itacaré'),
                                   ('ForumdeItacoatiara', 'Fórum de Itacoatiara'),
                                   ('ForumdeItaete', 'Fórum de Itaeté'),
                                   ('ForumdeItagi', 'Fórum de Itagi'),
                                   ('ForumdeItagiba', 'Fórum de Itagibá'),
                                   ('ForumdeItagimirim', 'Fórum de Itagimirim'),
                                   ('ForumdeItaguai', 'Fórum de Itaguai'),
                                   ('ForumdeItaguara', 'Fórum de Itaguara'),
                                   ('ForumdeItaguaru', 'Fórum de Itaguaru'),
                                   ('ForumdeItaguatins', 'Fórum de Itaguatins'),
                                   ('ForumdeItaguacu', 'Fórum de Itaguaçu'),
                                   ('ForumdeItaiba', 'Fórum de Itaiba'),
                                   ('ForumdeItainopolis', 'Fórum de Itainópolis'),
                                   ('ForumdeItaiopolis', 'Fórum de Itaiopolis'),
                                   ('ForumdeItaituba', 'Fórum de Itaituba'),
                                   ('ForumdeItajai', 'Fórum de Itajai'),
                                   ('ForumdeItajobi', 'Fórum de Itajobi'),
                                   ('ForumdeItajuba', 'Fórum de Itajubá'),
                                   ('ForumdeItajuipe', 'Fórum de Itajuípe'),
                                   ('ForumdeItaja', 'Fórum de Itajá'),
                                   ('ForumdeItalva', 'Fórum de Italva'),
                                   ('ForumdeItamaraca', 'Fórum de Itamaracá'),
                                   ('ForumdeItamaraju', 'Fórum de Itamaraju'),
                                   ('ForumdeItamarandiba', 'Fórum de Itamarandiba'),
                                   ('ForumdeItamarati', 'Fórum de Itamarati'),
                                   ('ForumdeItamari', 'Fórum de Itamari'),
                                   ('ForumdeItambacuri', 'Fórum de Itambacuri'),
                                   ('ForumdeItambe', 'Fórum de Itambé'),
                                   ('ForumdeItambePE', 'Fórum de Itambé - PE'),
                                   ('ForumdeItamogi', 'Fórum de Itamogi'),
                                   ('ForumdeItamonte', 'Fórum de Itamonte'),
                                   ('ForumdeItanhandu', 'Fórum de Itanhandu'),
                                   ('ForumdeItanhaem', 'Fórum de Itanhaém'),
                                   ('ForumdeItanhomi', 'Fórum de Itanhomi'),
                                   ('ForumdeItanhem', 'Fórum de Itanhém'),
                                   ('ForumdeItaocara', 'Fórum de Itaocara'),
                                   ('ForumdeItapaci', 'Fórum de Itapaci'),
                                   ('ForumdeItapagipe', 'Fórum de Itapagipe'),
                                   ('ForumdeItapaje', 'Fórum de Itapaje'),
                                   ('ForumdeItaparica', 'Fórum de Itaparica'),
                                   ('ForumdeItapebi', 'Fórum de Itapebi'),
                                   ('ForumdeItapecerica', 'Fórum de Itapecerica'),
                                   ('ForumdeItapecericadaSerra', 'Fórum de Itapecerica da Serra'),
                                   ('ForumdeItapecuruMirim', 'Fórum de Itapecuru Mirim'),
                                   ('ForumdeItapema', 'Fórum de Itapema'),
                                   ('ForumdeItapemirim', 'Fórum de Itapemirim'),
                                   ('ForumdeItaperuna', 'Fórum de Itaperuna'),
                                   ('ForumdeItapetim', 'Fórum de Itapetim'),
                                   ('ForumdeItapetinga', 'Fórum de Itapetinga'),
                                   ('ForumdeItapetininga', 'Fórum de Itapetininga'),
                                   ('ForumdeItapeva', 'Fórum de Itapeva'),
                                   ('ForumdeItapevi', 'Fórum de Itapevi'),
                                   ('ForumdeItapicuru', 'Fórum de Itapicuru'),
                                   ('ForumdeItapipoca', 'Fórum de Itapipoca'),
                                   ('ForumdeItapira', 'Fórum de Itapira'),
                                   ('ForumdeItapiranga', 'Fórum de Itapiranga'),
                                   ('ForumdeItapirapua', 'Fórum de Itapirapuã'),
                                   ('ForumdeItapissuma', 'Fórum de Itapissuma'),
                                   ('ForumdeItapitanga', 'Fórum de Itapitanga'),
                                   ('ForumdeItapiuna', 'Fórum de Itapiuna'),
                                   ('ForumdeItapoa', 'Fórum de Itapoa'),
                                   ('ForumdeItaporanga', 'Fórum de Itaporanga'),
                                   ('ForumdeItaporangaPB', 'Fórum de Itaporanga - PB'),
                                   ('ForumdeItaporangaDAjuda', 'Fórum de Itaporanga D''Ajuda'),
                                   ('ForumdeItapora', 'Fórum de Itaporã'),
                                   ('ForumdeItapuranga', 'Fórum de Itapuranga'),
                                   ('ForumdeItaquaquecetuba', 'Fórum de Itaquaquecetuba'),
                                   ('ForumdeItaquara', 'Fórum de Itaquara'),
                                   ('ForumdeItaqui', 'Fórum de Itaqui'),
                                   ('ForumdeItaquirai', 'Fórum de Itaquiraí'),
                                   ('ForumdeItaquitinga', 'Fórum de Itaquitinga'),
                                   ('ForumdeItarana', 'Fórum de Itarana'),
                                   ('ForumdeItarantim', 'Fórum de Itarantim'),
                                   ('ForumdeItarare', 'Fórum de Itararé'),
                                   ('ForumdeItarema', 'Fórum de Itarema'),
                                   ('ForumdeItariri', 'Fórum de Itariri'),
                                   ('ForumdeItatiaia', 'Fórum de Itatiaia'),
                                   ('ForumdeItatiba', 'Fórum de Itatiba'),
                                   ('ForumdeItatinga', 'Fórum de Itatinga'),
                                   ('ForumdeItatira', 'Fórum de Itatira'),
                                   ('ForumdeItaueira', 'Fórum de Itaueira'),
                                   ('ForumdeItaucu', 'Fórum de Itauçu'),
                                   ('ForumdeItai', 'Fórum de Itaí'),
                                   ('ForumdeItauba', 'Fórum de Itaúba'),
                                   ('ForumdeItauna', 'Fórum de Itaúna'),
                                   ('ForumdeItinga', 'Fórum de Itinga'),
                                   ('ForumdeItiquira', 'Fórum de Itiquira'),
                                   ('ForumdeItirapina', 'Fórum de Itirapina'),
                                   ('ForumdeItirucu', 'Fórum de Itiruçu'),
                                   ('ForumdeItiuba', 'Fórum de Itiúba'),
                                   ('ForumdeItororo', 'Fórum de Itororó'),
                                   ('ForumdeItu', 'Fórum de Itu'),
                                   ('ForumdeItuacu', 'Fórum de Ituaçu'),
                                   ('ForumdeItubera', 'Fórum de Ituberá'),
                                   ('ForumdeItuiutaba', 'Fórum de Ituiutaba'),
                                   ('ForumdeItumbiara', 'Fórum de Itumbiara'),
                                   ('ForumdeItumirim', 'Fórum de Itumirim'),
                                   ('ForumdeItupiranga', 'Fórum de Itupiranga'),
                                   ('ForumdeItuporanga', 'Fórum de Ituporanga'),
                                   ('ForumdeIturama', 'Fórum de Iturama'),
                                   ('ForumdeItuverava', 'Fórum de Ituverava'),
                                   ('ForumdeItapolis', 'Fórum de Itápolis'),
                                   ('ForumdeIvaipora', 'Fórum de Ivaiporã'),
                                   ('ForumdeIvinhema', 'Fórum de Ivinhema'),
                                   ('ForumdeIvolandia', 'Fórum de Ivolândia'),
                                   ('ForumdeIuna', 'Fórum de Iúna'),
                                   ('ForumdeLagoadoItaenga', 'Fórum de Lagoa do Itaenga'),
                                   ('ForumdeMajorIsidoro', 'Fórum de Major Isidoro'),
                                   ('ForumdeNovaIguacu', 'Fórum de Nova Iguacu'),
                                   ('ForumdePedroII', 'Fórum de Pedro II'),
                                   ('ForumdePioIX', 'Fórum de Pio IX'),
                                   ('ForumdePrincesaIsabel', 'Fórum de Princesa Isabel'),
                                   ('ForumdeQuedasdoIguacu', 'Fórum de Quedas do Iguaçu'),
                                   ('ForumdeSantaInes', 'Fórum de Santa Inês'),
                                   ('ForumdeSantaInesMA', 'Fórum de Santa Inês - MA')
                                   ;
                                   INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumdeSantaIsabel', 'Fórum de Santa Isabel'),
                                   ('ForumdeSantaIzabeldoIvai', 'Fórum de Santa Izabel do Ivaí'),
                                   ('ForumdeSantaIzabelDoPara', 'Fórum de Santa Izabel Do Pará'),
                                   ('ForumdeSantaIzabelDoRioNegro', 'Fórum de Santa Izabel Do Rio Negro'),
                                   ('ForumdeSantaLuziadoItanhy', 'Fórum de Santa Luzia do Itanhy'),
                                   ('ForumdeSantanadoIpanema', 'Fórum de Santana do Ipanema'),
                                   ('ForumdeSaoFranciscodeItabapoana', 'Fórum de São Francisco de Itabapoana'),
                                   ('ForumdeSaoJoaodoIvai', 'Fórum de São João do Ivaí'),
                                   ('ForumdeSaoMigueldoIguacu', 'Fórum de São Miguel do Iguaçu'),
                                   ('ForumdeVendaNovadoImigrante', 'Fórum de Venda Nova do Imigrante'),
                                   ('ForumDistritaldeIcoraci', 'Fórum Distrital de Icoraci'),
                                   ('ForumFederaldeFortalezaI', 'Fórum Federal de Fortaleza I'),
                                   ('ForumFederaldeFortalezaII', 'Fórum Federal de Fortaleza II'),
                                   ('ForumFederaldeFortalezaIII', 'Fórum Federal de Fortaleza III'),
                                   ('ForumFederaldeFozDoIguacu', 'Fórum Federal de Foz Do Iguaçu'),
                                   ('ForumFederaldeIlheus', 'Fórum Federal de Ilhéus'),
                                   ('ForumFederaldeImperatriz', 'Fórum Federal de Imperatriz'),
                                   ('ForumFederaldeIpatinga', 'Fórum Federal de Ipatinga'),
                                   ('ForumFederaldeItabaiana', 'Fórum Federal de Itabaiana'),
                                   ('ForumFederaldeItaborai', 'Fórum Federal de Itaboraí'),
                                   ('ForumFederaldeItabuna', 'Fórum Federal de Itabuna'),
                                   ('ForumFederaldeItajai', 'Fórum Federal de Itajaí'),
                                   ('ForumFederaldeItaperuna', 'Fórum Federal de Itaperuna'),
                                   ('ForumFederaldeJuizdeForaI', 'Fórum Federal de Juiz de Fora I'),
                                   ('ForumFederaldeJuizdeForaII', 'Fórum Federal de Juiz de Fora II'),
                                   ('ForumFederaldeRecifeI', 'Fórum Federal de Recife I'),
                                   ('ForumFederaldeRecifeII', 'Fórum Federal de Recife II'),
                                   ('ForumFederaldeVitoriaII', 'Fórum Federal de Vitória II'),
                                   ('ForumFederaldoRiodeJaneiroI', 'Fórum Federal do Rio de Janeiro I'),
                                   ('ForumFederaldoRiodeJaneiroII', 'Fórum Federal do Rio de Janeiro II'),
                                   ('ForumRegionalItaipava', 'Fórum Regional - Itaipava'),
                                   ('ForumRegionaldeInhomirimMage', 'Fórum Regional de Inhomirim - Magé'),
                                   ('ForumRegionaldeItaquera', 'Fórum Regional de Itaquera'),
                                   ('ForumRegionaldoIpiranga', 'Fórum Regional do Ipiranga'),
                                   ('TrabalhistadeFozdoIguacu', 'Trabalhista de Foz do Iguaçu'),
                                   ('TrabalhistadeIgarassu', 'Trabalhista de Igarassu'),
                                   ('TrabalhistadeIguatu', 'Trabalhista de Iguatu'),
                                   ('TrabalhistadeIjui', 'Trabalhista de Ijuí'),
                                   ('TrabalhistadeIlheus', 'Trabalhista de Ilhéus'),
                                   ('TrabalhistadeImbituba', 'Trabalhista de Imbituba'),
                                   ('TrabalhistadeImperatriz', 'Trabalhista de Imperatriz'),
                                   ('TrabalhistadeIndaial', 'Trabalhista de Indaial'),
                                   ('TrabalhistadeIndaiatuba', 'Trabalhista de Indaiatuba'),
                                   ('TrabalhistadeIpiau', 'Trabalhista de Ipiaú'),
                                   ('TrabalhistadeIpojuca', 'Trabalhista de Ipojuca'),
                                   ('TrabalhistadeIpora', 'Trabalhista de Iporá'),
                                   ('TrabalhistadeIrati', 'Trabalhista de Irati'),
                                   ('TrabalhistadeIrece', 'Trabalhista de Irecê'),
                                   ('TrabalhistadeItabaiana', 'Trabalhista de Itabaiana'),
                                   ('TrabalhistadeItabaianaSe', 'Trabalhista de Itabaiana/Se'),
                                   ('TrabalhistadeItaberaba', 'Trabalhista de Itaberaba'),
                                   ('TrabalhistadeItabira', 'Trabalhista de Itabira'),
                                   ('TrabalhistadeItaborai', 'Trabalhista de Itaboraí'),
                                   ('TrabalhistadeItabuna', 'Trabalhista de Itabuna'),
                                   ('TrabalhistadeItacoatiara', 'Trabalhista de Itacoatiara'),
                                   ('TrabalhistadeItaguai', 'Trabalhista de Itaguaí'),
                                   ('TrabalhistadeItaituba', 'Trabalhista de Itaituba'),
                                   ('TrabalhistadeItajai', 'Trabalhista de Itajaí'),
                                   ('TrabalhistadeItajuba', 'Trabalhista de Itajubá'),
                                   ('TrabalhistadeItamaraju', 'Trabalhista de Itamaraju'),
                                   ('TrabalhistadeItanhaem', 'Trabalhista de Itanhaém'),
                                   ('TrabalhistadeItapecericadaSerra', 'Trabalhista de Itapecerica da Serra'),
                                   ('TrabalhistadeItaperuna', 'Trabalhista de Itaperuna'),
                                   ('TrabalhistadeItapetinga', 'Trabalhista de Itapetinga'),
                                   ('TrabalhistadeItapetininga', 'Trabalhista de Itapetininga'),
                                   ('TrabalhistadeItapeva', 'Trabalhista de Itapeva'),
                                   ('TrabalhistadeItapira', 'Trabalhista de Itapira'),
                                   ('TrabalhistadeItaporanga', 'Trabalhista de Itaporanga'),
                                   ('TrabalhistadeItaquaquecetuba', 'Trabalhista de Itaquaquecetuba'),
                                   ('TrabalhistadeItarare', 'Trabalhista de Itararé'),
                                   ('TrabalhistadeItatiba', 'Trabalhista de Itatiba'),
                                   ('TrabalhistadeItauna', 'Trabalhista de Itaúna'),
                                   ('TrabalhistadeItu', 'Trabalhista de Itu'),
                                   ('TrabalhistadeItuiutaba', 'Trabalhista de Ituiutaba'),
                                   ('TrabalhistadeItumbiara', 'Trabalhista de Itumbiara'),
                                   ('TrabalhistadeItuverava', 'Trabalhista de Ituverava'),
                                   ('TrabalhistadeItapolis', 'Trabalhista de Itápolis'),
                                   ('TrabalhistadeIvaipora', 'Trabalhista de Ivaiporã'),
                                   ('TrabalhistadeNovaIguacu', 'Trabalhista de Nova Iguaçu'),
                                   ('TrabalhistadeSantaInes', 'Trabalhista de Santa Inês'),
                                   ('TrabalhistadeSantaIzabeldoPara', 'Trabalhista de Santa Izabel do Pará'),
                                   ('TrabalhistadeSantanadoIpanema', 'Trabalhista de Santana do Ipanema'),
                                   ('ForumdeJaboticabal', 'Fórum de Jaboticabal'),
                                   ('ForumdeJabuticatubas', 'Fórum de Jabuticatubas'),
                                   ('ForumdeJacarau', 'Fórum de Jacaraú'),
                                   ('ForumdeJacareacanga', 'Fórum de Jacareacanga'),
                                   ('ForumdeJacarezinho', 'Fórum de Jacarezinho'),
                                   ('ForumdeJacarei', 'Fórum de Jacareí'),
                                   ('ForumdeJaciara', 'Fórum de Jaciara'),
                                   ('ForumdeJacinto', 'Fórum de Jacinto'),
                                   ('ForumdeJacobina', 'Fórum de Jacobina'),
                                   ('ForumdeJacunda', 'Fórum de Jacundá'),
                                   ('ForumdeJacupiranga', 'Fórum de Jacupiranga'),
                                   ('ForumdeJacutinga', 'Fórum de Jacutinga'),
                                   ('ForumdeJacui', 'Fórum de Jacuí'),
                                   ('ForumdeJaguapita', 'Fórum de Jaguapitã'),
                                   ('ForumdeJaguaquara', 'Fórum de Jaguaquara'),
                                   ('ForumdeJaguarari', 'Fórum de Jaguarari'),
                                   ('ForumdeJaguaretama', 'Fórum de Jaguaretama'),
                                   ('ForumdeJaguari', 'Fórum de Jaguari'),
                                   ('ForumdeJaguariaiva', 'Fórum de Jaguariaíva'),
                                   ('ForumdeJaguaripe', 'Fórum de Jaguaripe'),
                                   ('ForumdeJaguariuna', 'Fórum de Jaguariúna'),
                                   ('ForumdeJaguaruana', 'Fórum de Jaguaruana'),
                                   ('ForumdeJaguaruna', 'Fórum de Jaguaruna'),
                                   ('ForumdeJaguarao', 'Fórum de Jaguarão'),
                                   ('ForumdeJaguare', 'Fórum de Jaguaré'),
                                   ('ForumdeJaicos', 'Fórum de Jaicós'),
                                   ('ForumdeJales', 'Fórum de Jales'),
                                   ('ForumdeJanauba', 'Fórum de Janaúba'),
                                   ('ForumdeJandaia', 'Fórum de Jandaia'),
                                   ('ForumdeJandaiadoSul', 'Fórum de Jandaia do Sul'),
                                   ('ForumdeJanduis', 'Fórum de Janduís'),
                                   ('ForumdeJanuaria', 'Fórum de Januária'),
                                   ('ForumdeJaparatuba', 'Fórum de Japaratuba'),
                                   ('ForumdeJaperi', 'Fórum de Japeri'),
                                   ('ForumdeJapoata', 'Fórum de Japoatã'),
                                   ('ForumdeJapura', 'Fórum de Japurá'),
                                   ('ForumdeJaraguadoSul', 'Fórum de Jaragua do Sul'),
                                   ('ForumdeJaragua', 'Fórum de Jaraguá'),
                                   ('ForumdeJardimMS', 'Fórum de Jardim - MS'),
                                   ('ForumdeJardimdePiranhas', 'Fórum de Jardim de Piranhas'),
                                   ('ForumdeJardimdoSerido', 'Fórum de Jardim do Seridó'),
                                   ('ForumdeJardinopolis', 'Fórum de Jardinópolis'),
                                   ('ForumdeJarinu', 'Fórum de Jarinu'),
                                   ('ForumdeJaru', 'Fórum de Jaru'),
                                   ('ForumdeJatai', 'Fórum de Jataí'),
                                   ('ForumdeJatauba', 'Fórum de Jataúba'),
                                   ('ForumdeJati', 'Fórum de Jati'),
                                   ('ForumdeJauru', 'Fórum de Jauru'),
                                   ('ForumdeJau', 'Fórum de Jaú'),
                                   ('ForumdeJequeri', 'Fórum de Jequeri'),
                                   ('ForumdeJequitinhonha', 'Fórum de Jequitinhonha'),
                                   ('ForumdeJequie', 'Fórum de Jequié'),
                                   ('ForumdeJeremoabo', 'Fórum de Jeremoabo'),
                                   ('ForumdeJerumenha', 'Fórum de Jerumenha'),
                                   ('ForumdeJeronimoMonteiro', 'Fórum de Jerônimo Monteiro'),
                                   ('ForumdeJiParana', 'Fórum de Ji-Paraná'),
                                   ('ForumdeJiquirica', 'Fórum de Jiquiriçá'),
                                   ('ForumdeJitauna', 'Fórum de Jitaúna'),
                                   ('ForumdeJoacaba', 'Fórum de Joacaba'),
                                   ('ForumdeJoaquimNabuco', 'Fórum de Joaquim Nabuco'),
                                   ('ForumdeJoaquimPires', 'Fórum de Joaquim Pires'),
                                   ('ForumdeJoaquimTavora', 'Fórum de Joaquim Távora'),
                                   ('ForumdeJoinville', 'Fórum de Joinville'),
                                   ('ForumdeJordao', 'Fórum de Jordão'),
                                   ('ForumdeJoselandia', 'Fórum de Joselândia'),
                                   ('ForumdeJosedeFreitas', 'Fórum de José de Freitas'),
                                   ('ForumdeJoaoDourado', 'Fórum de João Dourado'),
                                   ('ForumdeJoaoLisboa', 'Fórum de João Lisboa'),
                                   ('ForumdeJoaoMonlevade', 'Fórum de João Monlevade'),
                                   ('ForumdeJoaoNeiva', 'Fórum de João Neiva'),
                                   ('ForumdeJoaoPinheiro', 'Fórum de João Pinheiro'),
                                   ('ForumdeJuara', 'Fórum de Juara'),
                                   ('ForumdeJuazeirinho', 'Fórum de Juazeirinho'),
                                   ('ForumdeJuazeiro', 'Fórum de Juazeiro'),
                                   ('ForumdeJuazeiroDoNorte', 'Fórum de Juazeiro Do Norte'),
                                   ('ForumdeJucurutu', 'Fórum de Jucurutu'),
                                   ('ForumdeJuizdeFora', 'Fórum de Juiz de Fora'),
                                   ('ForumdeJundiai', 'Fórum de Jundiaí'),
                                   ('ForumdeJunqueiro', 'Fórum de Junqueiro'),
                                   ('ForumdeJunqueiropolis', 'Fórum de Junqueirópolis'),
                                   ('ForumdeJupi', 'Fórum de Jupi'),
                                   ('ForumdeJuquia', 'Fórum de Juquiá'),
                                   ('ForumdeJurema', 'Fórum de Jurema'),
                                   ('ForumdeJurua', 'Fórum de Jurua'),
                                   ('ForumdeJuscimeira', 'Fórum de Juscimeira'),
                                   ('ForumdeJussara', 'Fórum de Jussara'),
                                   ('ForumdeJutai', 'Fórum de Jutaí'),
                                   ('ForumdeJuina', 'Fórum de Juína'),
                                   ('ForumdeLaranjalDoJari', 'Fórum de Laranjal Do Jarí'),
                                   ('ForumdeMatadeSaoJoao', 'Fórum de Mata de São João'),
                                   ('ForumdeNovoSaoJoaquim', 'Fórum de Novo São Joaquim'),
                                   ('ForumdePauloJacinto', 'Fórum de Paulo Jacinto'),
                                   ('ForumdePresidenteJanioQuadros', 'Fórum de Presidente Jânio Quadros'),
                                   ('ForumdeRiachaodoJacuipe', 'Fórum de Riachão do Jacuípe'),
                                   ('ForumdeSaltodoJacui', 'Fórum de Salto do Jacuí'),
                                   ('ForumdeSantaMariadeJetiba', 'Fórum de Santa Maria de Jetibá'),
                                   ('ForumdeSenadorJosePorfirio', 'Fórum de Senador José Porfírio'),
                                   ('ForumdeSilvaJardim', 'Fórum de Silva Jardim'),
                                   ('ForumdeSaoJeronimo', 'Fórum de São Jerônimo'),
                                   ('ForumdeSaoJeronimodaSerra', 'Fórum de São Jerônimo da Serra'),
                                   ('ForumdeSaoJoaodeMeriti', 'Fórum de São Joao de Meriti'),
                                   ('ForumdeSaoJoaquim', 'Fórum de São Joaquim'),
                                   ('ForumdeSaoJoaquimdoMonte', 'Fórum de São Joaquim do Monte'),
                                   ('ForumdeSaoJose', 'Fórum de São José'),
                                   ('ForumdeSaoJosedaLagoaTapada', 'Fórum de São José da Lagoa Tapada'),
                                   ('ForumdeSaoJosedaLaje', 'Fórum de São José da Laje'),
                                   ('ForumdeSaoJosedaTapera', 'Fórum de São José da Tapera'),
                                   ('ForumdeSaoJosedeMipibu', 'Fórum de São José de Mipibu'),
                                   ('ForumdeSaoJosedePiranhas', 'Fórum de São José de Piranhas'),
                                   ('ForumdeSaoJosedeRibamar', 'Fórum de São José de Ribamar'),
                                   ('ForumdeSaoJosedoJacuipe', 'Fórum de São José do Jacuípe'),
                                   ('ForumdeSaoJosedoNorte', 'Fórum de São José do Norte'),
                                   ('ForumdeSaoJosedoOuro', 'Fórum de São José do Ouro'),
                                   ('ForumdeSaoJosedoRioPardo', 'Fórum de São José do Rio Pardo'),
                                   ('ForumdeSaoJosedoRioPreto', 'Fórum de São José do Rio Preto'),
                                   ('ForumdeSaoJosedoValedoRioPreto', 'Fórum de São José do Vale do Rio Preto'),
                                   ('ForumdeSaoJosedosPinhais', 'Fórum de São José dos Pinhais'),
                                   ('ForumdeSaoJoseDosQuatroMarcos', 'Fórum de São José Dos Quatro Marcos'),
                                   ('ForumdeSaoJoao', 'Fórum de São João'),
                                   ('ForumdeSaoJoaodaPonte', 'Fórum de São João da Ponte'),
                                   ('ForumdeSaoJoaoDelRei', 'Fórum de São João Del Rei'),
                                   ('ForumdeSaoJoaodoPiaui', 'Fórum de São João do Piauí'),
                                   ('ForumdeSaoJoaodoRiodoPeixe', 'Fórum de São João do Rio do Peixe'),
                                   ('ForumdeSaoJoaodoSabugi', 'Fórum de São João do Sabugi'),
                                   ('ForumdeSaoJoaodoTriunfo', 'Fórum de São João do Triunfo'),
                                   ('ForumdeSaoJoaodosPatos', 'Fórum de São João dos Patos'),
                                   ('ForumdeSaoJoaoNepomuceno', 'Fórum de São João Nepomuceno'),
                                   ('ForumdeVtoriaDoJari', 'Fórum de Vtória Do Jari'),
                                   ('ForumDistritaldeMangabeiraJoaoPessoa', 'Fórum Distrital de Mangabeira - João Pessoa'),
                                   ('ForumFederaldeJacarezinho', 'Fórum Federal de Jacarezinho'),
                                   ('ForumFederaldeJales', 'Fórum Federal de Jales'),
                                   ('ForumFederaldeJaraguaDoSul', 'Fórum Federal de Jaraguá Do Sul'),
                                   ('ForumFederaldeJau', 'Fórum Federal de Jaú'),
                                   ('ForumFederaldeJequie', 'Fórum Federal de Jequie'),
                                   ('ForumFederaldeJiParana', 'Fórum Federal de Ji-Paraná'),
                                   ('ForumFederaldeJoacaba', 'Fórum Federal de Joaçaba'),
                                   ('ForumFederaldeJoinville', 'Fórum Federal de Joinville'),
                                   ('ForumFederaldeJoaoPessoa', 'Fórum Federal de João Pessoa'),
                                   ('ForumFederaldeJuazeiro', 'Fórum Federal de Juazeiro'),
                                   ('ForumFederaldeJuazeirodoNorte', 'Fórum Federal de Juazeiro do Norte'),
                                   ('ForumFederaldeSaoJosedoRioPreto', 'Fórum Federal de São José do Rio Preto'),
                                   ('ForumFederaldeSaoJoaoDeMeriti', 'Fórum Federal de São João De Meriti'),
                                   ('ForumFederaldeSaoJoaoDelRei', 'Fórum Federal de São João Del Rei'),
                                   ('ForumJoaoMendesJunior', 'Fórum João Mendes Júnior'),
                                   ('ForumRegionaldeJacarepagua', 'Fórum Regional de Jacarepagua'),
                                   ('ForumRegionaldoJabaquara', 'Fórum Regional do Jabaquara'),
                                   ('JeccdePalmeiradosIndios', 'Jecc de Palmeira dos Índios'),
                                   ('JuizadodeSinop', 'Juizado de Sinop'),
                                   ('JuizadodeSorriso', 'Juizado de Sorriso'),
                                   ('JuizadodeTangaraDaSerra', 'Juizado de Tangará Da Serra'),
                                   ('JUVAM', 'JUVAM'),
                                   ('SuperiorTribunaldeJustica', 'Superior Tribunal de Justiça'),
                                   ('TrabalhistadeJaboticabal', 'Trabalhista de Jaboticabal'),
                                   ('TrabalhistadeJacarezinho', 'Trabalhista de Jacarezinho'),
                                   ('TrabalhistadeJacarei', 'Trabalhista de Jacareí'),
                                   ('TrabalhistadeJaciara', 'Trabalhista de Jaciara'),
                                   ('TrabalhistadeJacobina', 'Trabalhista de Jacobina'),
                                   ('TrabalhistadeJaguariaiva', 'Trabalhista de Jaguariaiva'),
                                   ('TrabalhistadeJales', 'Trabalhista de Jales'),
                                   ('TrabalhistadeJandira', 'Trabalhista de Jandira'),
                                   ('TrabalhistadeJanuaria', 'Trabalhista de Januária'),
                                   ('TrabalhistadeJaraguadoSul', 'Trabalhista de Jaraguá do Sul'),
                                   ('TrabalhistadeJardim', 'Trabalhista de Jardim'),
                                   ('TrabalhistadeJaru', 'Trabalhista de Jaru'),
                                   ('TrabalhistadeJatai', 'Trabalhista de Jataí'),
                                   ('TrabalhistadeJau', 'Trabalhista de Jaú'),
                                   ('TrabalhistadeJequie', 'Trabalhista de Jequié'),
                                   ('TrabalhistadeJiParana', 'Trabalhista de Ji-Paraná'),
                                   ('TrabalhistadeJoacaba', 'Trabalhista de Joaçaba'),
                                   ('TrabalhistadeJoinville', 'Trabalhista de Joinville'),
                                   ('TrabalhistadeJoaoMonlevade', 'Trabalhista de João Monlevade'),
                                   ('TrabalhistadeJoaoPessoa', 'Trabalhista de João Pessoa'),
                                   ('TrabalhistadeJuazeiro', 'Trabalhista de Juazeiro'),
                                   ('TrabalhistadeJuazeirodoNorte', 'Trabalhista de Juazeiro do Norte'),
                                   ('TrabalhistadeJuizdeFora', 'Trabalhista de Juiz de Fora'),
                                   ('TrabalhistadeJundiai', 'Trabalhista de Jundiaí'),
                                   ('TrabalhistadeJuina', 'Trabalhista de Juína'),
                                   ('TrabalhistadeLaranjaldoJari', 'Trabalhista de Laranjal do Jari'),
                                   ('TrabalhistadeManausPraca14deJaneiro', 'Trabalhista de Manaus - Praça 14 de Janeiro'),
                                   ('TrabalhistadeSaoJosedoRioPardo', 'Trabalhista de Sao José do Rio Pardo'),
                                   ('TrabalhistadeSaoJeronimo', 'Trabalhista de São Jerônimo'),
                                   ('TrabalhistadeSaoJose', 'Trabalhista de São José'),
                                   ('TrabalhistadeSaoJosedoRioPreto', 'Trabalhista de São José do Rio Preto'),
                                   ('TrabalhistadeSaoJosedosPinhais', 'Trabalhista de São José dos Pinhais'),
                                   ('TrabalhistadeSaoJoaodeMeriti', 'Trabalhista de São João de Meriti'),
                                   ('TrabalhistadeSaoJoaoDelRei', 'Trabalhista de São João Del Rei'),
                                   ('TrabalhistadeSaoJoaodosPatos', 'Trabalhista de São João dos Patos'),
                                   ('TrabalhistadoRiodeJaneiroLapa', 'Trabalhista do Rio de Janeiro - Lapa'),
                                   ('TribunaldeJusticadaParaiba', 'Tribunal de Justiça da Paraíba'),
                                   ('TribunaldeJusticadePernambuco', 'Tribunal de Justiça de Pernambuco'),
                                   ('TribunaldeJusticadeRondonia', 'Tribunal de Justiça de Rondônia'),
                                   ('TribunaldeJusticadeRoraima', 'Tribunal de Justiça de Roraima'),
                                   ('TribunaldeJusticadeSergipe', 'Tribunal de Justiça de Sergipe'),
                                   ('TribunaldeJusticadeSaoPaulo', 'Tribunal de Justiça de São Paulo'),
                                   ('TribunaldeJusticadoDistritoFederal', 'Tribunal de Justiça do Distrito Federal'),
                                   ('TribunaldeJusticadoMaranhao', 'Tribunal de Justiça do Maranhão'),
                                   ('TribunaldeJusticadoParana', 'Tribunal de Justiça do Paraná'),
                                   ('TribunaldeJusticadoPara', 'Tribunal de Justiça do Pará'),
                                   ('TribunaldeJusticadoPiaui', 'Tribunal de Justiça do Piauí'),
                                   ('TribunaldeJusticadoTocantins', 'Tribunal de Justiça do Tocantins'),
                                   ('VaradoTribunaldoJurideTeresina', 'Vara do Tribunal do Júri de Teresina'),
                                   ('ForumdePresidenteKennedy', 'Fórum de Presidente Kennedy'),
                                   ('ForumdeLagarto', 'Fórum de Lagarto'),
                                   ('ForumdeLage', 'Fórum de Lage'),
                                   ('ForumdeLagedoMuriae', 'Fórum de Lage do Muriaé'),
                                   ('ForumdeLages', 'Fórum de Lages'),
                                   ('ForumdeLagodaPedra', 'Fórum de Lago da Pedra'),
                                   ('ForumdeLagoVerde', 'Fórum de Lago Verde'),
                                   ('ForumdeLagoadoOuro', 'Fórum de Lagoa do Ouro'),
                                   ('ForumdeLagoadoPrata', 'Fórum de Lagoa do Prata'),
                                   ('ForumdeLagoaSanta', 'Fórum de Lagoa Santa'),
                                   ('ForumdeLagoaVermelha', 'Fórum de Lagoa Vermelha'),
                                   ('ForumdeLaguna', 'Fórum de Laguna'),
                                   ('ForumdeLajeado', 'Fórum de Lajeado'),
                                   ('ForumdeLajedo', 'Fórum de Lajedo'),
                                   ('ForumdeLajes', 'Fórum de Lajes'),
                                   ('ForumdeLajinha', 'Fórum de Lajinha'),
                                   ('ForumdeLambari', 'Fórum de Lambari'),
                                   ('ForumdeLandriSales', 'Fórum de Landri Sales'),
                                   ('ForumdeLapa', 'Fórum de Lapa'),
                                   ('ForumdeLapao', 'Fórum de Lapão'),
                                   ('ForumdeLaranjadaTerra', 'Fórum de Laranja da Terra'),
                                   ('ForumdeLaranjalPaulista', 'Fórum de Laranjal Paulista'),
                                   ('ForumdeLaranjeiras', 'Fórum de Laranjeiras'),
                                   ('ForumdeLaranjeirasdoSul', 'Fórum de Laranjeiras do Sul'),
                                   ('ForumdeLauroMuller', 'Fórum de Lauro Muller'),
                                   ('ForumdeLavras', 'Fórum de Lavras'),
                                   ('ForumdeLavrasdoSul', 'Fórum de Lavras do Sul'),
                                   ('ForumdeLavrasMangabeira', 'Fórum de Lavras Mangabeira'),
                                   ('ForumdeLebonRegis', 'Fórum de Lebon Regis'),
                                   ('ForumdeLeme', 'Fórum de Leme'),
                                   ('ForumdeLencois', 'Fórum de Lençóis'),
                                   ('ForumdeLeopoldina', 'Fórum de Leopoldina'),
                                   ('ForumdeLimaDuarte', 'Fórum de Lima Duarte'),
                                   ('ForumdeLimeira', 'Fórum de Limeira'),
                                   ('ForumdeLimoeiro', 'Fórum de Limoeiro'),
                                   ('ForumdeLimoeiroDoNorte', 'Fórum de Limoeiro Do Norte'),
                                   ('ForumdeLinhares', 'Fórum de Linhares'),
                                   ('ForumdeLins', 'Fórum de Lins'),
                                   ('ForumdeLivramentodeNossaSenhora', 'Fórum de Livramento de Nossa Senhora'),
                                   ('ForumdeLoanda', 'Fórum de Loanda'),
                                   ('ForumdeLondrina', 'Fórum de Londrina'),
                                   ('ForumdeLorena', 'Fórum de Lorena'),
                                   ('ForumdeLoreto', 'Fórum de Loreto'),
                                   ('ForumdeLucasDoRioVerde', 'Fórum de Lucas Do Rio Verde'),
                                   ('ForumdeLucena', 'Fórum de Lucena'),
                                   ('ForumdeLucelia', 'Fórum de Lucélia'),
                                   ('ForumdeLuz', 'Fórum de Luz'),
                                   ('ForumdeLuzilandia', 'Fórum de Luzilândia'),
                                   ('ForumdeLabrea', 'Fórum de Lábrea'),
                                   ('ForumdeMateusLeme', 'Fórum de Mateus Leme'),
                                   ('ForumdeMancioLima', 'Fórum de Mâncio Lima'),
                                   ('ForumdeNossaSenhoradeLourdes', 'Fórum de Nossa Senhora de Lourdes'),
                                   ('ForumdeNovaLima', 'Fórum de Nova Lima'),
                                   ('ForumdeNovaLondrina', 'Fórum de Nova Londrina'),
                                   ('ForumdeNovoLino', 'Fórum de Novo Lino'),
                                   ('ForumdePaesLandim', 'Fórum de Paes Landim'),
                                   ('ForumdePacodoLumiar', 'Fórum de Paço do Lumiar'),
                                   ('ForumdePedroLeopoldo', 'Fórum de Pedro Leopoldo'),
                                   ('ForumdePrimaveraDoLeste', 'Fórum de Primavera Do Leste'),
                                   ('ForumdeRioLargo', 'Fórum de Rio Largo'),
                                   ('ForumdeSaltodoLontra', 'Fórum de Salto do Lontra'),
                                   ('ForumdeSantaLeopoldina', 'Fórum de Santa Leopoldina'),
                                   ('ForumdeSantaLuz', 'Fórum de Santa Luz'),
                                   ('ForumdeSantaLuzia', 'Fórum de Santa Luzia'),
                                   ('ForumdeSantaLuziaMA', 'Fórum de Santa Luzia - MA'),
                                   ('ForumdeSantaLuziaMG', 'Fórum de Santa Luzia - MG'),
                                   ('ForumdeSantaLuziaDoOeste', 'Fórum de Santa Luzia Do Oeste'),
                                   ('ForumdeSantaLuziadoParua', 'Fórum de Santa Luzia do Paruá'),
                                   ('ForumdeSantaLuziaDoPara', 'Fórum de Santa Luzia Do Pará'),
                                   ('ForumdeSantaRosadeLima', 'Fórum de Santa Rosa de Lima'),
                                   ('ForumdeSantanadoLivramento', 'Fórum de Santana do Livramento'),
                                   ('ForumdeSenadorLaRoque', 'Fórum de Senador La Roque'),
                                   ('ForumdeSeteLagoas', 'Fórum de Sete Lagoas'),
                                   ('ForumdeSaoLeopoldo', 'Fórum de São Leopoldo'),
                                   ('ForumdeSaoLourencodoOeste', 'Fórum de São Lourenco do Oeste'),
                                   ('ForumdeSaoLourenco', 'Fórum de São Lourenço'),
                                   ('ForumdeSaoLourencodaMata', 'Fórum de São Lourenço da Mata'),
                                   ('ForumdeSaoLourencodoSul', 'Fórum de São Lourenço do Sul'),
                                   ('ForumdeSaoLuisdoQuitunde', 'Fórum de São Luis do Quitunde'),
                                   ('ForumdeSaoLuis', 'Fórum de São Luís'),
                                   ('ForumdeSaoLuisdoParaitinga', 'Fórum de São Luís do Paraitinga'),
                                   ('ForumdeTresLagoas', 'Fórum de Três Lagoas'),
                                   ('ForumdeAguasdeLindoia', 'Fórum de Águas de Lindóia'),
                                   ('ForumFederaldeLages', 'Fórum Federal de Lages'),
                                   ('ForumFederaldeLaguna', 'Fórum Federal de Laguna'),
                                   ('ForumFederaldeLajeado', 'Fórum Federal de Lajeado'),
                                   ('ForumFederaldeLavras', 'Fórum Federal de Lavras'),
                                   ('ForumFederaldeLimoeirodoNorte', 'Fórum Federal de Limoeiro do Norte'),
                                   ('ForumFederaldeLinhares', 'Fórum Federal de Linhares'),
                                   ('ForumFederaldeLondrina', 'Fórum Federal de Londrina'),
                                   ('ForumFederaldeLuziania', 'Fórum Federal de Luziânia'),
                                   ('ForumFederaldeSantanaDoLivramento', 'Fórum Federal de Santana Do Livramento'),
                                   ('ForumFederaldeSeteLagoas', 'Fórum Federal de Sete Lagoas'),
                                   ('ForumFederaldeSaoLuis', 'Fórum Federal de São Luís'),
                                   ('ForumFederaldeTresLagoas', 'Fórum Federal de Três Lagoas'),
                                   ('ForumLucioFontesManaus', 'Fórum Lúcio Fontes - Manaus'),
                                   ('ForumRegionaldaLapa', 'Fórum Regional da Lapa'),
                                   ('ForumRegionaldaLeopoldina', 'Fórum Regional da Leopoldina'),
                                   ('TrabalhistadeLagarto', 'Trabalhista de Lagarto'),
                                   ('TrabalhistadeLages', 'Trabalhista de Lages'),
                                   ('TrabalhistadeLagoaVermelha', 'Trabalhista de Lagoa Vermelha'),
                                   ('TrabalhistadeLajeado', 'Trabalhista de Lajeado'),
                                   ('TrabalhistadeLaranjeirasdoSul', 'Trabalhista de Laranjeiras do Sul'),
                                   ('TrabalhistadeLavras', 'Trabalhista de Lavras'),
                                   ('TrabalhistadeLeme', 'Trabalhista de Leme'),
                                   ('TrabalhistadeLencoisPaulista', 'Trabalhista de Lençóis Paulista'),
                                   ('TrabalhistadeLimeira', 'Trabalhista de Limeira'),
                                   ('TrabalhistadeLimoeiro', 'Trabalhista de Limoeiro'),
                                   ('TrabalhistadeLimoeirodoNorte', 'Trabalhista de Limoeiro do Norte'),
                                   ('TrabalhistadeLinhares', 'Trabalhista de Linhares'),
                                   ('TrabalhistadeLins', 'Trabalhista de Lins'),
                                   ('TrabalhistadeLoanda', 'Trabalhista de Loanda'),
                                   ('TrabalhistadeLondrina', 'Trabalhista de Londrina'),
                                   ('TrabalhistadeLorena', 'Trabalhista de Lorena'),
                                   ('TrabalhistadeLuziania', 'Trabalhista de Luziânia'),
                                   ('TrabalhistadeLabrea', 'Trabalhista de Lábrea'),
                                   ('TrabalhistadeNovaLima', 'Trabalhista de Nova Lima'),
                                   ('TrabalhistadePedroLeopoldo', 'Trabalhista de Pedro Leopoldo'),
                                   ('TrabalhistadePrimaveradoLeste', 'Trabalhista de Primavera do Leste'),
                                   ('TrabalhistadeSantaLuzia', 'Trabalhista de Santa Luzia'),
                                   ('TrabalhistadeSantanadoLivramento', 'Trabalhista de Santana do Livramento'),
                                   ('TrabalhistadeSeteLagoas', 'Trabalhista de Sete Lagoas'),
                                   ('TrabalhistadeSaoLeopoldo', 'Trabalhista de São Leopoldo'),
                                   ('TrabalhistadeSaoLourencodaMata', 'Trabalhista de São Lourenço da Mata'),
                                   ('TrabalhistadeSaoLuisdoQuitunde', 'Trabalhista de São Luis do Quitunde'),
                                   ('TrabalhistadeSaoLuis', 'Trabalhista de São Luís'),
                                   ('TrabalhistadeTresLagoas', 'Trabalhista de Três Lagoas'),
                                   ('ForumdeMacae', 'Fórum de Macae'),
                                   ('ForumdeMacambira', 'Fórum de Macambira'),
                                   ('ForumdeMacaparana', 'Fórum de Macaparana'),
                                   ('ForumdeMacapa', 'Fórum de Macapá'),
                                   ('ForumdeMacarani', 'Fórum de Macarani'),
                                   ('ForumdeMacatuba', 'Fórum de Macatuba'),
                                   ('ForumdeMacau', 'Fórum de Macau'),
                                   ('ForumdeMacaubal', 'Fórum de Macaubal'),
                                   ('ForumdeMacaiba', 'Fórum de Macaíba'),
                                   ('ForumdeMacaubas', 'Fórum de Macaúbas'),
                                   ('ForumdeMaceioPontaVerde', 'Fórum de Maceió - Ponta Verde'),
                                   ('ForumdeMachadinhoDOeste', 'Fórum de Machadinho D''Oeste'),
                                   ('ForumdeMachado', 'Fórum de Machado'),
                                   ('ForumdeMacurere', 'Fórum de Macurerê'),
                                   ('ForumdeMadalena', 'Fórum de Madalena'),
                                   ('ForumdeMadredeDeus', 'Fórum de Madre de Deus'),
                                   ('ForumdeMafra', 'Fórum de Mafra'),
                                   ('ForumdeMage', 'Fórum de Mage'),
                                   ('ForumdeMairi', 'Fórum de Mairi'),
                                   ('ForumdeMairinque', 'Fórum de Mairinque'),
                                   ('ForumdeMairipora', 'Fórum de Mairiporã'),
                                   ('ForumdeMalacacheta', 'Fórum de Malacacheta'),
                                   ('ForumdeMalhada', 'Fórum de Malhada'),
                                   ('ForumdeMalhador', 'Fórum de Malhador'),
                                   ('ForumdeMallet', 'Fórum de Mallet'),
                                   ('ForumdeMalta', 'Fórum de Malta'),
                                   ('ForumdeMamanguape', 'Fórum de Mamanguape'),
                                   ('ForumdeMambore', 'Fórum de Mamborê'),
                                   ('ForumdeManacapuru', 'Fórum de Manacapurú'),
                                   ('ForumdeManaquiri', 'Fórum de Manaquiri'),
                                   ('ForumdeMandaguari', 'Fórum de Mandaguarí'),
                                   ('ForumdeMandaguacu', 'Fórum de Mandaguaçú'),
                                   ('ForumdeManga', 'Fórum de Manga'),
                                   ('ForumdeMangaratiba', 'Fórum de Mangaratiba'),
                                   ('ForumdeMangueirinha', 'Fórum de Mangueirinha'),
                                   ('ForumdeManhuacu', 'Fórum de Manhuaçu'),
                                   ('ForumdeManhumirim', 'Fórum de Manhumirim'),
                                   ('ForumdeManicore', 'Fórum de Manicoré'),
                                   ('ForumdeManoelRibas', 'Fórum de Manoel Ribas'),
                                   ('ForumdeManoelUrbano', 'Fórum de Manoel Urbano'),
                                   ('ForumdeMantena', 'Fórum de Mantena'),
                                   ('ForumdeMantenopolis', 'Fórum de Mantenópolis'),
                                   ('ForumdeMaraRosa', 'Fórum de Mara Rosa'),
                                   ('ForumdeMaraba', 'Fórum de Marabá'),
                                   ('ForumdeMaracaju', 'Fórum de Maracaju'),
                                   ('ForumdeMaracanau', 'Fórum de Maracanaú'),
                                   ('ForumdeMaracana', 'Fórum de Maracanã'),
                                   ('ForumdeMaracacume', 'Fórum de Maracaçumé'),
                                   ('ForumdeMaracai', 'Fórum de Maracaí'),
                                   ('ForumdeMaracas', 'Fórum de Maracás'),
                                   ('ForumdeMaragogi', 'Fórum de Maragogi'),
                                   ('ForumdeMaragogipe', 'Fórum de Maragogipe'),
                                   ('ForumdeMaraial', 'Fórum de Maraial'),
                                   ('ForumdeMaranguape', 'Fórum de Maranguape'),
                                   ('ForumdeMarapanim', 'Fórum de Marapanim'),
                                   ('ForumdeMarataizes', 'Fórum de Marataízes'),
                                   ('ForumdeMarau', 'Fórum de Marau'),
                                   ('ForumdeMaravilhaSC', 'Fórum de Maravilha - SC'),
                                   ('ForumdeMaraa', 'Fórum de Maraã'),
                                   ('ForumdeMarcelinoRamos', 'Fórum de Marcelino Ramos'),
                                   ('ForumdeMarcelinoVieira', 'Fórum de Marcelino Vieira'),
                                   ('ForumdeMarcelandia', 'Fórum de Marcelândia'),
                                   ('ForumdeMarcionilioSouza', 'Fórum de Marcionílio Souza'),
                                   ('ForumdeMarco', 'Fórum de Marco'),
                                   ('ForumdeMarcolandia', 'Fórum de Marcolândia'),
                                   ('ForumdeMarcosParente', 'Fórum de Marcos Parente'),
                                   ('ForumdeMarechalDeodoro', 'Fórum de Marechal Deodoro'),
                                   ('ForumdeMarechalFloriano', 'Fórum de Marechal Floriano'),
                                   ('ForumdeMarechalThaumaturgo', 'Fórum de Marechal Thaumaturgo'),
                                   ('ForumdeMari', 'Fórum de Mari'),
                                   ('ForumdeMarialva', 'Fórum de Marialva'),
                                   ('ForumdeMariana', 'Fórum de Mariana'),
                                   ('ForumdeMaribondo', 'Fórum de Maribondo'),
                                   ('ForumdeMarica', 'Fórum de Marica'),
                                   ('ForumdeMarilandia', 'Fórum de Marilândia'),
                                   ('ForumdeMarilandiadoSul', 'Fórum de Marilândia do Sul'),
                                   ('ForumdeMaringa', 'Fórum de Maringá'),
                                   ('ForumdeMarituba', 'Fórum de Marituba'),
                                   ('ForumdeMartins', 'Fórum de Martins'),
                                   ('ForumdeMartinopolis', 'Fórum de Martinópolis'),
                                   ('ForumdeMaruim', 'Fórum de Maruim'),
                                   ('ForumdeMarilia', 'Fórum de Marília'),
                                   ('ForumdeMassape', 'Fórum de Massape'),
                                   ('ForumdeMataRoma', 'Fórum de Mata Roma'),
                                   ('ForumdeMatelandia', 'Fórum de Matelândia'),
                                   ('ForumdeMatiasOlimpio', 'Fórum de Matias Olímpio'),
                                   ('ForumdeMatinha', 'Fórum de Matinha'),
                                   ('ForumdeMatinhos', 'Fórum de Matinhos'),
                                   ('ForumdeMatozinhos', 'Fórum de Matozinhos'),
                                   ('ForumdeMatupa', 'Fórum de Matupá')
                                   ;
                                   INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumdeMatao', 'Fórum de Matão'),
                                   ('ForumdeMatoes', 'Fórum de Matões'),
                                   ('ForumdeMauriti', 'Fórum de Mauriti'),
                                   ('ForumdeMaues', 'Fórum de Maués'),
                                   ('ForumdeMazagao', 'Fórum de Mazagão'),
                                   ('ForumdeMedeirosNeto', 'Fórum de Medeiros Neto'),
                                   ('ForumdeMedianeira', 'Fórum de Medianeira'),
                                   ('ForumdeMedicilandia', 'Fórum de Medicilândia'),
                                   ('ForumdeMedina', 'Fórum de Medina'),
                                   ('ForumdeMelgaco', 'Fórum de Melgaço'),
                                   ('ForumdeMendes', 'Fórum de Mendes'),
                                   ('ForumdeMerces', 'Fórum de Mercês'),
                                   ('ForumdeMeruoca', 'Fórum de Meruoca'),
                                   ('ForumdeMesquita', 'Fórum de Mesquita'),
                                   ('ForumdeMessias', 'Fórum de Messias'),
                                   ('ForumdeMiguelPereira', 'Fórum de Miguel Pereira'),
                                   ('ForumdeMiguelopolis', 'Fórum de Miguelópolis'),
                                   ('ForumdeMilagres', 'Fórum de Milagres'),
                                   ('ForumdeMimosodoSul', 'Fórum de Mimoso do Sul'),
                                   ('ForumdeMinasNovas', 'Fórum de Minas Novas'),
                                   ('ForumdeMinacu', 'Fórum de Minaçu'),
                                   ('ForumdeMineiros', 'Fórum de Mineiros'),
                                   ('ForumdeMiracatu', 'Fórum de Miracatu'),
                                   ('ForumdeMiracema', 'Fórum de Miracema'),
                                   ('ForumdeMiracemaDoTocantins', 'Fórum de Miracema Do Tocantins'),
                                   ('ForumdeMirador', 'Fórum de Mirador'),
                                   ('ForumdeMiradouro', 'Fórum de Miradouro'),
                                   ('ForumdeMiranda', 'Fórum de Miranda'),
                                   ('ForumdeMirandadoNorte', 'Fórum de Miranda do Norte'),
                                   ('ForumdeMirandiba', 'Fórum de Mirandiba'),
                                   ('ForumdeMirandopolis', 'Fórum de Mirandópolis'),
                                   ('ForumdeMiranorte', 'Fórum de Miranorte'),
                                   ('ForumdeMirantedoParanapanema', 'Fórum de Mirante do Paranapanema'),
                                   ('ForumdeMirassol', 'Fórum de Mirassol'),
                                   ('ForumdeMirassolDOeste', 'Fórum de Mirassol D''Oeste'),
                                   ('ForumdeMirai', 'Fórum de Miraí'),
                                   ('ForumdeMirinzal', 'Fórum de Mirinzal'),
                                   ('ForumdeMocajuba', 'Fórum de Mocajuba'),
                                   ('ForumdeMocambo', 'Fórum de Mocambo'),
                                   ('ForumdeMococa', 'Fórum de Mococa'),
                                   ('ForumdeModelo', 'Fórum de Modelo'),
                                   ('ForumdeMogiGuacu', 'Fórum de Mogi-Guaçu'),
                                   ('ForumdeMogiMirim', 'Fórum de Mogi-Mirim'),
                                   ('ForumdeMoju', 'Fórum de Mojú'),
                                   ('ForumdeMombaca', 'Fórum de Mombaça'),
                                   ('ForumdeMondai', 'Fórum de Mondai'),
                                   ('ForumdeMongagua', 'Fórum de Mongaguá'),
                                   ('ForumdeMonsenhorTabosa', 'Fórum de Monsenhor Tabosa'),
                                   ('ForumdeMontalvania', 'Fórum de Montalvânia'),
                                   ('ForumdeMontanha', 'Fórum de Montanha'),
                                   ('ForumdeMonteMor', 'Fórum de Monte Mor'),
                                   ('ForumdeMonteSanto', 'Fórum de Monte Santo'),
                                   ('ForumdeMonteSantodeMinas', 'Fórum de Monte Santo de Minas'),
                                   ('ForumdeMonteSiao', 'Fórum de Monte Sião'),
                                   ('ForumdeMonteiro', 'Fórum de Monteiro'),
                                   ('ForumdeMontenegro', 'Fórum de Montenegro'),
                                   ('ForumdeMoncao', 'Fórum de Monção'),
                                   ('ForumdeMoradaNova', 'Fórum de Morada Nova'),
                                   ('ForumdeMoradaNovadeMinas', 'Fórum de Morada Nova de Minas'),
                                   ('ForumdeMoreilandia', 'Fórum de Moreilandia'),
                                   ('ForumdeMoreno', 'Fórum de Moreno'),
                                   ('ForumdeMorpara', 'Fórum de Morpará'),
                                   ('ForumdeMorretes', 'Fórum de Morretes'),
                                   ('ForumdeMorrinhos', 'Fórum de Morrinhos'),
                                   ('ForumdeMorros', 'Fórum de Morros'),
                                   ('ForumdeMortugaba', 'Fórum de Mortugaba'),
                                   ('ForumdeMossoro', 'Fórum de Mossoró'),
                                   ('ForumdeMossamedes', 'Fórum de Mossâmedes'),
                                   ('ForumdeMostardas', 'Fórum de Mostardas'),
                                   ('ForumdeMozarlandia', 'Fórum de Mozarlândia'),
                                   ('ForumdeMucajai', 'Fórum de Mucajaí'),
                                   ('ForumdeMucuge', 'Fórum de Mucugê'),
                                   ('ForumdeMucuri', 'Fórum de Mucuri'),
                                   ('ForumdeMucurici', 'Fórum de Mucurici'),
                                   ('ForumdeMulungu', 'Fórum de Mulungu'),
                                   ('ForumdeMundoNovoMS', 'Fórum de Mundo Novo - MS'),
                                   ('ForumdeMunizFreire', 'Fórum de Muniz Freire'),
                                   ('ForumdeMuqui', 'Fórum de Muqui'),
                                   ('ForumdeMuriae', 'Fórum de Muriaé'),
                                   ('ForumdeMuribeca', 'Fórum de Muribeca'),
                                   ('ForumdeMurici', 'Fórum de Murici'),
                                   ('ForumdeMuritiba', 'Fórum de Muritiba'),
                                   ('ForumdeMutum', 'Fórum de Mutum'),
                                   ('ForumdeMutuipe', 'Fórum de Mutuípe'),
                                   ('ForumdeMuzambinho', 'Fórum de Muzambinho'),
                                   ('ForumdeMaeDoRio', 'Fórum de Mãe Do Rio'),
                                   ('ForumdeNazaredaMata', 'Fórum de Nazaré da Mata'),
                                   ('ForumdeNovaMonteVerde', 'Fórum de Nova Monte Verde'),
                                   ('ForumdeNovaMutum', 'Fórum de Nova Mutum'),
                                   ('ForumdeOlindaNovadoMaranhao', 'Fórum de Olinda Nova do Maranhão'),
                                   ('ForumdePadreMarcos', 'Fórum de Padre Marcos'),
                                   ('ForumdePalmeiradasMissoes', 'Fórum de Palmeira das Missões'),
                                   ('ForumdeParadeMinas', 'Fórum de Pará de Minas'),
                                   ('ForumdePatosdeMinas', 'Fórum de Patos de Minas'),
                                   ('ForumdePedraMole', 'Fórum de Pedra Mole'),
                                   ('ForumdePinheiroMachado', 'Fórum de Pinheiro Machado'),
                                   ('ForumdePortoDeMoz', 'Fórum de Porto De Moz'),
                                   ('ForumdePortoMurtinho', 'Fórum de Porto Murtinho'),
                                   ('ForumdePresidenteDutraMA', 'Fórum de Presidente Dutra - MA'),
                                   ('ForumdePresidenteMedici', 'Fórum de Presidente Médici'),
                                   ('ForumdePrimeirodeMaio', 'Fórum de Primeiro de Maio'),
                                   ('ForumdeRioMaria', 'Fórum de Rio Maria'),
                                   ('ForumdeRioNegroMS', 'Fórum de Rio Negro - MS'),
                                   ('ForumdeRioPardodeMinas', 'Fórum de Rio Pardo de Minas'),
                                   ('ForumdeRolimDeMoura', 'Fórum de Rolim De Moura'),
                                   ('ForumdeSantaMaria', 'Fórum de Santa Maria'),
                                   ('ForumdeSantaMariaDF', 'Fórum de Santa Maria - DF'),
                                   ('ForumdeSantaMariadaVitoria', 'Fórum de Santa Maria da Vitória'),
                                   ('ForumdeSantaMariaDoPara', 'Fórum de Santa Maria Do Pará'),
                                   ('ForumdeSantaMariadoSuacui', 'Fórum de Santa Maria do Suaçuí'),
                                   ('ForumdeSantaMariaMadalena', 'Fórum de Santa Maria Madalena'),
                                   ('ForumdeSantaMariana', 'Fórum de Santa Mariana'),
                                   ('ForumdeSantaQuiteriadoMaranhao', 'Fórum de Santa Quitéria do Maranhão'),
                                   ('ForumdeSantaRitaMA', 'Fórum de Santa Rita - MA'),
                                   ('ForumdeSantanadoMatos', 'Fórum de Santana do Matos'),
                                   ('ForumdeSenaMadureira', 'Fórum de Sena Madureira'),
                                   ('ForumdeSimplicioMendes', 'Fórum de Simplício Mendes'),
                                   ('ForumdeSaoDomingosdoMaranhao', 'Fórum de São Domingos do Maranhão'),
                                   ('ForumdeSaoFranciscoMG', 'Fórum de São Francisco - MG'),
                                   ('ForumdeSaoFranciscodoMaranhao', 'Fórum de São Francisco do Maranhão'),
                                   ('ForumdeSaoMamede', 'Fórum de São Mamede'),
                                   ('ForumdeSaoManuel', 'Fórum de São Manuel'),
                                   ('ForumdeSaoMarcos', 'Fórum de São Marcos'),
                                   ('ForumdeSaoMateusdoMaranhao', 'Fórum de São Mateus do Maranhão'),
                                   ('ForumdeSaoMateusdoSul', 'Fórum de São Mateus do Sul'),
                                   ('ForumdeSaoMiguel', 'Fórum de São Miguel'),
                                   ('ForumdeSaoMigueldasMatas', 'Fórum de São Miguel das Matas'),
                                   ('ForumdeSaoMigueldoOeste', 'Fórum de São Miguel do Oeste'),
                                   ('ForumdeSaoMigueldoTapuio', 'Fórum de São Miguel do Tapuio'),
                                   ('ForumdeSaoRaimundodasMangabeiras', 'Fórum de São Raimundo das Mangabeiras'),
                                   ('ForumdeSaoRoquedeMinas', 'Fórum de São Roque de Minas'),
                                   ('ForumdeTrajanodeMorais', 'Fórum de Trajano de Morais'),
                                   ('ForumdeTresdeMaio', 'Fórum de Três de Maio'),
                                   ('ForumdeTresMarias', 'Fórum de Três Marias'),
                                   ('ForumdeVianaMA', 'Fórum de Viana - MA'),
                                   ('ForumdeVitoriaMunizFreire', 'Fórum de Vitória - Muniz Freire'),
                                   ('ForumdeVitoriadoMearim', 'Fórum de Vitória do Mearim'),
                                   ('ForumdeVicosaMG', 'Fórum de Viçosa - MG'),
                                   ('ForumDistritaldeMosqueiro', 'Fórum Distrital de Mosqueiro'),
                                   ('ForumFederaldeMacapa', 'Fórum Federal de Macapá'),
                                   ('ForumFederaldeMacae', 'Fórum Federal de Macaé'),
                                   ('ForumFederaldeMaceio', 'Fórum Federal de Maceió'),
                                   ('ForumFederaldeMafra', 'Fórum Federal de Mafra'),
                                   ('ForumFederaldeMage', 'Fórum Federal de Magé'),
                                   ('ForumFederaldeManaus', 'Fórum Federal de Manaus'),
                                   ('ForumFederaldeMaraba', 'Fórum Federal de Marabá'),
                                   ('ForumFederaldeMaringa', 'Fórum Federal de Maringá'),
                                   ('ForumFederaldeMarilia', 'Fórum Federal de Marília'),
                                   ('ForumFederaldeMossoro', 'Fórum Federal de Mossoró'),
                                   ('ForumFederaldePatosDeMinas', 'Fórum Federal de Patos De Minas'),
                                   ('ForumFederaldeSantaMaria', 'Fórum Federal de Santa Maria'),
                                   ('ForumFederaldeSaoMateus', 'Fórum Federal de São Mateus'),
                                   ('ForumFederaldeSaoMiguelDoOeste', 'Fórum Federal de São Miguel Do Oeste'),
                                   ('ForumRegionaldeMadureira', 'Fórum Regional de Madureira'),
                                   ('ForumRegionaldeSaoMiguelPaulista', 'Fórum Regional de São Miguel Paulista'),
                                   ('ForumRegionaldoMeier', 'Fórum Regional do Meier'),
                                   ('ForumUniversitariodeMaceio', 'Fórum Universitário de Maceió'),
                                   ('TrabalhistadeMacapa', 'Trabalhista de Macapá'),
                                   ('TrabalhistadeMacau', 'Trabalhista de Macau'),
                                   ('TrabalhistadeMacae', 'Trabalhista de Macaé'),
                                   ('TrabalhistadeMaceio', 'Trabalhista de Maceió'),
                                   ('TrabalhistadeMachadinhoDOeste', 'Trabalhista de Machadinho D''Oeste'),
                                   ('TrabalhistadeMafra', 'Trabalhista de Mafra'),
                                   ('TrabalhistadeMage', 'Trabalhista de Magé'),
                                   ('TrabalhistadeMamanguape', 'Trabalhista de Mamanguape'),
                                   ('TrabalhistadeManacapuru', 'Trabalhista de Manacapuru'),
                                   ('TrabalhistadeManhuacu', 'Trabalhista de Manhuaçu'),
                                   ('TrabalhistadeMaraba', 'Trabalhista de Marabá'),
                                   ('TrabalhistadeMaracanau', 'Trabalhista de Maracanaú'),
                                   ('TrabalhistadeMaringa', 'Trabalhista de Maringá'),
                                   ('TrabalhistadeMaruim', 'Trabalhista de Maruim'),
                                   ('TrabalhistadeMarilia', 'Trabalhista de Marília'),
                                   ('TrabalhistadeMatozinhos', 'Trabalhista de Matozinhos'),
                                   ('TrabalhistadeMatao', 'Trabalhista de Matão'),
                                   ('TrabalhistadeMaua', 'Trabalhista de Mauá'),
                                   ('TrabalhistadeMineiros', 'Trabalhista de Mineiros'),
                                   ('TrabalhistadeMirassolDOeste', 'Trabalhista de Mirassol D’Oeste'),
                                   ('TrabalhistadeMococa', 'Trabalhista de Mococa'),
                                   ('TrabalhistadeMogiGuacu', 'Trabalhista de Mogi-Guaçu'),
                                   ('TrabalhistadeMogiMirim', 'Trabalhista de Mogi-Mirim'),
                                   ('TrabalhistadeMonteiro', 'Trabalhista de Monteiro'),
                                   ('TrabalhistadeMontenegro', 'Trabalhista de Montenegro'),
                                   ('TrabalhistadeMundoNovo', 'Trabalhista de Mundo Novo'),
                                   ('TrabalhistadeMuriae', 'Trabalhista de Muriaé'),
                                   ('TrabalhistadeNazaredaMata', 'Trabalhista de Nazaré da Mata'),
                                   ('TrabalhistadePalmeiradasMissoes', 'Trabalhista de Palmeira das Missões'),
                                   ('TrabalhistadeParadeMinas', 'Trabalhista de Pará de Minas'),
                                   ('TrabalhistadePatosdeMinas', 'Trabalhista de Patos de Minas'),
                                   ('TrabalhistadePresidenteMedici', 'Trabalhista de Presidente Médici'),
                                   ('TrabalhistadeRolimdeMoura', 'Trabalhista de Rolim de Moura'),
                                   ('TrabalhistadeSantaMaria', 'Trabalhista de Santa Maria'),
                                   ('TrabalhistadeSenaMadureira', 'Trabalhista de Sena Madureira'),
                                   ('TrabalhistadeSaoMateus', 'Trabalhista de São Mateus'),
                                   ('TrabalhistadeSaoMiguelDOeste', 'Trabalhista de São Miguel D''Oeste'),
                                   ('ForumdeNanuque', 'Fórum de Nanuque'),
                                   ('ForumdeNatal', 'Fórum de Natal'),
                                   ('ForumdeNatividade', 'Fórum de Natividade'),
                                   ('ForumdeNatividadeTO', 'Fórum de Natividade - TO'),
                                   ('ForumdeNatercia', 'Fórum de Natércia'),
                                   ('ForumdeNavegantes', 'Fórum de Navegantes'),
                                   ('ForumdeNavirai', 'Fórum de Naviraí'),
                                   ('ForumdeNazare', 'Fórum de Nazaré'),
                                   ('ForumdeNazaredoPiaui', 'Fórum de Nazaré do Piauí'),
                                   ('ForumdeNazario', 'Fórum de Nazário'),
                                   ('ForumdeNepomuceno', 'Fórum de Nepomuceno'),
                                   ('ForumdeNeropolis', 'Fórum de Nerópolis'),
                                   ('ForumdeNevesPaulista', 'Fórum de Neves Paulista'),
                                   ('ForumdeNeopolis', 'Fórum de Neópolis'),
                                   ('ForumdeNhamunda', 'Fórum de Nhamundá'),
                                   ('ForumdeNhandeara', 'Fórum de Nhandeara'),
                                   ('ForumdeNiloPecanha', 'Fórum de Nilo Peçanha'),
                                   ('ForumdeNilopolis', 'Fórum de Nilopolis'),
                                   ('ForumdeNioaque', 'Fórum de Nioaque'),
                                   ('ForumdeNiquelandia', 'Fórum de Niquelândia'),
                                   ('ForumdeNiteroi', 'Fórum de Niteroi'),
                                   ('ForumdeNobres', 'Fórum de Nobres'),
                                   ('ForumdeNonoai', 'Fórum de Nonoai'),
                                   ('ForumdeNordestina', 'Fórum de Nordestina'),
                                   ('ForumdeNortelandia', 'Fórum de Nortelândia'),
                                   ('ForumdeNossaSenhoradasDores', 'Fórum de Nossa Senhora das Dores'),
                                   ('ForumdeNossaSenhoradoSocorro', 'Fórum de Nossa Senhora do Socorro'),
                                   ('ForumdeNossaSenhoradosRemedios', 'Fórum de Nossa Senhora dos Remédios'),
                                   ('ForumdeNovaFriburgo', 'Fórum de Nova Friburgo'),
                                   ('ForumdeNovaFatimaPR', 'Fórum de Nova Fátima - PR'),
                                   ('ForumdeNovaOdessa', 'Fórum de Nova Odessa'),
                                   ('ForumdeNovaOlindaDoNorte', 'Fórum de Nova Olinda Do Norte'),
                                   ('ForumdeNovaPetropolis', 'Fórum de Nova Petrópolis'),
                                   ('ForumdeNovaPonte', 'Fórum de Nova Ponte'),
                                   ('ForumdeNovaPrata', 'Fórum de Nova Prata'),
                                   ('ForumdeNovaResende', 'Fórum de Nova Resende'),
                                   ('ForumdeNovaRussas', 'Fórum de Nova Russas'),
                                   ('ForumdeNovaSerrana', 'Fórum de Nova Serrana'),
                                   ('ForumdeNovaSoure', 'Fórum de Nova Soure'),
                                   ('ForumdeNovaTimboteua', 'Fórum de Nova Timboteua'),
                                   ('ForumdeNovaUbirata', 'Fórum de Nova Ubiratã'),
                                   ('ForumdeNovaVenecia', 'Fórum de Nova Venécia'),
                                   ('ForumdeNovaVicosa', 'Fórum de Nova Viçosa'),
                                   ('ForumdeNovaXavantina', 'Fórum de Nova Xavantina'),
                                   ('ForumdeNovoOriente', 'Fórum de Novo Oriente'),
                                   ('ForumdeNovoProgresso', 'Fórum de Novo Progresso'),
                                   ('ForumdeNovoRepartimento', 'Fórum de Novo Repartimento'),
                                   ('ForumdeNuporanga', 'Fórum de Nuporanga'),
                                   ('ForumdeNaoMeToque', 'Fórum de Não-Me-Toque'),
                                   ('ForumdeNisiaFloresta', 'Fórum de Nísia Floresta'),
                                   ('ForumdeOurilandiaDoNorte', 'Fórum de Ourilândia Do Norte'),
                                   ('ForumdeParaisodoNorte', 'Fórum de Paraíso do Norte'),
                                   ('ForumdePonteNova', 'Fórum de Ponte Nova'),
                                   ('ForumdePortoNacional', 'Fórum de Porto Nacional'),
                                   ('ForumdeRiachaodasNeves', 'Fórum de Riachão das Neves'),
                                   ('ForumdeRibeiraodasNeves', 'Fórum de Ribeirão das Neves'),
                                   ('ForumdeRioNegrinho', 'Fórum de Rio Negrinho'),
                                   ('ForumdeRioNegroPR', 'Fórum de Rio Negro - PR'),
                                   ('ForumdeRioNovo', 'Fórum de Rio Novo'),
                                   ('ForumdeRioNovodoSul', 'Fórum de Rio Novo do Sul'),
                                   ('ForumdeSantaremNovo', 'Fórum de Santarém Novo'),
                                   ('ForumdeSerraDoNavio', 'Fórum de Serra Do Navio'),
                                   ('ForumdeSerraNegra', 'Fórum de Serra Negra'),
                                   ('ForumdeSerraNegradoNorte', 'Fórum de Serra Negra do Norte'),
                                   ('ForumdeSucupiradoNorte', 'Fórum de Sucupira do Norte'),
                                   ('ForumdeSaoDomingosdoNorte', 'Fórum de São Domingos do Norte'),
                                   ('ForumdeSaoRaimundoNonato', 'Fórum de São Raimundo Nonato'),
                                   ('ForumdeSitioNovo', 'Fórum de Sítio Novo'),
                                   ('ForumdeTabuleiroDoNorte', 'Fórum de Tabuleiro Do Norte'),
                                   ('ForumdeTanqueNovo', 'Fórum de Tanque Novo'),
                                   ('ForumdeTaquaritingadoNorte', 'Fórum de Taquaritinga do Norte'),
                                   ('ForumdeTerraNova', 'Fórum de Terra Nova'),
                                   ('ForumdeTerraNovaPE', 'Fórum de Terra Nova - PE'),
                                   ('ForumdeTerraNovaDoNorte', 'Fórum de Terra Nova Do Norte'),
                                   ('ForumdeAguaDocedoNorte', 'Fórum de Água Doce do Norte'),
                                   ('ForumFederaldeNatal', 'Fórum Federal de Natal'),
                                   ('ForumFederaldeNavirai', 'Fórum Federal de Naviraí'),
                                   ('ForumFederaldeNiteroi', 'Fórum Federal de Niterói'),
                                   ('ForumFederaldeNovaFriburgo', 'Fórum Federal de Nova Friburgo'),
                                   ('ForumRegionaldaNossaSenhoradoO', 'Fórum Regional da Nossa Senhora do Ó'),
                                   ('ForumRegionalOceanicaNiteroi', 'Fórum Regional Oceanica - Niterói'),
                                   ('TrabalhistadeNanuque', 'Trabalhista de Nanuque'),
                                   ('TrabalhistadeNatal', 'Trabalhista de Natal'),
                                   ('TrabalhistadeNavirai', 'Trabalhista de Naviraí'),
                                   ('TrabalhistadeNilopolis', 'Trabalhista de Nilópolis'),
                                   ('TrabalhistadeNiteroi', 'Trabalhista de Niterói'),
                                   ('TrabalhistadeNovaFriburgo', 'Trabalhista de Nova Friburgo'),
                                   ('TrabalhistadeNovaVenecia', 'Trabalhista de Nova Venécia'),
                                   ('TrabalhistadePonteNova', 'Trabalhista de Ponte Nova'),
                                   ('TrabalhistadeRibeiraodasNeves', 'Trabalhista de Ribeirão das Neves'),
                                   ('TrabalhistadeSaoRaimundoNonato', 'Trabalhista de São Raimundo Nonato'),
                                   ('ForumdeOeiras', 'Fórum de Oeiras'),
                                   ('ForumdeOeirasDoPara', 'Fórum de Oeiras Do Pará'),
                                   ('ForumdeOiapoque', 'Fórum de Oiapoque'),
                                   ('ForumdeOlhoDaguadasFlores', 'Fórum de Olho Dágua das Flores'),
                                   ('ForumdeOlinda', 'Fórum de Olinda'),
                                   ('ForumdeOlindina', 'Fórum de Olindina'),
                                   ('ForumdeOliveira', 'Fórum de Oliveira'),
                                   ('ForumdeOlimpia', 'Fórum de Olímpia'),
                                   ('ForumdeOriximina', 'Fórum de Oriximiná'),
                                   ('ForumdeOrizona', 'Fórum de Orizona'),
                                   ('ForumdeOrleans', 'Fórum de Orleans'),
                                   ('ForumdeOrlandia', 'Fórum de Orlândia'),
                                   ('ForumdeOrobo', 'Fórum de Orobó'),
                                   ('ForumdeOroco', 'Fórum de Orocó'),
                                   ('ForumdeOrtigueira', 'Fórum de Ortigueira'),
                                   ('ForumdeOros', 'Fórum de Orós'),
                                   ('ForumdeOsorio', 'Fórum de Osório'),
                                   ('ForumdeOuricuri', 'Fórum de Ouricuri'),
                                   ('ForumdeOurinhos', 'Fórum de Ourinhos'),
                                   ('ForumdeOuroFino', 'Fórum de Ouro Fino'),
                                   ('ForumdeOuroPreto', 'Fórum de Ouro Preto'),
                                   ('ForumdeOuroPretoDoOeste', 'Fórum de Ouro Preto Do Oeste'),
                                   ('ForumdeOurem', 'Fórum de Ourém'),
                                   ('ForumdePedroOsorio', 'Fórum de Pedro Osório'),
                                   ('ForumdePresidenteOlegario', 'Fórum de Presidente Olegário'),
                                   ('ForumdeRiodasOstras', 'Fórum de Rio das Ostras'),
                                   ('ForumdeRiodoOeste', 'Fórum de Rio do Oeste'),
                                   ('ForumdeRosarioOeste', 'Fórum de Rosário Oeste'),
                                   ('ForumdeSaoPauloDeOlivenca', 'Fórum de São Paulo De Olivença'),
                                   ('ForumdeTeofiloOtoni', 'Fórum de Teófilo Otôni'),
                                   ('ForumFederaldeOurinhos', 'Fórum Federal de Ourinhos'),
                                   ('TrabalhistadeOeiras', 'Trabalhista de Oeiras'),
                                   ('TrabalhistadeOlinda', 'Trabalhista de Olinda'),
                                   ('TrabalhistadeOlimpia', 'Trabalhista de Olímpia'),
                                   ('TrabalhistadeOrlandia', 'Trabalhista de Orlândia'),
                                   ('TrabalhistadeOsasco', 'Trabalhista de Osasco'),
                                   ('TrabalhistadeOsorio', 'Trabalhista de Osório'),
                                   ('TrabalhistadeOurinhos', 'Trabalhista de Ourinhos'),
                                   ('TrabalhistadeOuroPreto', 'Trabalhista de Ouro Preto'),
                                   ('TrabalhistadeOuroPretodoOeste', 'Trabalhista de Ouro Preto do Oeste'),
                                   ('TrabalhistadeTeofiloOtoni', 'Trabalhista de Teófilo Otoni'),
                                   ('ForumdePacaembu', 'Fórum de Pacaembu'),
                                   ('ForumdePacajus', 'Fórum de Pacajus'),
                                   ('ForumdePacaja', 'Fórum de Pacajá'),
                                   ('ForumdePacaraima', 'Fórum de Pacaraima'),
                                   ('ForumdePacatuba', 'Fórum de Pacatuba'),
                                   ('ForumdePalestina', 'Fórum de Palestina'),
                                   ('ForumdePalhoca', 'Fórum de Palhoca'),
                                   ('ForumdePalma', 'Fórum de Palma'),
                                   ('ForumdePalmacia', 'Fórum de Palmacia'),
                                   ('ForumdePalmares', 'Fórum de Palmares'),
                                   ('ForumdePalmaresdoSul', 'Fórum de Palmares do Sul'),
                                   ('ForumdePalmas', 'Fórum de Palmas'),
                                   ('ForumdePalmasTO', 'Fórum de Palmas - TO'),
                                   ('ForumdePalmeira', 'Fórum de Palmeira'),
                                   ('ForumdePalmeiradosIndios', 'Fórum de Palmeira dos Índios'),
                                   ('ForumdePalmeiraDOeste', 'Fórum de Palmeira D´Oeste'),
                                   ('ForumdePalmeirais', 'Fórum de Palmeirais'),
                                   ('ForumdePalmeiras', 'Fórum de Palmeiras'),
                                   ('ForumdePalmeirina', 'Fórum de Palmeirina'),
                                   ('ForumdePalmeiropolis', 'Fórum de Palmeirópolis'),
                                   ('ForumdePalmital', 'Fórum de Palmital'),
                                   ('ForumdePalmitalPR', 'Fórum de Palmital - PR'),
                                   ('ForumdePalmitos', 'Fórum de Palmitos'),
                                   ('ForumdePalotina', 'Fórum de Palotina'),
                                   ('ForumdePanambi', 'Fórum de Panambi'),
                                   ('ForumdePanama', 'Fórum de Panamá'),
                                   ('ForumdePancas', 'Fórum de Pancas'),
                                   ('ForumdePanelas', 'Fórum de Panelas'),
                                   ('ForumdePanorama', 'Fórum de Panorama'),
                                   ('ForumdePapanduva', 'Fórum de Papanduva'),
                                   ('ForumdeParacambi', 'Fórum de Paracambi'),
                                   ('ForumdeParacatu', 'Fórum de Paracatu'),
                                   ('ForumdeParacuru', 'Fórum de Paracuru'),
                                   ('ForumdeParagominas', 'Fórum de Paragominas'),
                                   ('ForumdeParaguacu', 'Fórum de Paraguaçu'),
                                   ('ForumdeParaguacuPaulista', 'Fórum de Paraguaçu Paulista'),
                                   ('ForumdeParaibadoSul', 'Fórum de Paraiba do Sul'),
                                   ('ForumdeParaibano', 'Fórum de Paraibano'),
                                   ('ForumdeParaibuna', 'Fórum de Paraibuna'),
                                   ('ForumdeParaipaba', 'Fórum de Paraipaba'),
                                   ('ForumdeParaisopolis', 'Fórum de Paraisópolis'),
                                   ('ForumdeParambu', 'Fórum de Parambu'),
                                   ('ForumdeParamirim', 'Fórum de Paramirim'),
                                   ('ForumdeParanacity', 'Fórum de Paranacity'),
                                   ('ForumdeParanaiguara', 'Fórum de Paranaiguara'),
                                   ('ForumdeParanapanema', 'Fórum de Paranapanema'),
                                   ('ForumdeParanatinga', 'Fórum de Paranatinga'),
                                   ('ForumdeParanavai', 'Fórum de Paranavaí'),
                                   ('ForumdeParanaiba', 'Fórum de Paranaíba'),
                                   ('ForumdeParanaita', 'Fórum de Paranaíta'),
                                   ('ForumdeParana', 'Fórum de Paranã'),
                                   ('ForumdeParaopeba', 'Fórum de Paraopeba'),
                                   ('ForumdeParati', 'Fórum de Parati'),
                                   ('ForumdeParatinga', 'Fórum de Paratinga'),
                                   ('ForumdeParauapebas', 'Fórum de Parauapebas'),
                                   ('ForumdeParaisoDoTocantins', 'Fórum de Paraíso Do Tocantins'),
                                   ('ForumdeParauna', 'Fórum de Paraúna'),
                                   ('ForumdeParelhas', 'Fórum de Parelhas'),
                                   ('ForumdeParintins', 'Fórum de Parintins'),
                                   ('ForumdeParipiranga', 'Fórum de Paripiranga'),
                                   ('ForumdeParipueira', 'Fórum de Paripueira'),
                                   ('ForumdePariqueraAcu', 'Fórum de Pariquera-Açu'),
                                   ('ForumdeParnagua', 'Fórum de Parnaguá'),
                                   ('ForumdeParnamirim', 'Fórum de Parnamirim'),
                                   ('ForumdeParnamirimRN', 'Fórum de Parnamirim - RN'),
                                   ('ForumdeParnarama', 'Fórum de Parnarama'),
                                   ('ForumdeParnaiba', 'Fórum de Parnaíba'),
                                   ('ForumdeParobe', 'Fórum de Parobé'),
                                   ('ForumdePassaQuatro', 'Fórum de Passa Quatro'),
                                   ('ForumdePassaTempo', 'Fórum de Passa Tempo'),
                                   ('ForumdePassagemFranca', 'Fórum de Passagem Franca'),
                                   ('ForumdePassira', 'Fórum de Passira'),
                                   ('ForumdePassoFundo', 'Fórum de Passo Fundo'),
                                   ('ForumdePassos', 'Fórum de Passos'),
                                   ('ForumdePatos', 'Fórum de Patos'),
                                   ('ForumdePatrocinio', 'Fórum de Patrocínio'),
                                   ('ForumdePatrocinioPaulista', 'Fórum de Patrocínio Paulista'),
                                   ('ForumdePatu', 'Fórum de Patu'),
                                   ('ForumdePaudosFerros', 'Fórum de Pau dos Ferros'),
                                   ('ForumdePaudalho', 'Fórum de Paudalho'),
                                   ('ForumdePauini', 'Fórum de Pauini'),
                                   ('ForumdePaulista', 'Fórum de Paulista'),
                                   ('ForumdePaulistaPB', 'Fórum de Paulista - PB'),
                                   ('ForumdePaulistana', 'Fórum de Paulistana'),
                                   ('ForumdePaulodeFaria', 'Fórum de Paulo de Faria'),
                                   ('ForumdePauloRamos', 'Fórum de Paulo Ramos'),
                                   ('ForumdePaulinia', 'Fórum de Paulínia'),
                                   ('ForumdePeabiru', 'Fórum de Peabiru'),
                                   ('ForumdePederneiras', 'Fórum de Pederneiras'),
                                   ('ForumdePedra', 'Fórum de Pedra'),
                                   ('ForumdePedraPreta', 'Fórum de Pedra Preta'),
                                   ('ForumdePedralva', 'Fórum de Pedralva'),
                                   ('ForumdePedrasdeFogo', 'Fórum de Pedras de Fogo'),
                                   ('ForumdePedregulho', 'Fórum de Pedregulho'),
                                   ('ForumdePedreira', 'Fórum de Pedreira'),
                                   ('ForumdePedreiras', 'Fórum de Pedreiras'),
                                   ('ForumdePedrinhas', 'Fórum de Pedrinhas'),
                                   ('ForumdePedroVelho', 'Fórum de Pedro Velho'),
                                   ('ForumdePeixe', 'Fórum de Peixe'),
                                   ('ForumdePelotas', 'Fórum de Pelotas'),
                                   ('ForumdePenalva', 'Fórum de Penalva'),
                                   ('ForumdePendencias', 'Fórum de Pendências'),
                                   ('ForumdePenedo', 'Fórum de Penedo'),
                                   ('ForumdePenapolis', 'Fórum de Penápolis'),
                                   ('ForumdePerdizes', 'Fórum de Perdizes'),
                                   ('ForumdePerdoes', 'Fórum de Perdões'),
                                   ('ForumdePereiro', 'Fórum de Pereiro'),
                                   ('ForumdePeruibe', 'Fórum de Peruíbe'),
                                   ('ForumdePesqueira', 'Fórum de Pesqueira'),
                                   ('ForumdePetrolina', 'Fórum de Petrolina'),
                                   ('ForumdePetrolandia', 'Fórum de Petrolândia'),
                                   ('ForumdePetropolis', 'Fórum de Petropolis'),
                                   ('ForumdePecanha', 'Fórum de Peçanha'),
                                   ('ForumdePianco', 'Fórum de Piancó'),
                                   ('ForumdePiata', 'Fórum de Piatã'),
                                   ('ForumdePiacabucu', 'Fórum de Piaçabuçu'),
                                   ('ForumdePicos', 'Fórum de Picos'),
                                   ('ForumdePicui', 'Fórum de Picuí'),
                                   ('ForumdePiedade', 'Fórum de Piedade'),
                                   ('ForumdePilar', 'Fórum de Pilar'),
                                   ('ForumdePilarPB', 'Fórum de Pilar - PB'),
                                   ('ForumdePilardoSul', 'Fórum de Pilar do Sul'),
                                   ('ForumdePiloes', 'Fórum de Pilões'),
                                   ('ForumdePimenteiras', 'Fórum de Pimenteiras'),
                                   ('ForumdePindamonhangaba', 'Fórum de Pindamonhangaba'),
                                   ('ForumdePindareMirim', 'Fórum de Pindaré-Mirim'),
                                   ('ForumdePindai', 'Fórum de Pindaí'),
                                   ('ForumdePindobacu', 'Fórum de Pindobaçu'),
                                   ('ForumdePinhais', 'Fórum de Pinhais'),
                                   ('ForumdePinhalzinho', 'Fórum de Pinhalzinho'),
                                   ('ForumdePinhalzinhoSC', 'Fórum de Pinhalzinho - SC'),
                                   ('ForumdePinheiral', 'Fórum de Pinheiral'),
                                   ('ForumdePinheiro', 'Fórum de Pinheiro'),
                                   ('ForumdePinheiros', 'Fórum de Pinheiros'),
                                   ('ForumdePinhaoPR', 'Fórum de Pinhão - PR'),
                                   ('ForumdePinhaoSE', 'Fórum de Pinhão - SE'),
                                   ('ForumdePioXii', 'Fórum de Pio Xii'),
                                   ('ForumdePiquete', 'Fórum de Piquete'),
                                   ('ForumdePiracaia', 'Fórum de Piracaia'),
                                   ('ForumdePiracanjuba', 'Fórum de Piracanjuba'),
                                   ('ForumdePiracicaba', 'Fórum de Piracicaba'),
                                   ('ForumdePiracuruca', 'Fórum de Piracuruca'),
                                   ('ForumdePirai', 'Fórum de Pirai'),
                                   ('ForumdePiraju', 'Fórum de Piraju'),
                                   ('ForumdePirambu', 'Fórum de Pirambu'),
                                   ('ForumdePiranga', 'Fórum de Piranga'),
                                   ('ForumdePiranhas', 'Fórum de Piranhas'),
                                   ('ForumdePirapetinga', 'Fórum de Pirapetinga'),
                                   ('ForumdePirapora', 'Fórum de Pirapora'),
                                   ('ForumdePirapozinho', 'Fórum de Pirapozinho'),
                                   ('ForumdePiraquara', 'Fórum de Piraquara'),
                                   ('ForumdePirassununga', 'Fórum de Pirassununga'),
                                   ('ForumdePiratini', 'Fórum de Piratini'),
                                   ('ForumdePiraidoSul', 'Fórum de Piraí do Sul'),
                                   ('ForumdePirenopolis', 'Fórum de Pirenópolis'),
                                   ('ForumdePiresDoRio', 'Fórum de Pires Do Rio'),
                                   ('ForumdePiripiri', 'Fórum de Piripiri'),
                                   ('ForumdePiritiba', 'Fórum de Piritiba'),
                                   ('ForumdePirpirituba', 'Fórum de Pirpirituba'),
                                   ('ForumdePitanga', 'Fórum de Pitanga'),
                                   ('ForumdePitangueiras', 'Fórum de Pitangueiras'),
                                   ('ForumdePitangui', 'Fórum de Pitangui'),
                                   ('ForumdePium', 'Fórum de Pium'),
                                   ('ForumdePiumhi', 'Fórum de Piumhi'),
                                   ('ForumdePiuma', 'Fórum de Piúma'),
                                   ('ForumdePlanaltinaDF', 'Fórum de Planaltina - DF'),
                                   ('ForumdePlanaltoRS', 'Fórum de Planalto - RS'),
                                   ('ForumdePocinhos', 'Fórum de Pocinhos'),
                                   ('ForumdePocoVerde', 'Fórum de Poco Verde'),
                                   ('ForumdePocone', 'Fórum de Poconé'),
                                   ('ForumdePojuca', 'Fórum de Pojuca'),
                                   ('ForumdePombal', 'Fórum de Pombal'),
                                   ('ForumdePombos', 'Fórum de Pombos'),
                                   ('ForumdePomerode', 'Fórum de Pomerode')
                                   ;
                                   INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumdePompeia', 'Fórum de Pompéia'),
                                   ('ForumdePompeu', 'Fórum de Pompéu'),
                                   ('ForumdePontaPora', 'Fórum de Ponta Porã'),
                                   ('ForumdePontal', 'Fórum de Pontal'),
                                   ('ForumdePontalina', 'Fórum de Pontalina'),
                                   ('ForumdePonteSerrada', 'Fórum de Ponte Serrada'),
                                   ('ForumdePoranga', 'Fórum de Poranga'),
                                   ('ForumdePorangaba', 'Fórum de Porangaba'),
                                   ('ForumdePorangatu', 'Fórum de Porangatu'),
                                   ('ForumdePorciuncula', 'Fórum de Porciuncula'),
                                   ('ForumdePorecatu', 'Fórum de Porecatu'),
                                   ('ForumdePortalegre', 'Fórum de Portalegre'),
                                   ('ForumdePorteirinha', 'Fórum de Porteirinha'),
                                   ('ForumdePortel', 'Fórum de Portel'),
                                   ('ForumdePorto', 'Fórum de Porto'),
                                   ('ForumdePortodaFolha', 'Fórum de Porto da Folha'),
                                   ('ForumdePortodePedras', 'Fórum de Porto de Pedras'),
                                   ('ForumdePortoFeliz', 'Fórum de Porto Feliz'),
                                   ('ForumdePortoFerreira', 'Fórum de Porto Ferreira'),
                                   ('ForumdePortoFranco', 'Fórum de Porto Franco'),
                                   ('ForumdePortoReal', 'Fórum de Porto Real'),
                                   ('ForumdePortoSeguro', 'Fórum de Porto Seguro'),
                                   ('ForumdePortoUniao', 'Fórum de Porto Uniao'),
                                   ('ForumdePortoWalter', 'Fórum de Porto Walter'),
                                   ('ForumdePortoXavier', 'Fórum de Porto Xavier'),
                                   ('ForumdePortao', 'Fórum de Portão'),
                                   ('ForumdePosse', 'Fórum de Posse'),
                                   ('ForumdePotiragua', 'Fórum de Potiraguá'),
                                   ('ForumdePotirendaba', 'Fórum de Potirendaba'),
                                   ('ForumdePoxoreo', 'Fórum de Poxoréo'),
                                   ('ForumdePoa', 'Fórum de Poá'),
                                   ('ForumdePocoFundo', 'Fórum de Poço Fundo'),
                                   ('ForumdePocoRedondo', 'Fórum de Poço Redondo'),
                                   ('ForumdePocao', 'Fórum de Poção'),
                                   ('ForumdePocaodePedras', 'Fórum de Poção de Pedras'),
                                   ('ForumdePocoes', 'Fórum de Poções'),
                                   ('ForumdePrado', 'Fórum de Prado'),
                                   ('ForumdePrados', 'Fórum de Prados'),
                                   ('ForumdePrainha', 'Fórum de Prainha'),
                                   ('ForumdePrata', 'Fórum de Prata'),
                                   ('ForumdePrataPB', 'Fórum de Prata - PB'),
                                   ('ForumdePratapolis', 'Fórum de Pratápolis'),
                                   ('ForumdePresidenteDutra', 'Fórum de Presidente Dutra'),
                                   ('ForumdePresidenteFigueiredo', 'Fórum de Presidente Figueiredo'),
                                   ('ForumdePresidentePrudente', 'Fórum de Presidente Prudente'),
                                   ('ForumdePresidenteVargas', 'Fórum de Presidente Vargas'),
                                   ('ForumdePresidenteVenceslau', 'Fórum de Presidente Venceslau'),
                                   ('ForumdePrimavera', 'Fórum de Primavera'),
                                   ('ForumdePrimaveraPA', 'Fórum de Primavera - PA'),
                                   ('ForumdePromissao', 'Fórum de Promissão'),
                                   ('ForumdePropria', 'Fórum de Propriá'),
                                   ('ForumdePrudentopolis', 'Fórum de Prudentópolis'),
                                   ('ForumdePedeSerra', 'Fórum de Pé de Serra'),
                                   ('ForumdePerola', 'Fórum de Pérola'),
                                   ('ForumdeQueimadasPB', 'Fórum de Queimadas - PB'),
                                   ('ForumdeRibasDoRioPardo', 'Fórum de Ribas Do Rio Pardo'),
                                   ('ForumdeRibeiradoPombal', 'Fórum de Ribeira do Pombal'),
                                   ('ForumdeRibeiraodoPinhal', 'Fórum de Ribeirão do Pinhal'),
                                   ('ForumdeRibeiraoPreto', 'Fórum de Ribeirão Preto'),
                                   ('ForumdeRiodasPedras', 'Fórum de Rio das Pedras'),
                                   ('ForumdeRioParanaiba', 'Fórum de Rio Paranaíba'),
                                   ('ForumdeRioPardo', 'Fórum de Rio Pardo'),
                                   ('ForumdeRioPiracicaba', 'Fórum de Rio Piracicaba'),
                                   ('ForumdeRioPomba', 'Fórum de Rio Pomba'),
                                   ('ForumdeRioPreto', 'Fórum de Rio Preto'),
                                   ('ForumdeRondonDoPara', 'Fórum de Rondon Do Pará'),
                                   ('ForumdeSantaRitadoPassaQuatro', 'Fórum de Santa Rita do Passa Quatro'),
                                   ('ForumdeSantaRosadoPurus', 'Fórum de Santa Rosa do Purus'),
                                   ('ForumdeSantaVitoriadoPalmar', 'Fórum de Santa Vitória do Palmar'),
                                   ('ForumdeSerraPreta', 'Fórum de Serra Preta'),
                                   ('ForumdeSocorrodoPiaui', 'Fórum de Socorro do Piauí'),
                                   ('ForumdeSoledadePB', 'Fórum de Soledade - PB'),
                                   ('ForumdeSaoDomingosdoPrata', 'Fórum de São Domingos do Prata'),
                                   ('ForumdeSaoFranciscodePaula', 'Fórum de São Francisco de Paula'),
                                   ('ForumdeSaoFranciscoDoPara', 'Fórum de São Francisco Do Pará'),
                                   ('ForumdeSaoFelixdoPiaui', 'Fórum de São Félix do Piauí'),
                                   ('ForumdeSaoPaulodoPotengi', 'Fórum de São Paulo do Potengi'),
                                   ('ForumdeSaoPedro', 'Fórum de São Pedro'),
                                   ('ForumdeSaoPedrodoPiaui', 'Fórum de São Pedro do Piauí'),
                                   ('ForumdeSaoPedrodoSul', 'Fórum de São Pedro do Sul'),
                                   ('ForumdeSaoSebastiaodoParaiso', 'Fórum de São Sebastião do Paraíso'),
                                   ('ForumdeSaoSebastiaodoPasse', 'Fórum de São Sebastião do Passé'),
                                   ('ForumdeTaperoaPB', 'Fórum de Taperoá - PB'),
                                   ('ForumdeTenentePortela', 'Fórum de Tenente Portela'),
                                   ('ForumdeTriunfoPE', 'Fórum de Triunfo - PE'),
                                   ('ForumdeTresPassos', 'Fórum de Três Passos'),
                                   ('ForumdeTresPontas', 'Fórum de Três Pontas'),
                                   ('ForumdeTupiPaulista', 'Fórum de Tupi Paulista'),
                                   ('ForumdeUniaodosPalmares', 'Fórum de União dos Palmares'),
                                   ('ForumdeValencadoPiaui', 'Fórum de Valença do Piauí'),
                                   ('ForumdeVarzeadaPalma', 'Fórum de Várzea da Palma'),
                                   ('ForumdeVarzeadoPoco', 'Fórum de Várzea do Poço'),
                                   ('ForumdeVarzeaPaulista', 'Fórum de Várzea Paulista'),
                                   ('ForumdeAguaPreta', 'Fórum de Água Preta'),
                                   ('ForumdoParanoa', 'Fórum do Paranoá'),
                                   ('ForumFederaldePalmas', 'Fórum Federal de Palmas'),
                                   ('ForumFederaldeParanavai', 'Fórum Federal de Paranavaí'),
                                   ('ForumFederaldePassoFundo', 'Fórum Federal de Passo Fundo'),
                                   ('ForumFederaldePassos', 'Fórum Federal de Passos'),
                                   ('ForumFederaldePelotas', 'Fórum Federal de Pelotas'),
                                   ('ForumFederaldePetrolina', 'Fórum Federal de Petrolina'),
                                   ('ForumFederaldePetropolis', 'Fórum Federal de Petrópolis'),
                                   ('ForumFederaldePicos', 'Fórum Federal de Picos'),
                                   ('ForumFederaldePiracicaba', 'Fórum Federal de Piracicaba'),
                                   ('ForumFederaldePontaPora', 'Fórum Federal de Ponta Porã'),
                                   ('ForumFederaldePortoVelho', 'Fórum Federal de Porto Velho'),
                                   ('ForumFederaldePresidentePrudente', 'Fórum Federal de Presidente Prudente'),
                                   ('ForumFederaldeRibeiraoPreto', 'Fórum Federal de Ribeirão Preto'),
                                   ('ForumFederaldeSaoSebastiaoDoParaiso', 'Fórum Federal de São Sebastião Do Paraíso'),
                                   ('ForumRegionaldaPavuna', 'Fórum Regional da Pavuna'),
                                   ('ForumRegionaldaPenha', 'Fórum Regional da Penha'),
                                   ('ForumRegionaldePinheiros', 'Fórum Regional de Pinheiros'),
                                   ('ForumRegionaldeVilaPrudente', 'Fórum Regional de Vila Prudente'),
                                   ('ForumRegionaldoPartenon', 'Fórum Regional do Partenon'),
                                   ('TrabalhistadePacajus', 'Trabalhista de Pacajus'),
                                   ('TrabalhistadePalmares', 'Trabalhista de Palmares'),
                                   ('TrabalhistadePalmas', 'Trabalhista de Palmas'),
                                   ('TrabalhistadePalmeiradosIndios', 'Trabalhista de Palmeira dos Índios'),
                                   ('TrabalhistadeParacatu', 'Trabalhista de Paracatu'),
                                   ('TrabalhistadeParagominas', 'Trabalhista de Paragominas'),
                                   ('TrabalhistadeParanagua', 'Trabalhista de Paranaguá'),
                                   ('TrabalhistadeParanavai', 'Trabalhista de Paranavaí'),
                                   ('TrabalhistadeParanaiba', 'Trabalhista de Paranaíba'),
                                   ('TrabalhistadeParauapebas', 'Trabalhista de Parauapebas'),
                                   ('TrabalhistadeParintins', 'Trabalhista de Parintins'),
                                   ('TrabalhistadeParnaiba', 'Trabalhista de Parnaíba'),
                                   ('TrabalhistadePassoFundo', 'Trabalhista de Passo Fundo'),
                                   ('TrabalhistadePassos', 'Trabalhista de Passos'),
                                   ('TrabalhistadePatos', 'Trabalhista de Patos'),
                                   ('TrabalhistadePatrocinio', 'Trabalhista de Patrocínio'),
                                   ('TrabalhistadePaudosFerros', 'Trabalhista de Pau dos Ferros'),
                                   ('TrabalhistadePaulista', 'Trabalhista de Paulista'),
                                   ('TrabalhistadePaulinia', 'Trabalhista de Paulínia'),
                                   ('TrabalhistadePederneiras', 'Trabalhista de Pederneiras'),
                                   ('TrabalhistadePedreiras', 'Trabalhista de Pedreiras'),
                                   ('TrabalhistadePelotas', 'Trabalhista de Pelotas'),
                                   ('TrabalhistadePenedo', 'Trabalhista de Penedo'),
                                   ('TrabalhistadePenapolis', 'Trabalhista de Penápolis'),
                                   ('TrabalhistadePesqueira', 'Trabalhista de Pesqueira'),
                                   ('TrabalhistadePetrolina', 'Trabalhista de Petrolina'),
                                   ('TrabalhistadePetropolis1', 'Trabalhista de Petrópolis 1'),
                                   ('TrabalhistadePetropolis2', 'Trabalhista de Petrópolis 2'),
                                   ('TrabalhistadePicos', 'Trabalhista de Picos'),
                                   ('TrabalhistadePicui', 'Trabalhista de Picuí'),
                                   ('TrabalhistadePiedade', 'Trabalhista de Piedade'),
                                   ('TrabalhistadePindamonhangaba', 'Trabalhista de Pindamonhangaba'),
                                   ('TrabalhistadePinhais', 'Trabalhista de Pinhais'),
                                   ('TrabalhistadePinheiro', 'Trabalhista de Pinheiro'),
                                   ('TrabalhistadePiracicaba', 'Trabalhista de Piracicaba'),
                                   ('TrabalhistadePirapora', 'Trabalhista de Pirapora'),
                                   ('TrabalhistadePirassununga', 'Trabalhista de Pirassununga'),
                                   ('TrabalhistadePiripiri', 'Trabalhista de Piripiri'),
                                   ('TrabalhistadePontaPora', 'Trabalhista de Ponta Porã'),
                                   ('TrabalhistadePorangatu', 'Trabalhista de Porangatu'),
                                   ('TrabalhistadePorecatu', 'Trabalhista de Porecatu'),
                                   ('TrabalhistadePortoFerreira', 'Trabalhista de Porto Ferreira'),
                                   ('TrabalhistadePortoVelho', 'Trabalhista de Porto Velho'),
                                   ('TrabalhistadePosse', 'Trabalhista de Posse'),
                                   ('TrabalhistadePoa', 'Trabalhista de Poá'),
                                   ('TrabalhistadePresidenteDutra', 'Trabalhista de Presidente Dutra'),
                                   ('TrabalhistadePresidenteFigueiredo', 'Trabalhista de Presidente Figueiredo'),
                                   ('TrabalhistadePresidentePrudente', 'Trabalhista de Presidente Prudente'),
                                   ('TrabalhistadePresidenteVenceslau', 'Trabalhista de Presidente Venceslau'),
                                   ('TrabalhistadePropria', 'Trabalhista de Propriá'),
                                   ('TrabalhistadeRibeiraoPires', 'Trabalhista de Ribeirão Pires'),
                                   ('TrabalhistadeRibeiraoPreto', 'Trabalhista de Ribeirão Preto'),
                                   ('TrabalhistadeSantaVitoriadoPalmar', 'Trabalhista de Santa Vitória do Palmar'),
                                   ('TrabalhistadeSantanadoParnaiba', 'Trabalhista de Santana do Parnaíba'),
                                   ('TrabalhistadeSaoPaulo', 'Trabalhista de São Paulo'),
                                   ('TrabalhistadeSaoSebastiaodoParaiso', 'Trabalhista de São Sebastião do Paraíso'),
                                   ('TrabalhistadeTresPassos', 'Trabalhista de Três Passos'),
                                   ('TrabalhistadeUniaodosPalmares', 'Trabalhista de União dos Palmares'),
                                   ('VarasdosFeitosdaFazendaPublicadeTeresina', 'Varas dos Feitos da Fazenda Pública de Teresina'),
                                   ('ForumdeQuarai', 'Fórum de Quaraí'),
                                   ('ForumdeQuata', 'Fórum de Quatá'),
                                   ('ForumdeQuebrangulo', 'Fórum de Quebrangulo'),
                                   ('ForumdeQueimadas', 'Fórum de Queimadas'),
                                   ('ForumdeQueimados', 'Fórum de Queimados'),
                                   ('ForumdeQueluz', 'Fórum de Queluz'),
                                   ('ForumdeQuerencia', 'Fórum de Querência'),
                                   ('ForumdeQuilombo', 'Fórum de Quilombo'),
                                   ('ForumdeQuipapa', 'Fórum de Quipapá'),
                                   ('ForumdeQuirinopolis', 'Fórum de Quirinópolis'),
                                   ('ForumdeQuissama', 'Fórum de Quissamã'),
                                   ('ForumdeQuixabeira', 'Fórum de Quixabeira'),
                                   ('ForumdeQuixada', 'Fórum de Quixadá'),
                                   ('ForumdeQuixelo', 'Fórum de Quixelo'),
                                   ('ForumdeQuixeramobim', 'Fórum de Quixeramobim'),
                                   ('ForumdeQuixere', 'Fórum de Quixere'),
                                   ('ForumdeSantaQuiteria', 'Fórum de Santa Quitéria'),
                                   ('ForumdeSeteQuedas', 'Fórum de Sete Quedas'),
                                   ('TrabalhistadeQuixada', 'Trabalhista de Quixadá'),
                                   ('9REGIAO', '9ª REGIÃO'),
                                   ('ForumdeRancharia', 'Fórum de Rancharia'),
                                   ('ForumdeRaposa', 'Fórum de Raposa'),
                                   ('ForumdeRaulSoares', 'Fórum de Raul Soares'),
                                   ('ForumdeRealeza', 'Fórum de Realeza'),
                                   ('ForumdeReboucas', 'Fórum de Rebouças'),
                                   ('ForumdeRedencao', 'Fórum de Redencao'),
                                   ('ForumdeRegeneracao', 'Fórum de Regeneração'),
                                   ('ForumdeRegenteFeijo', 'Fórum de Regente Feijó'),
                                   ('ForumdeRegistro', 'Fórum de Registro'),
                                   ('ForumdeRemanso', 'Fórum de Remanso'),
                                   ('ForumdeRemigio', 'Fórum de Remígio'),
                                   ('ForumdeReriutaba', 'Fórum de Reriutaba'),
                                   ('ForumdeResende', 'Fórum de Resende'),
                                   ('ForumdeReserva', 'Fórum de Reserva'),
                                   ('ForumdeResplendor', 'Fórum de Resplendor'),
                                   ('ForumdeRestingaSeca', 'Fórum de Restinga Seca'),
                                   ('ForumdeRetirolandia', 'Fórum de Retirolândia'),
                                   ('ForumdeRiachaodoDantas', 'Fórum de Riachao do Dantas'),
                                   ('ForumdeRiachodeSantana', 'Fórum de Riacho de Santana'),
                                   ('ForumdeRiachuelo', 'Fórum de Riachuelo'),
                                   ('ForumdeRiachao', 'Fórum de Riachão'),
                                   ('ForumdeRialma', 'Fórum de Rialma'),
                                   ('ForumdeRibeirao', 'Fórum de Ribeirão'),
                                   ('ForumdeRibeiropolis', 'Fórum de Ribeirópolis'),
                                   ('ForumdeRiodasFlores', 'Fórum de Rio das Flores'),
                                   ('ForumdeRiodoSul', 'Fórum de Rio do Sul'),
                                   ('ForumdeRioFormoso', 'Fórum de Rio Formoso'),
                                   ('ForumdeRioReal', 'Fórum de Rio Real'),
                                   ('ForumdeRioTinto', 'Fórum de Rio Tinto'),
                                   ('ForumdeRioVerde', 'Fórum de Rio Verde'),
                                   ('ForumdeRioVermelho', 'Fórum de Rio Vermelho'),
                                   ('ForumdeRodelas', 'Fórum de Rodelas'),
                                   ('ForumdeRolandia', 'Fórum de Rolândia'),
                                   ('ForumdeRorainopolis', 'Fórum de Rorainópolis'),
                                   ('ForumdeRosana', 'Fórum de Rosana'),
                                   ('ForumdeRoseira', 'Fórum de Roseira'),
                                   ('ForumdeRosario', 'Fórum de Rosário'),
                                   ('ForumdeRosariodoSul', 'Fórum de Rosário do Sul'),
                                   ('ForumdeRubiataba', 'Fórum de Rubiataba'),
                                   ('ForumdeRuropolis', 'Fórum de Rurópolis'),
                                   ('ForumdeRussas', 'Fórum de Russas'),
                                   ('ForumdeSantaRita', 'Fórum de Santa Rita'),
                                   ('ForumdeSantaRitadoSapucai', 'Fórum de Santa Rita do Sapucaí'),
                                   ('ForumdeSantaRosa', 'Fórum de Santa Rosa'),
                                   ('ForumdeSantaRosadoSul', 'Fórum de Santa Rosa do Sul'),
                                   ('ForumdeSantaRosadoViterbo', 'Fórum de Santa Rosa do Viterbo'),
                                   ('ForumdeSobradinhoRS', 'Fórum de Sobradinho - RS'),
                                   ('ForumdeSaoRafael', 'Fórum de São Rafael'),
                                   ('ForumdeSaoRomao', 'Fórum de São Romão'),
                                   ('ForumdeSaoRoque', 'Fórum de São Roque'),
                                   ('ForumdeTerraRica', 'Fórum de Terra Rica'),
                                   ('ForumdeTerraRoxa', 'Fórum de Terra Roxa'),
                                   ('ForumdeTresRios', 'Fórum de Tres Rios'),
                                   ('ForumdeTriunfoRS', 'Fórum de Triunfo - RS'),
                                   ('ForumdeVilaRica', 'Fórum de Vila Rica'),
                                   ('ForumdeVoltaRedonda', 'Fórum de Volta Redonda'),
                                   ('ForumdoRecife', 'Fórum do Recife'),
                                   ('ForumFederaldeResende', 'Fórum Federal de Resende'),
                                   ('ForumFederaldeRioDoSul', 'Fórum Federal de Rio Do Sul'),
                                   ('ForumFederaldeRioVerde', 'Fórum Federal de Rio Verde'),
                                   ('ForumFederaldeRondonopolis', 'Fórum Federal de Rondonópolis'),
                                   ('ForumFederaldeSantaRosa', 'Fórum Federal de Santa Rosa'),
                                   ('ForumFederaldeTresRios', 'Fórum Federal de Três Rios'),
                                   ('ForumFederaldeVoltaRedonda', 'Fórum Federal de Volta Redonda'),
                                   ('ForumRegionaldaRestinga', 'Fórum Regional da Restinga'),
                                   ('ForumRegionaldaTristeza', 'Fórum Regional da Tristeza'),
                                   ('ForumRegionaldeSantana', 'Fórum Regional de Santana'),
                                   ('ForumRegionaldo4Distrito', 'Fórum Regional do 4º Distrito'),
                                   ('ForumRegionaldoSarandi', 'Fórum Regional do Sarandi'),
                                   ('ForumRegionaldoTatuape', 'Fórum Regional do Tatuapé'),
                                   ('ForumJUVAMdeRondonopolis', 'Fórum/JUVAM de Rondonópolis'),
                                   ('TrabalhistadeFrancodaRocha', 'Trabalhista de Franco da Rocha'),
                                   ('TrabalhistadeRancharia', 'Trabalhista de Rancharia'),
                                   ('TrabalhistadeRecife', 'Trabalhista de Recife'),
                                   ('TrabalhistadeRedencao', 'Trabalhista de Redenção'),
                                   ('TrabalhistadeRegistro', 'Trabalhista de Registro'),
                                   ('TrabalhistadeResende', 'Trabalhista de Resende'),
                                   ('TrabalhistadeRibeirao', 'Trabalhista de Ribeirão'),
                                   ('TrabalhistadeRiodoSul', 'Trabalhista de Rio do Sul'),
                                   ('TrabalhistadeRioVerde', 'Trabalhista de Rio Verde'),
                                   ('TrabalhistadeRolandia', 'Trabalhista de Rolândia'),
                                   ('TrabalhistadeRondonopolis', 'Trabalhista de Rondonópolis'),
                                   ('TrabalhistadeRosariodoSul', 'Trabalhista de Rosário do Sul'),
                                   ('TrabalhistadeSantaRosa', 'Trabalhista de Santa Rosa'),
                                   ('TrabalhistadeSaoRoque', 'Trabalhista de São Roque'),
                                   ('TrabalhistadeTresRios', 'Trabalhista de Três Rios'),
                                   ('TrabalhistadeVoltaRedonda', 'Trabalhista de Volta Redonda'),
                                   ('TribunalRegionalFederalda1Regiao', 'Tribunal Regional Federal da 1ª Região'),
                                   ('TribunalRegionalFederalda2Regiao', 'Tribunal Regional Federal da 2ª Região'),
                                   ('TribunalRegionalFederalda3Regiao', 'Tribunal Regional Federal da 3ª Região'),
                                   ('TribunalRegionalFederalda4Regiao', 'Tribunal Regional Federal da 4ª Região'),
                                   ('TribunalRegionalFederalda5Regiao', 'Tribunal Regional Federal da 5ª Região'),
                                   ('TRTRegiao1', 'TRT - Região 1'),
                                   ('TRTRegiao10', 'TRT - Região 10'),
                                   ('TRTRegiao11', 'TRT - Região 11'),
                                   ('TRTRegiao12', 'TRT - Região 12'),
                                   ('TRTRegiao13', 'TRT - Região 13'),
                                   ('TRTRegiao14', 'TRT - Região 14'),
                                   ('TRTRegiao15', 'TRT - Região 15'),
                                   ('TRTRegiao16', 'TRT - Região 16'),
                                   ('TRTRegiao17', 'TRT - Região 17'),
                                   ('TRTRegiao18', 'TRT - Região 18'),
                                   ('TRTRegiao19', 'TRT - Região 19'),
                                   ('TRTRegiao2', 'TRT - Região 2'),
                                   ('TRTRegiao20', 'TRT - Região 20'),
                                   ('TRTRegiao21', 'TRT - Região 21'),
                                   ('TRTRegiao22', 'TRT - Região 22'),
                                   ('TRTRegiao23', 'TRT - Região 23'),
                                   ('TRTRegiao24', 'TRT - Região 24'),
                                   ('TRTRegiao3', 'TRT - Região 3'),
                                   ('TRTRegiao4', 'TRT - Região 4'),
                                   ('TRTRegiao5', 'TRT - Região 5'),
                                   ('TRTRegiao6', 'TRT - Região 6'),
                                   ('TRTRegiao8', 'TRT - Região 8'),
                                   ('TRTRegiao9', 'TRT - Região 9'),
                                   ('ForumdeSabara', 'Fórum de Sabará'),
                                   ('ForumdeSabinopolis', 'Fórum de Sabinópolis'),
                                   ('ForumdeSacramento', 'Fórum de Sacramento'),
                                   ('ForumdeSaire', 'Fórum de Sairé'),
                                   ('ForumdeSalesopolis', 'Fórum de Salesópolis'),
                                   ('ForumdeSalgado', 'Fórum de Salgado'),
                                   ('ForumdeSalgueiro', 'Fórum de Salgueiro'),
                                   ('ForumdeSalinas', 'Fórum de Salinas'),
                                   ('ForumdeSalinopolis', 'Fórum de Salinópolis'),
                                   ('ForumdeSaloa', 'Fórum de Saloá'),
                                   ('ForumdeSalto', 'Fórum de Salto'),
                                   ('ForumdeSalvaterra', 'Fórum de Salvaterra'),
                                   ('ForumdeSamambaia', 'Fórum de Samambaia'),
                                   ('ForumdeSananduva', 'Fórum de Sananduva'),
                                   ('ForumdeSanclerlandia', 'Fórum de Sanclerlândia'),
                                   ('ForumdeSanharo', 'Fórum de Sanharó'),
                                   ('ForumdeSantaFilomena', 'Fórum de Santa Filomena'),
                                   ('ForumdeSantaFedoSul', 'Fórum de Santa Fé do Sul'),
                                   ('ForumdeSantaTeresa', 'Fórum de Santa Teresa'),
                                   ('ForumdeSantaTeresinha', 'Fórum de Santa Teresinha'),
                                   ('ForumdeSantaVitoria', 'Fórum de Santa Vitória'),
                                   ('ForumdeSantana', 'Fórum de Santana'),
                                   ('ForumdeSantanadoSaoFrancisco', 'Fórum de Santana do São Francisco'),
                                   ('ForumdeSantarem', 'Fórum de Santarém'),
                                   ('ForumdeSantiago', 'Fórum de Santiago'),
                                   ('ForumdeSantoAngelo', 'Fórum de Santo Ângelo'),
                                   ('ForumdeSantos', 'Fórum de Santos'),
                                   ('ForumdeSantosDumont', 'Fórum de Santos Dumont'),
                                   ('ForumdeSapeacu', 'Fórum de Sapeaçu'),
                                   ('ForumdeSapezal', 'Fórum de Sapezal'),
                                   ('ForumdeSapiranga', 'Fórum de Sapiranga'),
                                   ('ForumdeSapucaia', 'Fórum de Sapucaia'),
                                   ('ForumdeSapucaiadoSul', 'Fórum de Sapucaia do Sul'),
                                   ('ForumdeSape', 'Fórum de Sapé'),
                                   ('ForumdeSaquarema', 'Fórum de Saquarema'),
                                   ('ForumdeSarandi', 'Fórum de Sarandi'),
                                   ('ForumdeSatuba', 'Fórum de Satuba'),
                                   ('ForumdeSaude', 'Fórum de Saúde'),
                                   ('ForumdeSeabra', 'Fórum de Seabra'),
                                   ('ForumdeSeara', 'Fórum de Seara'),
                                   ('ForumdeSeberi', 'Fórum de Seberi'),
                                   ('ForumdeSenadorFirmino', 'Fórum de Senador Firmino'),
                                   ('ForumdeSenges', 'Fórum de Sengés'),
                                   ('ForumdeSentoSe', 'Fórum de Sento Sé'),
                                   ('ForumdeSeropedica', 'Fórum de Seropedica'),
                                   ('ForumdeSerraUnesc', 'Fórum de Serra - Unesc'),
                                   ('ForumdeSerraDourada', 'Fórum de Serra Dourada'),
                                   ('ForumdeSerraTalhada', 'Fórum de Serra Talhada'),
                                   ('ForumdeSerrana', 'Fórum de Serrana'),
                                   ('ForumdeSerraria', 'Fórum de Serraria'),
                                   ('ForumdeSerrinha', 'Fórum de Serrinha'),
                                   ('ForumdeSerrita', 'Fórum de Serrita'),
                                   ('ForumdeSerro', 'Fórum de Serro'),
                                   ('ForumdeSerrolandia', 'Fórum de Serrolândia'),
                                   ('ForumdeSertanopolis', 'Fórum de Sertanópolis'),
                                   ('ForumdeSertania', 'Fórum de Sertânia'),
                                   ('ForumdeSertaozinho', 'Fórum de Sertãozinho'),
                                   ('ForumdeSidrolandia', 'Fórum de Sidrolândia'),
                                   ('ForumdeSilves', 'Fórum de Silves'),
                                   ('ForumdeSilvianopolis', 'Fórum de Silvianópolis'),
                                   ('ForumdeSilvania', 'Fórum de Silvânia'),
                                   ('ForumdeSimaoDias', 'Fórum de Simão Dias'),
                                   ('ForumdeSimoes', 'Fórum de Simões'),
                                   ('ForumdeSimoesFilho', 'Fórum de Simões Filho'),
                                   ('ForumdeSinop', 'Fórum de Sinop'),
                                   ('ForumdeSirinhaem', 'Fórum de Sirinhaém'),
                                   ('ForumdeSiriri', 'Fórum de Siriri'),
                                   ('ForumdeSobradinho', 'Fórum de Sobradinho'),
                                   ('ForumdeSobral', 'Fórum de Sobral'),
                                   ('ForumdeSocorro', 'Fórum de Socorro'),
                                   ('ForumdeSoledade', 'Fórum de Soledade'),
                                   ('ForumdeSolonopole', 'Fórum de Solonopole'),
                                   ('ForumdeSolanea', 'Fórum de Solânea'),
                                   ('ForumdeSombrio', 'Fórum de Sombrio'),
                                   ('ForumdeSonora', 'Fórum de Sonora'),
                                   ('ForumdeSorocaba', 'Fórum de Sorocaba'),
                                   ('ForumdeSorriso', 'Fórum de Sorriso'),
                                   ('ForumdeSoure', 'Fórum de Soure'),
                                   ('ForumdeSousa', 'Fórum de Sousa'),
                                   ('ForumdeSoutoSoares', 'Fórum de Souto Soares'),
                                   ('ForumdeSumare', 'Fórum de Sumaré'),
                                   ('ForumdeSumidouro', 'Fórum de Sumidouro'),
                                   ('ForumdeSume', 'Fórum de Sumé'),
                                   ('ForumdeSurubim', 'Fórum de Surubim'),
                                   ('ForumdeSuzano', 'Fórum de Suzano'),
                                   ('ForumdeSatiroDias', 'Fórum de Sátiro Dias'),
                                   ('ForumdeSaoDesiderio', 'Fórum de São Desidério'),
                                   ('ForumdeSaoDomingosSC', 'Fórum de São Domingos - SC'),
                                   ('ForumdeSaoDomingosSE', 'Fórum de São Domingos- SE'),
                                   ('ForumdeSaoFelipe', 'Fórum de São Felipe'),
                                   ('ForumdeSaoFidelis', 'Fórum de São Fidelis'),
                                   ('ForumdeSaoFranciscoSE', 'Fórum de São Francisco - SE'),
                                   ('ForumdeSaoFranciscodoSul', 'Fórum de São Francisco do Sul'),
                                   ('ForumdeSaoFelix', 'Fórum de São Félix'),
                                   ('ForumdeSaoFelixDoXingu', 'Fórum de São Félix Do Xingú'),
                                   ('ForumdeSaoSebastiaoSP', 'Fórum de São Sebastião - SP'),
                                   ('ForumdeSaoSebastiaoDoUatuma', 'Fórum de São Sebastião Do Uatumã'),
                                   ('ForumdeSaoSepe', 'Fórum de São Sepé'),
                                   ('ForumdeSaoSimaoSP', 'Fórum de São Simão - SP'),
                                   ('ForumdeSaoTome', 'Fórum de São Tomé'),
                                   ('ForumdeSaoValentim', 'Fórum de São Valentim'),
                                   ('ForumdeSaoVicente', 'Fórum de São Vicente'),
                                   ('ForumdeSaoVicentedoSul', 'Fórum de São Vicente do Sul'),
                                   ('ForumdeSaoVicenteFerrer', 'Fórum de São Vicente Ferrer'),
                                   ('ForumdeTaboaodaSerra', 'Fórum de Taboão da Serra'),
                                   ('ForumdeTangaraDaSerra', 'Fórum de Tangará Da Serra'),
                                   ('ForumdeTeixeiraSoares', 'Fórum de Teixeira Soares'),
                                   ('ForumdeTeodoroSampaioSP', 'Fórum de Teodoro Sampaio - SP'),
                                   ('ForumdeTerraSanta', 'Fórum de Terra Santa'),
                                   ('ForumdeUrbanoSantos', 'Fórum de Urbano Santos'),
                                   ('ForumFederaldeFeiraDeSantana', 'Fórum Federal de Feira De Santana'),
                                   ('ForumFederaldeSalgueiro', 'Fórum Federal de Salgueiro'),
                                   ('ForumFederaldeSalvador', 'Fórum Federal de Salvador'),
                                   ('ForumFederaldeSantarem', 'Fórum Federal de Santarém'),
                                   ('ForumFederaldeSantiago', 'Fórum Federal de Santiago'),
                                   ('ForumFederaldeSantoAngelo', 'Fórum Federal de Santo Ângelo'),
                                   ('ForumFederaldeSantos', 'Fórum Federal de Santos'),
                                   ('ForumFederaldeSerraTalhada', 'Fórum Federal de Serra Talhada'),
                                   ('ForumFederaldeSinop', 'Fórum Federal de Sinop'),
                                   ('ForumFederaldeSobral', 'Fórum Federal de Sobral'),
                                   ('ForumFederaldeSorocaba', 'Fórum Federal de Sorocaba'),
                                   ('ForumFederaldeSousa', 'Fórum Federal de Sousa'),
                                   ('ForumFatimaDoSul', 'Fórum Fátima Do Sul'),
                                   ('SupremoTribunalFederal', 'Supremo Tribunal Federal'),
                                   ('TrabalhistadeFeiradeSantana', 'Trabalhista de Feira de Santana'),
                                   ('TrabalhistadeFatimadoSul', 'Trabalhista de Fátima do Sul'),
                                   ('TrabalhistadeSabara', 'Trabalhista de Sabará'),
                                   ('TrabalhistadeSalgueiro', 'Trabalhista de Salgueiro'),
                                   ('TrabalhistadeSalto', 'Trabalhista de Salto'),
                                   ('TrabalhistadeSalvador', 'Trabalhista de Salvador'),
                                   ('TrabalhistadeSantarem', 'Trabalhista de Santarém'),
                                   ('TrabalhistadeSantiago', 'Trabalhista de Santiago'),
                                   ('TrabalhistadeSantoAngelo', 'Trabalhista de Santo Ângelo'),
                                   ('TrabalhistadeSantos', 'Trabalhista de Santos'),
                                   ('TrabalhistadeSapiranga', 'Trabalhista de Sapiranga'),
                                   ('TrabalhistadeSapucaiadoSul', 'Trabalhista de Sapucaia do Sul'),
                                   ('TrabalhistadeSerraTalhada', 'Trabalhista de Serra Talhada'),
                                   ('TrabalhistadeSertania', 'Trabalhista de Sertânia'),
                                   ('TrabalhistadeSertaozinho', 'Trabalhista de Sertãozinho'),
                                   ('TrabalhistadeSimoesFilho', 'Trabalhista de Simões Filho'),
                                   ('TrabalhistadeSinop', 'Trabalhista de Sinop'),
                                   ('TrabalhistadeSobral', 'Trabalhista de Sobral'),
                                   ('TrabalhistadeSoledade', 'Trabalhista de Soledade'),
                                   ('TrabalhistadeSorocaba', 'Trabalhista de Sorocaba'),
                                   ('TrabalhistadeSorriso', 'Trabalhista de Sorriso'),
                                   ('TrabalhistadeSousa', 'Trabalhista de Sousa'),
                                   ('TrabalhistadeSumare', 'Trabalhista de Sumaré'),
                                   ('TrabalhistadeSurubim', 'Trabalhista de Surubim'),
                                   ('TrabalhistadeSuzano', 'Trabalhista de Suzano'),
                                   ('TrabalhistadeSaoSebastiao', 'Trabalhista de São Sebastião'),
                                   ('TrabalhistadeSaoVicente', 'Trabalhista de São Vicente'),
                                   ('TrabalhistadeTaboaodaSerra', 'Trabalhista de Taboão da Serra'),
                                   ('TrabalhistadeTangaradaSerra', 'Trabalhista de Tangará da Serra'),
                                   ('TrabalhistadeTeodoroSampaio', 'Trabalhista de Teodoro Sampaio'),
                                   ('ForumdeTabapora', 'Fórum de Tabaporã'),
                                   ('ForumdeTabapua', 'Fórum de Tabapuã'),
                                   ('ForumdeTabatinga', 'Fórum de Tabatinga'),
                                   ('ForumdeTabira', 'Fórum de Tabira'),
                                   ('ForumdeTacaimbo', 'Fórum de Tacaimbó'),
                                   ('ForumdeTacaratu', 'Fórum de Tacaratu'),
                                   ('ForumdeTaguatingaDF', 'Fórum de Taguatinga - DF'),
                                   ('ForumdeTaguatingaTO', 'Fórum de Taguatinga - TO'),
                                   ('ForumdeTailandia', 'Fórum de Tailândia'),
                                   ('ForumdeTaio', 'Fórum de Taio'),
                                   ('ForumdeTaiobeiras', 'Fórum de Taiobeiras'),
                                   ('ForumdeTaipu', 'Fórum de Taipu'),
                                   ('ForumdeTambau', 'Fórum de Tambaú'),
                                   ('ForumdeTanabi', 'Fórum de Tanabi'),
                                   ('ForumdeTangara', 'Fórum de Tangara'),
                                   ('ForumdeTanhacu', 'Fórum de Tanhaçu'),
                                   ('ForumdeTapaua', 'Fórum de Tapauá'),
                                   ('ForumdeTapejara', 'Fórum de Tapejara'),
                                   ('ForumdeTapera', 'Fórum de Tapera'),
                                   ('ForumdeTaperoa', 'Fórum de Taperoá'),
                                   ('ForumdeTapes', 'Fórum de Tapes'),
                                   ('ForumdeTapurah', 'Fórum de Tapurah'),
                                   ('ForumdeTaquara', 'Fórum de Taquara'),
                                   ('ForumdeTaquari', 'Fórum de Taquari'),
                                   ('ForumdeTaquaritinga', 'Fórum de Taquaritinga'),
                                   ('ForumdeTaquarituba', 'Fórum de Taquarituba'),
                                   ('ForumdeTarauaca', 'Fórum de Tarauacá'),
                                   ('ForumdeTartarugalzinho', 'Fórum de Tartarugalzinho'),
                                   ('ForumdeTarumirim', 'Fórum de Tarumirim'),
                                   ('ForumdeTassoFragoso', 'Fórum de Tasso Fragoso'),
                                   ('ForumdeTatui', 'Fórum de Tatuí'),
                                   ('ForumdeTaubate', 'Fórum de Taubaté'),
                                   ('ForumdeTaua', 'Fórum de Tauá'),
                                   ('ForumdeTefe', 'Fórum de Tefé'),
                                   ('ForumdeTeixeira', 'Fórum de Teixeira'),
                                   ('ForumdeTeixeiradeFreitas', 'Fórum de Teixeira de Freitas'),
                                   ('ForumdeTeixeiras', 'Fórum de Teixeiras'),
                                   ('ForumdeTelha', 'Fórum de Telha')
                                   ;
                                   INSERT INTO harvey.forum (id, display_name) VALUES
                                   ('ForumdeTeofilandia', 'Fórum de Teofilândia'),
                                   ('ForumdeTeotonioVilela', 'Fórum de Teotônio Vilela'),
                                   ('ForumdeTerenos', 'Fórum de Terenos'),
                                   ('ForumdeTeresopolis', 'Fórum de Teresopolis'),
                                   ('ForumdeTeutonia', 'Fórum de Teutônia'),
                                   ('ForumdeTiangua', 'Fórum de Tianguá'),
                                   ('ForumdeTibagi', 'Fórum de Tibagi'),
                                   ('ForumdeTijucas', 'Fórum de Tijucas'),
                                   ('ForumdeTimbauba', 'Fórum de Timbaúba'),
                                   ('ForumdeTimbiras', 'Fórum de Timbiras'),
                                   ('ForumdeTimbo', 'Fórum de Timbo'),
                                   ('ForumdeTimon', 'Fórum de Timon'),
                                   ('ForumdeTimoteo', 'Fórum de Timóteo'),
                                   ('ForumdeTiros', 'Fórum de Tiros'),
                                   ('ForumdeTocantinopolis', 'Fórum de Tocantinópolis'),
                                   ('ForumdeTocantinia', 'Fórum de Tocantínia'),
                                   ('ForumdeToledo', 'Fórum de Toledo'),
                                   ('ForumdeTomazina', 'Fórum de Tomazina'),
                                   ('ForumdeTombos', 'Fórum de Tombos'),
                                   ('ForumdeToritama', 'Fórum de Toritama'),
                                   ('ForumdeTorres', 'Fórum de Torres'),
                                   ('ForumdeTouros', 'Fórum de Touros'),
                                   ('ForumdeTracunhaem', 'Fórum de Tracunhaém'),
                                   ('ForumdeTraipu', 'Fórum de Traipu'),
                                   ('ForumdeTrairi', 'Fórum de Trairi'),
                                   ('ForumdeTramandai', 'Fórum de Tramandaí'),
                                   ('ForumdeTremedal', 'Fórum de Tremedal'),
                                   ('ForumdeTremembe', 'Fórum de Tremembé'),
                                   ('ForumdeTrindade', 'Fórum de Trindade'),
                                   ('ForumdeTubarao', 'Fórum de Tubarao'),
                                   ('ForumdeTucano', 'Fórum de Tucano'),
                                   ('ForumdeTucuma', 'Fórum de Tucumã'),
                                   ('ForumdeTucunduva', 'Fórum de Tucunduva'),
                                   ('ForumdeTucurui', 'Fórum de Tucuruí'),
                                   ('ForumdeTuntum', 'Fórum de Tuntum'),
                                   ('ForumdeTupaciguara', 'Fórum de Tupaciguara'),
                                   ('ForumdeTupancireta', 'Fórum de Tupanciretã'),
                                   ('ForumdeTuparetama', 'Fórum de Tuparetama'),
                                   ('ForumdeTupa', 'Fórum de Tupã'),
                                   ('ForumdeTuriacu', 'Fórum de Turiaçu'),
                                   ('ForumdeTurmalina', 'Fórum de Turmalina'),
                                   ('ForumdeTurvo', 'Fórum de Turvo'),
                                   ('ForumdeTurvania', 'Fórum de Turvânia'),
                                   ('ForumdeTutoia', 'Fórum de Tutóia'),
                                   ('ForumFederaldeTabatinga', 'Fórum Federal de Tabatinga'),
                                   ('ForumFederaldeTaubate', 'Fórum Federal de Taubaté'),
                                   ('ForumFederaldeTeresina', 'Fórum Federal de Teresina'),
                                   ('ForumFederaldeTeresopolis', 'Fórum Federal de Teresópolis'),
                                   ('ForumFederaldeToledo', 'Fórum Federal de Toledo'),
                                   ('ForumFederaldeTubarao', 'Fórum Federal de Tubarão'),
                                   ('ForumFederaldeTupa', 'Fórum Federal de Tupã'),
                                   ('TrabalhistadeDiadema', 'Trabalhista de Diadema'),
                                   ('TrabalhistadeDiamantina', 'Trabalhista de Diamantina'),
                                   ('TrabalhistadeDiamantino', 'Trabalhista de Diamantino'),
                                   ('TrabalhistadeDianopolis', 'Trabalhista de Dianópolis'),
                                   ('TrabalhistadeDivinopolis', 'Trabalhista de Divinópolis'),
                                   ('TrabalhistadeDoisVizinhos', 'Trabalhista de Dois Vizinhos'),
                                   ('TrabalhistadeDourados', 'Trabalhista de Dourados'),
                                   ('TrabalhistadeDracena', 'Trabalhista de Dracena'),
                                   ('TrabalhistadeFarroupilha', 'Trabalhista de Farroupilha'),
                                   ('TrabalhistadeFeijo', 'Trabalhista de Feijó'),
                                   ('TrabalhistadeFernandopolis', 'Trabalhista de Fernandópolis'),
                                   ('TrabalhistadeFerrazdeVasconcelos', 'Trabalhista de Ferraz de Vasconcelos'),
                                   ('TrabalhistadeFloresta', 'Trabalhista de Floresta'),
                                   ('TrabalhistadeFloriano', 'Trabalhista de Floriano'),
                                   ('TrabalhistadeFlorianopolis', 'Trabalhista de Florianópolis'),
                                   ('TrabalhistadeFormiga', 'Trabalhista de Formiga'),
                                   ('TrabalhistadeFormosa', 'Trabalhista de Formosa'),
                                   ('TrabalhistadeFortaleza', 'Trabalhista de Fortaleza'),
                                   ('TrabalhistadeFraiburgo', 'Trabalhista de Fraiburgo'),
                                   ('TrabalhistadeFranca', 'Trabalhista de Franca'),
                                   ('TrabalhistadeFredericoWestphalen', 'Trabalhista de Frederico Westphalen'),
                                   ('TrabalhistadeTabatinga', 'Trabalhista de Tabatinga'),
                                   ('TrabalhistadeTaguatinga', 'Trabalhista de Taguatinga'),
                                   ('TrabalhistadeTanabi', 'Trabalhista de Tanabi'),
                                   ('TrabalhistadeTaperoa', 'Trabalhista de Taperoá'),
                                   ('TrabalhistadeTaquara', 'Trabalhista de Taquara'),
                                   ('TrabalhistadeTaquaritinga', 'Trabalhista de Taquaritinga'),
                                   ('TrabalhistadeTarauaca', 'Trabalhista de Tarauacá'),
                                   ('TrabalhistadeTatui', 'Trabalhista de Tatui'),
                                   ('TrabalhistadeTaubate', 'Trabalhista de Taubaté'),
                                   ('TrabalhistadeTefe', 'Trabalhista de Tefé'),
                                   ('TrabalhistadeTeixeiradeFreitas', 'Trabalhista de Teixeira de Freitas'),
                                   ('TrabalhistadeTeresina', 'Trabalhista de Teresina'),
                                   ('TrabalhistadeTeresopolis', 'Trabalhista de Teresópolis'),
                                   ('TrabalhistadeTiangua', 'Trabalhista de Tianguá'),
                                   ('TrabalhistadeTiete', 'Trabalhista de Tietê'),
                                   ('TrabalhistadeTimbauba', 'Trabalhista de Timbaúba'),
                                   ('TrabalhistadeTimon', 'Trabalhista de Timon'),
                                   ('TrabalhistadeToledo', 'Trabalhista de Toledo'),
                                   ('TrabalhistadeTorres', 'Trabalhista de Torres'),
                                   ('TrabalhistadeTriunfo', 'Trabalhista de Triunfo'),
                                   ('TrabalhistadeTubarao', 'Trabalhista de Tubarão'),
                                   ('TrabalhistadeTucurui', 'Trabalhista de Tucuruí'),
                                   ('TrabalhistadeTupa', 'Trabalhista de Tupã'),
                                   ('TrabalhistadeUbatuba', 'Trabalhista de Ubatuba'),
                                   ('TrabalhistadeUbaira', 'Trabalhista de Ubaíra'),
                                   ('TrabalhistadeUberaba', 'Trabalhista de Uberaba'),
                                   ('TrabalhistadeUberlandia', 'Trabalhista de Uberlândia'),
                                   ('TrabalhistadeUba', 'Trabalhista de Ubá'),
                                   ('TrabalhistadeUmuarama', 'Trabalhista de Umuarama'),
                                   ('TrabalhistadeUnai', 'Trabalhista de Unaí'),
                                   ('TrabalhistadeUniaodaVitoria', 'Trabalhista de União da Vitória'),
                                   ('TrabalhistadeUruacu', 'Trabalhista de Uruaçu'),
                                   ('TrabalhistadeUruguaiana', 'Trabalhista de Uruguaiana'),
                                   ('TrabalhistadeVacaria', 'Trabalhista de Vacaria'),
                                   ('TrabalhistadeValenca', 'Trabalhista de Valença'),
                                   ('TrabalhistadeVarginha', 'Trabalhista de Varginha'),
                                   ('TrabalhistadeViamao', 'Trabalhista de Viamão'),
                                   ('TrabalhistadeVideira', 'Trabalhista de Videira'),
                                   ('TrabalhistadeVilhena', 'Trabalhista de Vilhena'),
                                   ('TrabalhistadeVitoria', 'Trabalhista de Vitória'),
                                   ('TrabalhistadeVotuporanga', 'Trabalhista de Votuporanga'),
                                   ('TrabalhistadeXanxere', 'Trabalhista de Xanxerê'),
                                   ('TrabalhistadeXinguara', 'Trabalhista de Xinguara'),
                                   ('TrabalhistadeObidos', 'Trabalhista de Óbidos'),
                                   ('TST', 'TST'),
                                   ('VarasdaFamiladeTeresina', 'Varas da Famíla de Teresina'),
                                   ('ForumdeUaua', 'Fórum de Uauá'),
                                   ('ForumdeUbaitaba', 'Fórum de Ubaitaba'),
                                   ('ForumdeUbajara', 'Fórum de Ubajara'),
                                   ('ForumdeUbatuba', 'Fórum de Ubatuba'),
                                   ('ForumdeUbata', 'Fórum de Ubatã'),
                                   ('ForumdeUbaira', 'Fórum de Ubaíra'),
                                   ('ForumdeUberaba', 'Fórum de Uberaba'),
                                   ('ForumdeUberlandia', 'Fórum de Uberlândia'),
                                   ('ForumdeUbirata', 'Fórum de Ubiratã'),
                                   ('ForumdeUba', 'Fórum de Ubá'),
                                   ('ForumdeUibai', 'Fórum de Uibaí'),
                                   ('ForumdeUirauna', 'Fórum de Uiraúna'),
                                   ('ForumdeUlianopolis', 'Fórum de Ulianópolis'),
                                   ('ForumdeUmarizal', 'Fórum de Umarizal'),
                                   ('ForumdeUmbauba', 'Fórum de Umbaúba'),
                                   ('ForumdeUmbuzeiro', 'Fórum de Umbuzeiro'),
                                   ('ForumdeUmuarama', 'Fórum de Umuarama'),
                                   ('ForumdeUna', 'Fórum de Una'),
                                   ('ForumdeUnai', 'Fórum de Unaí'),
                                   ('ForumdeUniao', 'Fórum de União'),
                                   ('ForumdeUniaodaVitoria', 'Fórum de União da Vitória'),
                                   ('ForumdeUpanema', 'Fórum de Upanema'),
                                   ('ForumdeUrandi', 'Fórum de Urandi'),
                                   ('ForumdeUrai', 'Fórum de Uraí'),
                                   ('ForumdeUruana', 'Fórum de Uruana'),
                                   ('ForumdeUruara', 'Fórum de Uruará'),
                                   ('ForumdeUruacu', 'Fórum de Uruaçu'),
                                   ('ForumdeUrubici', 'Fórum de Urubici'),
                                   ('ForumdeUruburetama', 'Fórum de Uruburetama'),
                                   ('ForumdeUrucara', 'Fórum de Urucará'),
                                   ('ForumdeUrucurituba', 'Fórum de Urucurituba'),
                                   ('ForumdeUruguaiana', 'Fórum de Uruguaiana'),
                                   ('ForumdeUruoca', 'Fórum de Uruoca'),
                                   ('ForumdeUrupes', 'Fórum de Urupês'),
                                   ('ForumdeUrussanga', 'Fórum de Urussanga'),
                                   ('ForumdeUrutai', 'Fórum de Urutaí'),
                                   ('ForumdeUrucuca', 'Fórum de Uruçuca'),
                                   ('ForumdeUrucui', 'Fórum de Uruçui'),
                                   ('ForumdeUrania', 'Fórum de Urânia'),
                                   ('ForumdeUtinga', 'Fórum de Utinga'),
                                   ('ForumFederaldeUberaba', 'Fórum Federal de Uberaba'),
                                   ('ForumFederaldeUberlandia', 'Fórum Federal de Uberlândia'),
                                   ('ForumFederaldeUmuarama', 'Fórum Federal de Umuarama'),
                                   ('ForumFederaldeUniaoDaVitoria', 'Fórum Federal de União Da Vitória'),
                                   ('ForumFederaldeUruguaiana', 'Fórum Federal de Uruguaiana'),
                                   ('ForumdeVacaria', 'Fórum de Vacaria'),
                                   ('ForumdeValenca', 'Fórum de Valenca'),
                                   ('ForumdeValente', 'Fórum de Valente'),
                                   ('ForumdeValinhos', 'Fórum de Valinhos'),
                                   ('ForumdeValparaiso', 'Fórum de Valparaíso'),
                                   ('ForumdeVarginha', 'Fórum de Varginha'),
                                   ('ForumdeVarjao', 'Fórum de Varjão'),
                                   ('ForumdeVassouras', 'Fórum de Vassouras'),
                                   ('ForumdeVazante', 'Fórum de Vazante'),
                                   ('ForumdeVenturosa', 'Fórum de Venturosa'),
                                   ('ForumdeVera', 'Fórum de Vera'),
                                   ('ForumdeVeranopolis', 'Fórum de Veranópolis'),
                                   ('ForumdeVerdejante', 'Fórum de Verdejante'),
                                   ('ForumdeVertentes', 'Fórum de Vertentes'),
                                   ('ForumdeVespasiano', 'Fórum de Vespasiano'),
                                   ('ForumdeViamao', 'Fórum de Viamão'),
                                   ('ForumdeViana', 'Fórum de Viana'),
                                   ('ForumdeVianopolis', 'Fórum de Vianópolis'),
                                   ('ForumdeVicencia', 'Fórum de Vicência'),
                                   ('ForumdeVideira', 'Fórum de Videira'),
                                   ('ForumdeVigia', 'Fórum de Vigia'),
                                   ('ForumdeVilaVelha', 'Fórum de Vila Velha'),
                                   ('ForumdeVilhena', 'Fórum de Vilhena'),
                                   ('ForumdeVinhedo', 'Fórum de Vinhedo'),
                                   ('ForumdeViradouro', 'Fórum de Viradouro'),
                                   ('ForumdeVirginopolis', 'Fórum de Virginópolis'),
                                   ('ForumdeViseu', 'Fórum de Viseu'),
                                   ('ForumdeVitorinoFreire', 'Fórum de Vitorino Freire'),
                                   ('ForumdeVotorantim', 'Fórum de Votorantim'),
                                   ('ForumdeVotuporanga', 'Fórum de Votuporanga'),
                                   ('ForumFederaldeVarginha', 'Fórum Federal de Varginha'),
                                   ('ForumFederaldeVitoria', 'Fórum Federal de Vitória'),
                                   ('ForumdeWanderley', 'Fórum de Wanderley'),
                                   ('ForumdeWanderlandia', 'Fórum de Wanderlândia'),
                                   ('ForumdeXambioa', 'Fórum de Xambioá'),
                                   ('ForumdeXambre', 'Fórum de Xambrê'),
                                   ('ForumdeXanxere', 'Fórum de Xanxere'),
                                   ('ForumdeXapuri', 'Fórum de Xapuri'),
                                   ('ForumdeXaxim', 'Fórum de Xaxim'),
                                   ('ForumdeXinguara', 'Fórum de Xinguara'),
                                   ('ForumdeXiqueXique', 'Fórum de Xique-Xique'),
                                   ('ForumdeZeDoca', 'Fórum de Zé Doca')
                                   ;");
        }
        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DELETE FROM court_division WHERE id IN (
                'Camara',
                'CamaraCivel',
                'VaraCriminal',
                'VaraCivel',
                'TribunaldeInstrucaoNº3deMadrid',
                'VaradeFamilia/Sucessoes',
                'VaradeFazendaPublica',
                'VaradoTrabalho',
                'VaraEleitoral',
                'VaraEmpresarial',
                'SupremoTribunalFederal',
                'VaraFederal',
                'SecretariaJudiciaria',
                'TurmaRecursal',
                'VaraTrabalhista'
                );");
        }
    }
}
