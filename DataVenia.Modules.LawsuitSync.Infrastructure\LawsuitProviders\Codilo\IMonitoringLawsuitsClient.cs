using DataVenia.Modules.LawsuitSync.Domain.Entities;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Requests;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;
using Microsoft.AspNetCore.Mvc;
using Refit;

namespace DataVenia.Modules.LawsuitSync.Infrastructure.LawsuitProviders.Codilo;

public interface IMonitoringLawsuitsClient
{
    [Post("/v1/processo/novo")]
    Task<StartMonitoringResponse> StartLawsuitMonitoring([Body] StartMonitoringRequest request);
    
    [Delete("/v1/processo/excluir/{lawsuitId}")]
    Task<LawsuitStopMonitoringResponse?> CancelLawsuitMonitoring(string lawsuitId);
}
