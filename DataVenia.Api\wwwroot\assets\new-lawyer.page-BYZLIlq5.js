import{e as A,j as a,r as p,o as w,s as R,p as L,q as _,v as J,d as E,l as q,$ as U,O as T,c as g,a as I,J as W}from"./index-DxHSBLqJ.js";import{C as M}from"./card-header-title-MEld3mYM.js";import{L as X}from"./lawyers-487mkhkR.js";import{u as D}from"./use-controlled-list-DZx5PfZt.js";import{l as H}from"./index-BjcY0-OB.js";import{Z as Q}from"./schemas-CWY_3UB6.js";import{E as K}from"./external-link-TCLM46lm.js";import"./brazilian-values-a8H3KUqb.js";/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=A("AtSign",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8",key:"7n84p3"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=A("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=A("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=A("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),$=e=>a.jsx("p",{className:"text-description text-secondary-foreground",children:e.children}),se=[{label:"Acre",value:"AC"},{label:"Alagoas",value:"AL"},{label:"Amapá",value:"AP"},{label:"Amazonas",value:"AM"},{label:"Bahia",value:"BA"},{label:"Ceará",value:"CE"},{label:"Distrito Federal",value:"DF"},{label:"Espírito Santo",value:"ES"},{label:"Goiás",value:"GO"},{label:"Mato Grosso",value:"MT"},{label:"Mato Grosso do Sul",value:"MS"},{label:"Minas Gerais",value:"MG"},{label:"Pará",value:"PA"},{label:"Paraíba",value:"PB"},{label:"Paraná",value:"PR"},{label:"Pernambuco",value:"PE"},{label:"Piauí",value:"PI"},{label:"Rio de Janeiro",value:"RJ"},{label:"Rio Grande do Norte",value:"RN"},{label:"Rio Grande do Sul",value:"RS"},{label:"Rondônia",value:"RO"},{label:"Roraima",value:"RR"},{label:"Santa Catarina",value:"SC"},{label:"São Paulo",value:"SP"},{label:"Sergipe",value:"SE"},{label:"Tocantins",value:"TO"}],re=(e,n)=>e.localeCompare(n),le={sort:re,allowDots:!0,charset:"utf-8",parseArrays:!0,plainObjects:!0,charsetSentinel:!0,allowPrototypes:!1,depth:Number.MAX_SAFE_INTEGER,arrayLimit:Number.MAX_SAFE_INTEGER,parameterLimit:Number.MAX_SAFE_INTEGER},ne=e=>{const n=new FormData(e),l=new URLSearchParams(n);return H.parse(l.toString(),le)},oe=e=>e.replace("[",".").replace("]","").split("."),S=(e,n)=>oe(e).reduce((l,o)=>{var f;if(o==="")return l;const i=((f=l.shape)==null?void 0:f[o])||l;return i._def.typeName==="ZodArray"?i.element:i},n),y=e=>e.dataset.value?e.dataset.value:e.type==="checkbox"?e.checked:e.type==="number"?e.valueAsNumber:e.value||e.getAttribute("value"),ie=(e,n)=>{const[l,o]=p.useState(null),i=p.useRef({}),[f,N]=p.useState({}),k=(s,c)=>{const r=S(s,e);return{...c,name:s,id:s,form:n,required:!r.isOptional(),error:l==null?void 0:l[s],ref:t=>{t!==null&&(i.current[s]={element:t,schema:r})}}},F=(s,c)=>{const r=S(s,e);return{...c,name:s,id:s,form:n,required:!r.isOptional(),error:l==null?void 0:l[s],ref:t=>{t!==null&&(i.current[s]={element:t,schema:r})}}},G=(s,c)=>{const r=S(s,e);return{...c,name:s,id:s,form:n,required:!r.isOptional(),error:l==null?void 0:l[s],ref:t=>{t!==null&&(i.current[s]={element:t,schema:r})}}},B=(s,c)=>{const r=S(s,e);return{...c,name:s,id:s,required:!r.isOptional(),form:n,type:w.instance(r,Q)?"number":(c==null?void 0:c.type)??"text",error:l==null?void 0:l[s],ref:t=>{t!==null&&(i.current[s]={element:t,schema:r})}}};p.useEffect(()=>{const s=Object.values(i.current).map(r=>{const t=r.element.dataset.origin?document.querySelector(`[data-target="${r.element.name}"]`):r.element,v=r.schema.safeParse(y(t)),m=u=>{var j;const h=t.dataset.target||t.name;if(!h)return;const C=y(u.target)||(u.relatedTarget?y(u.relatedTarget):""),b=r.schema.safeParse(C);if(b.success)return t.setCustomValidity(""),N(x=>R(x,h,b.data)),o(x=>{const{[h]:O,...P}=x||{};return P===null||w.empty(P)?null:P});if(t.required){const x=((j=b.error.issues[0])==null?void 0:j.message)||"";t.setCustomValidity(x),o(O=>({...O,[h]:x}))}},d=t.getAttribute("data-trigger")||"blur";return t.addEventListener(d,m),t.tagName==="SELECT"&&t.addEventListener("change",m),{input:r,hasInitialError:t.required?!v.success:!1,unsubscribe:()=>{t.removeEventListener(d,m),t.tagName==="SELECT"&&t.addEventListener("change",m)}}});return s.some(r=>r.hasInitialError)&&o(r=>r===null?{}:r),()=>s.forEach(r=>r.unsubscribe())});const Z=p.useCallback(s=>c=>{const r=c.currentTarget,t=Object.values(i.current).reduce((m,d)=>{var j;const u=d.element,h=d.schema.safeParse(y(u));if(u.dataset.ignore==="ignore"||h.success)return m;const C=(j=h.error.issues[0])==null?void 0:j.message;u.setAttribute("data-initialized","true");const b=u.dataset.name||u.name||"";return{...m,[b]:C}},{}),v=w.empty(t)?null:t;o(v),s==null||s({form:r,errors:v||{}})},[]),V=p.useCallback(s=>c=>{c.preventDefault();const r=c.currentTarget;let t=ne(r);Array.from(document.querySelectorAll(`[form="${n}"]`)).forEach(d=>{if(d.tagName==="SELECT"){const u=d;t=R(t,u.name,u.value)}if(d.tagName==="INPUT"){const u=d;t=R(t,u.dataset.target||u.name,y(u))}});const m=e.safeParse({...t,...f});return m.success?s({form:r,json:t,data:m.data,event:c,reset:()=>L(r),success:!0,errors:[]}):s({form:r,json:t,event:c,data:t,success:!1,reset:()=>L(r),errors:m.error.issues.map(d=>({message:d.message,path:d.path.map(u=>String(u))}))})},[n]);return{state:f,input:B,datepicker:k,checkbox:G,select:F,onSubmit:V,errors:l,onInvalid:Z,disabled:l!==null,name:n,get:s=>_(f,s)||""}},ce=e=>{const n=e.replace(/[^0-9]/g,""),l=J(4,n.length,6);return n.length===6?[/\d/,/\d/,/\d/,/\d/,/\d/,/\d/,"/",/[A-Za-z]/,/[A-Za-z]/]:Array.from({length:l}).map(()=>/\d/).concat(["/",/[A-Z]/,/[A-Z]/])},ue=e=>{const[n,l]=p.useState(null);return a.jsx("li",{className:"flex w-full items-end gap-base",children:a.jsx(g,{...e.form.input(`oabs[${e.index}]`,{title:"OAB",container:"w-full",placeholder:"000000/BR",left:a.jsx("div",{className:"flex aspect-square size-10 items-center",children:a.jsx("img",{src:`/img/states/${(n==null?void 0:n.value.toLowerCase())||"brasil"}.svg`})}),mask:ce,onChange:o=>{const i=o.target.value.toUpperCase(),f=i.replace(/[^A-Z]/g,"");o.target.value=i;const N=se.find(k=>k.value===f);l(N||null)},right:e.disableDelete?null:a.jsx(I,{size:"small",theme:"ghost-danger","data-index":e.index,onClick:e.onRemove,icon:a.jsx(te,{size:16}),"aria-label":`Remover a OAB ${e.index+1}`})})})},`document-oab-index-${e.index}`)},de=e=>{const n=D(),l=o=>{const i=Number(o.currentTarget.dataset.index);n.remove(i)};return a.jsxs(T,{container:`w-full h-fit ${e.className}`,title:a.jsx(M,{title:"Dados jurídicos"}),className:"flex flex-col gap-kilo",children:[a.jsxs($,{children:["Caso você possua mais de uma OAB, basta adiciona-lá clicando no botão ",a.jsx("b",{children:"Nova OAB"})]}),a.jsx("ul",{className:"flex flex-row flex-wrap items-center gap-base",children:n.items.map(o=>a.jsx(ue,{form:e.form,index:o.index,onRemove:l,disableDelete:n.items.length<=1},`document-oab-index-${o}`))})]})},z=[{Render:()=>a.jsxs("span",{className:"flex items-center gap-1",children:[a.jsx(ae,{size:16}),"Celular/Whatsapp"]}),label:"Celular/Whatsapp",value:"cellphone","data-mask":"cellphone","data-placeholder":"(00) 90000-0000"},{Render:()=>a.jsxs("span",{className:"flex items-center gap-1",children:[a.jsx(Y,{size:16}),"Email"]}),label:"Email",value:"email","data-placeholder":"<EMAIL>"},{Render:()=>a.jsxs("span",{className:"flex items-center gap-1",children:[a.jsx(ee,{size:16}),"Instagram"]}),label:"Instagram",value:"instagram","data-placeholder":"@datavenia"}],me=e=>{const[n,l]=p.useState(""),o=z.find(i=>i.value===n);return a.jsxs("li",{className:"grid w-full grid-cols-1 gap-base lg:grid-cols-2",children:[a.jsx(W,{...e.form.select("contacts[0].type",{onReset:console.log,title:"Tipo de contato",placeholder:"Email ou Whatsapp",options:z,onChange:i=>l(i.target.value)})}),a.jsx(g,{...e.form.select("contacts[0].value",{title:"Contato",container:"w-full",mask:o==null?void 0:o["data-mask"],placeholder:(o==null?void 0:o["data-placeholder"])||"Valor do contato"})})]},`contact-item-${e.index}`)},fe=e=>{const n=D();return a.jsxs(T,{container:`w-full ${e.className}`,className:"flex flex-col gap-kilo",title:a.jsx(M,{title:"Contatos"}),children:[a.jsx($,{children:"Adicione aqui as formas de contato disponíveis do seu advogado. Essa informação será exibida apenas para os colaboradores na plataforma."}),a.jsx("ul",{className:"flex w-full flex-row flex-wrap items-center gap-base",children:n.items.map(l=>a.jsx(me,{index:l.index,form:e.form},`contact-item-${l}`))}),a.jsx(I,{onClick:n.add,className:"w-fit",children:"Novo contato"})]})},Ee=()=>({post:async e=>(e.form&&(e.form.reset(),L(e.form)),E.redirectResponse(e.link(e.path,{status:"success"})))});function Ae(){const e=ie(X.schemaCreate,"new-lawyer"),[n,l]=E.useQueryStringState(q.newLawyer),o=n.status==="success",i=()=>l(f=>({...f,status:void 0}));return a.jsxs(p.Fragment,{children:[o?a.jsxs(U,{theme:"success",title:"Sucesso",onClose:i,children:["O advogado foi cadastrado. Peça que o mesmo verifique o email de convite para o escritório."," ",a.jsxs(E.Link,{className:"link inline-flex items-center gap-1",href:q.office,children:["Voltar para a página do escritório ",a.jsx(K,{size:14})]})]}):null,a.jsxs(T,{title:a.jsx(M,{title:"Dados pessoais"}),children:[a.jsx(E.Form,{hidden:!0,method:"post",id:e.name,name:e.name,encType:"json",onInvalid:e.onInvalid(console.log),onSubmit:e.onSubmit(console.table)}),a.jsxs("div",{className:"grid grid-cols-1 gap-mega lg:grid-cols-2",children:[a.jsx(g,{...e.input("firstName",{title:"Primeiro nome",placeholder:"João"}),autoFocus:!0}),a.jsx(g,{...e.input("lastName",{title:"Sobrenome",placeholder:"Silva"})}),a.jsx(g,{...e.input("cpf",{title:"CPF",placeholder:"000.000.000-00",mask:"cpf"})}),a.jsx(g,{...e.input("email",{title:"Email",placeholder:"<EMAIL>",type:"email"})})]})]}),a.jsxs("section",{className:"grid grid-cols-1 gap-mega lg:grid-cols-3",children:[a.jsx(de,{form:e,className:""}),a.jsx(fe,{form:e,className:"lg:col-span-2"})]}),a.jsx("div",{className:"flex w-full justify-end",children:a.jsx(I,{form:e.name,type:"submit",children:"Salvar"})})]})}export{Ee as actions,Ae as default};
