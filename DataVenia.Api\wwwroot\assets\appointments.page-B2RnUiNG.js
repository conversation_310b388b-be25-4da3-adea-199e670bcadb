import{j as e,f as m,r as p,O as n,d as u,c as i,J as s,F as x,I as j}from"./index-DxHSBLqJ.js";import{z as a,S as r}from"./schemas-CWY_3UB6.js";import"./brazilian-values-a8H3KUqb.js";const c=t=>e.jsx("div",{className:"grid grid-cols-1 gap-mega lg:grid-cols-2",children:t.children});var l;(t=>{t.schemaCreate=a.object({type:r.string,name:r.string,description:r.string,from:a.object({from:a.date(),to:a.date()}),to:a.object({from:a.date(),to:a.date()}),responsibleLawyerId:r.uuid,recurrence:a.object({frequency:a.number().int()}),ownerLawyerId:r.uuid,participantLawyersId:a.array(r.uuid),alerts:a.array(a.object({ticks:a.number().int()}))})})(l||(l={}));const o="appointments",h=[],d=[{value:"unique",label:"Único acontecimento"},{value:"multiple-days",label:"Múltiplos dias"}],b=[{value:"none",label:"Sem repetição"},{value:"daily",label:"Diária"},{value:"weekly",label:"Uma vez por semana"},{value:"monthly",label:"Uma vez ao mês"}];function y(){const t=m(l.schemaCreate,o);return e.jsxs(p.Fragment,{children:[e.jsxs(n,{title:"Criar uma nova tarefa",children:[e.jsx(u.Form,{name:o,id:o}),e.jsxs(c,{children:[e.jsx(i,{...t.input("name",{placeholder:"Execução de prazo",title:"Nome da tarefa"}),autoFocus:!0}),e.jsx(s,{...t.input("type",{placeholder:"Prazo",title:"Tipo da tarefa",options:h})})]}),e.jsx(i,{...t.input("description",{placeholder:"Como executar",title:"Descrição"}),container:"mt-kilo"})]}),e.jsx(n,{title:"Data e hora",children:e.jsxs(c,{children:[e.jsxs("div",{className:"flex flex-wrap content-baseline gap-kilo",children:[e.jsxs("span",{className:"font-medium",children:["Data: ",x.brCalendar(new Date)]}),e.jsxs("div",{className:"flex w-full flex-col gap-kilo",children:[e.jsx(s,{required:!0,options:d,placeholder:"Único evento",title:"Intervalo da tarefa"}),e.jsx(s,{required:!0,options:d,placeholder:"Sem repetição",title:"Repetição do evento"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-kilo lg:grid-cols-2",children:[e.jsx(i,{placeholder:"HH:MM",required:!0,title:"Hora de início",mask:"time"}),e.jsx(i,{placeholder:"HH:MM",required:!0,title:"Hora de fim",mask:"time"})]})]}),e.jsx("div",{className:"w-full",children:e.jsx(j,{})})]})})]})}export{y as default,b as repetitionOptions};
