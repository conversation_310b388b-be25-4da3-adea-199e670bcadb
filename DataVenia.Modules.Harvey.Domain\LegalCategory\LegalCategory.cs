﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.LegalCategory;

public sealed class LegalCategory : Entity
{
    public static readonly LegalCategory CollectionAction = Create("CollectionAction", "Ação coletiva", 1);
    public static readonly LegalCategory DivorceAction = Create("DivorceAction", "Ação de divórcio", 2);
    public static readonly LegalCategory LaborAction = Create("LaborAction", "Ação trabalhista", 3);
    public static readonly LegalCategory TaxEnforcementAction = Create("TaxEnforcementAction", "Supremo Tribunal Federal", 4);
    public static readonly LegalCategory WritOfMandamus = Create("WritOfMandamus", "Mandado de segurança", 5);
    public static readonly LegalCategory HabeasCorpus = Create("HabeasCorpus", "Habeas Corpus", 6);
    public static readonly LegalCategory Other = Create("Other", "Other", 7);
    private LegalCategory() { }

    public string Id { get; private set; }
    public string DisplayName { get; private set; }
    public int Order { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public static LegalCategory Create(string name, string displayName, int order)
    {
        return new LegalCategory
        {
            Id = name,
            DisplayName = displayName,
            Order = order
        };
    }
}
