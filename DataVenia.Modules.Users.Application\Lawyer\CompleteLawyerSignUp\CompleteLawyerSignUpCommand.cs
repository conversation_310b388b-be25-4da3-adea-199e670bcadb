﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Users.Domain.SharedModels;

namespace DataVenia.Modules.Users.Application.Lawyer.CompleteLawyerSignUp;

public sealed record CompleteLawyerSignUpCommand(
    Guid SignUpToken, 
    string Password, 
    string FirstName, 
    string LastName, 
    IReadOnlyCollection<Contact> Contacts, 
    IReadOnlyCollection<string> Oabs, 
    string Cpf, 
    string? Rg, 
    string? Cnh, 
    string? Passport, 
    string? Ctps, 
    string? Pis, 
    string? VoterId)
    : ICommand<Guid>;
