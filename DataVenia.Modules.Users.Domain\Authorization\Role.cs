﻿namespace DataVenia.Modules.Users.Domain.Authorization;

public sealed class Role
{
    public static readonly Role OfficeAdministrator = new("OfficeAdministrator");
    public static readonly Role OfficeMember = new("OfficeMember");
    public static readonly Role OfficeClient = new("OfficeClient");
    public static readonly Role SystemAdministrator = new("SystemAdministrator", true);
    public static readonly Role SystemMember = new("SystemMember", true);

    private Role(string name, bool isGlobal = false)
    {
        Name = name;
        IsGlobal = isGlobal;
    }

    private Role()
    {
    }

    public string Name { get; private set; }
    public bool IsGlobal { get; private set; }
}
