﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class CreateDatabase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "lawsuit");

            migrationBuilder.CreateTable(
                name: "case",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    case_type_id = table.Column<string>(type: "text", nullable: false),
                    legal_category_id = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_case", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "folder",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    parent_folder_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_folder", x => x.id);
                    table.ForeignKey(
                        name: "fk_folder_folder_parent_folder_id",
                        column: x => x.parent_folder_id,
                        principalSchema: "lawsuit",
                        principalTable: "folder",
                        principalColumn: "id");
                });

            migrationBuilder.CreateTable(
                name: "lawsuit",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    cnj = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    lawsuit_type_id = table.Column<string>(type: "text", nullable: false),
                    legal_category_id = table.Column<string>(type: "text", nullable: false),
                    distributed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "lawsuit_response",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    title = table.Column<string>(type: "text", nullable: false),
                    cnj = table.Column<string>(type: "text", nullable: false),
                    folder_id = table.Column<Guid>(type: "uuid", nullable: true),
                    folder_name = table.Column<string>(type: "text", nullable: true),
                    legal_instance_id = table.Column<string>(type: "text", nullable: false),
                    lawsuit_type_id = table.Column<string>(type: "text", nullable: false),
                    legal_category_id = table.Column<string>(type: "text", nullable: false),
                    lawsuit_status_id = table.Column<string>(type: "text", nullable: false),
                    court = table.Column<string>(type: "text", nullable: false),
                    court_division_id = table.Column<string>(type: "text", nullable: false),
                    forum_id = table.Column<string>(type: "text", nullable: false),
                    court_href = table.Column<string>(type: "text", nullable: false),
                    cause_value = table.Column<decimal>(type: "numeric", nullable: true),
                    conviction_value = table.Column<decimal>(type: "numeric", nullable: true),
                    distributed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    responsibles = table.Column<List<Guid>>(type: "uuid[]", nullable: false)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "party_dto",
                schema: "lawsuit",
                columns: table => new
                {
                    user_id = table.Column<Guid>(type: "uuid", nullable: false),
                    is_client = table.Column<bool>(type: "boolean", nullable: false),
                    party_type = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "case_data",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    case_id = table.Column<Guid>(type: "uuid", nullable: false),
                    folder_id = table.Column<Guid>(type: "uuid", nullable: true),
                    cause_value = table.Column<decimal>(type: "numeric", nullable: false),
                    conviction_value = table.Column<decimal>(type: "numeric", nullable: true),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    observations = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    access = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_case_data", x => x.id);
                    table.ForeignKey(
                        name: "fk_case_data_case_case_id",
                        column: x => x.case_id,
                        principalSchema: "lawsuit",
                        principalTable: "case",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_case_data_folder_folder_id",
                        column: x => x.folder_id,
                        principalSchema: "lawsuit",
                        principalTable: "folder",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "lawsuit_data",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    cnj = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    lawsuit_id = table.Column<Guid>(type: "uuid", nullable: false),
                    folder_id = table.Column<Guid>(type: "uuid", nullable: true),
                    legal_instance_id = table.Column<string>(type: "text", nullable: false),
                    lawsuit_status_id = table.Column<string>(type: "text", nullable: false),
                    process_id = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    court = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    court_division_id = table.Column<string>(type: "text", nullable: false),
                    forum_id = table.Column<string>(type: "text", nullable: false),
                    cause_value = table.Column<decimal>(type: "numeric", nullable: false),
                    conviction_value = table.Column<decimal>(type: "numeric", nullable: true),
                    court_href = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    observations = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    access = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_data", x => x.id);
                    table.ForeignKey(
                        name: "fk_lawsuit_data_folder_folder_id",
                        column: x => x.folder_id,
                        principalSchema: "lawsuit",
                        principalTable: "folder",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_lawsuit_data_lawsuit_lawsuit_id",
                        column: x => x.lawsuit_id,
                        principalSchema: "lawsuit",
                        principalTable: "lawsuit",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "case_party",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    case_data_id = table.Column<Guid>(type: "uuid", nullable: false),
                    party_id = table.Column<Guid>(type: "uuid", nullable: false),
                    party_type = table.Column<string>(type: "text", nullable: false),
                    is_client = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_case_party", x => x.id);
                    table.ForeignKey(
                        name: "fk_case_party_case_data_case_data_id",
                        column: x => x.case_data_id,
                        principalSchema: "lawsuit",
                        principalTable: "case_data",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "case_responsible",
                schema: "lawsuit",
                columns: table => new
                {
                    case_data_id = table.Column<Guid>(type: "uuid", nullable: false),
                    lawyer_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_case_responsible", x => new { x.case_data_id, x.lawyer_id });
                    table.ForeignKey(
                        name: "fk_case_responsible_case_datas_case_data_id",
                        column: x => x.case_data_id,
                        principalSchema: "lawsuit",
                        principalTable: "case_data",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "lawsuit_party",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    lawsuit_data_id = table.Column<Guid>(type: "uuid", nullable: false),
                    party_id = table.Column<Guid>(type: "uuid", nullable: false),
                    party_type = table.Column<string>(type: "text", nullable: false),
                    is_client = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_party", x => x.id);
                    table.ForeignKey(
                        name: "fk_lawsuit_party_lawsuit_data_lawsuit_data_id",
                        column: x => x.lawsuit_data_id,
                        principalSchema: "lawsuit",
                        principalTable: "lawsuit_data",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "lawsuit_responsible",
                schema: "lawsuit",
                columns: table => new
                {
                    lawsuit_data_id = table.Column<Guid>(type: "uuid", nullable: false),
                    lawyer_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_responsible", x => new { x.lawsuit_data_id, x.lawyer_id });
                    table.ForeignKey(
                        name: "fk_lawsuit_responsible_lawsuit_datas_lawsuit_data_id",
                        column: x => x.lawsuit_data_id,
                        principalSchema: "lawsuit",
                        principalTable: "lawsuit_data",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_case_data_case_id",
                schema: "lawsuit",
                table: "case_data",
                column: "case_id");

            migrationBuilder.CreateIndex(
                name: "ix_case_data_folder_id",
                schema: "lawsuit",
                table: "case_data",
                column: "folder_id");

            migrationBuilder.CreateIndex(
                name: "ix_case_party_case_data_id_party_id",
                schema: "lawsuit",
                table: "case_party",
                columns: new[] { "case_data_id", "party_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_folder_parent_folder_id",
                schema: "lawsuit",
                table: "folder",
                column: "parent_folder_id");

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_data_folder_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                column: "folder_id");

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_data_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                column: "lawsuit_id");

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_party_lawsuit_data_id_party_id",
                schema: "lawsuit",
                table: "lawsuit_party",
                columns: new[] { "lawsuit_data_id", "party_id" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "case_party",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "case_responsible",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "lawsuit_party",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "lawsuit_response",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "lawsuit_responsible",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "party_dto",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "case_data",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "lawsuit_data",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "case",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "folder",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "lawsuit",
                schema: "lawsuit");
        }
    }
}
