﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.Status;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Harvey.Presentation.Status;

internal sealed class GetLawsuitStatus : IEndpoint
{
    private readonly ILogger<GetLawsuitStatus> _logger;

    public GetLawsuitStatus(ILogger<GetLawsuitStatus> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/status/lawsuits", async (string? displayName, [FromServices] ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<GetLawsuitStatusResponse>> result = await sender.Send(new GetLawsuitStatusQuery(displayName));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lawsuit status");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}
