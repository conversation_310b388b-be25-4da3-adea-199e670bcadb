﻿//<auto-generated/>

using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class ChangeLawsuitStepPropertyFromLawsuitIdToCnj : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_step_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_step");

            migrationBuilder.AlterColumn<Guid>(
                name: "lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_step",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<string>(
                name: "cnj",
                schema: "lawsuit",
                table: "lawsuit_step",
                type: "text",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_step_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_step",
                column: "lawsuit_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_lawsuit_step_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_step");

            migrationBuilder.DropColumn(
                name: "cnj",
                schema: "lawsuit",
                table: "lawsuit_step");

            migrationBuilder.AlterColumn<Guid>(
                name: "lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_step",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "fk_lawsuit_step_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "lawsuit_step",
                column: "lawsuit_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
