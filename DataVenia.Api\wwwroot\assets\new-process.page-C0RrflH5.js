import{j as e,r as t,O as i,d as c,c as r,J as d}from"./index-DxHSBLqJ.js";import{u as n}from"./use-controlled-list-DZx5PfZt.js";const m=[{value:"first",label:"1a instância"},{value:"second",label:"2a instância"}],u=l=>{const a=n();return e.jsx(i,{title:"Envolvidos",children:e.jsx("ul",{children:a.items.map(o=>e.jsxs("li",{className:"flex gap-kilo",children:[e.jsx(r,{form:l.form,name:`clients.${o.index}.name`,required:!0,placeholder:"Nome do cliente",title:"Nome"}),e.jsx(r,{form:l.form,name:`clients.${o.index}.role`,required:!0,placeholder:"Qualificação",title:"Qualificação"})]},o.id))})})},s="new-process-form";function j(){return e.jsxs(t.Fragment,{children:[e.jsxs(i,{title:"Novo processo",children:[e.jsx(c.Form,{hidden:!0,name:s,id:s}),e.jsx("div",{className:"mb-6",children:e.jsx("p",{className:"text-sm text-secondary",children:"Para a criação de um novo processo, preencha as informações dos campos corretamente."})}),e.jsxs("div",{className:"mt-kilo grid grid-cols-1 gap-kilo lg:grid-cols-3",children:[e.jsx(r,{placeholder:"Processo do Usuário 10",title:"Nome do processo",required:!0}),e.jsx(d,{options:m,placeholder:"1º grau",title:"Instância",required:!0}),e.jsx(r,{mask:"int",placeholder:"Digite o número do processo...",title:"Número do processo",required:!0}),e.jsx(r,{mask:"int",placeholder:"9999...",title:"Juízo",required:!0}),e.jsx(r,{placeholder:"Vara criminal...",title:"Vara",required:!0}),e.jsx(r,{placeholder:"Foro do Rio de Janeiro...",title:"Foro",required:!0})]})]}),e.jsx(i,{title:"Clientes",children:e.jsx("ul",{children:e.jsxs("li",{className:"flex gap-kilo",children:[e.jsx(r,{required:!0,placeholder:"Nome do cliente",title:"Nome"}),e.jsx(r,{required:!0,placeholder:"Qualificação",title:"Qualificação"})]})})}),e.jsx(u,{form:s})]})}export{j as default};
