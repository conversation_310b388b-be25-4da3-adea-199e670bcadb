﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.LegalInstance;

public sealed class LegalInstance : Entity
{
    public readonly static LegalInstance FirstInstance = Create("FirstInstance", "Primeira Instância", 1);
    public readonly static LegalInstance SecondInstance = Create("SecondInstance", "Segunda Instância", 2);
    public readonly static LegalInstance STJ = Create("STJ", "Superior Tribunal de Justiça", 3);
    public readonly static LegalInstance STF = Create("STF", "Supremo Tribunal Federal", 4);
    public readonly static LegalInstance TST = Create("TST", "Tribunal Superior de Trabalho", 5);
    public readonly static LegalInstance TSE = Create("TSE", "Tribunal Superior Eleitoral", 6);
    public readonly static LegalInstance STM = Create("STM", "Superior Tribunal Militar", 7);
    public readonly static LegalInstance Other = Create("Other", "Outras", 8);
    private LegalInstance() { }

    public string Id { get; private set; }
    public string DisplayName { get; private set; }
    public int Order { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public static LegalInstance Create(string name, string displayName, int order)
    {
        return new LegalInstance
        {
            Id = name,
            DisplayName = displayName,
            Order = order
        };
    }
}
