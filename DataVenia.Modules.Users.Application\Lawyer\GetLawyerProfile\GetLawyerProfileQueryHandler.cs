﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Infrastructure.IntermediateClasses;
using LawyerDomain = DataVenia.Modules.Users.Domain.Lawyers.Lawyer;

namespace DataVenia.Modules.Users.Application.Lawyers.GetLawyer;
internal sealed class GetLawyerProfileQueryHandler(
    ILawyerRepository lawyerRepository) : IQueryHandler<GetLawyerProfileQuery, GetLawyerProfileResponse>
{
    public async Task<Result<GetLawyerProfileResponse>> Handle(GetLawyerProfileQuery request, CancellationToken cancellationToken)
    {
            LawyerDomain? lawyer = await lawyerRepository.GetSingleWithOfficesByFilterAsync(l => l.Id == request.LawyerId, cancellationToken);

            if (lawyer == null)
                return Result.Failure<GetLawyerProfileResponse>(LawyerErrors.NotFound(request.LawyerId));
            
            Result<List<OfficeUserDto>> adminsResult = await lawyerRepository.GetOfficesAdministratorsAsync(request.LawyerId, cancellationToken);

            if (adminsResult.IsFailure)
                return Result.Failure<GetLawyerProfileResponse>(adminsResult.Error);
            
            var lawyerResponse = new GetLawyerProfileResponse(
            lawyer.Id,
            lawyer.Email,
            lawyer.FirstName,
            lawyer.LastName,
            lawyer.Oab?.Value,
            lawyer.Cpf ?? "",
            lawyer.Rg ?? "",
            lawyer.Cnh ?? "",
            lawyer.Passport ?? "",
            lawyer.Ctps ?? "",
            lawyer.Pis ?? "",
            lawyer.VoterId ?? "",
            lawyer.Contacts?.ToList(),
            lawyer.OfficeUsers?.Select(ou => new OfficeResponse(
                ou.Office.Id,
                ou.Office.Name,
                ou.Office.Cnpj,
                ou.Office.Website
            ))?.ToList(),
            adminsResult.Value,
            lawyer.TermsAndConditions,
            lawyer.Preferences
    );

        return lawyerResponse;
    }
}
