﻿using DataVenia.Common.Domain;
using MediatR;

namespace DataVenia.Common.Application.Messaging;

public interface ICommandHandler<in TCommand> : IRequestHandler<TCommand, Result>
    where TCommand : ICommand;

public interface ICommandHandler<in TCommand, TResponse> : IRequestHandler<TCommand, Result<TResponse>>
    where TCommand : ICommand<TResponse>;


public interface ICommandHandlerFr<in TCommand> : IRequestHandler<TCommand, FluentResults.Result>
    where TCommand : ICommandFr;
public interface ICommandHandlerFr<in TCommand, TResponse> : IRequestHandler<TCommand, FluentResults.Result<TResponse>>
    where TCommand : ICommandFr<TResponse>;
