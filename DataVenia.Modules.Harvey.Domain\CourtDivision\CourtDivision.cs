﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.CourtDivision;
public sealed class CourtDivision : Entity
{
    public string Id { get; private set; }
    public string DisplayName { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private CourtDivision() { }

    public static Result<CourtDivision> Create(string name, string displayName)
    {
        return new CourtDivision() { DisplayName = displayName, Id = name, CreatedAt = DateTime.UtcNow };
    }
}
