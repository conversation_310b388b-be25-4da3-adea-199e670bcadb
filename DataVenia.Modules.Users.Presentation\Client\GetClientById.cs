﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.GetClientById;
using DataVenia.Modules.Users.Application.Client.GetClients;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Client;

public class GetClientById : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/clients/{clientId}", async (Guid officeId, Guid clientId, ClaimsPrincipal claims, [FromServices] ISender sender) =>
            {
                Result<GetClientsResponse?> result = await sender.Send(new GetClientByIdQuery(
                    clientId));

                return result.Match(client => Results.Ok(new {item = client}), ApiResults.Problem);
            })
            .RequireAuthorization("office:clients:read")
            .WithTags(Tags.Client);
    }
}
