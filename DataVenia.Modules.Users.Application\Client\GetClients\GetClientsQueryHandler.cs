﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Client;
using Microsoft.Extensions.Logging;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;

namespace DataVenia.Modules.Users.Application.Client.GetClients;
public sealed class GetClientsQueryHandler(IClientRepository clientRepository, ILogger<GetClientsQueryHandler> logger) : IQueryHandler<GetClientsQuery, IReadOnlyCollection<GetClientsResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetClientsResponse>>> Handle(GetClientsQuery request, CancellationToken cancellationToken)
    {
        // buscar todos filtrando por office
        try
        {
            IReadOnlyCollection<ClientDomain> clients = (await clientRepository.GetAllByOfficeIdAsync(request.officeId, cancellationToken)).ToList();
            
            var clientsResponse = clients.Select(client =>
            {
                return new GetClientsResponse(
                    Id: client.Id,
                    Email: client.Email,
                    Name: client.Name,
                    Cpf: client.Cpf,
                    Companies: client.ClientCompanies.Select(cc => cc.CompanyId).ToList()
                    );
            }).ToList();

            return clientsResponse;
        } catch(Exception ex)
        {
            logger.LogError(ex, "Error getting clients");
            return Result.Failure<IReadOnlyCollection<GetClientsResponse>>(Error.InternalServerError());
        }

    }
}
