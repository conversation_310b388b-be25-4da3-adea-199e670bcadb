PATCH /api/offices/{{OFFICE_UUID}}/appointments HTTP/1.1
Host: {{DATAVENIA_HOST}}
Content-Type: application/json
Cookie: AccessToken={{ACCESS_TOKEN}}

{
  "Id": "fcbf9aa7-3078-4aa8-8edb-a3549ad28ccc",
  "type": "Consultation",
  "name": "Client Meeting",
  "description": "Meeting to discuss case details",
  "from": "09:00:00",
  "to": "10:30:00",
  "responsibleLawyerId": "123e4567-e89b-12d3-a456-************",
  "recurrence": {
    "frequency": "Weekly",
    "endDate": "2024-12-31T23:59:59",
    "daysOfWeek": [
      "Monday",
      "Wednesday"
    ],
    "daysOfMonth": []
  },
  "ownerLawyerId": "123e4567-e89b-12d3-a456-************",
  "participantLawyersIds": [
    "123e4567-e89b-12d3-a456-************",
    "123e4567-e89b-12d3-a456-************"
  ],
  "alerts": [
    "00:15:00",
    "01:00:00"
  ]
}
