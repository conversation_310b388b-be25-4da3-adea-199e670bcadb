﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Harvey.Domain.CourtDivision;
using CourtDivisionDomain = DataVenia.Modules.Harvey.Domain.CourtDivision.CourtDivision;
namespace DataVenia.Modules.Harvey.Application.CourtDivision;
internal sealed class GetCourtDivisionsQueryHandler(
    ICourtDivisionRepository courtDivisionRepository) : IQueryHandler<GetCourtDivisionsQuery, IReadOnlyCollection<GetCourtDivisionsResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetCourtDivisionsResponse>>> <PERSON>le(GetCourtDivisionsQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<CourtDivisionDomain> courtDivisions = await courtDivisionRepository.GetAllAsync(request.displayName, cancellationToken);

        var courtDivisionsResponse = courtDivisions.Select(courtDivision => new GetCourtDivisionsResponse(
            courtDivision.Id,
            courtDivision.DisplayName)).ToList();

        return courtDivisionsResponse;
    }
}
