﻿using DataVenia.Modules.Harvey.Domain.CourtDivision;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using CourtDivisionDomain = DataVenia.Modules.Harvey.Domain.CourtDivision.CourtDivision;

namespace DataVenia.Modules.Harvey.Infrastructure.CourtDivision;
public sealed class CourtDivisionRepository(HarveyDbContext context) : ICourtDivisionRepository
{
    public async Task<IReadOnlyCollection<CourtDivisionDomain>> GetAllAsync(string? displayName, CancellationToken cancellationToken = default)
    {
        IQueryable<CourtDivisionDomain> query = context.CourtDivisions;

        if (!string.IsNullOrWhiteSpace(displayName))
            query = query.Where(cd => cd.DisplayName.Contains(displayName));

        return await query.ToListAsync(cancellationToken);
    }
}
