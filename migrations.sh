#!/bin/bash

export PATH="/home/<USER>/.dotnet/tools:$PATH"

if [ -z "$1" ]; then
  echo "Error: No connection string provided."
  echo "Usage: $0 <argument>"
  exit 1
fi

CONN_STR=$1
echo "The first argument is: $CONN_STR"

echo "Starting migrations..."

CONFIG_FILE="migrations.json"
STARTUP_PROJECT="./DataVenia.Api/DataVenia.Api.csproj"

# Loop through each project and run the dotnet ef database update command
jq -c '.projects[]' "$CONFIG_FILE" | while read -r project; do
    PROJECT_PATH=$(echo "$project" | jq -r '.path')
    DBCONTEXT_NAME=$(echo "$project" | jq -r '.dbcontext')

    # Run the command for the current project
    echo "Running dotnet ef database update for project: $PROJECT_PATH with DbContext: $DBCONTEXT_NAME"
    dotnet ef database update \
        --project "$PROJECT_PATH" \
        --startup-project "$STARTUP_PROJECT" \
        --connection "$CONN_STR" \
        --context "$DBCONTEXT_NAME"

    if [[ $? -ne 0 ]]; then
        echo "Error occurred while updating the database for $PROJECT_PATH. Exiting..."
        exit 1
    fi
done

echo "Database updates completed for all projects."
