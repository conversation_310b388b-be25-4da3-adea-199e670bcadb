import{y as l,d as a,r as o,j as s,O as m,a as u,u as d,m as p,A as f,l as x,o as j,n as h}from"./index-DxHSBLqJ.js";import{C as y}from"./card-header-title-MEld3mYM.js";import{f as C,a as v}from"./brazilian-values-a8H3KUqb.js";import{U as E}from"./user-round-plus-79Wfoby6.js";import{E as w}from"./external-link-TCLM46lm.js";var r;(t=>{t.create=e=>({...e,id:e.id||"",name:e.name||"",document:e.document||"",followUp:e.followUp||null,contacts:e.contacts||[]})})(r||(r={}));const F=Array.from({length:50}).map((t,e)=>({id:l(),name:`Cliente ${e}`,type:e%2===0?"pj":"pf",document:e%2===0?"00000000000":"00000000000000",followUp:null,contacts:[{id:l(),type:"cellphone",value:"00900001234"}]})).sort((t,e)=>t.id.localeCompare(e.id)),i={cellphone:v,email:h.unary},N=p(t=>{t.add("name","Nome",{Element:e=>s.jsxs(a.Link,{href:x.clientById,paths:{id:e.value},className:"link",children:[s.jsx(w,{size:14}),e.value]})}),t.add("document","Documento",{Element:e=>C(e.value)}),t.add("contacts","Contato",{Element:e=>{const n=e.value[0];return j.keyof(i,n.type)?s.jsx(o.Fragment,{children:i[n.type](n.value)}):"--"}})}),A=async t=>(await f(),a.jsonResponse({items:F}));function L(){const t=a.useDataLoader(),[e,n]=o.useState([]);return s.jsx(o.Fragment,{children:s.jsx(m,{title:s.jsx(y,{title:"Meus clientes",side:s.jsxs(u,{size:"small",children:[s.jsx(E,{size:14}),"Novo cliente"]})}),children:s.jsx(d,{useControl:!0,filters:e,setFilters:c=>n(c),cols:N,operations:!1,name:"all-clients",loading:!(t!=null&&t.items),rows:(t==null?void 0:t.items)??[]})})})}export{L as default,A as loader};
