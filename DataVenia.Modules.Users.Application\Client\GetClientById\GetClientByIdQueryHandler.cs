﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Client.GetClients;
using DataVenia.Modules.Users.Domain.Client;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Client.GetClientById;

public sealed class GetClientByIdQueryHandler(
    IClientRepository clientRepository,
    ILogger<GetClientByIdQueryHandler> logger)
    : IQueryHandler<GetClientByIdQuery, GetClientsResponse?>
{
    public async Task<Result<GetClientsResponse?>> Handle(GetClientByIdQuery request,
        CancellationToken cancellationToken)
    {
        Domain.Client.Client? client = null;

        try
        {
            // pega o appointment pelo Id, validando escritório e se o lawyer que requisitou é vinculado ao appointment
            client = await clientRepository.GetSingleAsync(x => x.Id == request.ClientId, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "an error occurred while retrieving the client with id {ClientId}.",
                request.ClientId);
        }

        if (client == null)
            return Result.Failure<GetClientsResponse?>(new Error("Not.Found", "The client was not found.",
                ErrorType.NotFound));

        return new GetClientsResponse(
            client.Id,
            client.Email,
            client.Name,
            client.Cpf,
            Companies: client.ClientCompanies.Select(cc => cc.CompanyId).ToList());
    }
}
