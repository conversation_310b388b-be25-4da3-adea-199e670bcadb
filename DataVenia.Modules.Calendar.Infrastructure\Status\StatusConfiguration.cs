﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using StatusDomain = DataVenia.Modules.Calendar.Domain.Status.Status;
namespace DataVenia.Modules.Calendar.Infrastructure.Status;
internal sealed class StatusConfiguration : IEntityTypeConfiguration<StatusDomain>
{
    public void Configure(EntityTypeBuilder<StatusDomain> builder)
    {
        builder.ToTable("status");

        builder.Property(p => p.Id).HasColumnType("uuid");
        builder.HasKey(s => s.Id);

        builder.Property(s => s.DisplayName)
            .IsRequired()
            .HasMaxLength(64);

        builder.Property(s => s.OfficeId)
            .IsRequired();

        builder.Property(s => s.CreatedAt)
            .IsRequired();

        builder.Property(s => s.DeletedAt)
            .IsRequired(false);

        builder.Property(s => s.Description)
            .HasMaxLength(256);
    }
}
