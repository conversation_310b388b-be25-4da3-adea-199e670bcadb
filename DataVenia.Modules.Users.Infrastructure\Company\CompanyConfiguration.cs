﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
namespace DataVenia.Modules.Users.Infrastructure.Company;

internal sealed class CompanyConfiguration : IEntityTypeConfiguration<Domain.Company.Company>
{
    public void Configure(EntityTypeBuilder<Domain.Company.Company> builder)
    {
     builder.ToTable("company");
     
     builder.HasQueryFilter(u => u.DeletedAt == null);

     builder.Property(x => x.Id).HasColumnType("uuid");
     builder.HasKey(c => c.Id);

     builder.HasOne(c => c.Office)
         .WithMany()
         .HasForeignKey(c => c.OfficeId);
     
     // configure one-to-many relationship between company and clientCompany
     builder.HasMany(c => c.ClientCompanies)
         .WithOne()
         .HasForeignKey(c => c.CompanyId);
        
     builder.Property(c => c.Cnpj)
         .HasMaxLength(18)
         .IsRequired();

     builder.Property(c => c.Name)
         .HasMaxLength(200)
         .IsRequired();

     builder.Property(c => c.OfficeId)
         .IsRequired();

     builder.Property(c => c.CreatedBy)
         .IsRequired();
        
        
        // // Relacionamento com Contacts
        // builder.HasMany(c => c.Contacts)
        //     .WithOne()
        //     .HasForeignKey("ClientId") // FK na tabela Contacts
        //     .OnDelete(DeleteBehavior.NoAction);
        //
        // // Relacionamento com Addresses
        // builder.HasMany(c => c.Addresses)
        //     .WithOne()
        //     .HasForeignKey("ClientId") // FK na tabela Addresses
        //     .OnDelete(DeleteBehavior.NoAction);
    }
}
