﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.CasesData;

namespace DataVenia.Modules.Lawsuits.Domain.CasesResponsibles;

public class CaseResponsible : Entity
{
    public Guid CaseDataId { get; set; }
    public CaseData CaseData { get; set; }

    // responsible
    public Guid LawyerId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public static CaseResponsible Create(Guid caseId, Guid lawyerId)
    {
        var caseResponsible = new CaseResponsible()
        {
            CaseDataId = caseId,
            LawyerId = lawyerId,
            CreatedAt = DateTime.UtcNow
        };

        return caseResponsible;
    }
}
