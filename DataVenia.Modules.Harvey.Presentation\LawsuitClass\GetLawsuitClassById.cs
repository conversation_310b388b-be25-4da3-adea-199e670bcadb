﻿using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.LawsuitClass;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Harvey.Presentation.LawsuitClass;

public sealed class GetLawsuitClassById(ILogger<GetLawsuitClassById> _logger) : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/lawsuit-classes/{id}", async (int id, [FromServices] ISender sender) =>
        {
            try
            {
                Result<GetLawsuitClassByIdResponse> result = await sender.Send(new GetLawsuitClassByIdQuery(id));

                return result.Match(
                    success => Results.Ok(success),
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lawsuit class by id {Id}", id);
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}
