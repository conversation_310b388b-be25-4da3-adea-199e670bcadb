using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.LawsuitSync.Presentation.Endpoints;

public class CreateLawsuitMonitoringConfiguration : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/lawsuit-sync/monitoring/lawsuit/{cnj}/{subscriberId}",
            async (string cnj, string subscriberId, [FromServices] IStartLawsuitMonitoringAppService appService, [FromServices] ILogger<CreateLawsuitMonitoringConfiguration> logger) =>
            {
                try
                {
                    if (!Guid.TryParse(subscriberId, out var subscriberIdGuid))
                        return Results.BadRequest("Invalid subscriberId");

                    var result = await appService.ExecuteAsync(cnj, subscriberIdGuid).ConfigureAwait(false);

                    return result.IsSuccess ? Results.Ok() : Results.BadRequest(result.Errors);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to start the lawsuit-monitoring for cnj: {Cnj}", cnj);
                    return Results.Problem();
                }
            })
            .RequireAuthorization("office:lawsuits:create")
            .WithTags(Tags.LawsuitsMonitoring);
    }
}
