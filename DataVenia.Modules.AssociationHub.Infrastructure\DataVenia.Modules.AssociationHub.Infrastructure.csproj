﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\DataVenia.Common.Infrastructure\DataVenia.Common.Infrastructure.csproj" />
        <ProjectReference Include="..\DataVenia.Common.Presentation\DataVenia.Common.Presentation.csproj" />
        <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Application\DataVenia.Modules.AssociationHub.Application.csproj" />
        <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Domain\DataVenia.Modules.AssociationHub.Domain.csproj" />
        <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Presentation\DataVenia.Modules.AssociationHub.Presentation.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
        <PackageReference Include="MassTransit" Version="8.3.4" />
        <PackageReference Include="FluentResults" Version="3.16.0" />
    </ItemGroup>

</Project>
