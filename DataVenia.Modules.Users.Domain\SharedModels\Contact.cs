﻿using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
using DataVenia.Modules.Users.Domain.Lawyers;
using OfficeDomain = DataVenia.Modules.Users.Domain.Office.Office;

namespace DataVenia.Modules.Users.Domain.SharedModels;
public sealed class Contact
{
    public Guid Id { get; set; }
    public string Type { get; set; }
    public string Value { get; set; }

    public Guid? LawyerId { get; set; }
    public Lawyers.Lawyer Lawyer { get; set; }

    public Guid? OfficeId {get; set;}
    public OfficeDomain Office { get; set;}

    public Guid? ClientId { get; set; }
    public ClientDomain Client { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}
