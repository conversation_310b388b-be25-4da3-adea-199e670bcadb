﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Appointments.DeleteAppointment;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Calendar.Presentation.Appointment;
public sealed class DeleteAppointment : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapDelete("/offices/{officeId}/appointments/{appointmentId}", async (Guid officeId, Guid appointmentId, ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            Result result = await sender.Send(new DeleteAppointmentCommand(
                claims.GetUserId(),
                officeId,
                appointmentId
                ));

            return result.Match(Results.NoContent, ApiResults.Problem);
        })
        .RequireAuthorization("office:appointments:write")
        .WithTags(Tags.Calendar);
    }
}
