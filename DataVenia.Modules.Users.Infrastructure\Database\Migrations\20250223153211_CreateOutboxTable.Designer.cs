﻿// <auto-generated />
using System;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.Users.Infrastructure.Database.Migrations
{
    [DbContext(typeof(UsersDbContext))]
    [Migration("20250223153211_CreateOutboxTable")]
    partial class CreateOutboxTable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("user")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Authorization.Permission", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("code");

                    b.HasKey("Code")
                        .HasName("pk_permission");

                    b.ToTable("permission", "user");

                    b.HasData(
                        new
                        {
                            Code = "system:users:read"
                        },
                        new
                        {
                            Code = "system:users:update"
                        },
                        new
                        {
                            Code = "office:users:write"
                        },
                        new
                        {
                            Code = "office:users:read"
                        },
                        new
                        {
                            Code = "office:status:read"
                        },
                        new
                        {
                            Code = "office:clients:create"
                        },
                        new
                        {
                            Code = "office:clients:delete"
                        },
                        new
                        {
                            Code = "office:clients:update"
                        },
                        new
                        {
                            Code = "office:clients:read"
                        },
                        new
                        {
                            Code = "office:lawsuits:read"
                        },
                        new
                        {
                            Code = "office:lawsuits:update"
                        },
                        new
                        {
                            Code = "office:lawsuits:create"
                        },
                        new
                        {
                            Code = "office:appointments:read"
                        },
                        new
                        {
                            Code = "office:appointments:write"
                        },
                        new
                        {
                            Code = "office:appointments:update"
                        },
                        new
                        {
                            Code = "office:invites:create"
                        },
                        new
                        {
                            Code = "system:invites:read"
                        },
                        new
                        {
                            Code = "system:invites:update"
                        },
                        new
                        {
                            Code = "system:harvey:read"
                        },
                        new
                        {
                            Code = "system:general:all"
                        },
                        new
                        {
                            Code = "system:administrator:all"
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Authorization.Role", b =>
                {
                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<bool>("IsGlobal")
                        .HasColumnType("boolean")
                        .HasColumnName("is_global");

                    b.HasKey("Name")
                        .HasName("pk_role");

                    b.ToTable("role", "user");

                    b.HasData(
                        new
                        {
                            Name = "OfficeMember",
                            IsGlobal = false
                        },
                        new
                        {
                            Name = "OfficeAdministrator",
                            IsGlobal = false
                        },
                        new
                        {
                            Name = "OfficeClient",
                            IsGlobal = false
                        },
                        new
                        {
                            Name = "SystemAdministrator",
                            IsGlobal = true
                        },
                        new
                        {
                            Name = "SystemMember",
                            IsGlobal = true
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Authorization.UserGlobalRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("role_name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_global_role");

                    b.HasIndex("RoleName")
                        .HasDatabaseName("ix_user_global_role_role_name");

                    b.HasIndex("UserId", "RoleName")
                        .IsUnique()
                        .HasDatabaseName("IX_UserGlobalRole_UserId_RoleName");

                    b.ToTable("user_global_role", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Client.Client", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Cpf")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)")
                        .HasColumnName("cpf");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Email")
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<Guid>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_client");

                    b.HasIndex("OfficeId")
                        .HasDatabaseName("ix_client_office_id");

                    b.ToTable("client", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.ClientCompany.ClientCompany", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("role");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_client_company");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_client_company_client_id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_client_company_company_id");

                    b.ToTable("client_company", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Company.Company", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasMaxLength(18)
                        .HasColumnType("character varying(18)")
                        .HasColumnName("cnpj");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<Guid>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_company");

                    b.HasIndex("OfficeId")
                        .HasDatabaseName("ix_company_office_id");

                    b.ToTable("company", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer.OfficeUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("InvitationStatus")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("invitation_status");

                    b.Property<Guid>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid")
                        .HasColumnName("owner_id");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("role_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_office_user");

                    b.HasIndex("OwnerId")
                        .HasDatabaseName("ix_office_user_owner_id");

                    b.HasIndex("RoleName")
                        .HasDatabaseName("ix_office_user_role_name");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_office_user_user_id");

                    b.HasIndex("OfficeId", "UserId")
                        .IsUnique()
                        .HasDatabaseName("ix_office_user_office_id_user_id");

                    b.ToTable("office_user", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Oab.Oab", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid>("LawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawyer_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("value");

                    b.HasKey("Id")
                        .HasName("pk_oab");

                    b.HasIndex("LawyerId")
                        .IsUnique()
                        .HasDatabaseName("ix_oab_lawyer_id");

                    b.HasIndex("Value")
                        .IsUnique()
                        .HasDatabaseName("ix_oab_value");

                    b.ToTable("oab", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Office.Office", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cnpj");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Website")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("website");

                    b.HasKey("Id")
                        .HasName("pk_office");

                    b.ToTable("office", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Outbox.Outbox", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("message_type");

                    b.Property<string>("Payload")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("payload");

                    b.Property<bool>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.HasKey("Id")
                        .HasName("pk_outbox");

                    b.ToTable("outbox", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.SharedModels.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("city");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<string>("Complement")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("complement");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Neighborhood")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("neighborhood");

                    b.Property<Guid?>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("postal_code");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("state");

                    b.Property<string>("Street")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("street");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_address");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_address_client_id");

                    b.HasIndex("OfficeId")
                        .HasDatabaseName("ix_address_office_id");

                    b.ToTable("address", "user", t =>
                        {
                            t.HasCheckConstraint("CK_Address_OneOwner", "(\"office_id\" IS NOT NULL)");
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.SharedModels.Contact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("LawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawyer_id");

                    b.Property<Guid?>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.HasKey("Id")
                        .HasName("pk_contact");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_contact_client_id");

                    b.HasIndex("LawyerId")
                        .HasDatabaseName("ix_contact_lawyer_id");

                    b.HasIndex("OfficeId")
                        .HasDatabaseName("ix_contact_office_id");

                    b.ToTable("contact", "user", t =>
                        {
                            t.HasCheckConstraint("CK_Contact_OneOwner", "(\"lawyer_id\" IS NOT NULL AND \"office_id\" IS NULL) OR (\"lawyer_id\" IS NULL AND \"office_id\" IS NOT NULL)");
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.SignUpToken.SignUpToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<Guid>("LawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawyer_id");

                    b.Property<Guid>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.Property<Guid>("Token")
                        .HasColumnType("uuid")
                        .HasColumnName("token");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_sign_up_token");

                    b.HasIndex("LawyerId")
                        .HasDatabaseName("ix_sign_up_token_lawyer_id");

                    b.ToTable("sign_up_token", "user");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Users.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Cnh")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)")
                        .HasColumnName("cnh");

                    b.Property<string>("Cpf")
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)")
                        .HasColumnName("cpf");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Ctps")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("ctps");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("email");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("first_name");

                    b.Property<Guid?>("IdentityId")
                        .HasColumnType("uuid")
                        .HasColumnName("identity_id");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("last_name");

                    b.Property<string>("Passport")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("passport");

                    b.Property<string>("Pis")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("pis");

                    b.Property<string>("Preferences")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("preferences");

                    b.Property<string>("Rg")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)")
                        .HasColumnName("rg");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("user_type");

                    b.Property<string>("VoterId")
                        .HasMaxLength(12)
                        .HasColumnType("character varying(12)")
                        .HasColumnName("voter_id");

                    b.HasKey("Id")
                        .HasName("pk_user");

                    b.HasIndex("Cpf")
                        .HasDatabaseName("ix_user_cpf");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("ix_user_email");

                    b.HasIndex("IdentityId")
                        .IsUnique()
                        .HasDatabaseName("ix_user_identity_id")
                        .HasFilter("identity_id IS NOT NULL");

                    b.ToTable("user", "user");

                    b.HasDiscriminator<string>("UserType").HasValue("User");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("PermissionRole", b =>
                {
                    b.Property<string>("PermissionCode")
                        .HasColumnType("character varying(100)")
                        .HasColumnName("permission_code");

                    b.Property<string>("RoleName")
                        .HasColumnType("character varying(50)")
                        .HasColumnName("role_name");

                    b.HasKey("PermissionCode", "RoleName")
                        .HasName("pk_role_permission");

                    b.HasIndex("RoleName")
                        .HasDatabaseName("ix_role_permission_role_name");

                    b.ToTable("role_permission", "user");

                    b.HasData(
                        new
                        {
                            PermissionCode = "office:lawsuits:create",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:lawsuits:read",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:lawsuits:update",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:appointments:read",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:appointments:write",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:appointments:update",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:clients:create",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:clients:read",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:clients:update",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:clients:delete",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:status:read",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:users:read",
                            RoleName = "OfficeMember"
                        },
                        new
                        {
                            PermissionCode = "office:lawsuits:read",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:lawsuits:update",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:lawsuits:create",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:appointments:read",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:appointments:write",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:appointments:update",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:clients:create",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:clients:read",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:clients:update",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:clients:delete",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:users:read",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:status:read",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:invites:create",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:users:write",
                            RoleName = "OfficeAdministrator"
                        },
                        new
                        {
                            PermissionCode = "system:users:read",
                            RoleName = "SystemMember"
                        },
                        new
                        {
                            PermissionCode = "system:users:update",
                            RoleName = "SystemMember"
                        },
                        new
                        {
                            PermissionCode = "system:invites:read",
                            RoleName = "SystemMember"
                        },
                        new
                        {
                            PermissionCode = "system:invites:update",
                            RoleName = "SystemMember"
                        },
                        new
                        {
                            PermissionCode = "system:harvey:read",
                            RoleName = "SystemMember"
                        },
                        new
                        {
                            PermissionCode = "system:general:all",
                            RoleName = "SystemMember"
                        },
                        new
                        {
                            PermissionCode = "system:administrator:all",
                            RoleName = "SystemAdministrator"
                        },
                        new
                        {
                            PermissionCode = "office:users:write",
                            RoleName = "SystemAdministrator"
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Lawyers.Lawyer", b =>
                {
                    b.HasBaseType("DataVenia.Modules.Users.Domain.Users.User");

                    b.HasDiscriminator().HasValue("Lawyer");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Authorization.UserGlobalRole", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Authorization.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleName")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_global_role_role_role_name");

                    b.HasOne("DataVenia.Modules.Users.Domain.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_global_role_user_user_id");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Client.Client", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Office.Office", "Office")
                        .WithMany()
                        .HasForeignKey("OfficeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_client_office_office_id");

                    b.Navigation("Office");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.ClientCompany.ClientCompany", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Client.Client", "Client")
                        .WithMany("ClientCompanies")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_client_company_client_client_id");

                    b.HasOne("DataVenia.Modules.Users.Domain.Company.Company", "Company")
                        .WithMany("ClientCompanies")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_client_company_company_company_id");

                    b.Navigation("Client");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Company.Company", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Office.Office", "Office")
                        .WithMany()
                        .HasForeignKey("OfficeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_company_office_office_id");

                    b.Navigation("Office");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer.OfficeUser", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Office.Office", "Office")
                        .WithMany("OfficeUsers")
                        .HasForeignKey("OfficeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_office_user_office_office_id");

                    b.HasOne("DataVenia.Modules.Users.Domain.Lawyers.Lawyer", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_office_user_lawyers_owner_id");

                    b.HasOne("DataVenia.Modules.Users.Domain.Authorization.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleName")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_office_user_role_role_name");

                    b.HasOne("DataVenia.Modules.Users.Domain.Users.User", "User")
                        .WithMany("OfficeUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_office_user_user_user_id");

                    b.Navigation("Office");

                    b.Navigation("Owner");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Oab.Oab", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Lawyers.Lawyer", "Lawyer")
                        .WithOne("Oab")
                        .HasForeignKey("DataVenia.Modules.Users.Domain.Oab.Oab", "LawyerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_oab_lawyers_lawyer_id");

                    b.Navigation("Lawyer");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.SharedModels.Address", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Client.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .HasConstraintName("fk_address_clients_client_id");

                    b.HasOne("DataVenia.Modules.Users.Domain.Office.Office", "Office")
                        .WithMany("Addresses")
                        .HasForeignKey("OfficeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_address_offices_office_id");

                    b.Navigation("Client");

                    b.Navigation("Office");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.SharedModels.Contact", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Client.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .HasConstraintName("fk_contact_clients_client_id");

                    b.HasOne("DataVenia.Modules.Users.Domain.Lawyers.Lawyer", "Lawyer")
                        .WithMany("Contacts")
                        .HasForeignKey("LawyerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_contact_lawyers_lawyer_id");

                    b.HasOne("DataVenia.Modules.Users.Domain.Office.Office", "Office")
                        .WithMany("Contacts")
                        .HasForeignKey("OfficeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_contact_offices_office_id");

                    b.Navigation("Client");

                    b.Navigation("Lawyer");

                    b.Navigation("Office");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.SignUpToken.SignUpToken", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Lawyers.Lawyer", "Lawyer")
                        .WithMany("SignUpToken")
                        .HasForeignKey("LawyerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sign_up_token_lawyers_lawyer_id");

                    b.Navigation("Lawyer");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Users.User", b =>
                {
                    b.OwnsOne("DataVenia.Modules.Users.Domain.TermsAndConditions", "TermsAndConditions", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("uuid");

                            b1.Property<DateTime?>("AcceptedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<string>("Url")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.Property<string>("Version")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("UserId");

                            b1.ToTable("user", "user");

                            b1.ToJson("terms_and_conditions");

                            b1.WithOwner()
                                .HasForeignKey("UserId")
                                .HasConstraintName("fk_user_user_id");
                        });

                    b.Navigation("TermsAndConditions")
                        .IsRequired();
                });

            modelBuilder.Entity("PermissionRole", b =>
                {
                    b.HasOne("DataVenia.Modules.Users.Domain.Authorization.Permission", null)
                        .WithMany()
                        .HasForeignKey("PermissionCode")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_role_permission_permission_permission_code");

                    b.HasOne("DataVenia.Modules.Users.Domain.Authorization.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleName")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_role_permission_role_role_name");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Client.Client", b =>
                {
                    b.Navigation("ClientCompanies");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Company.Company", b =>
                {
                    b.Navigation("ClientCompanies");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Office.Office", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("Contacts");

                    b.Navigation("OfficeUsers");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Users.User", b =>
                {
                    b.Navigation("OfficeUsers");
                });

            modelBuilder.Entity("DataVenia.Modules.Users.Domain.Lawyers.Lawyer", b =>
                {
                    b.Navigation("Contacts");

                    b.Navigation("Oab");

                    b.Navigation("SignUpToken");
                });
#pragma warning restore 612, 618
        }
    }
}
