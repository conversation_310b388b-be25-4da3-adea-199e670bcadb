﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddCreatedAtToEverything : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "lawsuit",
                table: "lawsuit_responsible",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "lawsuit",
                table: "lawsuit_responsible",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "lawsuit",
                table: "lawsuit_party",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "lawsuit",
                table: "lawsuit_party",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "lawsuit",
                table: "folder",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "lawsuit",
                table: "folder",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "lawsuit",
                table: "case_responsible",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "lawsuit",
                table: "case_responsible",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "created_at",
                schema: "lawsuit",
                table: "case_party",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "updated_at",
                schema: "lawsuit",
                table: "case_party",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "lawsuit",
                table: "lawsuit_responsible");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "lawsuit",
                table: "lawsuit_responsible");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "lawsuit",
                table: "lawsuit_party");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "lawsuit",
                table: "lawsuit_party");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "lawsuit",
                table: "folder");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "lawsuit",
                table: "folder");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "lawsuit",
                table: "case_responsible");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "lawsuit",
                table: "case_responsible");

            migrationBuilder.DropColumn(
                name: "created_at",
                schema: "lawsuit",
                table: "case_party");

            migrationBuilder.DropColumn(
                name: "updated_at",
                schema: "lawsuit",
                table: "case_party");
        }
    }
}
