using FluentResults;
using Microsoft.AspNetCore.Http;
using System.Globalization;

namespace DataVenia.Common.Presentation.ApiResults;

public static class FluentResultsExtensions
{
    /// <summary>
    /// Converts a FluentResults Result to an appropriate HTTP response
    /// </summary>
    /// <param name="result">The FluentResults Result to convert</param>
    /// <returns>IResult representing the appropriate HTTP response</returns>
    public static IResult ToHttpResult(this Result result)
    {
        if (result.IsSuccess)
            return Results.NoContent();

        var error = result.Errors.FirstOrDefault();
        int statusCode = error?.Metadata != null && error.Metadata.TryGetValue("StatusCode", out var codeObj)
            ? Convert.ToInt32(codeObj, CultureInfo.InvariantCulture)
            : 500;

        return Results.Problem(
            detail: string.Join("; ", result.Errors.Select(e => e.Message)), 
            statusCode: statusCode);
    }

    /// <summary>
    /// Converts a FluentResults Result&lt;T&gt; to an appropriate HTTP response
    /// </summary>
    /// <typeparam name="T">The type of the result value</typeparam>
    /// <param name="result">The FluentResults Result&lt;T&gt; to convert</param>
    /// <returns>IResult representing the appropriate HTTP response</returns>
    public static IResult ToHttpResult<T>(this Result<T> result)
    {
        if (result.IsSuccess)
            return Results.Ok(result.Value);

        var error = result.Errors.FirstOrDefault();
        int statusCode = error?.Metadata != null && error.Metadata.TryGetValue("StatusCode", out var codeObj)
            ? Convert.ToInt32(codeObj, CultureInfo.InvariantCulture)
            : 500;

        return Results.Problem(
            detail: string.Join("; ", result.Errors.Select(e => e.Message)), 
            statusCode: statusCode);
    }

    /// <summary>
    /// Converts a FluentResults Result&lt;T&gt; to an appropriate HTTP response with a custom success response
    /// </summary>
    /// <typeparam name="T">The type of the result value</typeparam>
    /// <param name="result">The FluentResults Result&lt;T&gt; to convert</param>
    /// <param name="successResponse">Function to create the success response</param>
    /// <returns>IResult representing the appropriate HTTP response</returns>
    public static IResult ToHttpResult<T>(this Result<T> result, Func<T, IResult> successResponse)
    {
        if (result.IsSuccess)
            return successResponse(result.Value);

        var error = result.Errors.FirstOrDefault();
        int statusCode = error?.Metadata != null && error.Metadata.TryGetValue("StatusCode", out var codeObj)
            ? Convert.ToInt32(codeObj, CultureInfo.InvariantCulture)
            : 500;

        return Results.Problem(
            detail: string.Join("; ", result.Errors.Select(e => e.Message)), 
            statusCode: statusCode);
    }

    /// <summary>
    /// Converts a FluentResults Result to an appropriate HTTP response with Created status
    /// </summary>
    /// <param name="result">The FluentResults Result to convert</param>
    /// <param name="location">The location of the created resource</param>
    /// <param name="value">The created resource value</param>
    /// <returns>IResult representing the appropriate HTTP response</returns>
    public static IResult ToCreatedResult<T>(this Result<T> result, string location, object? value = null)
    {
        if (result.IsSuccess)
            return Results.Created(location, value ?? result.Value);

        var error = result.Errors.FirstOrDefault();
        int statusCode = error?.Metadata != null && error.Metadata.TryGetValue("StatusCode", out var codeObj)
            ? Convert.ToInt32(codeObj, CultureInfo.InvariantCulture)
            : 500;

        return Results.Problem(
            detail: string.Join("; ", result.Errors.Select(e => e.Message)), 
            statusCode: statusCode);
    }
}
