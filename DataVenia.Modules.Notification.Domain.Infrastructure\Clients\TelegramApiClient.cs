using System.Text;
using System.Text.Json;
using DataVenia.Modules.Notification.Domain.Models.Telegram;

namespace DataVenia.Modules.Notification.Domain.Infrastructure.Clients;

public sealed class TelegramApiClient(HttpClient httpClient)
{
    public async Task<TelegramResponse?> SendMessageAsync(string token, TelegramSendMessageRequest request)
    {
        string url = $"/bot{token}/sendMessage";

        using var requestMessage = new HttpRequestMessage();
        requestMessage.Method = HttpMethod.Post;
        requestMessage.RequestUri = new Uri(url, UriKind.Relative);
        requestMessage.Content = new StringContent(
            JsonSerializer.Serialize(request),
            Encoding.UTF8,
            "application/json"
        );

        HttpResponseMessage response = await httpClient.SendAsync(requestMessage);

        response.EnsureSuccessStatusCode();

        string responseContent = await response.Content.ReadAsStringAsync();

        return JsonSerializer.Deserialize<TelegramResponse>(responseContent);
    }
}
