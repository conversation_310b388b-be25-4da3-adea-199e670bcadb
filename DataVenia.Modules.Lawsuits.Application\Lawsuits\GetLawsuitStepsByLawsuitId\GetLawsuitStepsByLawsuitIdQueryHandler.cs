﻿using DataVenia.Common.Application.Messaging;
using FluentResults;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitStepsByCnj;

internal sealed class GetLawsuitStepsByLawsuitIdQueryHandler(
    ILawsuitStepRepository lawsuitStepsRepository,
    ILawsuitRepository lawsuitRepository)
    : IQueryHandlerFr<GetLawsuitStepsByLawsuitIdQuery, IReadOnlyCollection<LawsuitStepsResponse>>
{
    public async Task<Result<IReadOnlyCollection<LawsuitStepsResponse>>> Handle(GetLawsuitStepsByLawsuitIdQuery request, CancellationToken cancellationToken)
    { 
        var lawsuitFound = await lawsuitRepository.GetLawsuitByIdAsync(request.LawsuitId, cancellationToken);
        
        if(lawsuitFound == null)
            return Result.Fail(new Error("Lawsuit.Not.Found").WithMetadata("StatusCode", 404));
        
        var lastMonitoringEntry = lawsuitFound.MonitoringHistory.LastOrDefault();
        DateTime? stoppedAt = lastMonitoringEntry?.StoppedAt;
        
        var lawsuitSteps = await lawsuitStepsRepository.GetLawsuitStepsByCnjAsync(lawsuitFound.Cnj, request.Instance, stoppedAt, cancellationToken: cancellationToken);
        
        return lawsuitSteps;
    }
}
