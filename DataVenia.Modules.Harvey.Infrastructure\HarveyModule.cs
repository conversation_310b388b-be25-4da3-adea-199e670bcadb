﻿using DataVenia.Common.Contracts.Harvey;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.Abstractions.Data;
using DataVenia.Modules.Harvey.Application.DataImport;
using DataVenia.Modules.Harvey.Domain.Action;
using DataVenia.Modules.Harvey.Domain.CourtDivision;
using DataVenia.Modules.Harvey.Domain.Forum;
using DataVenia.Modules.Harvey.Domain.LawsuitClass;
using DataVenia.Modules.Harvey.Domain.LawsuitTopic;
using DataVenia.Modules.Harvey.Domain.LegalCategory;
using DataVenia.Modules.Harvey.Domain.LegalInstance;
using DataVenia.Modules.Harvey.Domain.PartyType;
using DataVenia.Modules.Harvey.Domain.Status;
using DataVenia.Modules.Harvey.Infrastructure.Action;
using DataVenia.Modules.Harvey.Infrastructure.CourtDivision;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using DataVenia.Modules.Harvey.Infrastructure.Facades;
using DataVenia.Modules.Harvey.Infrastructure.Forum;
using DataVenia.Modules.Harvey.Infrastructure.LawsuitClass;
using DataVenia.Modules.Harvey.Infrastructure.LawsuitTopic;
using DataVenia.Modules.Harvey.Infrastructure.LegalCategory;
using DataVenia.Modules.Harvey.Infrastructure.LegalInstance;
using DataVenia.Modules.Harvey.Infrastructure.PartyType;
using DataVenia.Modules.Harvey.Infrastructure.Status;
using DataVenia.Modules.Harvey.Presentation;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DataVenia.Modules.Harvey.Infrastructure;

public static class HarveyModule
{
    public static IServiceCollection AddHarveyModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddInfrastructure(configuration);

        services.AddEndpoints(AssemblyReference.Assembly);

        return services;
    }

    private static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<HarveyDbContext>((sp, options) =>
            options
                .UseNpgsql(
                    configuration.GetConnectionString("Database"),
                    npgsqlOptions => npgsqlOptions
                        .MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Harvey))
                .UseSnakeCaseNamingConvention());

        services.AddScoped<ILawsuitTypeRepository, LawsuitTypeRepository>();
        services.AddScoped<ICourtDivisionRepository, CourtDivisionRepository>();
        services.AddScoped<IForumRepository, ForumRepository>();
        services.AddScoped<ILegalInstanceRepository, LegalInstanceRepository>();
        services.AddScoped<ILawsuitStatusRepository, LawsuitStatusRepository>();
        services.AddScoped<IPartyTypeRepository, PartyTypeRepository>();
        services.AddScoped<ILegalCategoryRepository, LegalCategoryRepository>();
        services.AddScoped<ILawsuitClassRepository, LawsuitClassRepository>();
        services.AddScoped<ILawsuitTopicRepository, LawsuitTopicRepository>();

        // Facades
        services.AddScoped<ILawsuitTopicFacade, LawsuitTopicFacade>();
        services.AddScoped<ILawsuitClassFacade, LawsuitClassFacade>();
        services.AddScoped<IForumFacade, ForumFacade>();
        services.AddScoped<ILawsuitTypeFacade, LawsuitTypeFacade>();

        services.AddScoped<DataImportService>();

        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<HarveyDbContext>());
    }
}
