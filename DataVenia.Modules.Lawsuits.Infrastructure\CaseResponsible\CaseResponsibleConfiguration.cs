﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.CaseResponsible;

public class CaseResponsibleConfiguration: IEntityTypeConfiguration<Domain.CasesResponsibles.CaseResponsible>
{
    public void Configure(EntityTypeBuilder<Domain.CasesResponsibles.CaseResponsible> builder)
    {
        builder.ToTable("case_responsible"); 

        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.<PERSON>Key(lu => new { CaseId = lu.CaseDataId, lu.LawyerId });

        builder.HasOne(lr => lr.CaseData)
            .WithMany(l => l.Responsibles) 
            .HasForeignKey(lu => lu.CaseDataId)
            .OnDelete(DeleteBehavior.NoAction); 

        builder.Property(lr => lr.LawyerId)
            .IsRequired();
    }

}
