﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.CasesParties;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Lawsuits.Domain.LawsuitParties;
using DataVenia.Modules.Lawsuits.Domain.LawsuitParty;

namespace DataVenia.Modules.Lawsuits.Application.Cases.GetCases;

internal sealed class GetCaseByIdQueryHandler(ICaseRepository caseRepository
    // ,ICasePartyRepository casePartyRepository
        )
    : IQueryHandler<GetCaseByIdQuery, EnrichedCaseResponse?>
{
    public async Task<Result<EnrichedCaseResponse?>> Handle(GetCaseByIdQuery request, CancellationToken cancellationToken)
    {
        Result<IReadOnlyCollection<CaseResponse>> cases = await caseRepository.GetCasesAsync(request.userId, request.officeId, request.caseId, cancellationToken);

        if(cases.IsFailure)
            return Result.Failure<EnrichedCaseResponse?>(cases.Error);
        
        CaseResponse? @case = cases.Value.FirstOrDefault();
        
        if(@case == null)
            return Result.Failure<EnrichedCaseResponse?>(new Error("Not.Found", "The case was not found.", ErrorType.NotFound));
        
        // Result<IReadOnlyCollection<CaseParty>> caseParties = await casePartyRepository.GetCasePartiesAsync(request.caseId, cancellationToken);
        
        // Map each CaseParty to a CasePartyResponse
        // var casePartyResponses = caseParties.Value
        //     .Select(lp => new CasePartyResponse(
        //         Id: lp.Id,
        //         PartyId: lp.PartyId,
        //         CaseDataId: lp.CaseDataId,
        //         PartyType: lp.PartyType,
        //         IsClient: lp.IsClient
        //     ))
        //     .ToList();

        // 3) Create the EnrichedLawsuitResponse
        
        var enrichedCase = new EnrichedCaseResponse(
            Id: @case.Id,
            Title: @case.Title,
            FolderId: @case.FolderId,
            FolderName: @case.FolderName,
            // CaseParties: casePartyResponses,
            CauseValue: @case.CauseValue,
            ConvictionValue: @case.ConvictionValue,
            CreatedAt: @case.CreatedAt,
            ResponsibleIds: @case.ResponsibleIds, 
            Access: @case.Access,
            Description: @case.Description,
            Observations: @case.Observations
        );
        
        return Result.Success<EnrichedCaseResponse?>(enrichedCase);
    }
}
