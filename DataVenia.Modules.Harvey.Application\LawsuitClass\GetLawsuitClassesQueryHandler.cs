﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Harvey.Domain.LawsuitClass;
using FluentResults;

namespace DataVenia.Modules.Harvey.Application.LawsuitClass;

internal sealed class GetLawsuitClassesQueryHandler(
    ILawsuitClassRepository lawsuitClassRepository) : IQueryHandlerFr<GetLawsuitClassesQuery, IReadOnlyCollection<GetLawsuitClassesResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetLawsuitClassesResponse>>> Handle(GetLawsuitClassesQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<Domain.LawsuitClass.LawsuitClass> classes = await lawsuitClassRepository.GetAllAsync(request.acronym, cancellationToken);

        var classesResponse = classes.Select(c => new GetLawsuitClassesResponse(
            c.Id,
            c.Type,
            c.LegalProvision,
            c.Article,
            c.Acronym,
            c.<PERSON>,
            c.<PERSON>ole,
            c.Passive<PERSON>,
            c.<PERSON>lossary,
            c.<PERSON>,
            c.<PERSON>,
            c.<PERSON>)).ToList();

        return classesResponse;
    }
}
