﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Domain.Appointments;

namespace DataVenia.Modules.Calendar.Domain.AppointmentParticipant;
public sealed class AppointmentParticipant : Entity
{
    public Guid AppointmentId { get; private set; }
    public Guid LawyerId { get; private set; }

    public Appointment Appointment { get; private set; }

    public static Result<AppointmentParticipant> Create(Guid appointmentId, Guid lawyerId)
    {
        var appointmentParticipant = new AppointmentParticipant()
        {
            AppointmentId = appointmentId,
            LawyerId = lawyerId
        };

        return appointmentParticipant;
    }
}
