﻿using DataVenia.Modules.Lawsuits.Application.Folders;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;

[Keyless]
public sealed record BaseLawsuitResponse(
    Guid Id,
    string? Title,
    string Cnj,
    Guid? FolderId,
    string? FolderName,
    string? LegalInstanceId,
    string? LawsuitTypeId,
    string? ClassId,
    string TopicIds,
    string? LawsuitStatusId,
    string? JudgingOrganId,
    string? JudgingOrganHref,
    decimal? CauseValue,
    decimal? ConvictionValue,
    string? Description,
    string? Observations,
    DateTime? DistributedAt,
    DateTime CreatedAt,
    List<Guid> ResponsibleIds,
    string? Access,
    Guid? EvolvedFromCaseId,
    Guid? GroupingCaseId,
    bool IsInstanceCreatedByUser,
    bool MonitoringEnabled,
    string MonitoringHistory);

public sealed record LawsuitResponse(
    Guid Id,
    string? Title,
    string Cnj,
    Guid? FolderId,
    string? FolderName,
    string? LegalInstanceId,
    string? LawsuitTypeId,
    string? ClassId,
    List<int> TopicIds,
    string? LawsuitStatusId,
    string? JudgingOrganId,
    string? JudgingOrganHref,
    decimal? CauseValue,
    decimal? ConvictionValue,
    string? Description,
    DateTime? DistributedAt,
    DateTime CreatedAt,
    List<Guid> ResponsibleIds,
    string? Access,
    Guid? EvolvedFromCaseId,
    Guid? GroupingCaseId,
    bool IsInstanceCreatedByUser,
    bool MonitoringEnabled);

public sealed record EnrichedLawsuitResponse(
    Guid Id,
    string? Title,
    string Cnj,
    Guid? FolderId,
    string? FolderName,
    string? LegalInstanceId,
    string? LawsuitTypeId,
    string? ClassId,
    List<int> TopicIds,
    string? LawsuitStatusId,
    // List<LawsuitPartyResponse> LawsuitParties,
    string? JudgingOrganId,
    string? JudgingOrganHref,
    decimal? CauseValue,
    decimal? ConvictionValue,
    string? Description,
    string? Observations,
    DateTime? DistributedAt,
    DateTime CreatedAt,
    List<Guid> ResponsibleIds,
    string? Access,
    Guid? EvolvedFromCaseId,
    Guid? GroupingCaseId,
    bool IsInstanceCreatedByUser,
    bool MonitoringEnabled,
    IReadOnlyCollection<MonitoringHistoryEntry> MonitoringHistory)
{
    public FolderResponse? Folder => FolderId.HasValue && !string.IsNullOrEmpty(FolderName)
        ? new FolderResponse(FolderId.Value, FolderName!)
        : null;
}

public sealed record LawsuitPartyResponse(Guid Id, Guid PartyId, Guid LawsuitDataId, string PartyType, bool IsClient);
