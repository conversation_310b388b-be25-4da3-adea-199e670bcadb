using DataVenia.Common.Domain.Helpers;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Callbacks;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.LawsuitSync.Presentation.Endpoints;

public class CodiloCallbacks : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/lawsuit-sync/callbacks/codilo/",
                async (HttpRequest request, [FromServices] IReceiveLawsuitCallbackAppService appService,
                    [FromServices] ILogger<CodiloCallbacks> logger) =>
                {
                    try
                    {
                        using var reader = new StreamReader(request.Body);
                        var body = await reader.ReadToEndAsync().ConfigureAwait(false);

                        logger.LogInformation("Received codilo callback");

                        var callback = JsonHelper.Deserialize<MonitoringCallback>(body);

                        if (callback.IsFailed)
                        {
                            var logBody = JsonHelper.Deserialize<object>(body);
                            if (logBody.IsSuccess)
                                logger.LogWarning("{LogBody}", logBody.Value);
                            
                            logger.LogWarning("Failed to deserialize callback.");
                            return Results.BadRequest("Invalid request body.");
                        }

                        logger.LogInformation("Parsed codilo callback - cnj {Cnj}", callback.Value.Cnj);

                        var result = await appService.ExecuteAsync(callback.Value).ConfigureAwait(false);

                        return result.IsSuccess ? Results.Ok() : Results.BadRequest(result.Errors);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Failed to receive callback for lawsuit-monitoring.");
                        return Results.Problem();
                    }
                })
            .WithTags(Tags.LawsuitsMonitoringCallback);
    }
}
