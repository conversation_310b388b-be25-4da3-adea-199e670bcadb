{"profiles": {"Docker Compose": {"commandName": "DockerCompose", "commandVersion": "1.0", "composeLaunchAction": "LaunchBrowser", "composeLaunchServiceName": "datavenia.api", "composeLaunchUrl": "{Scheme}://localhost:{ServicePort}/swagger", "serviceActions": {"datavenia.api": "StartDebugging", "datavenia.database": "StartWithoutDebugging", "datavenia.identity": "StartWithoutDebugging", "datavenia.redis": "StartWithoutDebugging", "datavenia.seq": "StartWithoutDebugging"}}, "teste": {"commandName": "DockerCompose", "commandVersion": "1.0", "serviceActions": {"datavenia.api": "StartDebugging", "datavenia.database": "StartWithoutDebugging", "datavenia.identity": "StartWithoutDebugging", "datavenia.redis": "StartWithoutDebugging", "datavenia.seq": "StartWithoutDebugging"}}}}