﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Users.Domain.SharedModels;

namespace DataVenia.Modules.Users.Application.Lawyer.UpdateLawyer;

public sealed record UpdateLawyerCommand(Guid UserId, string? FirstName = null, string? LastName = null, IReadOnlyCollection<string>? Oabs = null, IReadOnlyCollection<Contact>? Contacts = null, string? Cpf = null, string? Rg = null, string? Cnh = null, string? Passport = null, string? Ctps = null, string? Pis = null, string? VoterId = null) : ICommand;
