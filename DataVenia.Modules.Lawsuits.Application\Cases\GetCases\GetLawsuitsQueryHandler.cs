﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Lawsuits.Application.Cases.GetCases;
internal sealed class GetCasesQueryHandler(ICaseRepository caseRepository)
    : IQueryHandler<GetCasesQuery, IReadOnlyCollection<CaseResponse>>
{
    public async Task<Result<IReadOnlyCollection<CaseResponse>>> Handle(GetCasesQuery request, CancellationToken cancellationToken)
    {
        Result<IReadOnlyCollection<CaseResponse>> cases = await caseRepository.GetCasesAsync(request.userId, request.officeId, cancellationToken: cancellationToken);

        return cases;
    }
}
