﻿using System.Text.Json;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.User;
using DataVenia.Modules.Users.Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Users.Infrastructure.Users;

internal sealed class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("user");

        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(u => u.Id);

        builder.HasDiscriminator<string>("UserType")
            .HasValue<User>("User")
            .HasValue<Lawyer>("Lawyer");
        
        JsonSerializerOptions jsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };
        
        builder.OwnsOne(u => u.TermsAndConditions, sa =>
        {
            sa.ToJson(); // Tells EF to store the owned type as JSON (supported with Npgsql provider in EF Core 6+)
        });


        
        // Configure Preferences as jsonb
        builder
            .Property(e => e.Preferences)
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, jsonOptions),
                v => JsonSerializer.Deserialize<Preferences>(v, jsonOptions)
            );

        builder.Property(c => c.FirstName).HasMaxLength(200).IsRequired();
        builder.Property(c => c.LastName).HasMaxLength(200).IsRequired();
        builder.Property(c => c.Email).HasMaxLength(300);
        builder.HasIndex(u => u.Email).IsUnique();
        
        builder.Property(c => c.IdentityId).IsRequired(false).HasColumnType("uuid");
        builder.HasIndex(u => u.IdentityId).HasFilter("identity_id IS NOT NULL").IsUnique();
        
        builder.Property(c => c.Cpf).HasMaxLength(11);
        builder.HasIndex(c => c.Cpf);
        
        builder.Property(c => c.Rg).HasMaxLength(15);
        builder.Property(c => c.Cnh).HasMaxLength(15);
        builder.Property(c => c.Passport).HasMaxLength(20);
        builder.Property(c => c.Ctps).HasMaxLength(20);
        builder.Property(c => c.Pis).HasMaxLength(20);
        builder.Property(c => c.VoterId).HasMaxLength(12);
    }
}
