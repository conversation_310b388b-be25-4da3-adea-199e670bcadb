﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Common.Contracts.OfficeLawyer;

namespace DataVenia.Modules.Users.Infrastructure.IntermediateClasses;

internal sealed class OfficeUserConfiguration : IEntityTypeConfiguration<OfficeUser>
{
    public void Configure(EntityTypeBuilder<OfficeUser> builder)
    {
        builder.ToTable("office_user");
        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.HasKey(ou => ou.Id);

        // Configure the relationship between OfficeUser and Office
        builder.HasOne(ou => ou.Office)
            .WithMany(o => o.OfficeUsers)
            .HasForeignKey(ou => ou.OfficeId);

        // Configure the relationship between OfficeUser and Lawyer
        builder.HasOne(ou => ou.User)
            .WithMany(l => l.OfficeUsers)
            .HasForeignKey(ou => ou.UserId);

        // Configure the relationship between OfficeUser and Owner (also a Lawyer)
        builder.HasOne(ou => ou.Owner)
            .WithMany()
            .HasForeignKey(ou => ou.OwnerId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure the relationship between OfficeUser and Role
        builder.HasOne(ou => ou.Role)
            .WithMany()
            .HasForeignKey(ou => ou.RoleName);

        builder.Property(ou => ou.RoleName)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(ou => ou.InvitationStatus)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<InvitationStatus>(v));

        builder.HasIndex(ou => new
            {
                ou.OfficeId,
                ou.UserId
            })
            .IsUnique();
    }
}
