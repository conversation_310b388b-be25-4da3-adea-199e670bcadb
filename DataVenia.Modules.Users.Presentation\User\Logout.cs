﻿using DataVenia.Common.Presentation.Endpoints;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.User;

public class Logout : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("logout", (HttpContext http) =>
            {
                // Remove the authentication cookies by deleting them
                http.Response.Cookies.Delete("AccessToken");
                http.Response.Cookies.Delete("RefreshToken");
                return Results.Redirect("/login");
            })
            .AllowAnonymous() // Or require authentication if you want only logged-in users to call logout
            .WithTags(Tags.Users);
    }
}
