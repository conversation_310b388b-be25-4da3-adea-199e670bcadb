﻿using System.Text.Json;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
internal sealed class GetLawsuitsQueryHandler(ILawsuitRepository lawsuitRepository)
    : IQueryHandler<GetLawsuitsQuery, IReadOnlyCollection<LawsuitResponse>>
{
    public async Task<Result<IReadOnlyCollection<LawsuitResponse>>> Handle(GetLawsuitsQuery request, CancellationToken cancellationToken)
    {
        Result<IReadOnlyCollection<BaseLawsuitResponse>> baseResult = await lawsuitRepository.GetLawsuitsAsync(request.userId, request.officeId, cancellationToken: cancellationToken);

        if (baseResult.IsFailure)
            return Result.Failure<IReadOnlyCollection<LawsuitResponse>>(baseResult.Error);
            
        var mapped = baseResult.Value
            .Select(b => new LawsuitResponse(
                b.Id,
                b.<PERSON>,
                b.<PERSON>nj,
                b.<PERSON>old<PERSON>,
                b.<PERSON>,
                b.<PERSON>d,
                b.<PERSON>uitTypeId,
                b.<PERSON>d,
                JsonSerializer.Deserialize<List<int>>(b.TopicIds) ?? [],
                b.LawsuitStatusId,
                b.JudgingOrganId,
                b.JudgingOrganHref,
                b.CauseValue,
                b.ConvictionValue,
                b.Description,
                b.DistributedAt,
                b.CreatedAt,
                b.ResponsibleIds,
                b.Access,
                b.EvolvedFromCaseId,
                b.GroupingCaseId,
                b.IsInstanceCreatedByUser,
                b.MonitoringEnabled
            ))
            .ToList();

        return Result.Success<IReadOnlyCollection<LawsuitResponse>>(mapped);
    }
}
