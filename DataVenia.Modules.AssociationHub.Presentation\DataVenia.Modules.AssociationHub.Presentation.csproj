﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\DataVenia.Common.Contracts\DataVenia.Common.Contracts.csproj" />
        <ProjectReference Include="..\DataVenia.Common.Presentation\DataVenia.Common.Presentation.csproj" />
        <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Application\DataVenia.Modules.AssociationHub.Application.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="MassTransit" Version="8.3.4" />
        <PackageReference Include="MediatR" Version="12.4.1" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    </ItemGroup>

</Project>
