import{e as n,f as p,r as a,j as e,Q as f,d as h,c as j,J as g,a as u,h as t,S as k,l as c,t as y,B as v,O as o,I as b,F as N,i as S,Z as L}from"./index-DxHSBLqJ.js";import{z as I,S as d}from"./schemas-CWY_3UB6.js";import{P as M}from"./plus-C0S_1p2f.js";import"./brazilian-values-a8H3KUqb.js";/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=n("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=n("Link2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]]);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=n("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),F=I.object({name:d.string,responsible:d.uuid}),q=()=>{const s=p(F,"task-modal"),[r,i]=a.useState(!1),[m,x]=a.useState([]);return a.useEffect(()=>{r&&x([{label:"Fulano",value:"0192fe24-35f5-75bb-b08d-4715603ae6ad"},{label:"Beltrano",value:"0192fe24-5564-78bd-9afb-3c51c29ecc95"}])},[r]),e.jsxs(a.Fragment,{children:[e.jsxs(f,{type:"dialog",title:"Criar uma nova tarefa",open:r,onChange:i,children:[e.jsx("p",{children:"Descrição que irá ajudar a criar uma task"}),e.jsx(h.Form,{children:e.jsxs("div",{className:"mt-4 grid grid-cols-1 gap-kilo lg:grid-cols-2 min-w-xs container w-full",children:[e.jsx(j,{...s.input("name",{title:"Nome da tarefa",placeholder:"Abrir caso do processo 123"})}),e.jsx(g,{...s.select("responsible",{options:m,title:"Responsável",placeholder:"João da Silva"})})]})})]}),e.jsx(u,{size:"small",icon:e.jsx(M,{size:16}),onClick:()=>i(!0),children:"Nova tarefa"})]})},A=["Reunião com cliente","Organizar processos","Prazo fatal","Estudo de caso","Ligar para empresa X"],l=s=>e.jsx("nav",{children:e.jsxs(h.Link,{className:"link flex w-fit items-center gap-1 text-sm text-foreground/60",href:s.href,children:[e.jsx(O,{size:14}),s.desc]})});function D(){const[s,r]=a.useState(void 0);return e.jsxs(a.Fragment,{children:[e.jsxs("div",{className:"3xl:grid-cols-4 grid grid-cols-1 gap-kilo xl:grid-cols-2",children:[e.jsx(t,{Icon:k,title:"Processos em andamento",children:42,footer:e.jsx(l,{href:c.processes,desc:"Ver processos"})}),e.jsx(t,{Icon:z,title:"Arrecadamento mensal",children:y(65700),footer:e.jsx(l,{href:c.processes,desc:"Ver finanças"})}),e.jsx(t,{Icon:C,title:"Usuários ativos",children:140,footer:e.jsx(l,{href:c.processes,desc:"Ver clientes"})}),e.jsx(t,{Icon:v,title:"Empresas atendidas",children:70,footer:e.jsx(l,{href:c.processes,desc:"Ver clientes"})})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-kilo lg:grid-cols-6",children:[e.jsx(o,{title:"Minha agenda",container:"lg:col-span-4",children:e.jsx(b,{onChange:r,date:s,changeOnlyOnClick:!0})}),e.jsx(o,{title:"Tarefas do dia",container:"lg:col-span-2",className:"space-y-kilo",children:s===void 0?e.jsx("p",{className:"text-sm text-disabled",children:"Selecione uma data no calendário para ver suas tarefas"}):e.jsxs("div",{children:[e.jsx("header",{children:e.jsx("h2",{className:"text-balance text-lg font-medium capitalize",children:N.date(s)})}),e.jsx(S,{className:"my-kilo space-y-base",children:A.map(i=>e.jsx(L,{asTask:!0,children:i},i))}),e.jsx(q,{})]})})]})]})}export{D as default};
