﻿using System.Linq.Expressions;

namespace DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
public interface IOfficeUserRepository
{
    Task<OfficeUser?> GetSingleByFilterAsync(Expression<Func<OfficeUser, bool>> filter, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<OfficeUser?>> GetManyByFilterAsync(Expression<Func<OfficeUser, bool>> filter, CancellationToken cancellationToken = default);
    void Insert(OfficeUser officeLawyer);
    void InsertRange(IEnumerable<OfficeUser> officeUsers);
    void Update(OfficeUser officeLawyer);

    Task<List<Guid>> GetBoundLawyerIdsAsync(Guid officeId, IEnumerable<Guid> lawyerIds,
        CancellationToken cancellationToken = default);
}
