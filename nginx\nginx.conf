server {
    listen 80;
    server_name datavenia.io;
                                                                                                                                  
    root /usr/share/nginx/html;
    index index.html;
                                                                                                                                  
    location / {
        try_files $uri $uri/ /index.html;
    }
                                                                                                                                  
    location /api/ {
        proxy_pass http://localhost:5163;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
                                                                                                                                  
        # Security headers
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header Content-Security-Policy "default-src 'self';";
                                                                                                                                  
        # Timeouts and limits
        client_max_body_size 10M;
        proxy_read_timeout 60s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
    }
}
