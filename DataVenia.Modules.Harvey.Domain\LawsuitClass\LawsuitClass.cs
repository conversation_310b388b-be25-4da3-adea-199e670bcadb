﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.LawsuitClass;

public sealed class LawsuitClass : Entity
{
    public int Id { get; private set; }
    public string Type { get; private set; } = string.Empty;
    public string LegalProvision { get; private set; } = string.Empty;
    public string Article { get; private set; } = string.Empty;
    public string Acronym { get; private set; } = string.Empty;
    public string OldAcronym { get; private set; } = string.Empty;
    public string ActivePole { get; private set; } = string.Empty;
    public string PassivePole { get; private set; } = string.Empty;
    public string Glossary { get; private set; } = string.Empty;
    public bool IsOwnNumbering { get; private set; }
    public bool IsFirstInstance { get; private set; }
    public bool IsSecondInstance { get; private set; }
    public bool JustEsJuizadoEs { get; private set; }
    public bool JustEsTurmas { get; private set; }
    public bool JustEs1grauMil { get; private set; }
    public bool JustEs2grauMil { get; private set; }
    public bool JustEsJuizadoEsFp { get; private set; }
    public bool JustTuEsUn { get; private set; }
    public bool JustFed1grau { get; private set; }
    public bool JustFed2grau { get; private set; }
    public bool JustFedJuizadoEs { get; private set; }
    public bool JustFedTurmas { get; private set; }
    public bool JustFedNacional { get; private set; }
    public bool JustFedRegional { get; private set; }
    public bool JustTrab1grau { get; private set; }
    public bool JustTrab2grau { get; private set; }
    public bool JustTrabTst { get; private set; }
    public bool JustTrabCsjt { get; private set; }
    public bool Stf { get; private set; }
    public bool Stj { get; private set; }
    public bool Cjf { get; private set; }
    public bool Cnj { get; private set; }
    public bool JustMilUniao1grau { get; private set; }
    public bool JustMilUniaoStm { get; private set; }
    public bool JustMilEst1grau { get; private set; }
    public bool JustMilEstTjm { get; private set; }
    public bool JustElei1grau { get; private set; }
    public bool JustElei2grau { get; private set; }
    public bool JustEleiTse { get; private set; }
    public string? IncludedBy { get; private set; }
    public DateTime? IncludedAt { get; private set; }
    public string? UserIp { get; private set; }
    public string? UpdatedBy { get; private set; }
    public string? ProcedureId { get; private set; }
    public string? ProcedureOrigin { get; private set; }
    public bool IsCriminal { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public LawsuitClass() { }

    public static Result<LawsuitClass> Create(
        int id,
        string type,
        string legalProvision,
        string article,
        string acronym,
        string oldAcronym,
        string activePole,
        string passivePole,
        string glossary,
        bool isOwnNumbering,
        bool isFirstInstance,
        bool isSecondInstance,
        bool justEsJuizadoEs,
        bool justEsTurmas,
        bool justEs1grauMil,
        bool justEs2grauMil,
        bool justEsJuizadoEsFp,
        bool justTuEsUn,
        bool justFed1grau,
        bool justFed2grau,
        bool justFedJuizadoEs,
        bool justFedTurmas,
        bool justFedNacional,
        bool justFedRegional,
        bool justTrab1grau,
        bool justTrab2grau,
        bool justTrabTst,
        bool justTrabCsjt,
        bool stf,
        bool stj,
        bool cjf,
        bool cnj,
        bool justMilUniao1grau,
        bool justMilUniaoStm,
        bool justMilEst1grau,
        bool justMilEstTjm,
        bool justElei1grau,
        bool justElei2grau,
        bool justEleiTse,
        string? includedBy,
        DateTime? includedAt,
        string? userIp,
        string? updatedBy,
        string? procedureId,
        string? procedureOrigin,
        bool isCriminal)
    {
        return new LawsuitClass
        {
            Id = id,
            Type = type,
            LegalProvision = legalProvision,
            Article = article,
            Acronym = acronym,
            OldAcronym = oldAcronym,
            ActivePole = activePole,
            PassivePole = passivePole,
            Glossary = glossary,
            IsOwnNumbering = isOwnNumbering,
            IsFirstInstance = isFirstInstance,
            IsSecondInstance = isSecondInstance,
            JustEsJuizadoEs = justEsJuizadoEs,
            JustEsTurmas = justEsTurmas,
            JustEs1grauMil = justEs1grauMil,
            JustEs2grauMil = justEs2grauMil,
            JustEsJuizadoEsFp = justEsJuizadoEsFp,
            JustTuEsUn = justTuEsUn,
            JustFed1grau = justFed1grau,
            JustFed2grau = justFed2grau,
            JustFedJuizadoEs = justFedJuizadoEs,
            JustFedTurmas = justFedTurmas,
            JustFedNacional = justFedNacional,
            JustFedRegional = justFedRegional,
            JustTrab1grau = justTrab1grau,
            JustTrab2grau = justTrab2grau,
            JustTrabTst = justTrabTst,
            JustTrabCsjt = justTrabCsjt,
            Stf = stf,
            Stj = stj,
            Cjf = cjf,
            Cnj = cnj,
            JustMilUniao1grau = justMilUniao1grau,
            JustMilUniaoStm = justMilUniaoStm,
            JustMilEst1grau = justMilEst1grau,
            JustMilEstTjm = justMilEstTjm,
            JustElei1grau = justElei1grau,
            JustElei2grau = justElei2grau,
            JustEleiTse = justEleiTse,
            IncludedBy = includedBy,
            IncludedAt = includedAt,
            UserIp = userIp,
            UpdatedBy = updatedBy,
            ProcedureId = procedureId,
            ProcedureOrigin = procedureOrigin,
            IsCriminal = isCriminal,
            CreatedAt = DateTime.UtcNow
        };
    }
}
