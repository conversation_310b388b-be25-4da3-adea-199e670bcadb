﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using LawsuitPartyDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitParties.LawsuitParty;
namespace DataVenia.Modules.Lawsuits.Infrastructure.IntermediateClasses;

public sealed class LawsuitPartyConfiguration : IEntityTypeConfiguration<LawsuitPartyDomain>
{
    public void Configure(EntityTypeBuilder<LawsuitPartyDomain> builder)
    {
        builder.ToTable("lawsuit_party");
        
        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(lp => lp.Id);

        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.Property(lp => lp.PartyType)
               .IsRequired();

        builder.Property(lp => lp.IsClient)
               .IsRequired();

        // Relacionamento com Lawsuit
        builder.HasOne(lp => lp.LawsuitData)
               .WithMany(l => l.LawsuitParties)
               .HasForeignKey(lp => lp.LawsuitDataId)
               .OnDelete(DeleteBehavior.NoAction);

        builder
            .HasIndex(lp => new
            {
                lp.LawsuitDataId,
                lp.PartyId
            })
            .IsUnique();
    }
}
