﻿using DataVenia.Modules.Harvey.Domain.Action;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using LawsuitTypeDomain = DataVenia.Modules.Harvey.Domain.Action.LawsuitType;
namespace DataVenia.Modules.Harvey.Infrastructure.Action;
public sealed class LawsuitTypeRepository(HarveyDbContext context) : ILawsuitTypeRepository
{
    public async Task<IReadOnlyCollection<LawsuitTypeDomain>> GetAllAsync(string? displayName, CancellationToken cancellationToken = default)
    {
        IQueryable<LawsuitTypeDomain> query = context.LawsuitTypes;

        if(!string.IsNullOrWhiteSpace(displayName))
            query = query.Where(a => a.DisplayName.Contains(displayName));

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<LawsuitTypeDomain?> GetByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        return await context.LawsuitTypes.FirstOrDefaultAsync(lt => lt.Id == id, cancellationToken);
    }
}
