﻿# Requisitos

Esse documento foi criado com base no [documento de requisitos](https://docs.google.com/document/d/1v86AI9NsxYXjhkmha6tJlGkPRMEq_-gq9qNvLKjA1wg/edit).

# Premissa<PERSON> as entidades deverão ter CRUD, porém nem todas fazem sentido de serem deletadas, ficando a critério da entidade em questão ser removida logicamente ou não ter o delete.

# Criação de usuários

- CRUD clientes/empresas
  - O CRUD de clientes será dividido em Cliente PF e PJ
- CRUD advogados
    - Apesar de não ser um requisito do MVP, podemos implementar ligações entre escritório:advogado→N:M, permitindo que um advogado possa fazer parte de mais de um escritório
    - Os advogados terão seu tipo na associação com o escritório, identificando seu cargo para um determinado escritório
- CRUD escritórios - Parte administrativa
- Prospecção e acompanhamento de clientes

# Casos e processos

- CRUD casos
  - Um caso pode ser promovido a processo
  - Um caso pode englobar vários processos
- CRUD processos
  - Processos são ações são ligadas ao jurídico, possuindo informações como vara, foro e instância
  - Um processo pode ser repassado de um advogado para outro, mantendo o registro de que essa ação aconteceu na timeline do processo

# Agenda

- Armazena prazos e tarefas
- A agenda deve estar conectada aos processos
  - Essa conexão se dá ao fato de que um processo pode gerar tarefas.
- Um item da agenda deverá ter um responsável e convidados
  - Convidados podem ser outros advogados ou clientes PF/PJ
  - Todo item da agenda deverá ter uma data de início, data de estimada para o fim e data da real finalização

# Financeiro

- Cálculo baseado nos processos fechados para informativos de ganho
- Balanço mensal/anual com base nos processos em andamento + fechados
- A importação de dados de planilha deverá ter um formato para **upload**

# Upload

- Upload de documentos de um caso/processo
- Planilhas financeiras
- Importações de outros sistemas