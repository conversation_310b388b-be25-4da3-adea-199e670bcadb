﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AddressDomain = DataVenia.Modules.Users.Domain.SharedModels.Address;

namespace DataVenia.Modules.Users.Infrastructure.SharedModels;
internal sealed class AddressConfiguration : IEntityTypeConfiguration<AddressDomain>
{
    public void Configure(EntityTypeBuilder<AddressDomain> builder)
    {
        // Nome da tabela
        builder.ToTable("address");

        // Chave primária
        builder.HasKey(a => a.Id);

        // Propriedades obrigatórias
        builder.Property(a => a.Neighborhood)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(a => a.City)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(a => a.Street)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(a => a.PostalCode)
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(a => a.State)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(a => a.Complement)
            .HasMaxLength(500);

        // Relacionamento com Client (um cliente pode ter vários endereços)
        // builder.HasOne(a => a.Client)
        //     .WithMany(c => c.Addresses) // Assuming Client has an Addresses collection
        //     .HasForeignKey(a => a.ClientId)
        //     .OnDelete(DeleteBehavior.NoAction);

        // Relacionamento com Office (um escritório pode ter vários endereços)
        builder.HasOne(a => a.Office)
            .WithMany(o => o.Addresses) // Assuming Office has an Addresses collection
            .HasForeignKey(a => a.OfficeId)
            .OnDelete(DeleteBehavior.NoAction);

        // Constraint para garantir que apenas um relacionamento seja populado (ClientId ou OfficeId)
        builder.ToTable(tb => tb.HasCheckConstraint("CK_Address_OneOwner",
            // "(\"client_id\" IS NOT NULL AND \"office_id\" IS NULL) OR " +
            "(\"office_id\" IS NOT NULL)"));
    }
}
