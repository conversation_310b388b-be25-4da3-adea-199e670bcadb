﻿using System.Linq.Expressions;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Infrastructure.IntermediateClasses;

namespace DataVenia.Modules.Users.Domain.Lawyers;
public interface ILawyerRepository
{
    Task<Lawyer?> GetSingleByFilterAsync(Expression<Func<Lawyer, bool>> filter, CancellationToken cancellationToken = default);
    Task<Lawyer?> GetSingleWithOfficesByFilterAsync(Expression<Func<Lawyer, bool>> filter, CancellationToken cancellationToken = default);
    Task<Result<Lawyer>> GetSingleBySignUpTokenAsync(Guid token, CancellationToken cancellationToken = default);
    Task<Lawyer?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<List<Lawyer>> GetByEmailsAsync(IEnumerable<string> emails, CancellationToken cancellationToken = default);
    Task<Result<List<OfficeUserDto>>> GetOfficesAdministratorsAsync(Guid lawyerId,
        CancellationToken cancellationToken = default);
    void Insert(Lawyer lawyer);
    void Update(Lawyer lawyer);

    Task<Result<Role?>> GetRoleByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<Lawyer>> GetAllByOfficeIdAsync(Guid officeId, CancellationToken cancellationToken = default);
    Task<Guid?> GetIdentityId(Guid lawyerId, CancellationToken cancellationToken = default);
    void InsertRange(IEnumerable<Lawyer> lawyers);
    Task<IReadOnlyList<Lawyer>> GetByIdsAsync(List<Guid> ids, CancellationToken cancellationToken = default);
}
