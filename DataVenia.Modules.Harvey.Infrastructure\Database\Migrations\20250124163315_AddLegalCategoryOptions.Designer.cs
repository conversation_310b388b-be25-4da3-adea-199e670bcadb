﻿// <auto-generated />
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    [DbContext(typeof(HarveyDbContext))]
    [Migration("20250124163315_AddLegalCategoryOptions")]
    partial class AddLegalCategoryOptions
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("harvey")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.Action.LawsuitType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_type");

                    b.ToTable("lawsuit_type", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.CourtDivision.CourtDivision", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.HasKey("Id")
                        .HasName("pk_court_division");

                    b.ToTable("court_division", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.Forum.Forum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.HasKey("Id")
                        .HasName("pk_forum");

                    b.ToTable("forum", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LegalCategory.LegalCategory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.HasKey("Id")
                        .HasName("pk_legal_category");

                    b.ToTable("legal_category", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "CollectionAction",
                            DisplayName = "Ação coletiva",
                            Order = 1
                        },
                        new
                        {
                            Id = "DivorceAction",
                            DisplayName = "Ação de divórcio",
                            Order = 2
                        },
                        new
                        {
                            Id = "HabeasCorpus",
                            DisplayName = "Habeas Corpus",
                            Order = 6
                        },
                        new
                        {
                            Id = "LaborAction",
                            DisplayName = "Ação trabalhista",
                            Order = 3
                        },
                        new
                        {
                            Id = "TaxEnforcementAction",
                            DisplayName = "Supremo Tribunal Federal",
                            Order = 4
                        },
                        new
                        {
                            Id = "WritOfMandamus",
                            DisplayName = "Mandado de segurança",
                            Order = 5
                        },
                        new
                        {
                            Id = "Other",
                            DisplayName = "Other",
                            Order = 7
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LegalInstance.LegalInstance", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.HasKey("Id")
                        .HasName("pk_legal_instance");

                    b.ToTable("legal_instance", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "FirstInstance",
                            DisplayName = "Primeira Instância",
                            Order = 1
                        },
                        new
                        {
                            Id = "SecondInstance",
                            DisplayName = "Segunda Instância",
                            Order = 2
                        },
                        new
                        {
                            Id = "STF",
                            DisplayName = "Supremo Tribunal Federal",
                            Order = 4
                        },
                        new
                        {
                            Id = "STJ",
                            DisplayName = "Superior Tribunal de Justiça",
                            Order = 3
                        },
                        new
                        {
                            Id = "Other",
                            DisplayName = "Outras",
                            Order = 5
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.PartyType.PartyType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.HasKey("Id")
                        .HasName("pk_party_type");

                    b.ToTable("party_type", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "Lawyer",
                            DisplayName = "Advogado",
                            Order = 1
                        },
                        new
                        {
                            Id = "Defendant",
                            DisplayName = "Réu",
                            Order = 3
                        },
                        new
                        {
                            Id = "Plaintiff",
                            DisplayName = "Autor",
                            Order = 2
                        },
                        new
                        {
                            Id = "Witness",
                            DisplayName = "Testemunha",
                            Order = 4
                        },
                        new
                        {
                            Id = "Other",
                            DisplayName = "Outro",
                            Order = 5
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.Status.Status", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_status");

                    b.ToTable("lawsuit_status", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "Pending",
                            DisplayName = "Pendente"
                        },
                        new
                        {
                            Id = "InProgress",
                            DisplayName = "Em progresso"
                        },
                        new
                        {
                            Id = "Suspended",
                            DisplayName = "Suspendido"
                        },
                        new
                        {
                            Id = "AwaitingAppeal",
                            DisplayName = "Aguardando Apelação"
                        },
                        new
                        {
                            Id = "Completed",
                            DisplayName = "Completo"
                        },
                        new
                        {
                            Id = "Closed",
                            DisplayName = "Fechado"
                        },
                        new
                        {
                            Id = "Cancelled",
                            DisplayName = "Cancelado"
                        },
                        new
                        {
                            Id = "AwaitingClient",
                            DisplayName = "Aguardando cliente"
                        },
                        new
                        {
                            Id = "AwaitingDecision",
                            DisplayName = "Aguardando decisão"
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
