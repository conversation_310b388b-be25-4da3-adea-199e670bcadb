namespace DataVenia.Modules.Notification.Domain.Models.Settings;

public sealed class NotificationSettings
{
    /// <summary>
    /// mail of the user that created the password
    /// </summary>
    public string SmtpUsername { get; set; }
    /// <summary>
    /// password settled on google account for api iterations
    /// </summary>
    public string SmtpPassword { get; set; }
    /// <summary>
    /// Group mail. eg. <EMAIL>
    /// </summary>
    public string SmtpGroup { get; set; }
    public string SmtpGroupName { get; set; }
    public string TelegramBotToken { get; set; }
}
