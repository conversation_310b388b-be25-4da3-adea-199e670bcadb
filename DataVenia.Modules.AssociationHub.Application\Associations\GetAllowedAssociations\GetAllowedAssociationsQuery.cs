using DataVenia.Common.Application.Messaging;

namespace DataVenia.Modules.AssociationHub.Application.Associations.GetAllowedAssociations;

public sealed record GetAllowedAssociationsQuery(string EntityType) : IQueryFr<AllowedAssociationsResponse>;

public sealed record AllowedAssociationsResponse(
    string EntityType,
    IReadOnlyCollection<string> AllowedAssociations,
    IReadOnlyCollection<string> ForbiddenAssociations,
    string AssociationMode);
