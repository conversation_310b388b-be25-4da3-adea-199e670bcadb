﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class DeletedAt : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "lawsuit",
                table: "lawsuit_responsible",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "lawsuit",
                table: "lawsuit_party",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "color",
                schema: "lawsuit",
                table: "folder",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "lawsuit",
                table: "folder",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "lawsuit",
                table: "case_responsible",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "lawsuit",
                table: "case_party",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "lawsuit",
                table: "lawsuit_responsible");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "lawsuit",
                table: "lawsuit_party");

            migrationBuilder.DropColumn(
                name: "color",
                schema: "lawsuit",
                table: "folder");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "lawsuit",
                table: "folder");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "lawsuit",
                table: "case_responsible");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "lawsuit",
                table: "case_party");
        }
    }
}
