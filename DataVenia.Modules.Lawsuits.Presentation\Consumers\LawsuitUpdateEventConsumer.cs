﻿using System.Text.Json;
using DataVenia.Common.Contracts.Events.LawsuitSync;
using DataVenia.Common.Contracts.Events.Notification;
using DataVenia.Common.Contracts.Harvey;
using DataVenia.Common.Contracts.Lawyer;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Application.DataDivergences;
using DataVenia.Modules.Lawsuits.Application.Lawsuits;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitStepsByCnj;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Domain.Outbox;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using LawsuitStep = DataVenia.Modules.Lawsuits.Domain.LawsuitSteps.LawsuitStep;

namespace DataVenia.Modules.Lawsuits.Presentation.Consumers;

public class LawsuitUpdateEventConsumer(
    ILawsuitRepository lawsuitRepository,
    IUnitOfWork unitOfWork,
    ILogger<LawsuitUpdateEventConsumer> logger,
    ILawsuitStepRepository lawsuitStepsRepository,
    ILawsuitDataRepository lawsuitDataRepository,
    IDataDivergenceRepository dataDivergenceRepository,
    ILawyerFacade lawyersFacade,
    IOutboxRepository outboxRepository,
    IConfiguration configuration,
    ILawsuitTopicFacade lawsuitTopicFacade,
    ILawsuitClassFacade lawsuitClassFacade,
    IForumFacade forumFacade,
    ILawsuitTypeFacade lawsuitTypeFacade)
    : IConsumer<LawsuitUpdateEvent>
{
    public async Task Consume(ConsumeContext<LawsuitUpdateEvent> context)
    {
        context = context ?? throw new ArgumentNullException(nameof(context));

        LawsuitUpdateEvent eventMessage = context.Message;

        using (logger.BeginScope(new
               {
                   Class = nameof(LawsuitUpdateEventConsumer), eventMessage.Cnj, eventMessage.Instance
               }))
        {
            // 1. Fetch all lawsuits for the given cnj
            var lawsuitsResult =
                await lawsuitRepository.GetLawsuitsByCnjAsync(eventMessage.Cnj, context.CancellationToken);

            if (lawsuitsResult.IsFailed)
            {
                logger.LogError("Error getting lawsuits by cnj when consuming steps: {@Error}",
                    JsonSerializer.Serialize(lawsuitsResult.Errors));
                return;
            }

            var lawsuits = lawsuitsResult.Value;

            // 2. Get All saved steps for the lawsuit
            var lawsuitStepsResult =
                await lawsuitStepsRepository.GetLawsuitStepsByCnjAsync(eventMessage.Cnj,
                    cancellationToken: context.CancellationToken);

            if (lawsuitStepsResult.IsFailed)
            {
                logger.LogError("Error getting lawsuit steps by cnj when consuming steps: {@Error}",
                    JsonSerializer.Serialize(lawsuitStepsResult.Errors));
                return;
            }

            // Filter out steps that we already have in the database
            var newSteps = MapEventToSteps(eventMessage, lawsuitStepsResult.Value);
            if (newSteps.Count > 0)
            {
                lawsuitStepsRepository.InsertRange(newSteps);
            }
            else
            {
                logger.LogInformation("No new steps to insert");
            }

            // 3. Coleta todos os IDs de lawsuits para buscar todos os lawsuitData em uma só query
            var lawsuitIds = lawsuits.Select(x => x.Id).ToList();
            var allLawsuitDataResult =
                await lawsuitDataRepository.GetLatestOfEachInstanceByLawsuitIdsAsync(lawsuitIds,
                    context.CancellationToken);
            if (allLawsuitDataResult.IsFailed)
            {
                logger.LogError("Error getting all latest lawsuit data by instance: {@Error}",
                    JsonSerializer.Serialize(allLawsuitDataResult.Errors));
                return;
            }

            var allLawsuitData = allLawsuitDataResult.Value;

            var lawsuitResponsiblesIdsToNotify = new HashSet<Guid>();

            // 4. Iterate over each lawsuit and apply the rules only if MonitoringEnabled = true
            foreach (var lawsuit in lawsuits)
            {
                if (
                    !lawsuit.MonitoringEnabled
                    && lawsuit.IsFirstTimeSyncCompleted)
                {
                    logger.LogInformation("Skipping lawsuit {LawsuitId} because monitoring is disabled.", lawsuit.Id);
                    continue;
                }

                // Check and update Cover data in the Lawsuit entity
                if (eventMessage.Cover != null)
                {
                    var divergencesResult = await lawsuit.CheckAndUpdateCoverDataAsync(
                        eventMessage.Instance,
                        eventMessage.Cover.LawsuitType.ToString(),
                        eventMessage.Cover.Class,
                        eventMessage.Cover.DistributedAt,
                        eventMessage.Cover.JudgingOrgan,
                        eventMessage.Cover.CauseValue,
                        eventMessage.Cover.Topics,
                        lawsuitTopicFacade,
                        lawsuitClassFacade,
                        forumFacade,
                        lawsuitTypeFacade,
                        context.CancellationToken);

                    if (divergencesResult.IsFailed)
                    {
                        var firstError = divergencesResult.Errors.FirstOrDefault();
                        if (firstError != null && firstError.Metadata != null &&
                            firstError.Metadata.TryGetValue("StatusCode", out var statusCodeObj) &&
                            statusCodeObj is int and (204 or 208))
                        {
                            logger.LogWarning("Codilo integration sent cover data but was not needed for lawsuit {LawsuitId}. Skipping. {ErrorMessage}", lawsuit.Id, firstError.Message);
                            continue;
                        }

                        logger.LogError("Failed to check and update data divergences for lawsuit {LawsuitId}.", lawsuit.Id);
                    }
                    else
                    {
                        dataDivergenceRepository.Insert(divergencesResult.Value);
                    }

                    lawsuitRepository.Update(lawsuit);
                }

                var relatedLawsuitData = allLawsuitData.Where(d => d.LawsuitId == lawsuit.Id).ToList();

                var instanceData = relatedLawsuitData
                    .Where(x => x.LegalInstanceId == eventMessage.Instance)
                    .OrderByDescending(x => x.CreatedAt)
                    .FirstOrDefault();

                // if the user haven't created the instance yet, we should create it
                if (instanceData is null)
                {
                    logger.LogWarning("Instance {Instance} not found in data; will use user-created",
                        eventMessage.Instance);
                    var createdByUser = relatedLawsuitData
                        .Where(x => x.IsInstanceCreatedByUser)
                        .OrderByDescending(x => x.CreatedAt)
                        .FirstOrDefault();

                    if (createdByUser is null)
                    {
                        logger.LogError("No user-created instance found for lawsuit {Id}", lawsuit.Id);
                        continue;
                    }

                    lawsuitResponsiblesIdsToNotify = lawsuitResponsiblesIdsToNotify
                        .Concat(createdByUser.Responsibles.Select(r => r.LawyerId)).ToHashSet();

                    // TODO: Refator to use eventMessage.Cover
                    var newLawsuitData = LawsuitData.Create(
                        createdByUser.Title ?? "",
                        createdByUser.Cnj,
                        lawsuit.Id,
                        createdByUser.FolderId,
                        eventMessage.Instance,
                        eventMessage.Status,
                        createdByUser.TopicIds.ToList(),
                        createdByUser.JudgingOrganId ?? "",
                        createdByUser.CauseValue,
                        null,
                        createdByUser.JudgingOrganHref ?? "",
                        null,
                        null,
                        createdByUser.Access,
                        createdByUser.Responsibles.Select(r => r.LawyerId).ToList(),
                        createdByUser.EvolvedFromCaseId,
                        createdByUser.GroupingCaseId,
                        false
                    );

                    if (newLawsuitData.IsFailed)
                    {
                        logger.LogError("Error creating lawsuit data: {@Error}",
                            newLawsuitData.Errors.FirstOrDefault()!.Message);
                        return;
                    }

                    lawsuitDataRepository.Insert(newLawsuitData.Value);
                }
                else
                {
                    lawsuitResponsiblesIdsToNotify = lawsuitResponsiblesIdsToNotify
                        .Concat(instanceData.Responsibles.Select(r => r.LawyerId)).ToHashSet();

                    if (instanceData.LawsuitStatusId != eventMessage.Status)
                    {
                        var updatedLawsuitData = LawsuitData.Create(
                            instanceData.Title,
                            instanceData.Cnj,
                            lawsuit.Id,
                            instanceData.FolderId,
                            instanceData.LegalInstanceId,
                            eventMessage.Status,
                            instanceData.TopicIds.ToList(),
                            instanceData.JudgingOrganId,
                            instanceData.CauseValue,
                            instanceData.ConvictionValue,
                            instanceData.JudgingOrganHref,
                            instanceData.Description,
                            instanceData.Observations,
                            instanceData.Access,
                            instanceData.Responsibles.Select(r => r.LawyerId).ToList(),
                            instanceData.EvolvedFromCaseId,
                            instanceData.GroupingCaseId,
                            instanceData.IsInstanceCreatedByUser
                        );
                        if (updatedLawsuitData.IsFailed)
                        {
                            logger.LogError("Error creating lawsuit data: {@Error}",
                                updatedLawsuitData.Errors.FirstOrDefault()!.Message);
                            return;
                        }

                        lawsuitDataRepository.Insert(updatedLawsuitData.Value);
                    }
                }
            }

            // send notification to lawyers
            if (newSteps.Count > 0)
                await EnqueueNotifications(context, lawsuitResponsiblesIdsToNotify, allLawsuitData, eventMessage,
                    newSteps);

            try
            {
                await unitOfWork.SaveChangesAsync(context.CancellationToken);
                logger.LogInformation("Inserted {Count} new steps", newSteps.Count);
                logger.LogInformation("Event processed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Exception occurred while consuming lawsuit update event.");

                // Throwing an exception will let MassTransit handle the retry as per configuration.
                throw new Exception("Lawsuit update event consumer failed");
            }
        }
    }

    private async Task EnqueueNotifications(ConsumeContext<LawsuitUpdateEvent> context,
        HashSet<Guid> lawsuitResponsiblesIdsToNotify,
        List<LawsuitData> allLawsuitData, LawsuitUpdateEvent eventMessage, List<LawsuitStep> newSteps)
    {
        var lawyers = await lawyersFacade.GetByIdsAsync(lawsuitResponsiblesIdsToNotify.ToList(),
            context.CancellationToken);

        var lawyerToLawsuits = allLawsuitData
            .SelectMany(d => d.Responsibles.Select(r => new { r.LawyerId, d.LawsuitId }))
            .GroupBy(x => x.LawyerId)
            .ToDictionary(
                g => g.Key,
                g => g.Select(x => x.LawsuitId).ToList()
            );

        var lawsuitUrl = configuration["Lawsuits:LawsuitUrl"];

// 3. Produce one Lawyer model per lawsuitId
        var lawyerModels = lawyers
            .SelectMany(l =>
            {
                if (!lawyerToLawsuits.TryGetValue(l.Id, out var lawsuitIds))
                    throw new InvalidOperationException($"No lawsuits found for lawyer {l.Id}");

                // for each lawsuitId, create a separate Lawyer instance
                return lawsuitIds.Select(lawsuitId =>
                {
                    var url = new Uri($"{lawsuitUrl}/{lawsuitId}", UriKind.Absolute);
                    return new Lawyer(l.Email, l.Name, url);
                });
            })
            .ToList();

        var emailNotificationEvent = new LawsuitStepsUpdate(
            CnjFormat(eventMessage.Cnj),
            lawyerModels,
            newSteps.Count
        );

        string payloadJson = JsonSerializer.Serialize(emailNotificationEvent);

        var outboxMessage = new Outbox
        {
            MessageType = nameof(LawsuitStepsUpdate),
            Payload = payloadJson,
            CreatedAt = DateTime.UtcNow,
            ProcessedAt = null
        };

        outboxRepository.Insert(outboxMessage);
    }

    private static string CnjFormat(string rawCnj)
    {
        if (string.IsNullOrWhiteSpace(rawCnj))
            return rawCnj;

        var digits = new string(rawCnj.Where(char.IsDigit).ToArray());

        if (digits.Length != 20)
            return rawCnj;

        return
            $"{digits.Substring(0, 7)}-{digits.Substring(7, 2)}.{digits.Substring(9, 4)}.{digits.Substring(13, 1)}.{digits.Substring(14, 2)}.{digits.Substring(16, 4)}";
    }

    private List<LawsuitStep> MapEventToSteps(
        LawsuitUpdateEvent eventMessage,
        IReadOnlyCollection<LawsuitStepsResponse> existingSteps)
    {
        var existingIds = existingSteps.Select(step => step.Id).ToHashSet();
        var newSteps = new List<LawsuitStep>();

        foreach (var step in eventMessage.Steps)
        {
            if (!existingIds.Contains(Guid.TryParse(step.Id, out var result)
                    ? result
                    : throw new ArgumentException("Step Id is not a valid Guid")))
            {
                newSteps.Add(LawsuitStep.Create(
                    step.Id,
                    step.Title,
                    step.Description,
                    eventMessage.Status,
                    eventMessage.Cnj,
                    eventMessage.Instance,
                    step.Date,
                    step.ActionBy,
                    step.Secret
                ));
            }
        }

        return newSteps;
    }
}
