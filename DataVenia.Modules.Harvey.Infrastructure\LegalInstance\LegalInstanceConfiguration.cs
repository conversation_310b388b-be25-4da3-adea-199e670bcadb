﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using LegalInstanceDomain = DataVenia.Modules.Harvey.Domain.LegalInstance.LegalInstance;

namespace DataVenia.Modules.Harvey.Infrastructure.LegalInstance;

internal sealed class LegalInstanceConfiguration : IEntityTypeConfiguration<LegalInstanceDomain>
{
    public void Configure(EntityTypeBuilder<LegalInstanceDomain> builder)
    {
        // Table Mapping
        builder.ToTable("legal_instance");

        // Primary Key
        builder.HasKey(li => li.Id);

        builder.Property(li => li.DisplayName)
            .IsRequired()
            .HasMaxLength(512);
        builder.HasIndex(x => x.DisplayName).IsUnique();

        builder.Property(li => li.Order)
            .IsRequired();
        
        builder.Property(f => f.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Seed Data
        builder.HasData(
            LegalInstanceDomain.FirstInstance,
            LegalInstanceDomain.SecondInstance,
            LegalInstanceDomain.STF,
            LegalInstanceDomain.STJ,
            LegalInstanceDomain.TST,
            LegalInstanceDomain.TSE,
            LegalInstanceDomain.STM,
            LegalInstanceDomain.Other
        );
    }
}
