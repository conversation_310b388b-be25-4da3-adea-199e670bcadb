﻿using DataVenia.Modules.Harvey.Domain.LegalCategory;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using LegalCategoryDomain = DataVenia.Modules.Harvey.Domain.LegalCategory.LegalCategory;
namespace DataVenia.Modules.Harvey.Infrastructure.LegalCategory;

public sealed class LegalCategoryRepository(HarveyDbContext context) : ILegalCategoryRepository
{
    public async Task<IReadOnlyCollection<LegalCategoryDomain>> GetAllAsync(string? displayName, CancellationToken cancellationToken = default)
    {
        IQueryable<LegalCategoryDomain> query = context.LegalCategories;

        if (!string.IsNullOrWhiteSpace(displayName))
            query = query.Where(li => li.DisplayName.Contains(displayName));

        return await query.ToListAsync(cancellationToken);
    }
}
