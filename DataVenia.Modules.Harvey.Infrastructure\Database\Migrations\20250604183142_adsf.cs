﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class adsf : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "just_trab2grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_trab_2_grau");

            migrationBuilder.RenameColumn(
                name: "just_trab1grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_trab_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_uniao1grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_mil_uniao_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_est1grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_mil_est_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_fed2grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_fed_2_grau");

            migrationBuilder.RenameColumn(
                name: "just_fed1grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_fed_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_es2grau_mil",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_es_2_grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_es1grau_mil",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_es_1_grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_elei2grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_elei_2_grau");

            migrationBuilder.RenameColumn(
                name: "just_elei1grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_elei_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_trab2grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_trab_2_grau");

            migrationBuilder.RenameColumn(
                name: "just_trab1grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_trab_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_uniao1grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_mil_uniao_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_est1grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_mil_est_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_fed2grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_fed_2_grau");

            migrationBuilder.RenameColumn(
                name: "just_fed1grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_fed_1_grau");

            migrationBuilder.RenameColumn(
                name: "just_es2grau_mil",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_es_2_grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_es1grau_mil",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_es_1_grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_elei2grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_elei_2_grau");

            migrationBuilder.RenameColumn(
                name: "just_elei1grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_elei_1_grau");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "just_trab_2_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_trab2grau");

            migrationBuilder.RenameColumn(
                name: "just_trab_1_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_trab1grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_uniao_1_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_mil_uniao1grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_est_1_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_mil_est1grau");

            migrationBuilder.RenameColumn(
                name: "just_fed_2_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_fed2grau");

            migrationBuilder.RenameColumn(
                name: "just_fed_1_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_fed1grau");

            migrationBuilder.RenameColumn(
                name: "just_es_2_grau_mil",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_es2grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_es_1_grau_mil",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_es1grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_elei_2_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_elei2grau");

            migrationBuilder.RenameColumn(
                name: "just_elei_1_grau",
                schema: "harvey",
                table: "lawsuit_topic",
                newName: "just_elei1grau");

            migrationBuilder.RenameColumn(
                name: "just_trab_2_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_trab2grau");

            migrationBuilder.RenameColumn(
                name: "just_trab_1_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_trab1grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_uniao_1_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_mil_uniao1grau");

            migrationBuilder.RenameColumn(
                name: "just_mil_est_1_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_mil_est1grau");

            migrationBuilder.RenameColumn(
                name: "just_fed_2_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_fed2grau");

            migrationBuilder.RenameColumn(
                name: "just_fed_1_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_fed1grau");

            migrationBuilder.RenameColumn(
                name: "just_es_2_grau_mil",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_es2grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_es_1_grau_mil",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_es1grau_mil");

            migrationBuilder.RenameColumn(
                name: "just_elei_2_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_elei2grau");

            migrationBuilder.RenameColumn(
                name: "just_elei_1_grau",
                schema: "harvey",
                table: "lawsuit_class",
                newName: "just_elei1grau");
        }
    }
}
