﻿using System.Linq.Expressions;
using System.Reflection;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.ClientCompany;
using DataVenia.Modules.Users.Domain.Company;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.SignUpToken;
using DataVenia.Modules.Users.Domain.Users;
using DataVenia.Modules.Users.Infrastructure.Client;
using DataVenia.Modules.Users.Infrastructure.ClientCompany;
using DataVenia.Modules.Users.Infrastructure.Company;
using DataVenia.Modules.Users.Infrastructure.IntermediateClasses;
using DataVenia.Modules.Users.Infrastructure.Lawyers;
using DataVenia.Modules.Users.Infrastructure.Oab;
using DataVenia.Modules.Users.Infrastructure.Office;
using DataVenia.Modules.Users.Infrastructure.SharedModels;
using DataVenia.Modules.Users.Infrastructure.SignUpTokens;
using DataVenia.Modules.Users.Infrastructure.UserGlobalRole;
using DataVenia.Modules.Users.Infrastructure.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using static System.Reflection.BindingFlags;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
using CompanyDomain = DataVenia.Modules.Users.Domain.Company.Company;
using ClientCompanyDomain = DataVenia.Modules.Users.Domain.ClientCompany.ClientCompany;
using OabDomain = DataVenia.Modules.Users.Domain.Oab.Oab;
using OfficeDomain = DataVenia.Modules.Users.Domain.Office.Office;
using UserGlobalRoleDomain = DataVenia.Modules.Users.Domain.Authorization.UserGlobalRole;
namespace DataVenia.Modules.Users.Infrastructure.Database;

public sealed class UsersDbContext(DbContextOptions<UsersDbContext> options) : DbContext(options), IUnitOfWork
{
    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        => base.SaveChangesAsync(cancellationToken);
    public DbSet<User> Users { get; set; }
    public DbSet<Lawyer> Lawyers { get; set; }
    public DbSet<OabDomain> Oabs { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<OfficeDomain> Offices { get; set;}
    public DbSet<OfficeUser> OfficeUsers { get; set; }
    public DbSet<UserGlobalRoleDomain> UserGlobalRoles { get; set; }
    public DbSet<ClientDomain> Clients { get; set; }
    public DbSet<CompanyDomain> Companies { get; set; }
    public DbSet<ClientCompanyDomain> ClientCompanies { get; set; }
    public DbSet<SignUpToken> SignUpTokens { get; set; }
    public DbSet<Domain.Outbox.Outbox> Outbox { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema(Schemas.User);

        modelBuilder.ApplyConfiguration(new UserConfiguration());
        modelBuilder.ApplyConfiguration(new LawyerConfiguration());
        modelBuilder.ApplyConfiguration(new RoleConfiguration());
        modelBuilder.ApplyConfiguration(new PermissionConfiguration());
        modelBuilder.ApplyConfiguration(new OfficeConfiguration());
        modelBuilder.ApplyConfiguration(new OfficeUserConfiguration());
        modelBuilder.ApplyConfiguration(new ContactConfiguration());
        modelBuilder.ApplyConfiguration(new AddressConfiguration());
        modelBuilder.ApplyConfiguration(new OabConfiguration());
        modelBuilder.ApplyConfiguration(new UserGlobalRoleConfiguration());
        modelBuilder.ApplyConfiguration(new ClientConfiguration());
        modelBuilder.ApplyConfiguration(new CompanyConfiguration());
        modelBuilder.ApplyConfiguration(new ClientCompanyConfiguration()); 
        modelBuilder.ApplyConfiguration(new SignUpTokenConfiguration());
    }
}
