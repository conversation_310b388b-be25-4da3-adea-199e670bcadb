﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using StatusDomain = DataVenia.Modules.Harvey.Domain.LawsuitStatus.LawsuitStatus;

namespace DataVenia.Modules.Harvey.Infrastructure.Status;

public sealed class LawsuitStatusConfiguration : IEntityTypeConfiguration<StatusDomain>
{
    public void Configure(EntityTypeBuilder<StatusDomain> builder)
    {
        builder.ToTable("lawsuit_status");

        builder.Property(x => x.Id);
        builder.HasIndex(x => x.Id).IsUnique();
        builder.HasKey(f => f.Id);

        builder.Property(f => f.DisplayName)
            .IsRequired()
            .HasMaxLength(128);
        builder.HasIndex(x => x.DisplayName).IsUnique();

        builder.Property(f => f.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        
        builder.HasData(
            StatusDomain.Pending,
            StatusDomain.InProgress,
            StatusDomain.Suspended,
            StatusDomain.AwaitingAppeal,
            StatusDomain.Completed,
            StatusDomain.Closed,
            StatusDomain.Cancelled,
            StatusDomain.AwaitingClient,
            StatusDomain.AwaitingDecision);
    }
}
