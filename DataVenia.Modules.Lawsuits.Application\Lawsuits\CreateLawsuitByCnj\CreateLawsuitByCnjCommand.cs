﻿using DataVenia.Common.Application.Messaging;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateLawsuitByCnj;

public sealed record CreateLawsuitByCnjCommand(
    Guid UserId,
    Guid OfficeId,
    string Cnj,
    string? Title,
    string? LawsuitTypeId,
    List<Guid> ResponsibleIds,
    string? Description,
    Guid? EvolvedFromCaseId,
    Guid? GroupingCaseId,
    bool MonitoringEnabled) : ICommandFr<Guid>;
