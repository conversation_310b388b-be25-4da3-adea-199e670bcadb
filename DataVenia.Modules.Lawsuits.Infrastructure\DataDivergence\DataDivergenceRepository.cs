﻿using DataVenia.Modules.Lawsuits.Application.DataDivergences;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using FluentResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Infrastructure.DataDivergence;

internal sealed class DataDivergenceRepository(LawsuitsDbContext context, ILogger<DataDivergenceRepository> logger): IDataDivergenceRepository
{
    public void Insert(Domain.DataDivergence.DataDivergence dataDivergence)
    {
        context.DataDivergences.Add(dataDivergence);
    }

    public async Task<Result<IReadOnlyCollection<Domain.DataDivergence.DataDivergence>>> GetLatestForEachInstanceByLawsuitIdAsync(Guid lawsuitId, CancellationToken cancellationToken = default)
    {
        try
        {
            var dataDivergences = await context.DataDivergences
                .Where(dd => dd.LawsuitId == lawsuitId)
                .GroupBy(dd => dd.InstanceId)
                .Select(g => g
                    .OrderByDescending(dd => dd.CreatedAt)
                    .First())
                .ToListAsync(cancellationToken);

            return Result.Ok<IReadOnlyCollection<Domain.DataDivergence.DataDivergence>>(dataDivergences);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting data divergences for lawsuit {LawsuitId}", lawsuitId);
            return Result.Fail<IReadOnlyCollection<Domain.DataDivergence.DataDivergence>>("Failed to retrieve data divergences");
        }
    }

    public async Task<Result<Domain.DataDivergence.DataDivergence>> GetByIdAsync(Guid divergenceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var dataDivergence = await context.DataDivergences
                .FirstOrDefaultAsync(dd => dd.Id == divergenceId, cancellationToken);

            if (dataDivergence == null)
            {
                return Result.Fail<Domain.DataDivergence.DataDivergence>("DataDivergence.Not.Found");
            }

            return Result.Ok(dataDivergence);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting data divergence {DivergenceId}", divergenceId);
            return Result.Fail<Domain.DataDivergence.DataDivergence>("Failed to retrieve data divergence");
        }
    }

    public void Update(Domain.DataDivergence.DataDivergence dataDivergence)
    {
        context.DataDivergences.Update(dataDivergence);
    }
}
