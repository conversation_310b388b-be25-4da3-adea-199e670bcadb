﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddDefaultValueForMonitoringHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "pk_outboxes",
                schema: "lawsuit",
                table: "outboxes");

            migrationBuilder.RenameTable(
                name: "outboxes",
                schema: "lawsuit",
                newName: "outbox",
                newSchema: "lawsuit");

            migrationBuilder.AlterColumn<string>(
                name: "monitoring_history",
                schema: "lawsuit",
                table: "lawsuit",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb",
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.AddPrimaryKey(
                name: "pk_outbox",
                schema: "lawsuit",
                table: "outbox",
                column: "id");

            migrationBuilder.CreateIndex(
                name: "ix_outbox_processed",
                schema: "lawsuit",
                table: "outbox",
                column: "processed");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "pk_outbox",
                schema: "lawsuit",
                table: "outbox");

            migrationBuilder.DropIndex(
                name: "ix_outbox_processed",
                schema: "lawsuit",
                table: "outbox");

            migrationBuilder.RenameTable(
                name: "outbox",
                schema: "lawsuit",
                newName: "outboxes",
                newSchema: "lawsuit");

            migrationBuilder.AlterColumn<string>(
                name: "monitoring_history",
                schema: "lawsuit",
                table: "lawsuit",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldDefaultValueSql: "'[]'::jsonb");

            migrationBuilder.AddPrimaryKey(
                name: "pk_outboxes",
                schema: "lawsuit",
                table: "outboxes",
                column: "id");
        }
    }
}
