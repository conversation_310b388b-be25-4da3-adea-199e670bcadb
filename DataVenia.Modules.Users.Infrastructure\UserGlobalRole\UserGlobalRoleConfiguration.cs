﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UserGlobalRoleDomain = DataVenia.Modules.Users.Domain.Authorization.UserGlobalRole;

namespace DataVenia.Modules.Users.Infrastructure.UserGlobalRole;
internal sealed class UserGlobalRoleConfiguration : IEntityTypeConfiguration<UserGlobalRoleDomain>
{
    public void Configure(EntityTypeBuilder<UserGlobalRoleDomain> builder)
    {
        builder.ToTable("user_global_role");

        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(ou => ou.Id);

        // Configure the relationship between UserGlobalRole and User
        builder.HasOne(ou => ou.User)
            .WithMany()
            .HasForeignKey(ou => ou.UserId);

        // Configure the relationship between UserGlobalRole and Role
        builder.HasOne(ou => ou.Role)
            .WithMany()
            .HasForeignKey(ou => ou.RoleName);

        builder.Property(ou => ou.RoleName)
            .HasMaxLength(50)
            .IsRequired();

        builder.HasIndex(ou => new { ou.UserId, ou.RoleName })
                .IsUnique()
                .HasDatabaseName("IX_UserGlobalRole_UserId_RoleName");
    }
}
