﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\DataVenia.Common.Application\DataVenia.Common.Application.csproj" />
        <ProjectReference Include="..\DataVenia.Common.Contracts\DataVenia.Common.Contracts.csproj" />
        <ProjectReference Include="..\DataVenia.Common.Domain\DataVenia.Common.Domain.csproj" />
        <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Domain\DataVenia.Modules.AssociationHub.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
        <PackageReference Include="FluentResults" Version="3.16.0" />
    </ItemGroup>

</Project>
