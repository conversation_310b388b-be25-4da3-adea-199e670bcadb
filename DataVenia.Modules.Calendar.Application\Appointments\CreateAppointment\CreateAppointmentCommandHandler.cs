﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Application.Abstractions.Data;
using DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointment;
using DataVenia.Modules.Calendar.Domain.Appointments;
using DataVenia.Modules.Calendar.Domain.Status;
using AppointmentDomain = DataVenia.Modules.Calendar.Domain.Appointments.Appointment;

namespace DataVenia.Modules.Calendar.Application.Appointment.CreateAppointment;

public sealed class CreateAppointmentCommandHandler(
    IUnitOfWork unitOfWork,
    IAppointmentRepository appointmentRepository,
    IOfficeLawyerFacade officeLawyerFacade) : ICommandHandler<CreateAppointmentCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateAppointmentCommand request, CancellationToken cancellationToken)
    {
        Result<Recurrence> recurrenceResult = request.Recurrence != null ? Recurrence.Create(
                request.Recurrence.Frequency,
                request.Recurrence.DaysOfWeek,
                request.Recurrence.DaysOfMonth,
                request.Recurrence.StartDate,
                request.Recurrence.EndDate,
                request.Recurrence.StartTime,
                request.Recurrence.EndTime).Value : Result.Failure<Recurrence>(new Error("Invalid.Recurrence", "Recurrence cannot be null", ErrorType.Validation));

        if (recurrenceResult.IsFailure)
            return Result.Failure<Guid>(recurrenceResult.Error);

        Result<AppointmentDomain> appointmentResult = AppointmentDomain.Create(
            request.Type,
            request.Name,
            request.Description,
            request.ResponsibleLawyerId,
            recurrenceResult.Value,
            request.OwnerLawyerId,
            request.OwnerOfficeId,
            request.ParticipantLawyersIds,
            request.Alerts,
            request.StatusId);

        if (appointmentResult.IsFailure)
            return Result.Failure<Guid>(appointmentResult.Error);

        #region valida se todos os participantes estão no mesmo office
        HashSet<Guid> allParticipants =
        [
            .. request.ParticipantLawyersIds,
            request.OwnerLawyerId,
            request.ResponsibleLawyerId,
        ];

        IReadOnlyCollection<OfficeLawyerDto> officeLawyers = await officeLawyerFacade.GetActiveByOfficeAndLawyers(request.officeId, allParticipants.ToList());

        if (officeLawyers.Count != allParticipants.Count)
            return Result.Failure<Guid>(AppointmentErrors.LaywerNotFound);
        #endregion

        appointmentRepository.Insert(appointmentResult.Value);

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return appointmentResult.Value.Id;
    }
}
