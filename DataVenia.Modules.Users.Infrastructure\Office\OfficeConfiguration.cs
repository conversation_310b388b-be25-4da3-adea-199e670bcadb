﻿using System.Reflection.Emit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DataVenia.Modules.Users.Domain.Lawyers;

namespace DataVenia.Modules.Users.Infrastructure.Office;
internal sealed class OfficeConfiguration : IEntityTypeConfiguration<Domain.Office.Office>
{
    public void Configure(EntityTypeBuilder<Domain.Office.Office> builder)
    {
        builder.ToTable("office");
        
        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(u => u.Id);

        builder
            .HasMany(lc => lc.Contacts)
            .WithOne()
            .<PERSON><PERSON><PERSON>ign<PERSON><PERSON>("OfficeId") 
            .OnDelete(DeleteBehavior.NoAction);  
        
        builder
            .HasMany(lc => lc.Addresses)
            .WithOne()
            .HasForeignKey("OfficeId")  
            .OnDelete(DeleteBehavior.NoAction);  
    }
}
