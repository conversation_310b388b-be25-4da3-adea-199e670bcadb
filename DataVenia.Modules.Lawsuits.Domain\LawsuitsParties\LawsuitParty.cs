﻿using DataVenia.Common.Domain;
using LawsuitDataDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData;

namespace DataVenia.Modules.Lawsuits.Domain.LawsuitParties;
public sealed class LawsuitParty : Entity
{
    public Guid Id { get; set; }
    public Guid LawsuitDataId { get; private set; }
    public LawsuitDataDomain LawsuitData { get; private set; }

    public Guid PartyId { get; private set; }

    public string PartyType { get; private set; }
    public bool IsClient { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private LawsuitParty() { }

    public static LawsuitParty Create(Guid lawsuitDataId, Guid partyId, string partyType, bool isClient)
    {
        var lawsuitParty = new LawsuitParty()
        {
            Id = Guid.NewGuid(),
            LawsuitDataId = lawsuitDataId,
            PartyId = partyId,
            PartyType = partyType,
            IsClient = isClient,
            CreatedAt = DateTime.UtcNow
        };

        return lawsuitParty;
    }

    public void Update(string partyType, bool isClient)
    {
        PartyType = partyType;
        IsClient = isClient;
        UpdatedAt = DateTime.UtcNow;
    }
}
