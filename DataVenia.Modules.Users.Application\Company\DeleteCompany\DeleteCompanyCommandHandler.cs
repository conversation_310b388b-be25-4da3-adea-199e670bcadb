﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Client.UpdateClient;
using DataVenia.Modules.Users.Application.Company.UpdateCompanyPatch;
using DataVenia.Modules.Users.Domain.Company;
using DataVenia.Modules.Users.Domain.Lawyers;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Company.DeleteCompany;

public class DeleteCompanyCommandHandler(ICompanyRepository companyRepository, IUnitOfWork unitOfWork, ILogger<UpdateCompanyPatchCommandHandler> logger) : ICommandHandler<DeleteCompanyCommand>
{
    public async Task<Result> Handle(DeleteCompanyCommand request, CancellationToken cancellationToken)
    {
        Domain.Company.Company? company = await companyRepository.GetSingleAsync(x => x.Id == request.CompanyId, cancellationToken);

        if (company is null)
        {
            logger.LogError("Company not found: {CompanyId}", request.CompanyId);
            return Result.Failure(LawyerErrors.NotFound(request.CompanyId));
        }

        company.SoftDelete();

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        } catch (Exception ex)
        {
            logger.LogError(ex, "Error soft deleting company in the database.");
            return Result.Failure<Guid>(Error.InternalServerError());
        }
        
        return Result.Success();
    }
}
