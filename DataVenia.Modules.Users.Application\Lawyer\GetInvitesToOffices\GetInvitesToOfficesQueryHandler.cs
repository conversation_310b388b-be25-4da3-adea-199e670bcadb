﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;

namespace DataVenia.Modules.Users.Application.Lawyers.GetInvitesToOffices;
public sealed class GetInvitesToOfficesQueryHandler(
    IOfficeUserRepository officeLawyerRepository)
    : IQueryHandler<GetInvitesToOfficesQuery, IReadOnlyCollection<OfficeUser?>>
{
    public async Task<Result<IReadOnlyCollection<OfficeUser?>>> Handle(GetInvitesToOfficesQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<OfficeUser?> invites = await officeLawyerRepository.GetManyByFilterAsync(lcl => lcl.UserId == request.lawyerId, cancellationToken);

        return Result.Success(invites);
    }
}
