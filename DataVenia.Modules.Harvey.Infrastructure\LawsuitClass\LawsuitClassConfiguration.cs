﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Harvey.Infrastructure.LawsuitClass;

internal sealed class LawsuitClassConfiguration : IEntityTypeConfiguration<Domain.LawsuitClass.LawsuitClass>
{
    public void Configure(EntityTypeBuilder<Domain.LawsuitClass.LawsuitClass> builder)
    {
        // Table Mapping
        builder.ToTable("lawsuit_class");

        // Primary Key
        builder.HasKey(c => c.Id);

        // Properties
        builder.Property(c => c.Type)
            .HasMaxLength(255);

        builder.Property(c => c.LegalProvision)
            .HasMaxLength(255);

        builder.Property(c => c.Article)
            .HasMaxLength(255);

        builder.Property(c => c.Acronym)
            .HasMaxLength(50)
            .IsRequired();
        builder.HasIndex(c => c.Acronym);

        builder.Property(c => c.OldAcronym)
            .HasMaxLength(50);

        builder.Property(c => c.ActivePole)
            .HasMaxLength(255);

        builder.Property(c => c.PassivePole)
            .HasMaxLength(255);

        builder.Property(c => c.Glossary)
            .HasColumnType("text");

        builder.Property(c => c.IsOwnNumbering)
            
            .IsRequired();

        builder.Property(c => c.IsFirstInstance)
            
            .IsRequired();

        builder.Property(c => c.IsSecondInstance)
            
            .IsRequired();

        builder.Property(c => c.JustEsJuizadoEs)
            
            .IsRequired();

        builder.Property(c => c.JustEsTurmas)
            
            .IsRequired();

        builder.Property(c => c.JustEs1grauMil)
            .HasColumnName("just_es_1_grau_mil")
            .IsRequired();

        builder.Property(c => c.JustEs2grauMil)
            .HasColumnName("just_es_2_grau_mil")
            .IsRequired();

        builder.Property(c => c.JustEsJuizadoEsFp)
            
            .IsRequired();

        builder.Property(c => c.JustTuEsUn)
            
            .IsRequired();

        builder.Property(c => c.JustFed1grau)
            .HasColumnName("just_fed_1_grau")
            .IsRequired();

        builder.Property(c => c.JustFed2grau)
            .HasColumnName("just_fed_2_grau")
            .IsRequired();

        builder.Property(c => c.JustFedJuizadoEs)
            
            .IsRequired();

        builder.Property(c => c.JustFedTurmas)
            
            .IsRequired();

        builder.Property(c => c.JustFedNacional)
            
            .IsRequired();

        builder.Property(c => c.JustFedRegional)
            
            .IsRequired();

        builder.Property(c => c.JustTrab1grau)
            .HasColumnName("just_trab_1_grau")
            .IsRequired();

        builder.Property(c => c.JustTrab2grau)
            .HasColumnName("just_trab_2_grau")
            .IsRequired();

        builder.Property(c => c.JustTrabTst)
            
            .IsRequired();

        builder.Property(c => c.JustTrabCsjt)
            
            .IsRequired();

        builder.Property(c => c.Stf)
            
            .IsRequired();

        builder.Property(c => c.Stj)
            
            .IsRequired();

        builder.Property(c => c.Cjf)
            
            .IsRequired();

        builder.Property(c => c.Cnj)
            
            .IsRequired();

        builder.Property(c => c.JustMilUniao1grau)
            .HasColumnName("just_mil_uniao_1_grau")
            .IsRequired();

        builder.Property(c => c.JustMilUniaoStm)
            .IsRequired();

        builder.Property(c => c.JustMilEst1grau)
            .HasColumnName("just_mil_est_1_grau")
            .IsRequired();

        builder.Property(c => c.JustMilEstTjm)
            .IsRequired();

        builder.Property(c => c.JustElei1grau)
            .HasColumnName("just_elei_1_grau")
            .IsRequired();

        builder.Property(c => c.JustElei2grau)
            .HasColumnName("just_elei_2_grau")
            .IsRequired();

        builder.Property(c => c.JustEleiTse)
            .IsRequired();

        builder.Property(c => c.IncludedBy)
            .HasMaxLength(255);

        builder.Property(c => c.UserIp)
            .HasMaxLength(50);

        builder.Property(c => c.UpdatedBy)
            .HasMaxLength(255);

        builder.Property(c => c.ProcedureId)
            .HasMaxLength(50);

        builder.Property(c => c.ProcedureOrigin)
            .HasMaxLength(50);

        builder.Property(c => c.IsCriminal)
            .HasMaxLength(1);

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
    }
}
