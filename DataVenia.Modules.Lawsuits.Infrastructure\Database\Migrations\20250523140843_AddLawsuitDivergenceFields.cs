﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddLawsuitDivergenceFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "data_divergences",
                schema: "lawsuit",
                table: "lawsuit",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");

            migrationBuilder.AddColumn<bool>(
                name: "is_first_time_sync_completed",
                schema: "lawsuit",
                table: "lawsuit",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "data_divergences",
                schema: "lawsuit",
                table: "lawsuit");

            migrationBuilder.DropColumn(
                name: "is_first_time_sync_completed",
                schema: "lawsuit",
                table: "lawsuit");
        }
    }
}
