﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class CreateDatabase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "harvey");

            migrationBuilder.CreateTable(
                name: "court_division",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_court_division", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "forum",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_forum", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "lawsuit_status",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_status", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "lawsuit_type",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_type", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "legal_category",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    order = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_legal_category", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "legal_instance",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    order = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_legal_instance", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "party_type",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    order = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_party_type", x => x.id);
                });

            migrationBuilder.InsertData(
                schema: "harvey",
                table: "lawsuit_status",
                columns: new[] { "id", "display_name" },
                values: new object[,]
                {
                    { "AwaitingAppeal", "Aguardando Apelação" },
                    { "AwaitingClient", "Aguardando cliente" },
                    { "AwaitingDecision", "Aguardando decisão" },
                    { "Cancelled", "Cancelado" },
                    { "Closed", "Fechado" },
                    { "Completed", "Completo" },
                    { "InProgress", "Em progresso" },
                    { "Pending", "Pendente" },
                    { "Suspended", "Suspendido" }
                });

            migrationBuilder.InsertData(
                schema: "harvey",
                table: "legal_instance",
                columns: new[] { "id", "display_name", "order" },
                values: new object[,]
                {
                    { "FirstInstance", "Primeira Instância", 1 },
                    { "Other", "Outras", 5 },
                    { "SecondInstance", "Segunda Instância", 2 },
                    { "STF", "Supremo Tribunal Federal", 4 },
                    { "STJ", "Superior Tribunal de Justiça", 3 }
                });

            migrationBuilder.InsertData(
                schema: "harvey",
                table: "party_type",
                columns: new[] { "id", "display_name", "order" },
                values: new object[,]
                {
                    { "Defendant", "Réu", 3 },
                    { "Lawyer", "Advogado", 1 },
                    { "Other", "Outro", 5 },
                    { "Plaintiff", "Autor", 2 },
                    { "Witness", "Testemunha", 4 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "court_division",
                schema: "harvey");

            migrationBuilder.DropTable(
                name: "forum",
                schema: "harvey");

            migrationBuilder.DropTable(
                name: "lawsuit_status",
                schema: "harvey");

            migrationBuilder.DropTable(
                name: "lawsuit_type",
                schema: "harvey");

            migrationBuilder.DropTable(
                name: "legal_category",
                schema: "harvey");

            migrationBuilder.DropTable(
                name: "legal_instance",
                schema: "harvey");

            migrationBuilder.DropTable(
                name: "party_type",
                schema: "harvey");
        }
    }
}
