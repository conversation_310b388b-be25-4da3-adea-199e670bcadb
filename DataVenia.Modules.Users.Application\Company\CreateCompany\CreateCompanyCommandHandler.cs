﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Company;
using Microsoft.Extensions.Logging;
namespace DataVenia.Modules.Users.Application.Company.CreateCompany;
internal sealed class CreateCompanyCommandHandler(
    ICompanyRepository companyRepository,
    IUnitOfWork unitOfWork,
    ILogger<CreateCompanyCommandHandler> logger)
    : ICommandHandler<CreateCompanyCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateCompanyCommand request, CancellationToken cancellationToken)
    {
        Result<Domain.Company.Company> companyResult = Domain.Company.Company.Create(
            request.Cnpj,
            request.Name,
            request.OfficeId,
            request.LawyerId
        );

        if (companyResult.IsFailure)
        {
            logger.LogError("Error creating company. {@Error}", companyResult.Error);
            return Result.Failure<Guid>(companyResult.Error);
        }

        companyRepository.Insert(companyResult.Value);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving company to database: {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return companyResult.Value.Id;
    }
}
