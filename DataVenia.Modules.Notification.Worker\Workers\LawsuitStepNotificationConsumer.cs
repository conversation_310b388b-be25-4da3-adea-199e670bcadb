using DataVenia.Common.Contracts.Events.Notification;
using DataVenia.Modules.Notification.Application.Factories;
using DataVenia.Modules.Notification.Domain.Enums;
using DataVenia.Modules.Notification.Worker.Helpers;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Notification.Worker.Workers;

public sealed class LawsuitStepNotificationConsumer(
    NotificationStrategyFactory strategyFactory,
    ILogger<LawsuitStepNotificationConsumer> logger,
    IConfiguration configuration) : IConsumer<LawsuitStepsUpdate>
{
    private readonly string _companyLogo = configuration["Notification:CompanyLogo"] ?? throw new ArgumentException(nameof(_companyLogo));
    private readonly string _companyName = configuration["Notification:CompanyName"] ?? throw new ArgumentException(nameof(_companyName));
    private const string HtmlName = "steps-email.html";
    public async Task Consume(ConsumeContext<LawsuitStepsUpdate> context)
    {
        using var _ = logger.BeginScope("LawsuitStepNotificationConsumer");
        context = context ?? throw new ArgumentNullException(nameof(context));

        var notificationEvent = context.Message;

        var strategy = strategyFactory.Create(NotificationEventType.Email);

        if (strategy != null)
        {
            var subject = $"Novos Andamentos no Processo {notificationEvent.Cnj} – Atualização Disponível";

            foreach (var lawyer in notificationEvent.Lawyers)
            {
                var mailMessage =
                    MailHelper.CreateMailMessage(HtmlName, new
                    {
                        COMPANY_NAME = _companyName,
                        COMPANY_LOGO = _companyLogo,
                        CNJ = notificationEvent.Cnj,
                        LAWSUIT_COUNTER = notificationEvent.QtdNewSteps,
                        LAWYER = lawyer.Name,
                        MOVEMENTS_URL = lawyer.LawsuitStepsUrl.ToString()
                    });

                await strategy.SendNotificationAsync(mailMessage, lawyer.Email,
                    subject).ConfigureAwait(false);

                logger.LogTrace("Processed mail notification for {Recipient}", lawyer.Email);
            }
        }
    }
}
