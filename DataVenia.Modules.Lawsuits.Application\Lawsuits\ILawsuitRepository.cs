﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits;
public interface ILawsuitRepository
{
    Task<Lawsuit?> GetLawsuitByIdAsync(Guid lawsuitId, CancellationToken cancellationToken = default);
    Task<FluentResults.Result<IReadOnlyCollection<Lawsuit>>> GetLawsuitsByCnjAsync(string cnj, CancellationToken cancellationToken = default);
    Task<Result<IReadOnlyCollection<BaseLawsuitResponse>>> GetLawsuitsAsync(Guid userId, Guid officeId, Guid? lawsuitId = null, CancellationToken cancellationToken = default);
    void Insert(Lawsuit lawsuit);
    void Update(Lawsuit lawsuit);
}

