﻿// <auto-generated />
using System;
using DataVenia.Modules.Calendar.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.Calendar.Infrastructure.Database.Migrations
{
    [DbContext(typeof(CalendarDbContext))]
    [Migration("20250205030811_CreateDatabase")]
    partial class CreateDatabase
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("calendar")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DataVenia.Modules.Calendar.Domain.AppointmentParticipant.AppointmentParticipant", b =>
                {
                    b.Property<Guid>("AppointmentId")
                        .HasColumnType("uuid")
                        .HasColumnName("appointment_id");

                    b.Property<Guid>("LawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawyer_id");

                    b.HasKey("AppointmentId", "LawyerId")
                        .HasName("pk_appointment_participant");

                    b.ToTable("appointment_participant", "calendar");
                });

            modelBuilder.Entity("DataVenia.Modules.Calendar.Domain.Appointments.Appointment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Alerts")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("Alerts");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OwnerLawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("owner_lawyer_id");

                    b.Property<Guid>("OwnerOfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("owner_office_id");

                    b.Property<Guid?>("RecurrenceId")
                        .HasColumnType("uuid")
                        .HasColumnName("recurrence_id");

                    b.Property<Guid>("ResponsibleLawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("responsible_lawyer_id");

                    b.Property<Guid?>("StatusId")
                        .HasColumnType("uuid")
                        .HasColumnName("status_id");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_appointment");

                    b.HasIndex("RecurrenceId")
                        .HasDatabaseName("ix_appointment_recurrence_id");

                    b.HasIndex("StatusId")
                        .HasDatabaseName("ix_appointment_status_id");

                    b.ToTable("appointment", "calendar");
                });

            modelBuilder.Entity("DataVenia.Modules.Calendar.Domain.Appointments.Recurrence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateOnly>("EndDate")
                        .HasColumnType("date")
                        .HasColumnName("end_date");

                    b.Property<TimeOnly>("EndTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("end_time");

                    b.Property<string>("Frequency")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("frequency");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date")
                        .HasColumnName("start_date");

                    b.Property<TimeOnly>("StartTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("start_time");

                    b.HasKey("Id")
                        .HasName("pk_recurrence");

                    b.ToTable("recurrence", "calendar");
                });

            modelBuilder.Entity("DataVenia.Modules.Calendar.Domain.Status.Status", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("description");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("display_name");

                    b.Property<Guid>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.Property<int>("OrderIndex")
                        .HasColumnType("integer")
                        .HasColumnName("order_index");

                    b.HasKey("Id")
                        .HasName("pk_status");

                    b.ToTable("status", "calendar");
                });

            modelBuilder.Entity("DataVenia.Modules.Calendar.Domain.AppointmentParticipant.AppointmentParticipant", b =>
                {
                    b.HasOne("DataVenia.Modules.Calendar.Domain.Appointments.Appointment", null)
                        .WithMany("Participants")
                        .HasForeignKey("AppointmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_appointment_participant_appointment_appointment_id");
                });

            modelBuilder.Entity("DataVenia.Modules.Calendar.Domain.Appointments.Appointment", b =>
                {
                    b.HasOne("DataVenia.Modules.Calendar.Domain.Appointments.Recurrence", "Recurrence")
                        .WithMany()
                        .HasForeignKey("RecurrenceId")
                        .HasConstraintName("fk_appointment_recurrence_recurrence_id");

                    b.HasOne("DataVenia.Modules.Calendar.Domain.Status.Status", "Status")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_appointment_statuses_status_id");

                    b.Navigation("Recurrence");

                    b.Navigation("Status");
                });

            modelBuilder.Entity("DataVenia.Modules.Calendar.Domain.Appointments.Appointment", b =>
                {
                    b.Navigation("Participants");
                });
#pragma warning restore 612, 618
        }
    }
}
