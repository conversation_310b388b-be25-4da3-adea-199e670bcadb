﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Users.Infrastructure.Database;
public static class ContextExtensions
{
    public static async Task<T?> GetSingleByFilterAsync<T>(this UsersDbContext context, Expression<Func<T, bool>> filter, CancellationToken cancellation = default) where T : class
    {
        return await context.Set<T>().SingleOrDefaultAsync(filter, cancellation);
    }

    public static async Task<IReadOnlyCollection<T?>> GetManyByFilterAsync<T>(this UsersDbContext context, Expression<Func<T, bool>> filter, CancellationToken cancellationToken = default) where T : class
    {
        return await context.Set<T>().Where(filter).ToListAsync(cancellationToken);
    } 
}
