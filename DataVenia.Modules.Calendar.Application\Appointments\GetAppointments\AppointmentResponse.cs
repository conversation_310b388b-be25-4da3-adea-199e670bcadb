﻿using DataVenia.Modules.Calendar.Domain.AppointmentParticipant;
using DataVenia.Modules.Calendar.Domain.Appointments;
using StatusDomain = DataVenia.Modules.Calendar.Domain.Status.Status;

namespace DataVenia.Modules.Calendar.Application.Appointments.GetAppointment;
public sealed record AppointmentResponse(
    Guid Id,
    string Type,
    string Name,
    string Description,
    Guid ResponsibleLawyerId,
    Recurrence? Recurrence,
    Guid OwnerLawyerId,
    IReadOnlyCollection<AppointmentParticipant> ParticipantLawyersIds,
    IReadOnlyCollection<TimeSpan> Alerts,
    StatusDomain Status
);
