﻿using DataVenia.Modules.Lawsuits.Domain.CasesData;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.CasesData;

public class CaseDataConfiguration : IEntityTypeConfiguration<CaseData>
{
    public void Configure(EntityTypeBuilder<CaseData> builder)
    {
        builder.ToTable("case_data");

        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.HasKey(ld => ld.Id);

        builder.Property(ld => ld.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ld => ld.Description)
            .HasMaxLength(500);

        builder.Property(ld => ld.Observations)
            .HasMaxLength(500);

        builder.HasOne(l => l.Folder)
            .WithMany()
            .HasForeignKey(l => l.FolderId)
            .OnDelete(DeleteBehavior.Restrict);

        // Relacionamento com CaseResponsibles (One-to-Many)
        builder.HasMany(l => l.Responsibles)
            .WithOne(lp => lp.CaseData)
            .HasForeignKey(ld => ld.CaseDataId)
            .OnDelete(DeleteBehavior.NoAction);

        // Relacionamento com CaseParty (One-to-Many)
        builder.HasMany(l => l.CaseParties)
            .WithOne(lp => lp.CaseData)
            .HasForeignKey(ld => ld.CaseDataId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
