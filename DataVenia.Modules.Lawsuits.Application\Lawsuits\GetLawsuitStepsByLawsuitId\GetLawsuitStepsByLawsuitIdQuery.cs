﻿using DataVenia.Common.Application.Messaging;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitStepsByCnj;


public sealed record GetLawsuitStepsByLawsuitIdQuery(Guid UserId, Guid OfficeId, Guid LawsuitId, string? Instance = null) : IQueryFr<IReadOnlyCollection<LawsuitStepsResponse>>;

public sealed record LawsuitStepsResponse(Guid Id, string Cnj, string? Title, string? Description, DateTime CreatedAt, DateTime? OccurredAt);
