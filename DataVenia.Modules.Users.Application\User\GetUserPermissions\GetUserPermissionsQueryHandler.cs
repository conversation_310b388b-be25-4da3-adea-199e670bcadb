﻿using System.Data.Common;
using Dapper;
using DataVenia.Common.Application.Authorization;
using DataVenia.Common.Application.Data;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Users;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Users.GetUserPermissions;

internal sealed class GetUserPermissionsQueryHandler(
        IDbConnectionFactory dbConnectionFactory,
        ILogger<GetUserPermissionsQueryHandler> logger)
    : IQueryHandler<GetUserPermissionsQuery, PermissionsResponse>
{
    public async Task<Result<PermissionsResponse>> Handle(
        GetUserPermissionsQuery request,
        CancellationToken cancellationToken)
    {
        await using DbConnection connection = await dbConnectionFactory.OpenConnectionAsync();

        const string sql =
            $"""
             SELECT DISTINCT
                 u.id AS UserId,
                 rp.permission_code AS "Permission"
             FROM "user"."user" u
             JOIN "user".office_user ou ON ou.user_id = u.id AND ou.invitation_status = 'ACTIVE'
             JOIN "user".role_permission rp ON rp.role_name = ou.role_name
             WHERE ou.user_id = @UserId AND ou.office_id = @OfficeId
             """;

        var parameters = new { request.UserId, request.OfficeId };

        try
        {
            List<UserPermission> permissions = (await connection.QueryAsync<UserPermission>(sql, parameters)).AsList();

            if (permissions.Count == 0)
            {
                return Result.Failure<PermissionsResponse>(UserErrors.NotFound(request.UserId));
            }

            return new PermissionsResponse(permissions[0].UserId, permissions.Select(p => p.Permission).ToHashSet());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting user permissions");
            return new PermissionsResponse(Guid.NewGuid(), new HashSet<string>());
        }
    }

    internal sealed class UserPermission
    {
        internal Guid UserId { get; init; }

        internal string Permission { get; init; }
    }
}
