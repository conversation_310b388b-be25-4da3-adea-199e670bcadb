﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Users;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.User.AcceptTermsAndConditions;

public class UpdateUserPatchCommandHandler(
    IUserRepository userRepository,
    IUnitOfWork unitOfWork,
    ILogger<UpdateUserPatchCommandHandler> logger) : ICommandHandler<UpdateUserPatchCommand>
{
    public async Task<Result> Handle(UpdateUserPatchCommand request, CancellationToken cancellationToken)
    {
        Domain.Users.User? user = null;
        try
        {
            user = await userRepository.GetAsync(x => x.Id == request.JwtUserId, cancellationToken);
        } catch(Exception ex)
        {
            logger.LogError(ex, "Error getting user. {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        if (user == null)
            return Result.Failure(new Error("User.Not.Found", "User not found", ErrorType.Conflict));
        
        user.UpdatePartial(request.AcceptedAt);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, nameof(UpdateUserPatchCommandHandler));
            return Result.Failure(new Error("Internal.Server.Error", "Something weird happened", ErrorType.InternalServerError));
        }
        
        return Result.Success();
    }
}
