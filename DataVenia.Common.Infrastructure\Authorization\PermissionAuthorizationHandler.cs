﻿using DataVenia.Common.Application.Authorization;
using DataVenia.Common.Application.Exceptions;
using DataVenia.Common.Infrastructure.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.Infrastructure.Authorization;

internal sealed class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IPermissionService _permissionService;
    private readonly ILogger<PermissionAuthorizationHandler> _logger;

    public PermissionAuthorizationHandler(
        IHttpContextAccessor httpContextAccessor,
        IPermissionService permissionService,
        ILogger<PermissionAuthorizationHandler> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _permissionService = permissionService;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        HttpContext? httpContext = _httpContextAccessor.HttpContext;
        if (httpContext == null)
            return;

        Guid userId;

        try
        {
            userId = context.User.GetUserId();
        }
        catch (DataVeniaException ex)
        {
            _logger.LogError(ex, "Error getting user id");
            return;
        }

        if (requirement.IsGlobalPermission())
        {
            Domain.Result<PermissionsResponse> globalPermissionResult = await _permissionService.GetUserGlobalPermissionsAsync(userId);

            if (globalPermissionResult.IsFailure)
                return;

            if (globalPermissionResult.Value.Permissions.Contains(requirement.Permission))
                context.Succeed(requirement);
        }
        else
        {
            object? officeIdValue = httpContext.Request.RouteValues["officeId"];
            if (officeIdValue == null || !Guid.TryParse(officeIdValue.ToString(), out Guid officeId))
                return;

            Domain.Result<PermissionsResponse> result = await _permissionService.GetUserPermissionsAsync(userId, officeId);

            if (result.IsFailure)
                return;

            HashSet<string> permissions = result.Value.Permissions;

            if (permissions.Contains(requirement.Permission))
                context.Succeed(requirement);
        }
    }
}
