using DataVenia.Modules.LawsuitSync.Domain.Entities;
using FluentResults;

namespace DataVenia.Modules.LawsuitSync.Domain.Interfaces;

public interface ILawsuitMonitoringRepository
{
    Task<Result> AddLawsuitMonitoringConfiguration(LawsuitMonitoringConfiguration lawsuitMonitoringConfiguration);
    Task<Result<List<string>>> UpdateLawsuitMonitoringConfiguration(LawsuitMonitoringConfiguration updatedConfiguration);
    Task<Result> AddLawsuitMonitoringConfigurationInBatch(IEnumerable<LawsuitMonitoringConfiguration> lawsuitMonitoringConfiguration);
    Task<Result> AddLawsuitEventsHistory(LawsuitEventsHistory lawsuitEventsHistory);
    Task<Result<bool>> ExistsLawsuitMonitoring(string cnj);
    Task<Result<List<LawsuitMonitoringConfiguration>>> GetLawsuitMonitoring(string cnj);
    Task<Result> DeleteLawsuitMonitoringConfiguration(string cnj);
}
