### Create Keycloak user
@BEARER_TOKEN = variable value
POST /admin/realms/datavenia/users HTTP/1.1
Host: localhost:18080
Content-Type: application/json
Authorization: Bearer {{BEARER_TOKEN}}

{
  "enabled": true,
  "username": "datavenia",
  "email": "<EMAIL>",
  "firstName": "Data",
  "lastName": "Venia",
  "emailVerified": "True",
  "credentials": [
    {
      "type": "password",
      "value": "123",
      "temporary": false
    }
  ]
}
