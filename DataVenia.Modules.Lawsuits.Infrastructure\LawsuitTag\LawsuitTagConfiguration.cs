﻿//using Microsoft.EntityFrameworkCore;
//using LawsuitTagDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitTag.LawsuitTag;
//namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitTag;
//public sealed class LawsuitTagConfiguration : IEntityTypeConfiguration<LawsuitTagDomain>
//{
//    public void Configure(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder<LawsuitTagDomain> builder)
//    {
//        builder.ToTable("lawsuit_tag");

//        builder.HasKey(lt => new { lt.LawsuitId, lt.TagId });

//        // Relacionamento com Lawsuit
//        builder.HasOne(lt => lt.Lawsuit)
//               .WithMany(l => l.LawsuitTags)
//               .HasForeignKey(lt => lt.LawsuitId)
//               .OnDelete(DeleteBehavior.NoAction);

//        // Relacionamento com Tag
//        builder.HasOne(lt => lt.Tag)
//               .WithMany()
//               .HasForeignKey(lt => lt.TagId)
//               .OnDelete(DeleteBehavior.NoAction);
//    }
//}
