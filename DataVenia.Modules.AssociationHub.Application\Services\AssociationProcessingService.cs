using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;
using DataVenia.Modules.AssociationHub.Domain.Services;
using FluentResults;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.AssociationHub.Application.Services;

public interface IAssociationProcessingService
{
    Task<Result> ProcessAssociationRequestAsync(AssociationRequest associationRequest, CancellationToken cancellationToken = default);
}

public sealed class AssociationProcessingService(
    IEntityHttpService entityHttpService,
    EntityConfigurationRegistry configurationRegistry,
    ILogger<AssociationProcessingService> logger) : IAssociationProcessingService
{
    public async Task<Result> ProcessAssociationRequestAsync(AssociationRequest associationRequest, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Processing association request {RequestId} for {EntityType}:{EntityId} -> {TargetEntityType}:{TargetEntityId}",
                associationRequest.Id,
                associationRequest.EntityType,
                associationRequest.EntityId,
                associationRequest.TargetEntityType,
                associationRequest.TargetEntityId);

            // Get current entity data
            var entityResult = await entityHttpService.GetEntityAsync(
                associationRequest.EntityType,
                associationRequest.EntityId,
                cancellationToken);

            if (entityResult.IsFailed)
            {
                logger.LogWarning("Failed to get entity {EntityType}:{EntityId}: {Error}",
                    associationRequest.EntityType,
                    associationRequest.EntityId,
                    entityResult.Errors.FirstOrDefault()?.Message);
                return Result.Fail(entityResult.Errors);
            }

            var entityData = entityResult.Value;
            var associations = new Dictionary<string, List<Guid>>(entityData.Associations);

            // Apply the association operation
            var targetEntityType = associationRequest.TargetEntityType;
            if (!associations.ContainsKey(targetEntityType))
            {
                associations[targetEntityType] = new List<Guid>();
            }

            var targetList = associations[targetEntityType];

            switch (associationRequest.Operation)
            {
                case AssociationOperation.Add:
                    if (!targetList.Contains(associationRequest.TargetEntityId))
                    {
                        targetList.Add(associationRequest.TargetEntityId);
                        logger.LogDebug("Added association {TargetEntityType}:{TargetEntityId} to {EntityType}:{EntityId}",
                            targetEntityType,
                            associationRequest.TargetEntityId,
                            associationRequest.EntityType,
                            associationRequest.EntityId);
                    }
                    break;

                case AssociationOperation.Remove:
                    if (targetList.Remove(associationRequest.TargetEntityId))
                    {
                        logger.LogDebug("Removed association {TargetEntityType}:{TargetEntityId} from {EntityType}:{EntityId}",
                            targetEntityType,
                            associationRequest.TargetEntityId,
                            associationRequest.EntityType,
                            associationRequest.EntityId);
                    }
                    break;
            }

            // Update the entity with new associations
            var updateResult = await entityHttpService.UpdateEntityAssociationsAsync(
                associationRequest.EntityType,
                associationRequest.EntityId,
                associations,
                cancellationToken);

            if (updateResult.IsFailed)
            {
                logger.LogWarning("Failed to update entity {EntityType}:{EntityId}: {Error}",
                    associationRequest.EntityType,
                    associationRequest.EntityId,
                    updateResult.Errors.FirstOrDefault()?.Message);
                return updateResult;
            }

            logger.LogInformation("Successfully processed association request {RequestId}",
                associationRequest.Id);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error processing association request {RequestId}",
                associationRequest.Id);
            return Result.Fail(new Error("An unexpected error occurred while processing the association request")
                .WithMetadata("StatusCode", 500));
        }
    }
}
