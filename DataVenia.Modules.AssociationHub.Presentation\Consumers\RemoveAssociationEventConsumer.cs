using DataVenia.Common.Contracts.Events.AssociationHub;
using DataVenia.Modules.AssociationHub.Application.Associations.RemoveAssociation;
using MassTransit;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.AssociationHub.Presentation.Consumers;

public sealed class RemoveAssociationEventConsumer(
    [FromServices] ISender sender,
    ILogger<RemoveAssociationEventConsumer> logger) : IConsumer<RemoveAssociationEvent>
{
    public async Task Consume(ConsumeContext<RemoveAssociationEvent> context)
    {
        var message = context.Message;

        logger.LogInformation("Received RemoveAssociationEvent for {EntityType}:{EntityId} with {AssociationCount} association types",
            message.EntityType,
            message.EntityId,
            message.Associations.Count);

        // Convert the event associations to command associations
        var commandAssociations = message.Associations.Select(a => new AssociationTarget(
            a.TargetEntityType,
            a.TargetEntityIds.AsReadOnly())).ToList();

        var command = new RemoveAssociationCommand(
            message.EntityType,
            message.EntityId,
            commandAssociations);

        var result = await sender.Send(command);

        if (result.IsFailed)
        {
            var errorMessage = string.Join(", ", result.Errors.Select(e => e.Message));
            logger.LogError("Failed to remove associations: {Error}", errorMessage);
            throw new InvalidOperationException($"Failed to remove associations: {errorMessage}");
        }

        logger.LogInformation("Successfully processed RemoveAssociationEvent for {EntityType}:{EntityId}",
            message.EntityType,
            message.EntityId);
    }
}
