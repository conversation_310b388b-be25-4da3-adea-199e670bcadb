using System.Globalization;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.AssociationHub.Application.Associations.CreateAssociation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.AssociationHub.Presentation.Associations;

internal sealed class CreateAssociation : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/associations", async ([FromBody] CreateAssociationRequest request, [FromServices] ISender sender) =>
        {
            var commandAssociations = request.Associations.Select(a => new AssociationTarget(
                a.TargetEntityType,
                a.TargetEntityIds)).ToList();

            var result = await sender.Send(new CreateAssociationCommand(
                request.EntityType,
                request.EntityId,
                commandAssociations));

            if (result.IsSuccess)
                return Results.NoContent();
            else
            {
                var error = result.Errors.FirstOrDefault();
                int statusCode = error?.Metadata != null && error.Metadata.TryGetValue("StatusCode", out var codeObj)
                    ? Convert.ToInt32(codeObj, CultureInfo.InvariantCulture)
                    : 500;

                return Results.Problem(detail: string.Join("; ", result.Errors.Select(e => e.Message)), statusCode: statusCode);
            }
        })
        .RequireAuthorization("system:associations:create")
        .WithTags(Tags.Associations);
    }
private sealed record CreateAssociationRequest
{
    public string EntityType { get; init; } = string.Empty;
    public Guid EntityId { get; init; }
    public IReadOnlyCollection<AssociationTargetRequest> Associations { get; init; } = [];
}

private sealed record AssociationTargetRequest
{
    public string TargetEntityType { get; init; } = string.Empty;
    public IReadOnlyCollection<Guid> TargetEntityIds { get; init; } = [];
}
}

