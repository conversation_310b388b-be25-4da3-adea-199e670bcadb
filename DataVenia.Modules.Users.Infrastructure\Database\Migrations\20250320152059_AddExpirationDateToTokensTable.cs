﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Users.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddExpirationDateToTokensTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "expiration_date",
                schema: "user",
                table: "sign_up_token",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "type",
                schema: "user",
                table: "sign_up_token",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "expiration_date",
                schema: "user",
                table: "sign_up_token");

            migrationBuilder.DropColumn(
                name: "type",
                schema: "user",
                table: "sign_up_token");
        }
    }
}
