﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using OfficeDomain = DataVenia.Modules.Users.Domain.Office.Office;
using DataVenia.Modules.Users.Domain.Office;
using Microsoft.Extensions.Logging;
using DataVenia.Modules.Users.Domain.Authorization;
using LawyerDomain = DataVenia.Modules.Users.Domain.Lawyers.Lawyer;

namespace DataVenia.Modules.Users.Application.Office.InviteLawyerToOffice;
public sealed class InviteLawyerToOfficeCommandHandler(
    IOfficeUserRepository officeUserRepository,
    ILawyerRepository lawyerRepository,
    IOfficeRepository officeRepository,
    IUnitOfWork unitOfWork,
    ILogger<InviteLawyerToOfficeCommandHandler> logger) : ICommandHandler<InviteLawyerToOfficeCommand>
{
    public async Task<Result> Handle(InviteLawyerToOfficeCommand request, CancellationToken cancellationToken)
    {
        // client tem a permissão?
        // verificado no endpoint

        // lawyer a ser chamado existe?
        LawyerDomain? lawyerToBeAdded = null;

        try
        {
            lawyerToBeAdded = await lawyerRepository.GetByEmailAsync(request.email, cancellationToken);
        } catch(Exception ex)
        {
            logger.LogError(ex, "Error getting lawyer by email. {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        if (lawyerToBeAdded == null)
        {
            logger.LogWarning("Invited lawyer does not exist. {Email}", request.email);
            return Result.Failure(OfficeUserErrors.UserUnauthorizedToInvite);
        }

        // office a ser vinculado existe?
        OfficeDomain? existingOffice = await officeRepository.GetAsync(lc => lc.Id == request.officeId, cancellationToken);

        if (existingOffice == null)
        {
            logger.LogWarning("Office does not exist. {OfficeId}", request.officeId);
            return Result.Failure(OfficeUserErrors.UserUnauthorizedToInvite);
        }

        // lawyer já está vinculado a office?
        OfficeUser? existingOfficeLawyer = await officeUserRepository.GetSingleByFilterAsync(lcl => lcl.UserId == lawyerToBeAdded.Id && lcl.OfficeId == request.officeId, cancellationToken);

        if (existingOfficeLawyer != null)
        {
            logger.LogWarning("Lawyer is already invited to the office, regardless of the status. {@ExistingOfficeLawyer}", existingOfficeLawyer);
            return Result.Failure(OfficeUserErrors.UserUnauthorizedToInvite);
        }

        Result<Role?> memberRole = null;
        try
        {
            memberRole = await lawyerRepository.GetRoleByNameAsync(Role.OfficeMember.Name, cancellationToken);
        } catch(Exception ex)
        {
            logger.LogError(ex, "Error getting role by name. {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        if (memberRole.IsFailure)
        {
            logger.LogError("It wasn't possible to find the OfficeMember role in the Database {MemberRoleFailure}", memberRole.Error);
            return Result.Failure<Guid>(memberRole.Error);
        }

        // cria vinculo
        var officeLawyer = OfficeUser.Create(request.officeId, lawyerToBeAdded.Id, request.adminId, memberRole.Value.Name);

        officeUserRepository.Insert(officeLawyer);
        
        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while saving the officeLawyer entity. {@OfficeLawyer}", officeLawyer);
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return Result.Success();
    }
}
