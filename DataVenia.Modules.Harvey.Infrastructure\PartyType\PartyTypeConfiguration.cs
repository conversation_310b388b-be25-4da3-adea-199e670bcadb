﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using PartyTypeDomain = DataVenia.Modules.Harvey.Domain.PartyType.PartyType;

namespace DataVenia.Modules.Harvey.Infrastructure.PartyType;
public sealed class PartyTypeConfiguration : IEntityTypeConfiguration<PartyTypeDomain>
{
    public void Configure(EntityTypeBuilder<PartyTypeDomain> builder)
    {
        // Table Mapping
        builder.ToTable("party_type");

        // Primary Key
        builder.HasKey(li => li.Id);

        builder.Property(li => li.DisplayName)
               .IsRequired()
               .HasMaxLength(64);

        builder.Property(li => li.Order)
               .IsRequired();

        builder.Property(f => f.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        
        // Seed Data
        builder.HasData(
            PartyTypeDomain.Lawyer,
            PartyTypeDomain.Defendant,
            PartyTypeDomain.Plaintiff,
            PartyTypeDomain.Witness,
            PartyTypeDomain.Other
        );
    }
}
