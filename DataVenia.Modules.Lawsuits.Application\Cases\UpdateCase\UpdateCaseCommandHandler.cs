﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.CasesData;
using Microsoft.Extensions.Logging;

//using LawsuitPartyDomain = DataVenia.Modules.Lawsuits.Domain.IntermediateClasses.LawsuitParty;
namespace DataVenia.Modules.Lawsuits.Application.Cases.UpdateCase;
public sealed class UpdateCaseCommandHandler(
    ICaseRepository caseRepository,
    ICaseDataRepository caseDataRepository,
    //ILawsuitPartyRepository lawsuitPartyRepository,
    IOfficeLawyerFacade officeLawyerFacade,
    IUnitOfWork unitOfWork,
    ILogger<UpdateCaseCommandHandler> logger) : ICommandHandler<UpdateCaseCommand>
{
    public async Task<Result> Handle(UpdateCaseCommand request, CancellationToken cancellationToken)
    {
        // Validate lawsuit existence
        Case? @case = await caseRepository.GetCaseByIdAsync(request.CaseId, cancellationToken);
        if (@case == null)
            return Result.Failure(Error.NotFound("Case.Not.Found", "The specified lawsuit does not exist."));
        
        if (!request.ResponsibleIds.Any())
            return Result.Failure(new Error("No.Responsible", "You must assign at least one responsible for the lawsuit", ErrorType.Validation));

        // Validate responsible users
        IReadOnlyCollection<OfficeLawyerDto> responsibles = await officeLawyerFacade.GetActiveByOfficeAndLawyers(request.OfficeId, request.ResponsibleIds);
        if (responsibles.Count != request.ResponsibleIds.Count)
            return Result.Failure(Error.Conflict("Not.Authorized", "There are nonexistent users being attached to the lawsuit"));

        // Create new lawsuitData record
        var newCaseData = CaseData.Create(
        request.Title,
        @case.Id,
        request.FolderId,
        request.CauseValue,
        request.ConvictionValue,
        request.Description,
        request.Observations,
        request.Access,
        // request.Parties,
        request.ResponsibleIds
        );

        caseDataRepository.Insert(newCaseData);

        // Save changes
        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, nameof(UpdateCaseCommandHandler));
            return Result.Failure(new Error("Internal.Server.Error", "Something weird happened.", ErrorType.InternalServerError));
        }

        return Result.Success();
    }
}
