using DataVenia.Common.Contracts.Harvey;
using DataVenia.Modules.Harvey.Domain.Forum;

namespace DataVenia.Modules.Harvey.Infrastructure.Facades;

public sealed class ForumFacade(IForumRepository forumRepository) : IForumFacade
{
    public async Task<bool> ExistsAsync(string forumId, CancellationToken cancellationToken = default)
    {
        var forum = await forumRepository.GetByIdAsync(forumId, cancellationToken);
        return forum != null;
    }
}
