﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Users.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemoveCascadeRelationsFromTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_address_offices_office_id",
                schema: "user",
                table: "address");

            migrationBuilder.DropForeignKey(
                name: "fk_contact_lawyers_lawyer_id",
                schema: "user",
                table: "contact");

            migrationBuilder.DropForeignKey(
                name: "fk_contact_offices_office_id",
                schema: "user",
                table: "contact");

            migrationBuilder.DropForeignKey(
                name: "fk_oab_lawyers_lawyer_id",
                schema: "user",
                table: "oab");

            migrationBuilder.AddForeignKey(
                name: "fk_address_offices_office_id",
                schema: "user",
                table: "address",
                column: "office_id",
                principalSchema: "user",
                principalTable: "office",
                principalColumn: "id");

            migrationBuilder.AddForeign<PERSON>ey(
                name: "fk_contact_lawyers_lawyer_id",
                schema: "user",
                table: "contact",
                column: "lawyer_id",
                principalSchema: "user",
                principalTable: "user",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_contact_offices_office_id",
                schema: "user",
                table: "contact",
                column: "office_id",
                principalSchema: "user",
                principalTable: "office",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_oab_lawyers_lawyer_id",
                schema: "user",
                table: "oab",
                column: "lawyer_id",
                principalSchema: "user",
                principalTable: "user",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_address_offices_office_id",
                schema: "user",
                table: "address");

            migrationBuilder.DropForeignKey(
                name: "fk_contact_lawyers_lawyer_id",
                schema: "user",
                table: "contact");

            migrationBuilder.DropForeignKey(
                name: "fk_contact_offices_office_id",
                schema: "user",
                table: "contact");

            migrationBuilder.DropForeignKey(
                name: "fk_oab_lawyers_lawyer_id",
                schema: "user",
                table: "oab");

            migrationBuilder.AddForeignKey(
                name: "fk_address_offices_office_id",
                schema: "user",
                table: "address",
                column: "office_id",
                principalSchema: "user",
                principalTable: "office",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_contact_lawyers_lawyer_id",
                schema: "user",
                table: "contact",
                column: "lawyer_id",
                principalSchema: "user",
                principalTable: "user",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_contact_offices_office_id",
                schema: "user",
                table: "contact",
                column: "office_id",
                principalSchema: "user",
                principalTable: "office",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_oab_lawyers_lawyer_id",
                schema: "user",
                table: "oab",
                column: "lawyer_id",
                principalSchema: "user",
                principalTable: "user",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
