﻿using System.Linq.Expressions;
using DataVenia.Modules.Users.Application.ClientCompany;
using DataVenia.Modules.Users.Application.ClientCompany.GetClientCompanies;
using DataVenia.Modules.Users.Domain.ClientCompany;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using ClientCompanyDomain = DataVenia.Modules.Users.Domain.ClientCompany.ClientCompany;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;

namespace DataVenia.Modules.Users.Infrastructure.ClientCompany;

public sealed class ClientCompanyRepository(UsersDbContext context) : IClientCompanyRepository
{
    public void Insert(ClientCompanyDomain clientCompany)
    {
        context.ClientCompanies.Add(clientCompany);
    }
    
    public async Task<ClientCompanyDomain?> GetSingleAsync(Expression<Func<ClientCompanyDomain, bool>> filter, CancellationToken cancellationToken = default)
    {
        try
        {
            return await context.ClientCompanies
            .Where(filter)
            .FirstOrDefaultAsync(cancellationToken);
            
        } catch (Exception ex)
        {
            throw new Exception("Error getting client company", ex);
        }
    }
    
    // resolver isso aqui
    public async Task<IReadOnlyCollection<GetClientCompaniesResponse>> GetManyAsync(GetClientCompaniesFilter filter, CancellationToken cancellationToken = default)
    {
        IQueryable<ClientCompanyDomain> query = context.ClientCompanies;

        if (filter.ClientId.HasValue)
            query = query.Where(li => li.ClientId == filter.ClientId);
        
        if(filter.CompanyId.HasValue)
            query = query.Where(li => li.CompanyId == filter.CompanyId);

        if(filter.Role.HasValue)
            query = query.Where(li => li.Role == filter.Role);
        
        query = query.Include(cc => cc.Client)
                     .Include(cc => cc.Company);

        List<ClientCompanyDomain> response = await query.ToListAsync(cancellationToken);

        IReadOnlyCollection<GetClientCompaniesResponse> responseList = response.Select(x => new GetClientCompaniesResponse(
            Id: x.Id,
            ClientId: x.ClientId,
            ClientName: x.Client.Name,
            CompanyId: x.CompanyId,
            CompanyName: x.Company.Name,
            Role: x.Role,
            CreatedAt: x.CreatedAt
        )).ToList();
        
        return responseList;
    }
}
