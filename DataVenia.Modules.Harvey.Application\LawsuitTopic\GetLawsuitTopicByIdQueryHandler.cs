﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Harvey.Domain.LawsuitTopic;
using FluentResults;

namespace DataVenia.Modules.Harvey.Application.LawsuitTopic;

internal sealed class GetLawsuitTopicByIdQueryHandler(
    ILawsuitTopicRepository lawsuitTopicRepository) : IQueryHandlerFr<GetLawsuitTopicByIdQuery, GetLawsuitTopicByIdResponse>
{
    public async Task<Result<GetLawsuitTopicByIdResponse>> Handle(GetLawsuitTopicByIdQuery request, CancellationToken cancellationToken)
    {
        Domain.LawsuitTopic.LawsuitTopic? lawsuitTopic = await lawsuitTopicRepository.GetByIdAsync(request.id, cancellationToken);

        if (lawsuitTopic is null)
            return Result.Fail<GetLawsuitTopicByIdResponse>(new Error("LawsuitTopic.Not.Found").WithMetadata("StatusCode", 404));

        var response = new GetLawsuitTopicByIdResponse(
            lawsuitTopic.Id,
            lawsuitTopic.LegalProvision,
            lawsuitTopic.Article,
            lawsuitTopic.Glossary,
            lawsuitTopic.IsSecret,
            lawsuitTopic.SecondaryTopic,
            lawsuitTopic.PreviousCrime,
            lawsuitTopic.IsFirstInstance,
            lawsuitTopic.IsSecondInstance,
            lawsuitTopic.JustEsJuizadoEs,
            lawsuitTopic.JustEsTurmas,
            lawsuitTopic.JustEs1grauMil,
            lawsuitTopic.JustEs2grauMil,
            lawsuitTopic.JustEsJuizadoEsFp,
            lawsuitTopic.JustTuEsUn,
            lawsuitTopic.JustFed1grau,
            lawsuitTopic.JustFed2grau,
            lawsuitTopic.JustFedJuizadoEs,
            lawsuitTopic.JustFedTurmas,
            lawsuitTopic.JustFedNacional,
            lawsuitTopic.JustFedRegional,
            lawsuitTopic.JustTrab1grau,
            lawsuitTopic.JustTrab2grau,
            lawsuitTopic.JustTrabTst,
            lawsuitTopic.JustTrabCsjt,
            lawsuitTopic.Stf,
            lawsuitTopic.Stj,
            lawsuitTopic.Cjf,
            lawsuitTopic.Cnj,
            lawsuitTopic.JustMilUniao1grau,
            lawsuitTopic.JustMilUniaoStm,
            lawsuitTopic.JustMilEst1grau,
            lawsuitTopic.JustMilEstTjm,
            lawsuitTopic.JustElei1grau,
            lawsuitTopic.JustElei2grau,
            lawsuitTopic.JustEleiTse,
            lawsuitTopic.UpdatedBy,
            lawsuitTopic.UpdatedAt);

        return response;
    }
}
