﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.LawsuitSync.Domain.LawsuitEvent;
public sealed class LawsuitEvent : Entity
{
    private LawsuitEvent() { }
    public Guid Id { get; private set; }
    public static LawsuitEvent Create()
    {
        var result = new LawsuitEvent() { Id = Guid.CreateVersion7()};

        // result.Raise(); --> pra enviar o domain event que gera o integration event

        return result;
    }
}
