﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Lawyers;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.ClientCompany.DeleteClientCompany;

public class DeleteClientCompanyCommandHandler(IClientCompanyRepository clientCompanyRepository, IUnitOfWork unitOfWork, ILogger<DeleteClientCompanyCommandHandler> logger) : ICommandHandler<DeleteClientCompanyCommand>
{
    public async Task<Result> Handle(DeleteClientCompanyCommand request, CancellationToken cancellationToken)
    {
        Domain.ClientCompany.ClientCompany? clientCompany = await clientCompanyRepository.GetSingleAsync(x => x.Id == request.ClientCompanyId, cancellationToken);

        if (clientCompany is null)
        {
            logger.LogError("ClientCompany not found: {ClientCompanyId}", request.ClientCompanyId);
            return Result.Failure(new Error("Not.Found", $"ClientCompany with id {request.ClientCompanyId} not found", ErrorType.NotFound));
        }

        clientCompany.SoftDelete();

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        } catch (Exception ex)
        {
            logger.LogError(ex, "Error soft deleting clientCompany in the database.");
            return Result.Failure<Guid>(Error.InternalServerError());
        }
        
        return Result.Success();
    }
}
