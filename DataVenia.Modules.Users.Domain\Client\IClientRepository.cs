﻿using System.Linq.Expressions;

namespace DataVenia.Modules.Users.Domain.Client;
public interface IClientRepository
{
    Task<IEnumerable<Client>> GetAllByOfficeIdAsync(Guid officeId, CancellationToken cancellationToken = default);
    Task<Client?> GetSingleAsync(Expression<Func<Client, bool>> filter, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<Client>> GetManyAsync(Expression<Func<Client, bool>> filter, CancellationToken cancellationToken = default);
    void Insert(Client client);
}
