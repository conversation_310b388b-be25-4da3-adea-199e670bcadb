﻿using System.Globalization;
using System.Security.Cryptography;

namespace DataVenia.Common.SeedDatabase;
public static class Generator
{
    public static string GenerateValidCnpj()
    {
        int[] multiplier1 = { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
        int[] multiplier2 = { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
        string cnpjSeed = string.Concat(Enumerable.Range(0, 8).Select(_ => GetRandomNumber(0, 10))) + "0001";

        int sum = 0;
        for (int i = 0; i < 12; i++)
            sum += int.Parse(cnpjSeed[i].ToString(), CultureInfo.InvariantCulture) * multiplier1[i];

        int remainder = sum % 11;
        int firstCheckDigit = remainder < 2 ? 0 : 11 - remainder;

        cnpjSeed += firstCheckDigit;
        sum = 0;

        for (int i = 0; i < 13; i++)
            sum += int.Parse(cnpjSeed[i].ToString(), CultureInfo.InvariantCulture) * multiplier2[i];

        remainder = sum % 11;
        int secondCheckDigit = remainder < 2 ? 0 : 11 - remainder;

        cnpjSeed += secondCheckDigit;

        return cnpjSeed;
    }


    public static string GenerateRg()
    {
        return GetRandomNumber(*********, *********).ToString(System.Globalization.CultureInfo.InvariantCulture); // 9 dígitos
    }

    public static string GenerateCtps()
    {
        long number = GetRandomNumber(1000000, 9999999); // 7 dígitos
        long series = GetRandomNumber(1000, 9999);       // 4 dígitos
        return $"{number} {series}";
    }

    public static string GeneratePassport()
    {
        string letters = $"{(char)GetRandomNumber('A', 'Z' + 1)}{(char)GetRandomNumber('A', 'Z' + 1)}"; // Duas letras aleatórias
        long numbers = GetRandomNumber(1000000, 9999999); // 7 dígitos
        return $"{letters}{numbers}";
    }
    public static string GenerateVoterId()
    {
        return GetRandomNumber(*********000, *********999).ToString(System.Globalization.CultureInfo.InvariantCulture); // 12 dígitos
    }

    public static string GeneratePis()
    {
        long[] pis = new long[10];
        for (long i = 0; i < 10; i++)
        {
            pis[i] = GetRandomNumber(0, 10);
        }

        long sum = 0;
        long[] weights = { 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };

        for (long i = 0; i < 10; i++)
        {
            sum += pis[i] * weights[i];
        }

        long remainder = sum % 11;
        long checkDigit = remainder < 2 ? 0 : 11 - remainder;

        return string.Concat(pis) + checkDigit;
    }

    public static string GenerateCnh()
    {
        long[] cnh = new long[11];
        for (long i = 0; i < 9; i++)
        {
            cnh[i] = GetRandomNumber(0, 10);
        }

        // Primeiro dígito verificador
        long sum1 = 0;
        for (long i = 0; i < 9; i++)
        {
            sum1 += cnh[i] * (9 - i);
        }

        long remainder1 = sum1 % 11;
        cnh[9] = remainder1 > 9 ? 0 : remainder1;

        // Segundo dígito verificador
        long sum2 = 0;
        for (long i = 0; i < 9; i++)
        {
            sum2 += cnh[i] * (1 + i);
        }

        long remainder2 = sum2 % 11;
        cnh[10] = remainder2 > 9 ? 0 : remainder2;

        return string.Concat(cnh);
    }

    public static List<string> GenerateValidOab()
    {
        long number = GetRandomNumber(1000, 999999); // Gera um número de 4 a 6 dígitos
        string uf = "SP"; // Exemplo fixo de UF. Em uma aplicação real, você pode querer gerar aleatoriamente entre os estados.
        return new List<string>() { $"{number}/{uf}" };
    }

    public static string GenerateValidCpf()
    {
        long[] cpf = new long[11];

        // Gerando os primeiros 9 dígitos aleatórios
        for (long i = 0; i < 9; i++)
        {
            cpf[i] = GetRandomNumber(0, 10); // Usando a função segura para gerar números
        }

        // Calculando o primeiro dígito verificador
        cpf[9] = CalculateCpfDigit(cpf, 10);

        // Calculando o segundo dígito verificador
        cpf[10] = CalculateCpfDigit(cpf, 11);

        // Convertendo o array de longeiros para uma string formatada de CPF
        return string.Join("", cpf);
    }

    private static long CalculateCpfDigit(long[] cpf, long weight)
    {
        long sum = 0;
        for (long i = 0; i < weight - 1; i++)
        {
            sum += cpf[i] * (weight - i);
        }

        long remainder = sum % 11;
        return remainder < 2 ? 0 : 11 - remainder;
    }

    private static long GetRandomNumber(long minValue, long maxValue)
    {
        // Usando RandomNumberGenerator para obter um número aleatório entre minValue e maxValue
        byte[] randomNumber = new byte[1];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(randomNumber);
        }
        double asciiValueOfRandomCharacter = Convert.ToDouble(randomNumber[0]);

        // Pegando o valor dentro do intervalo desejado
        double multiplier = Math.Max(0, asciiValueOfRandomCharacter / 255d - 0.00000000001d);
        long range = maxValue - minValue + 1;
        double randomValueInRange = Math.Floor(multiplier * range);

        return (long)(minValue + randomValueInRange);
    }
}
