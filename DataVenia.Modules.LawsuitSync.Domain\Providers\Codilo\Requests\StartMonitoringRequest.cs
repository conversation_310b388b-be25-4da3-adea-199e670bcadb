﻿using System.Text.Json.Serialization;
using Refit;

namespace DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Requests;

public class StartMonitoringRequest
{
    [JsonPropertyName("cnj")]
    public required string Cnj { get; set; }
    
    [JsonPropertyName("ignore")]
    public bool Ignore { get; set; }
    
    [JsonPropertyName("callbacks")]
    public required IEnumerable<LawsuitMonitoringCallback> Callbacks { get; set; }
}

public class LawsuitMonitoringCallback
{
    [JsonPropertyName("method")]
    public required string Method { get; set; }
    
    [JsonPropertyName("url")]
    public required Uri Url { get; set; }
}
