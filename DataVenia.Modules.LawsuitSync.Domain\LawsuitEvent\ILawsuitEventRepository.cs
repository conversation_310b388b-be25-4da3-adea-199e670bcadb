﻿using System.Linq.Expressions;

namespace DataVenia.Modules.LawsuitSync.Domain.LawsuitEvent;
public interface ILawsuitEventRepository
{
    Task<LawsuitEvent?> GetSingleAsync(Expression<Func<LawsuitEvent, bool>> filter, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<LawsuitEvent>> GetManyAsync(Expression<Func<LawsuitEvent, bool>> filter, CancellationToken cancellationToken = default);
    void Delete(LawsuitEvent appointment);
    void Insert(LawsuitEvent appointment);
    void Update(LawsuitEvent appointment);
}
