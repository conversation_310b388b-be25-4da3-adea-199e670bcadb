﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.SeedDatabase.UsersModule;
public sealed class UserGlobalRoleSeed
{
    private readonly UsersDbContext _usersDbContext;
    private readonly ILogger<UserGlobalRoleSeed> _logger;

    public UserGlobalRoleSeed(UsersDbContext usersDbContext, ILogger<UserGlobalRoleSeed> logger)
    {
        _usersDbContext = usersDbContext;
        _logger = logger;
    }

    public async Task Seed(Guid userId, string roleName)
    {
        Result<UserGlobalRole> userGlobalRoleFaker = UserGlobalRole.Create(userId, roleName);

        UserGlobalRole newGlobalRole = userGlobalRoleFaker.Value;

        bool exists = await _usersDbContext.UserGlobalRoles.AnyAsync(ugr => ugr.UserId == userId && ugr.RoleName == roleName);
        if (!exists)
        {
            _usersDbContext.UserGlobalRoles.Add(newGlobalRole);
            await _usersDbContext.SaveChangesAsync();
        }
        else
        {
            _logger.LogInformation("User global role already exists");
        }
    }
}
