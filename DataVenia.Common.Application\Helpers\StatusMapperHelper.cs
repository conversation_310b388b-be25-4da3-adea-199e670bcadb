using System.Globalization;
using System.Text;
using DataVenia.Common.Domain.Lawsuit;

namespace DataVenia.Modules.LawsuitSync.Application.Helpers;

public static class StatusMapperHelper
{
    private static readonly IReadOnlyList<LawsuitStatus> StatusPriority =
    [
        LawsuitStatus.InProgress,
        LawsuitStatus.RemittedToHigherCourt,
        LawsuitStatus.AwaitingAppeal,
        LawsuitStatus.AwaitingDecision,
        LawsuitStatus.Suspended,
        LawsuitStatus.None,
        LawsuitStatus.Completed,
        LawsuitStatus.Archived,
        LawsuitStatus.Cancelled
    ];

    public static LawsuitStatus GetOverallStatus(IEnumerable<string> instanceStatuses)
    {
        var parsedStatuses = instanceStatuses
            .Select(s => Enum.TryParse<LawsuitStatus>(s, out var result) ? result : LawsuitStatus.InProgress)
            .ToList();

        return StatusPriority.FirstOrDefault(parsedStatuses.Contains);
    }

    public static LawsuitStatus MapMatterMovementsStatus(string? movement)
    {
        if (string.IsNullOrWhiteSpace(movement))
        {
            return LawsuitStatus.InProgress;
        }

        movement = CleanText(movement.ToLowerInvariant());

        if (movement.Contains("cancelamento") || movement.Contains("processo cancelado"))
        {
            return LawsuitStatus.Cancelled;
        }

        if (movement.Contains("suspensao"))
        {
            return LawsuitStatus.Suspended;
        }

        if (movement.Contains("arquivado") || movement.Contains("arquivamento"))
        {
            return LawsuitStatus.Archived;
        }

        if (movement.Contains("transito em julgado") || movement.Contains("baixado"))
        {
            return LawsuitStatus.Completed;
        }

        if (movement.Contains("recurso interposto") || movement.Contains("apelacao recebida"))
        {
            return LawsuitStatus.AwaitingAppeal;
        }

        if (movement.Contains("sentenca proferida") ||
            movement.Contains("acordao publicado") ||
            movement.Contains("execucao iniciada") ||
            movement.Contains("cumprimento de sentenca iniciado"))
        {
            return LawsuitStatus.AwaitingDecision;
        }

        if (movement.Contains("remetidos os autos") ||
            movement.Contains("remessa dos autos") ||
            movement.Contains("encaminhamento ao tribunal superior") ||
            movement.Contains("subida de recurso") ||
            movement.Contains("recurso especial") ||
            movement.Contains("recurso extraordinario") ||
            movement.Contains("remetido ao"))
        {
            return LawsuitStatus.RemittedToHigherCourt;
        }

        return LawsuitStatus.InProgress;
    }

    private static string CleanText(string texto)
    {
        return string.IsNullOrEmpty(texto)
            ? texto
            : new string(texto
                .Normalize(NormalizationForm.FormD)
                .ToCharArray()
                .Where(c => CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
                .ToArray());
    }
}
