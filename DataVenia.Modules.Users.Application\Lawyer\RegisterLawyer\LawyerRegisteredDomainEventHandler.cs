﻿using DataVenia.Common.Application.EventBus;
using DataVenia.Common.Application.Exceptions;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Lawyers.GetLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.IntegrationEvents;
using MediatR;
using Microsoft.Extensions.Logging;
using LawyerDomain = DataVenia.Modules.Users.Domain.Lawyers.Lawyer;

namespace DataVenia.Modules.Users.Application.Lawyers.RegisterLawyer;
internal sealed class LawyerRegisteredDomainEventHandler(
    ISender sender, 
    IEventBus eventBus,
    ILogger<LawyerRegisteredDomainEventHandler> logger) : IDomainEventHandler<LawyerRegisteredDomainEvent>
{
    public async Task Handle(LawyerRegisteredDomainEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            Result<GetLawyerProfileResponse> result = await sender.Send(new GetLawyerProfileQuery(notification.LawyerId), cancellationToken);

            if (result.IsFailure)
                throw new DataVeniaException(nameof(GetLawyerProfileQuery), result.Error);

            await eventBus.PublishAsync(
                new LawyerRegisteredIntegrationEvent(
                    notification.Id,
                    notification.OccurredOnUtc,
                    result.Value.Id,
                    result.Value.Email,
                    result.Value.FirstName,
                    result.Value.LastName,
                    result.Value.Oab,
                    result.Value.Rg,
                    result.Value.Cpf,
                    result.Value.Cnh,
                    result.Value.Ctps,
                    result.Value.Passport,
                    result.Value.Pis,
                    result.Value.VoterId),
                cancellationToken);
        }
        catch (Exception ex) 
        {
            logger.LogError(ex, nameof(LawyerRegisteredDomainEventHandler));
        }
    }
}
