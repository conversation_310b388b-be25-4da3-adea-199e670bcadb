using System.Net;
using System.Net.Mail;
using DataVenia.Modules.Notification.Domain.Infrastructure.Clients;
using DataVenia.Modules.Notification.Domain.Models.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace DataVenia.Modules.Notification.Domain.Infrastructure.IoC;

public static class NotificationInfraInjections
{
    private const string SmtpServer = "smtp.gmail.com";
    private const int SmtpPort = 587;

    public static IServiceCollection AddNotificationInfra(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.Configure<NotificationSettings>(configuration.GetSection("Notification:NotificationSettings"));

        services.AddHttpClient<TelegramApiClient>(c => c.BaseAddress = new Uri("https://api.telegram.org/"));

        services.AddScoped<SmtpClient>(sp =>
        {
            IOptions<NotificationSettings> options = sp.GetService<IOptions<NotificationSettings>>() ??
                                                     throw new NullReferenceException("notificationSettings is null");
            NotificationSettings configurations = options.Value;

            return new SmtpClient(SmtpServer, SmtpPort)
            {
                Credentials = new NetworkCredential(configurations.SmtpUsername, configurations.SmtpPassword),
                EnableSsl = true
            };
        });

        return services;
    }
}
