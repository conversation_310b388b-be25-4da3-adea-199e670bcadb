﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using LawsuitDataDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData;
namespace DataVenia.Modules.Lawsuits.Domain.LawsuitResponsibles;
public class LawsuitResponsible: Entity
{
    public Guid LawsuitDataId { get; set; }
    public LawsuitDataDomain LawsuitData { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // responsible
    public Guid LawyerId { get; set; }

    public static LawsuitResponsible Create(Guid lawsuitDataId, Guid lawyerId)
    {
        var lawsuitResponsible = new LawsuitResponsible()
        {
            LawsuitDataId = lawsuitDataId,
            LawyerId = lawyerId,
            CreatedAt = DateTime.UtcNow
        };

        return lawsuitResponsible;
    }
}
