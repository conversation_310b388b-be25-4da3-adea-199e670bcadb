﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.User.GetResetPasswordToken;
using DataVenia.Modules.Users.Application.User.Login;
using DataVenia.Modules.Users.Application.User.ResetPassword;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Users.Presentation.User;

public sealed class ResetPassword : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPut("reset-password",
                async ([FromBody] ResetPasswordRequest request, [FromServices] ISender sender) =>
                    await ResetPasswordAsync(request, sender))
            .AllowAnonymous()
            .WithTags(Tags.Users);

        routeBuilder.MapPost("reset-password",
                async (StartResetPasswordRequest request, [FromServices] ISender sender) =>
                    await GenerateResetPasswordToken(request, sender))
            .AllowAnonymous()
            .WithTags(Tags.Users);
    }


    private static async Task<IResult> ResetPasswordAsync(ResetPasswordRequest request, [FromServices] ISender sender)
    {
        if (string.IsNullOrWhiteSpace(request.Username) || string.IsNullOrWhiteSpace(request.Password) || request.Token == Guid.Empty)
            return Results.BadRequest();

        Result result = await sender.Send(new ResetPasswordCommand(request.Token,
            request.Username,
            request.Password));

        return result.Match(Results.NoContent, ApiResults.Problem);
    }

    private static async Task<IResult> GenerateResetPasswordToken(StartResetPasswordRequest request, [FromServices] ISender sender)
    {
        if (string.IsNullOrWhiteSpace(request.Username))
            return Results.BadRequest();

        Result result = await sender.Send(new GetResetPasswordTokenCommand(request.Username));

        return result.Match(Results.NoContent, ApiResults.Problem);
    }
}

public class StartResetPasswordRequest
{
    public string Username { get; set; }
}

public class ResetPasswordRequest
{
    public string Username { get; set; }
    public string Password { get; set; }
    public Guid Token { get; set; }
}
