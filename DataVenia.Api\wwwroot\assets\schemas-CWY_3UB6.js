import{i as De}from"./brazilian-values-a8H3KUqb.js";var g;(function(r){r.assertEqual=n=>n;function e(n){}r.assertIs=e;function t(n){throw new Error}r.assertNever=t,r.arrayToEnum=n=>{const a={};for(const i of n)a[i]=i;return a},r.getValidEnumValues=n=>{const a=r.objectKeys(n).filter(o=>typeof n[n[o]]!="number"),i={};for(const o of a)i[o]=n[o];return r.objectValues(i)},r.objectValues=n=>r.objectKeys(n).map(function(a){return n[a]}),r.objectKeys=typeof Object.keys=="function"?n=>Object.keys(n):n=>{const a=[];for(const i in n)Object.prototype.hasOwnProperty.call(n,i)&&a.push(i);return a},r.find=(n,a)=>{for(const i of n)if(a(i))return i},r.isInteger=typeof Number.isInteger=="function"?n=>Number.isInteger(n):n=>typeof n=="number"&&isFinite(n)&&Math.floor(n)===n;function s(n,a=" | "){return n.map(i=>typeof i=="string"?`'${i}'`:i).join(a)}r.joinValues=s,r.jsonStringifyReplacer=(n,a)=>typeof a=="bigint"?a.toString():a})(g||(g={}));var xe;(function(r){r.mergeShapes=(e,t)=>({...e,...t})})(xe||(xe={}));const f=g.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),j=r=>{switch(typeof r){case"undefined":return f.undefined;case"string":return f.string;case"number":return isNaN(r)?f.nan:f.number;case"boolean":return f.boolean;case"function":return f.function;case"bigint":return f.bigint;case"symbol":return f.symbol;case"object":return Array.isArray(r)?f.array:r===null?f.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?f.promise:typeof Map<"u"&&r instanceof Map?f.map:typeof Set<"u"&&r instanceof Set?f.set:typeof Date<"u"&&r instanceof Date?f.date:f.object;default:return f.unknown}},d=g.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),Le=r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:");class w extends Error{constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){const t=e||function(a){return a.message},s={_errors:[]},n=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(n);else if(i.code==="invalid_return_type")n(i.returnTypeError);else if(i.code==="invalid_arguments")n(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let o=s,l=0;for(;l<i.path.length;){const c=i.path[l];l===i.path.length-1?(o[c]=o[c]||{_errors:[]},o[c]._errors.push(t(i))):o[c]=o[c]||{_errors:[]},o=o[c],l++}}};return n(this),s}static assert(e){if(!(e instanceof w))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,g.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):s.push(e(n));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}w.create=r=>new w(r);const W=(r,e)=>{let t;switch(r.code){case d.invalid_type:r.received===f.undefined?t="Required":t=`Expected ${r.expected}, received ${r.received}`;break;case d.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,g.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:t=`Unrecognized key(s) in object: ${g.joinValues(r.keys,", ")}`;break;case d.invalid_union:t="Invalid input";break;case d.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${g.joinValues(r.options)}`;break;case d.invalid_enum_value:t=`Invalid enum value. Expected ${g.joinValues(r.options)}, received '${r.received}'`;break;case d.invalid_arguments:t="Invalid function arguments";break;case d.invalid_return_type:t="Invalid function return type";break;case d.invalid_date:t="Invalid date";break;case d.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:g.assertNever(r.validation):r.validation!=="regex"?t=`Invalid ${r.validation}`:t="Invalid";break;case d.too_small:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:t="Invalid input";break;case d.too_big:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?t=`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:t="Invalid input";break;case d.custom:t="Invalid input";break;case d.invalid_intersection_types:t="Intersection results could not be merged";break;case d.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case d.not_finite:t="Number must be finite";break;default:t=e.defaultError,g.assertNever(r)}return{message:t}};let Oe=W;function ze(r){Oe=r}function ue(){return Oe}const le=r=>{const{data:e,path:t,errorMaps:s,issueData:n}=r,a=[...t,...n.path||[]],i={...n,path:a};if(n.message!==void 0)return{...n,path:a,message:n.message};let o="";const l=s.filter(c=>!!c).slice().reverse();for(const c of l)o=c(i,{data:e,defaultError:o}).message;return{...n,path:a,message:o}},Ue=[];function u(r,e){const t=ue(),s=le({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===W?void 0:W].filter(n=>!!n)});r.common.issues.push(s)}class k{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const n of t){if(n.status==="aborted")return m;n.status==="dirty"&&e.dirty(),s.push(n.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const n of t){const a=await n.key,i=await n.value;s.push({key:a,value:i})}return k.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const n of t){const{key:a,value:i}=n;if(a.status==="aborted"||i.status==="aborted")return m;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value!=="__proto__"&&(typeof i.value<"u"||n.alwaysSet)&&(s[a.value]=i.value)}return{status:e.value,value:s}}}const m=Object.freeze({status:"aborted"}),U=r=>({status:"dirty",value:r}),b=r=>({status:"valid",value:r}),ke=r=>r.status==="aborted",be=r=>r.status==="dirty",G=r=>r.status==="valid",X=r=>typeof Promise<"u"&&r instanceof Promise;function fe(r,e,t,s){if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(r)}function Ee(r,e,t,s,n){if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}var h;(function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(h||(h={}));var J,H;class O{constructor(e,t,s,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ce=(r,e)=>{if(G(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new w(r.common.issues);return this._error=t,this._error}}};function v(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:n}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:n}:{errorMap:(i,o)=>{var l,c;const{message:y}=r;return i.code==="invalid_enum_value"?{message:y??o.defaultError}:typeof o.data>"u"?{message:(l=y??s)!==null&&l!==void 0?l:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(c=y??t)!==null&&c!==void 0?c:o.defaultError}},description:n}}class _{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return j(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:j(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new k,ctx:{common:e.parent.common,data:e.data,parsedType:j(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(X(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;const n={common:{issues:[],async:(s=t==null?void 0:t.async)!==null&&s!==void 0?s:!1,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:j(e)},a=this._parseSync({data:e,path:n.path,parent:n});return Ce(n,a)}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:j(e)},n=this._parse({data:e,path:s.path,parent:s}),a=await(X(n)?n:Promise.resolve(n));return Ce(s,a)}refine(e,t){const s=n=>typeof t=="string"||typeof t>"u"?{message:t}:typeof t=="function"?t(n):t;return this._refinement((n,a)=>{const i=e(n),o=()=>a.addIssue({code:d.custom,...s(n)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((s,n)=>e(s)?!0:(n.addIssue(typeof t=="function"?t(s,n):t),!1))}_refinement(e){return new S({schema:this,typeName:p.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return N.create(this,this._def)}nullable(){return P.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return C.create(this,this._def)}promise(){return Y.create(this,this._def)}or(e){return ee.create([this,e],this._def)}and(e){return te.create(this,e,this._def)}transform(e){return new S({...v(this._def),schema:this,typeName:p.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new ie({...v(this._def),innerType:this,defaultValue:t,typeName:p.ZodDefault})}brand(){return new Te({typeName:p.ZodBranded,type:this,...v(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new oe({...v(this._def),innerType:this,catchValue:t,typeName:p.ZodCatch})}describe(e){const t=this.constructor;return new t({...this._def,description:e})}pipe(e){return ce.create(this,e)}readonly(){return de.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Be=/^c[^\s-]{8,}$/i,We=/^[0-9a-z]+$/,qe=/^[0-9A-HJKMNP-TV-Z]{26}$/,Ye=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Je=/^[a-z0-9_-]{21}$/i,He=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ge=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Xe="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let ge;const Qe=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ke=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,Fe=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Re="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",et=new RegExp(`^${Re}$`);function Ie(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function tt(r){return new RegExp(`^${Ie(r)}$`)}function je(r){let e=`${Re}T${Ie(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function rt(r,e){return!!((e==="v4"||!e)&&Qe.test(r)||(e==="v6"||!e)&&Ke.test(r))}class Z extends _{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==f.string){const a=this._getOrReturnCtx(e);return u(a,{code:d.invalid_type,expected:f.string,received:a.parsedType}),m}const s=new k;let n;for(const a of this._def.checks)if(a.kind==="min")e.data.length<a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),s.dirty());else if(a.kind==="max")e.data.length>a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),s.dirty());else if(a.kind==="length"){const i=e.data.length>a.value,o=e.data.length<a.value;(i||o)&&(n=this._getOrReturnCtx(e,n),i?u(n,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):o&&u(n,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),s.dirty())}else if(a.kind==="email")Ge.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"email",code:d.invalid_string,message:a.message}),s.dirty());else if(a.kind==="emoji")ge||(ge=new RegExp(Xe,"u")),ge.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"emoji",code:d.invalid_string,message:a.message}),s.dirty());else if(a.kind==="uuid")Ye.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"uuid",code:d.invalid_string,message:a.message}),s.dirty());else if(a.kind==="nanoid")Je.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"nanoid",code:d.invalid_string,message:a.message}),s.dirty());else if(a.kind==="cuid")Be.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"cuid",code:d.invalid_string,message:a.message}),s.dirty());else if(a.kind==="cuid2")We.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"cuid2",code:d.invalid_string,message:a.message}),s.dirty());else if(a.kind==="ulid")qe.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"ulid",code:d.invalid_string,message:a.message}),s.dirty());else if(a.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),u(n,{validation:"url",code:d.invalid_string,message:a.message}),s.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"regex",code:d.invalid_string,message:a.message}),s.dirty())):a.kind==="trim"?e.data=e.data.trim():a.kind==="includes"?e.data.includes(a.value,a.position)||(n=this._getOrReturnCtx(e,n),u(n,{code:d.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),s.dirty()):a.kind==="toLowerCase"?e.data=e.data.toLowerCase():a.kind==="toUpperCase"?e.data=e.data.toUpperCase():a.kind==="startsWith"?e.data.startsWith(a.value)||(n=this._getOrReturnCtx(e,n),u(n,{code:d.invalid_string,validation:{startsWith:a.value},message:a.message}),s.dirty()):a.kind==="endsWith"?e.data.endsWith(a.value)||(n=this._getOrReturnCtx(e,n),u(n,{code:d.invalid_string,validation:{endsWith:a.value},message:a.message}),s.dirty()):a.kind==="datetime"?je(a).test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{code:d.invalid_string,validation:"datetime",message:a.message}),s.dirty()):a.kind==="date"?et.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{code:d.invalid_string,validation:"date",message:a.message}),s.dirty()):a.kind==="time"?tt(a).test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{code:d.invalid_string,validation:"time",message:a.message}),s.dirty()):a.kind==="duration"?He.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"duration",code:d.invalid_string,message:a.message}),s.dirty()):a.kind==="ip"?rt(e.data,a.version)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"ip",code:d.invalid_string,message:a.message}),s.dirty()):a.kind==="base64"?Fe.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"base64",code:d.invalid_string,message:a.message}),s.dirty()):g.assertNever(a);return{status:s.value,value:e.data}}_regex(e,t,s){return this.refinement(n=>e.test(n),{validation:t,code:d.invalid_string,...h.errToObj(s)})}_addCheck(e){return new Z({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...h.errToObj(e)})}url(e){return this._addCheck({kind:"url",...h.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...h.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...h.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...h.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...h.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...h.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...h.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...h.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...h.errToObj(e)})}datetime(e){var t,s;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0?t:!1,local:(s=e==null?void 0:e.local)!==null&&s!==void 0?s:!1,...h.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...h.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...h.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...h.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...h.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...h.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...h.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...h.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...h.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...h.errToObj(t)})}nonempty(e){return this.min(1,h.errToObj(e))}trim(){return new Z({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Z({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Z({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Z.create=r=>{var e;return new Z({checks:[],typeName:p.ZodString,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0?e:!1,...v(r)})};function st(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,n=t>s?t:s,a=parseInt(r.toFixed(n).replace(".","")),i=parseInt(e.toFixed(n).replace(".",""));return a%i/Math.pow(10,n)}class A extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==f.number){const a=this._getOrReturnCtx(e);return u(a,{code:d.invalid_type,expected:f.number,received:a.parsedType}),m}let s;const n=new k;for(const a of this._def.checks)a.kind==="int"?g.isInteger(e.data)||(s=this._getOrReturnCtx(e,s),u(s,{code:d.invalid_type,expected:"integer",received:"float",message:a.message}),n.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:d.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:d.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="multipleOf"?st(e.data,a.value)!==0&&(s=this._getOrReturnCtx(e,s),u(s,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(s=this._getOrReturnCtx(e,s),u(s,{code:d.not_finite,message:a.message}),n.dirty()):g.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,s,n){return new A({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:h.toString(n)}]})}_addCheck(e){return new A({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:h.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:h.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:h.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:h.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&g.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}A.create=r=>new A({checks:[],typeName:p.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...v(r)});class M extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce&&(e.data=BigInt(e.data)),this._getType(e)!==f.bigint){const a=this._getOrReturnCtx(e);return u(a,{code:d.invalid_type,expected:f.bigint,received:a.parsedType}),m}let s;const n=new k;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:d.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:d.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(s=this._getOrReturnCtx(e,s),u(s,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):g.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,s,n){return new M({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:h.toString(n)}]})}_addCheck(e){return new M({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}M.create=r=>{var e;return new M({checks:[],typeName:p.ZodBigInt,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0?e:!1,...v(r)})};class Q extends _{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==f.boolean){const s=this._getOrReturnCtx(e);return u(s,{code:d.invalid_type,expected:f.boolean,received:s.parsedType}),m}return b(e.data)}}Q.create=r=>new Q({typeName:p.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...v(r)});class D extends _{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==f.date){const a=this._getOrReturnCtx(e);return u(a,{code:d.invalid_type,expected:f.date,received:a.parsedType}),m}if(isNaN(e.data.getTime())){const a=this._getOrReturnCtx(e);return u(a,{code:d.invalid_date}),m}const s=new k;let n;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:d.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),s.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:d.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),s.dirty()):g.assertNever(a);return{status:s.value,value:new Date(e.data.getTime())}}_addCheck(e){return new D({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:h.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:h.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}D.create=r=>new D({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:p.ZodDate,...v(r)});class he extends _{_parse(e){if(this._getType(e)!==f.symbol){const s=this._getOrReturnCtx(e);return u(s,{code:d.invalid_type,expected:f.symbol,received:s.parsedType}),m}return b(e.data)}}he.create=r=>new he({typeName:p.ZodSymbol,...v(r)});class K extends _{_parse(e){if(this._getType(e)!==f.undefined){const s=this._getOrReturnCtx(e);return u(s,{code:d.invalid_type,expected:f.undefined,received:s.parsedType}),m}return b(e.data)}}K.create=r=>new K({typeName:p.ZodUndefined,...v(r)});class F extends _{_parse(e){if(this._getType(e)!==f.null){const s=this._getOrReturnCtx(e);return u(s,{code:d.invalid_type,expected:f.null,received:s.parsedType}),m}return b(e.data)}}F.create=r=>new F({typeName:p.ZodNull,...v(r)});class q extends _{constructor(){super(...arguments),this._any=!0}_parse(e){return b(e.data)}}q.create=r=>new q({typeName:p.ZodAny,...v(r)});class V extends _{constructor(){super(...arguments),this._unknown=!0}_parse(e){return b(e.data)}}V.create=r=>new V({typeName:p.ZodUnknown,...v(r)});class I extends _{_parse(e){const t=this._getOrReturnCtx(e);return u(t,{code:d.invalid_type,expected:f.never,received:t.parsedType}),m}}I.create=r=>new I({typeName:p.ZodNever,...v(r)});class pe extends _{_parse(e){if(this._getType(e)!==f.undefined){const s=this._getOrReturnCtx(e);return u(s,{code:d.invalid_type,expected:f.void,received:s.parsedType}),m}return b(e.data)}}pe.create=r=>new pe({typeName:p.ZodVoid,...v(r)});class C extends _{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),n=this._def;if(t.parsedType!==f.array)return u(t,{code:d.invalid_type,expected:f.array,received:t.parsedType}),m;if(n.exactLength!==null){const i=t.data.length>n.exactLength.value,o=t.data.length<n.exactLength.value;(i||o)&&(u(t,{code:i?d.too_big:d.too_small,minimum:o?n.exactLength.value:void 0,maximum:i?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),s.dirty())}if(n.minLength!==null&&t.data.length<n.minLength.value&&(u(t,{code:d.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),s.dirty()),n.maxLength!==null&&t.data.length>n.maxLength.value&&(u(t,{code:d.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>n.type._parseAsync(new O(t,i,t.path,o)))).then(i=>k.mergeArray(s,i));const a=[...t.data].map((i,o)=>n.type._parseSync(new O(t,i,t.path,o)));return k.mergeArray(s,a)}get element(){return this._def.type}min(e,t){return new C({...this._def,minLength:{value:e,message:h.toString(t)}})}max(e,t){return new C({...this._def,maxLength:{value:e,message:h.toString(t)}})}length(e,t){return new C({...this._def,exactLength:{value:e,message:h.toString(t)}})}nonempty(e){return this.min(1,e)}}C.create=(r,e)=>new C({type:r,minLength:null,maxLength:null,exactLength:null,typeName:p.ZodArray,...v(e)});function z(r){if(r instanceof x){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=N.create(z(s))}return new x({...r._def,shape:()=>e})}else return r instanceof C?new C({...r._def,type:z(r.element)}):r instanceof N?N.create(z(r.unwrap())):r instanceof P?P.create(z(r.unwrap())):r instanceof E?E.create(r.items.map(e=>z(e))):r}class x extends _{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=g.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==f.object){const c=this._getOrReturnCtx(e);return u(c,{code:d.invalid_type,expected:f.object,received:c.parsedType}),m}const{status:s,ctx:n}=this._processInputParams(e),{shape:a,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof I&&this._def.unknownKeys==="strip"))for(const c in n.data)i.includes(c)||o.push(c);const l=[];for(const c of i){const y=a[c],T=n.data[c];l.push({key:{status:"valid",value:c},value:y._parse(new O(n,T,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof I){const c=this._def.unknownKeys;if(c==="passthrough")for(const y of o)l.push({key:{status:"valid",value:y},value:{status:"valid",value:n.data[y]}});else if(c==="strict")o.length>0&&(u(n,{code:d.unrecognized_keys,keys:o}),s.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const y of o){const T=n.data[y];l.push({key:{status:"valid",value:y},value:c._parse(new O(n,T,n.path,y)),alwaysSet:y in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const y of l){const T=await y.key,Ze=await y.value;c.push({key:T,value:Ze,alwaysSet:y.alwaysSet})}return c}).then(c=>k.mergeObjectSync(s,c)):k.mergeObjectSync(s,l)}get shape(){return this._def.shape()}strict(e){return h.errToObj,new x({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var n,a,i,o;const l=(i=(a=(n=this._def).errorMap)===null||a===void 0?void 0:a.call(n,t,s).message)!==null&&i!==void 0?i:s.defaultError;return t.code==="unrecognized_keys"?{message:(o=h.errToObj(e).message)!==null&&o!==void 0?o:l}:{message:l}}}:{}})}strip(){return new x({...this._def,unknownKeys:"strip"})}passthrough(){return new x({...this._def,unknownKeys:"passthrough"})}extend(e){return new x({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new x({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:p.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new x({...this._def,catchall:e})}pick(e){const t={};return g.objectKeys(e).forEach(s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])}),new x({...this._def,shape:()=>t})}omit(e){const t={};return g.objectKeys(this.shape).forEach(s=>{e[s]||(t[s]=this.shape[s])}),new x({...this._def,shape:()=>t})}deepPartial(){return z(this)}partial(e){const t={};return g.objectKeys(this.shape).forEach(s=>{const n=this.shape[s];e&&!e[s]?t[s]=n:t[s]=n.optional()}),new x({...this._def,shape:()=>t})}required(e){const t={};return g.objectKeys(this.shape).forEach(s=>{if(e&&!e[s])t[s]=this.shape[s];else{let a=this.shape[s];for(;a instanceof N;)a=a._def.innerType;t[s]=a}}),new x({...this._def,shape:()=>t})}keyof(){return Ae(g.objectKeys(this.shape))}}x.create=(r,e)=>new x({shape:()=>r,unknownKeys:"strip",catchall:I.create(),typeName:p.ZodObject,...v(e)});x.strictCreate=(r,e)=>new x({shape:()=>r,unknownKeys:"strict",catchall:I.create(),typeName:p.ZodObject,...v(e)});x.lazycreate=(r,e)=>new x({shape:r,unknownKeys:"strip",catchall:I.create(),typeName:p.ZodObject,...v(e)});class ee extends _{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;function n(a){for(const o of a)if(o.result.status==="valid")return o.result;for(const o of a)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;const i=a.map(o=>new w(o.ctx.common.issues));return u(t,{code:d.invalid_union,unionErrors:i}),m}if(t.common.async)return Promise.all(s.map(async a=>{const i={...t,common:{...t.common,issues:[]},parent:null};return{result:await a._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}})).then(n);{let a;const i=[];for(const l of s){const c={...t,common:{...t.common,issues:[]},parent:null},y=l._parseSync({data:t.data,path:t.path,parent:c});if(y.status==="valid")return y;y.status==="dirty"&&!a&&(a={result:y,ctx:c}),c.common.issues.length&&i.push(c.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const o=i.map(l=>new w(l));return u(t,{code:d.invalid_union,unionErrors:o}),m}}get options(){return this._def.options}}ee.create=(r,e)=>new ee({options:r,typeName:p.ZodUnion,...v(e)});const R=r=>r instanceof se?R(r.schema):r instanceof S?R(r.innerType()):r instanceof ne?[r.value]:r instanceof $?r.options:r instanceof ae?g.objectValues(r.enum):r instanceof ie?R(r._def.innerType):r instanceof K?[void 0]:r instanceof F?[null]:r instanceof N?[void 0,...R(r.unwrap())]:r instanceof P?[null,...R(r.unwrap())]:r instanceof Te||r instanceof de?R(r.unwrap()):r instanceof oe?R(r._def.innerType):[];class ve extends _{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.object)return u(t,{code:d.invalid_type,expected:f.object,received:t.parsedType}),m;const s=this.discriminator,n=t.data[s],a=this.optionsMap.get(n);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(u(t,{code:d.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),m)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){const n=new Map;for(const a of t){const i=R(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(n.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);n.set(o,a)}}return new ve({typeName:p.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...v(s)})}}function we(r,e){const t=j(r),s=j(e);if(r===e)return{valid:!0,data:r};if(t===f.object&&s===f.object){const n=g.objectKeys(e),a=g.objectKeys(r).filter(o=>n.indexOf(o)!==-1),i={...r,...e};for(const o of a){const l=we(r[o],e[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}else if(t===f.array&&s===f.array){if(r.length!==e.length)return{valid:!1};const n=[];for(let a=0;a<r.length;a++){const i=r[a],o=e[a],l=we(i,o);if(!l.valid)return{valid:!1};n.push(l.data)}return{valid:!0,data:n}}else return t===f.date&&s===f.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class te extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),n=(a,i)=>{if(ke(a)||ke(i))return m;const o=we(a.value,i.value);return o.valid?((be(a)||be(i))&&t.dirty(),{status:t.value,value:o.data}):(u(s,{code:d.invalid_intersection_types}),m)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([a,i])=>n(a,i)):n(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}te.create=(r,e,t)=>new te({left:r,right:e,typeName:p.ZodIntersection,...v(t)});class E extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.array)return u(s,{code:d.invalid_type,expected:f.array,received:s.parsedType}),m;if(s.data.length<this._def.items.length)return u(s,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),m;!this._def.rest&&s.data.length>this._def.items.length&&(u(s,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...s.data].map((i,o)=>{const l=this._def.items[o]||this._def.rest;return l?l._parse(new O(s,i,s.path,o)):null}).filter(i=>!!i);return s.common.async?Promise.all(a).then(i=>k.mergeArray(t,i)):k.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new E({...this._def,rest:e})}}E.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new E({items:r,typeName:p.ZodTuple,rest:null,...v(e)})};class re extends _{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.object)return u(s,{code:d.invalid_type,expected:f.object,received:s.parsedType}),m;const n=[],a=this._def.keyType,i=this._def.valueType;for(const o in s.data)n.push({key:a._parse(new O(s,o,s.path,o)),value:i._parse(new O(s,s.data[o],s.path,o)),alwaysSet:o in s.data});return s.common.async?k.mergeObjectAsync(t,n):k.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,s){return t instanceof _?new re({keyType:e,valueType:t,typeName:p.ZodRecord,...v(s)}):new re({keyType:Z.create(),valueType:e,typeName:p.ZodRecord,...v(t)})}}class me extends _{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.map)return u(s,{code:d.invalid_type,expected:f.map,received:s.parsedType}),m;const n=this._def.keyType,a=this._def.valueType,i=[...s.data.entries()].map(([o,l],c)=>({key:n._parse(new O(s,o,s.path,[c,"key"])),value:a._parse(new O(s,l,s.path,[c,"value"]))}));if(s.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const l of i){const c=await l.key,y=await l.value;if(c.status==="aborted"||y.status==="aborted")return m;(c.status==="dirty"||y.status==="dirty")&&t.dirty(),o.set(c.value,y.value)}return{status:t.value,value:o}})}else{const o=new Map;for(const l of i){const c=l.key,y=l.value;if(c.status==="aborted"||y.status==="aborted")return m;(c.status==="dirty"||y.status==="dirty")&&t.dirty(),o.set(c.value,y.value)}return{status:t.value,value:o}}}}me.create=(r,e,t)=>new me({valueType:e,keyType:r,typeName:p.ZodMap,...v(t)});class L extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.set)return u(s,{code:d.invalid_type,expected:f.set,received:s.parsedType}),m;const n=this._def;n.minSize!==null&&s.data.size<n.minSize.value&&(u(s,{code:d.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),n.maxSize!==null&&s.data.size>n.maxSize.value&&(u(s,{code:d.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());const a=this._def.valueType;function i(l){const c=new Set;for(const y of l){if(y.status==="aborted")return m;y.status==="dirty"&&t.dirty(),c.add(y.value)}return{status:t.value,value:c}}const o=[...s.data.values()].map((l,c)=>a._parse(new O(s,l,s.path,c)));return s.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(e,t){return new L({...this._def,minSize:{value:e,message:h.toString(t)}})}max(e,t){return new L({...this._def,maxSize:{value:e,message:h.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}L.create=(r,e)=>new L({valueType:r,minSize:null,maxSize:null,typeName:p.ZodSet,...v(e)});class B extends _{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.function)return u(t,{code:d.invalid_type,expected:f.function,received:t.parsedType}),m;function s(o,l){return le({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ue(),W].filter(c=>!!c),issueData:{code:d.invalid_arguments,argumentsError:l}})}function n(o,l){return le({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ue(),W].filter(c=>!!c),issueData:{code:d.invalid_return_type,returnTypeError:l}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Y){const o=this;return b(async function(...l){const c=new w([]),y=await o._def.args.parseAsync(l,a).catch(_e=>{throw c.addIssue(s(l,_e)),c}),T=await Reflect.apply(i,this,y);return await o._def.returns._def.type.parseAsync(T,a).catch(_e=>{throw c.addIssue(n(T,_e)),c})})}else{const o=this;return b(function(...l){const c=o._def.args.safeParse(l,a);if(!c.success)throw new w([s(l,c.error)]);const y=Reflect.apply(i,this,c.data),T=o._def.returns.safeParse(y,a);if(!T.success)throw new w([n(y,T.error)]);return T.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new B({...this._def,args:E.create(e).rest(V.create())})}returns(e){return new B({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new B({args:e||E.create([]).rest(V.create()),returns:t||V.create(),typeName:p.ZodFunction,...v(s)})}}class se extends _{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}se.create=(r,e)=>new se({getter:r,typeName:p.ZodLazy,...v(e)});class ne extends _{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return u(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),m}return{status:"valid",value:e.data}}get value(){return this._def.value}}ne.create=(r,e)=>new ne({value:r,typeName:p.ZodLiteral,...v(e)});function Ae(r,e){return new $({values:r,typeName:p.ZodEnum,...v(e)})}class $ extends _{constructor(){super(...arguments),J.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return u(t,{expected:g.joinValues(s),received:t.parsedType,code:d.invalid_type}),m}if(fe(this,J)||Ee(this,J,new Set(this._def.values)),!fe(this,J).has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return u(t,{received:t.data,code:d.invalid_enum_value,options:s}),m}return b(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return $.create(e,{...this._def,...t})}exclude(e,t=this._def){return $.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}J=new WeakMap;$.create=Ae;class ae extends _{constructor(){super(...arguments),H.set(this,void 0)}_parse(e){const t=g.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==f.string&&s.parsedType!==f.number){const n=g.objectValues(t);return u(s,{expected:g.joinValues(n),received:s.parsedType,code:d.invalid_type}),m}if(fe(this,H)||Ee(this,H,new Set(g.getValidEnumValues(this._def.values))),!fe(this,H).has(e.data)){const n=g.objectValues(t);return u(s,{received:s.data,code:d.invalid_enum_value,options:n}),m}return b(e.data)}get enum(){return this._def.values}}H=new WeakMap;ae.create=(r,e)=>new ae({values:r,typeName:p.ZodNativeEnum,...v(e)});class Y extends _{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.promise&&t.common.async===!1)return u(t,{code:d.invalid_type,expected:f.promise,received:t.parsedType}),m;const s=t.parsedType===f.promise?t.data:Promise.resolve(t.data);return b(s.then(n=>this._def.type.parseAsync(n,{path:t.path,errorMap:t.common.contextualErrorMap})))}}Y.create=(r,e)=>new Y({type:r,typeName:p.ZodPromise,...v(e)});class S extends _{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===p.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),n=this._def.effect||null,a={addIssue:i=>{u(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(a.addIssue=a.addIssue.bind(a),n.type==="preprocess"){const i=n.transform(s.data,a);if(s.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return m;const l=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return l.status==="aborted"?m:l.status==="dirty"||t.value==="dirty"?U(l.value):l});{if(t.value==="aborted")return m;const o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?m:o.status==="dirty"||t.value==="dirty"?U(o.value):o}}if(n.type==="refinement"){const i=o=>{const l=n.refinement(o,a);if(s.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){const o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?m:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?m:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(n.type==="transform")if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!G(i))return i;const o=n.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>G(i)?Promise.resolve(n.transform(i.value,a)).then(o=>({status:t.value,value:o})):i);g.assertNever(n)}}S.create=(r,e,t)=>new S({schema:r,typeName:p.ZodEffects,effect:e,...v(t)});S.createWithPreprocess=(r,e,t)=>new S({schema:e,effect:{type:"preprocess",transform:r},typeName:p.ZodEffects,...v(t)});class N extends _{_parse(e){return this._getType(e)===f.undefined?b(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}N.create=(r,e)=>new N({innerType:r,typeName:p.ZodOptional,...v(e)});class P extends _{_parse(e){return this._getType(e)===f.null?b(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}P.create=(r,e)=>new P({innerType:r,typeName:p.ZodNullable,...v(e)});class ie extends _{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===f.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}ie.create=(r,e)=>new ie({innerType:r,typeName:p.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...v(e)});class oe extends _{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return X(n)?n.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new w(s.common.issues)},input:s.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new w(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}oe.create=(r,e)=>new oe({innerType:r,typeName:p.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...v(e)});class ye extends _{_parse(e){if(this._getType(e)!==f.nan){const s=this._getOrReturnCtx(e);return u(s,{code:d.invalid_type,expected:f.nan,received:s.parsedType}),m}return{status:"valid",value:e.data}}}ye.create=r=>new ye({typeName:p.ZodNaN,...v(r)});const nt=Symbol("zod_brand");class Te extends _{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class ce extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const a=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?m:a.status==="dirty"?(t.dirty(),U(a.value)):this._def.out._parseAsync({data:a.value,path:s.path,parent:s})})();{const n=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return n.status==="aborted"?m:n.status==="dirty"?(t.dirty(),{status:"dirty",value:n.value}):this._def.out._parseSync({data:n.value,path:s.path,parent:s})}}static create(e,t){return new ce({in:e,out:t,typeName:p.ZodPipeline})}}class de extends _{_parse(e){const t=this._def.innerType._parse(e),s=n=>(G(n)&&(n.value=Object.freeze(n.value)),n);return X(t)?t.then(n=>s(n)):s(t)}unwrap(){return this._def.innerType}}de.create=(r,e)=>new de({innerType:r,typeName:p.ZodReadonly,...v(e)});function Me(r,e={},t){return r?q.create().superRefine((s,n)=>{var a,i;if(!r(s)){const o=typeof e=="function"?e(s):typeof e=="string"?{message:e}:e,l=(i=(a=o.fatal)!==null&&a!==void 0?a:t)!==null&&i!==void 0?i:!0,c=typeof o=="string"?{message:o}:o;n.addIssue({code:"custom",...c,fatal:l})}}):q.create()}const at={object:x.lazycreate};var p;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(p||(p={}));const it=(r,e={message:`Input not instance of ${r.name}`})=>Me(t=>t instanceof r,e),$e=Z.create,Pe=A.create,ot=ye.create,dt=M.create,Ve=Q.create,ct=D.create,ut=he.create,lt=K.create,ft=F.create,ht=q.create,pt=V.create,mt=I.create,yt=pe.create,vt=C.create,_t=x.create,gt=x.strictCreate,xt=ee.create,kt=ve.create,bt=te.create,wt=E.create,Tt=re.create,Zt=me.create,Ct=L.create,St=B.create,Nt=se.create,Ot=ne.create,Et=$.create,Rt=ae.create,It=Y.create,Se=S.create,jt=N.create,At=P.create,Mt=S.createWithPreprocess,$t=ce.create,Pt=()=>$e().optional(),Vt=()=>Pe().optional(),Dt=()=>Ve().optional(),Lt={string:r=>Z.create({...r,coerce:!0}),number:r=>A.create({...r,coerce:!0}),boolean:r=>Q.create({...r,coerce:!0}),bigint:r=>M.create({...r,coerce:!0}),date:r=>D.create({...r,coerce:!0})},zt=m;var Ut=Object.freeze({__proto__:null,defaultErrorMap:W,setErrorMap:ze,getErrorMap:ue,makeIssue:le,EMPTY_PATH:Ue,addIssueToContext:u,ParseStatus:k,INVALID:m,DIRTY:U,OK:b,isAborted:ke,isDirty:be,isValid:G,isAsync:X,get util(){return g},get objectUtil(){return xe},ZodParsedType:f,getParsedType:j,ZodType:_,datetimeRegex:je,ZodString:Z,ZodNumber:A,ZodBigInt:M,ZodBoolean:Q,ZodDate:D,ZodSymbol:he,ZodUndefined:K,ZodNull:F,ZodAny:q,ZodUnknown:V,ZodNever:I,ZodVoid:pe,ZodArray:C,ZodObject:x,ZodUnion:ee,ZodDiscriminatedUnion:ve,ZodIntersection:te,ZodTuple:E,ZodRecord:re,ZodMap:me,ZodSet:L,ZodFunction:B,ZodLazy:se,ZodLiteral:ne,ZodEnum:$,ZodNativeEnum:ae,ZodPromise:Y,ZodEffects:S,ZodTransformer:S,ZodOptional:N,ZodNullable:P,ZodDefault:ie,ZodCatch:oe,ZodNaN:ye,BRAND:nt,ZodBranded:Te,ZodPipeline:ce,ZodReadonly:de,custom:Me,Schema:_,ZodSchema:_,late:at,get ZodFirstPartyTypeKind(){return p},coerce:Lt,any:ht,array:vt,bigint:dt,boolean:Ve,date:ct,discriminatedUnion:kt,effect:Se,enum:Et,function:St,instanceof:it,intersection:bt,lazy:Nt,literal:Ot,map:Zt,nan:ot,nativeEnum:Rt,never:mt,null:ft,nullable:At,number:Pe,object:_t,oboolean:Dt,onumber:Vt,optional:jt,ostring:Pt,pipeline:$t,preprocess:Mt,promise:It,record:Tt,set:Ct,strictObject:gt,string:$e,symbol:ut,transformer:Se,tuple:wt,undefined:lt,union:xt,unknown:pt,void:yt,NEVER:zt,ZodIssueCode:d,quotelessJson:Le,ZodError:w}),Ne;(r=>{r.string=Ut.string({message:"Campo obrigatório"}).min(1,"Campo obrigatório"),r.email=r.string.email("Email inválido"),r.uuid=r.string.uuid("Informe um valor válido"),r.cpf=r.string.refine(De,{message:"Informe um CPF válido"}),r.oab=r.string.regex(/^[[0-9]{4,6}\/[A-Z]{2}$/,"OAB inválida")})(Ne||(Ne={}));export{Ne as S,A as Z,Ut as z};
