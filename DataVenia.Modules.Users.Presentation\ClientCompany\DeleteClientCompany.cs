﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.ClientCompany.DeleteClientCompany;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.ClientCompany;

public class DeleteClientCompany : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapDelete("offices/{officeId}/client-companies/{clientCompanyId}", async (Guid officeId, Guid clientCompanyId, [FromServices] ISender sender) =>
            {
                Result result = await sender.Send(new DeleteClientCompanyCommand(
                    clientCompanyId));
                
                return result.Match(Results.NoContent, ApiResults.Problem);
            })
            .RequireAuthorization("office:clients:delete")
            .WithTags(Tags.Client);
    }
}
