using DataVenia.Common.Domain.Lawsuit;

namespace DataVenia.Common.Contracts.Events.LawsuitSync;

public record LawsuitUpdateEvent(
    string Cnj,
    List<string> Subscriptions,
    string Instance,
    string Status,
    Cover Cover,
    List<Party> Parties,
    List<LawsuitStep> Steps
);

public record LawsuitStep(
    string Id,
    DateTime Date,
    string? Title,
    string? Description,
    string? ActionBy,
    bool Secret
);

public record Cover(
    string Class,
    List<string> Topics,
    LawsuitType LawsuitType,
    string JudgingOrgan,
    decimal CauseValue,
    DateTime DistributedAt
);

public record Party(
    string Name,
    PartyPole Pole,
    string Description,
    string TaxId
);
