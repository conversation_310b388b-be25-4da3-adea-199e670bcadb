import{e as Qi,r as P,j as S,X as to,D as ln,a as Zt}from"./index-DxHSBLqJ.js";import{P as cn}from"./plus-C0S_1p2f.js";/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eo=Qi("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);function no(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}function Wt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const he=t=>Array.isArray(t);function As(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function St(t){return typeof t=="string"||Array.isArray(t)}function un(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function De(t,e,n,s){if(typeof e=="function"){const[i,r]=un(s);e=e(n!==void 0?n:t.custom,i,r)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,r]=un(s);e=e(n!==void 0?n:t.custom,i,r)}return e}function Gt(t,e,n){const s=t.getProps();return De(s,e,n!==void 0?n:s.custom,t)}const Me=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Re=["initial",...Me],Ct=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ot=new Set(Ct),$=t=>t*1e3,z=t=>t/1e3,so={type:"spring",stiffness:500,damping:25,restSpeed:10},io=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),oo={type:"keyframes",duration:.8},ro={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ao=(t,{keyframes:e})=>e.length>2?oo:ot.has(t)?t.startsWith("scale")?io(e[1]):so:ro;function Ee(t,e){return t?t[e]||t.default||t:void 0}const lo={skipAnimations:!1,useManualTiming:!1},co=t=>t!==null;function $t(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(co),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return!r||s===void 0?i[r]:s}const F=t=>t;function uo(t){let e=new Set,n=new Set,s=!1,i=!1;const r=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function a(c){r.has(c)&&(l.schedule(c),t()),c(o)}const l={schedule:(c,u=!1,d=!1)=>{const f=d&&s?e:n;return u&&r.add(c),f.has(c)||f.add(c),c},cancel:c=>{n.delete(c),r.delete(c)},process:c=>{if(o=c,s){i=!0;return}s=!0,[e,n]=[n,e],n.clear(),e.forEach(a),s=!1,i&&(i=!1,l.process(c))}};return l}const Rt=["read","resolveKeyframes","update","preRender","render","postRender"],ho=40;function ws(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,o=Rt.reduce((p,y)=>(p[y]=uo(r),p),{}),{read:a,resolveKeyframes:l,update:c,preRender:u,render:d,postRender:h}=o,f=()=>{const p=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(p-i.timestamp,ho),1),i.timestamp=p,i.isProcessing=!0,a.process(i),l.process(i),c.process(i),u.process(i),d.process(i),h.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(f))},m=()=>{n=!0,s=!0,i.isProcessing||t(f)};return{schedule:Rt.reduce((p,y)=>{const v=o[y];return p[y]=(b,A=!1,C=!1)=>(n||m(),v.schedule(b,A,C)),p},{}),cancel:p=>{for(let y=0;y<Rt.length;y++)o[Rt[y]].cancel(p)},state:i,steps:o}}const{schedule:V,cancel:q,state:R,steps:Jt}=ws(typeof requestAnimationFrame<"u"?requestAnimationFrame:F,!0),Vs=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,fo=1e-7,mo=12;function po(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=Vs(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>fo&&++a<mo);return o}function Dt(t,e,n,s){if(t===e&&n===s)return F;const i=r=>po(r,0,1,t,n);return r=>r===0||r===1?r:Vs(i(r),e,s)}const Cs=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ds=t=>e=>1-t(1-e),Ms=Dt(.33,1.53,.69,.99),Le=Ds(Ms),Rs=Cs(Le),Es=t=>(t*=2)<1?.5*Le(t):.5*(2-Math.pow(2,-10*(t-1))),Fe=t=>1-Math.sin(Math.acos(t)),Ls=Ds(Fe),Fs=Cs(Fe),js=t=>/^0[^.\s]+$/u.test(t);function go(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||js(t):!0}let de=F;const ks=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Bs=t=>e=>typeof e=="string"&&e.startsWith(t),Is=Bs("--"),yo=Bs("var(--"),je=t=>yo(t)?vo.test(t.split("/*")[0].trim()):!1,vo=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,xo=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function To(t){const e=xo.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Os(t,e,n=1){const[s,i]=To(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const o=r.trim();return ks(o)?parseFloat(o):o}return je(i)?Os(i,e,n+1):i}const Z=(t,e,n)=>n>e?e:n<t?t:n,gt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},At={...gt,transform:t=>Z(0,1,t)},Et={...gt,default:1},Mt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Y=Mt("deg"),_=Mt("%"),T=Mt("px"),Po=Mt("vh"),bo=Mt("vw"),hn={..._,parse:t=>_.parse(t)/100,transform:t=>_.transform(t*100)},So=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),dn=t=>t===gt||t===T,fn=(t,e)=>parseFloat(t.split(", ")[e]),mn=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/u);if(i)return fn(i[1],e);{const r=s.match(/^matrix\((.+)\)$/u);return r?fn(r[1],t):0}},Ao=new Set(["x","y","z"]),wo=Ct.filter(t=>!Ao.has(t));function Vo(t){const e=[];return wo.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const ft={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:mn(4,13),y:mn(5,14)};ft.translateX=ft.x;ft.translateY=ft.y;const Ns=t=>e=>e.test(t),Co={test:t=>t==="auto",parse:t=>t},Us=[gt,T,_,Y,bo,Po,Co],pn=t=>Us.find(Ns(t)),it=new Set;let fe=!1,me=!1;function _s(){if(me){const t=Array.from(it).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=Vo(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,o])=>{var a;(a=s.getValue(r))===null||a===void 0||a.set(o)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}me=!1,fe=!1,it.forEach(t=>t.complete()),it.clear()}function Ks(){it.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(me=!0)})}function Do(){Ks(),_s()}class ke{constructor(e,n,s,i,r,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(it.add(this),fe||(fe=!0,V.read(Ks),V.resolveKeyframes(_s))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;for(let r=0;r<e.length;r++)if(e[r]===null)if(r===0){const o=i==null?void 0:i.get(),a=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const l=s.readValue(n,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),i&&o===void 0&&i.set(e[0])}else e[r]=e[r-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),it.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,it.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Tt=t=>Math.round(t*1e5)/1e5,Be=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Mo(t){return t==null}const Ro=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ie=(t,e)=>n=>!!(typeof n=="string"&&Ro.test(n)&&n.startsWith(t)||e&&!Mo(n)&&Object.prototype.hasOwnProperty.call(n,e)),Ws=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,o,a]=s.match(Be);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},Eo=t=>Z(0,255,t),Qt={...gt,transform:t=>Math.round(Eo(t))},st={test:Ie("rgb","red"),parse:Ws("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+Qt.transform(t)+", "+Qt.transform(e)+", "+Qt.transform(n)+", "+Tt(At.transform(s))+")"};function Lo(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const pe={test:Ie("#"),parse:Lo,transform:st.transform},lt={test:Ie("hsl","hue"),parse:Ws("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+_.transform(Tt(e))+", "+_.transform(Tt(n))+", "+Tt(At.transform(s))+")"},E={test:t=>st.test(t)||pe.test(t)||lt.test(t),parse:t=>st.test(t)?st.parse(t):lt.test(t)?lt.parse(t):pe.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?st.transform(t):lt.transform(t)},Fo=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function jo(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Be))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Fo))===null||n===void 0?void 0:n.length)||0)>0}const Gs="number",$s="color",ko="var",Bo="var(",gn="${}",Io=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function wt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(Io,l=>(E.test(l)?(s.color.push(r),i.push($s),n.push(E.parse(l))):l.startsWith(Bo)?(s.var.push(r),i.push(ko),n.push(l)):(s.number.push(r),i.push(Gs),n.push(parseFloat(l))),++r,gn)).split(gn);return{values:n,split:a,indexes:s,types:i}}function zs(t){return wt(t).values}function Hs(t){const{split:e,types:n}=wt(t),s=e.length;return i=>{let r="";for(let o=0;o<s;o++)if(r+=e[o],i[o]!==void 0){const a=n[o];a===Gs?r+=Tt(i[o]):a===$s?r+=E.transform(i[o]):r+=i[o]}return r}}const Oo=t=>typeof t=="number"?0:t;function No(t){const e=zs(t);return Hs(t)(e.map(Oo))}const J={test:jo,parse:zs,createTransformer:Hs,getAnimatableNone:No},Uo=new Set(["brightness","contrast","saturate","opacity"]);function _o(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Be)||[];if(!s)return t;const i=n.replace(s,"");let r=Uo.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Ko=/\b([a-z-]*)\(.*?\)/gu,ge={...J,getAnimatableNone:t=>{const e=t.match(Ko);return e?e.map(_o).join(" "):t}},Wo={borderWidth:T,borderTopWidth:T,borderRightWidth:T,borderBottomWidth:T,borderLeftWidth:T,borderRadius:T,radius:T,borderTopLeftRadius:T,borderTopRightRadius:T,borderBottomRightRadius:T,borderBottomLeftRadius:T,width:T,maxWidth:T,height:T,maxHeight:T,top:T,right:T,bottom:T,left:T,padding:T,paddingTop:T,paddingRight:T,paddingBottom:T,paddingLeft:T,margin:T,marginTop:T,marginRight:T,marginBottom:T,marginLeft:T,backgroundPositionX:T,backgroundPositionY:T},Go={rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:Et,scaleX:Et,scaleY:Et,scaleZ:Et,skew:Y,skewX:Y,skewY:Y,distance:T,translateX:T,translateY:T,translateZ:T,x:T,y:T,z:T,perspective:T,transformPerspective:T,opacity:At,originX:hn,originY:hn,originZ:T},yn={...gt,transform:Math.round},Oe={...Wo,...Go,zIndex:yn,size:T,fillOpacity:At,strokeOpacity:At,numOctaves:yn},$o={...Oe,color:E,backgroundColor:E,outlineColor:E,fill:E,stroke:E,borderColor:E,borderTopColor:E,borderRightColor:E,borderBottomColor:E,borderLeftColor:E,filter:ge,WebkitFilter:ge},Ne=t=>$o[t];function Xs(t,e){let n=Ne(t);return n!==ge&&(n=J),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const zo=new Set(["auto","none","0"]);function Ho(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!zo.has(r)&&wt(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=Xs(n,i)}class Ys extends ke{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),je(c))){const u=Os(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!So.has(s)||e.length!==2)return;const[i,r]=e,o=pn(i),a=pn(r);if(o!==a)if(dn(o)&&dn(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)go(e[i])&&s.push(i);s.length&&Ho(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ft[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var e;const{element:n,name:s,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const r=n.getValue(s);r&&r.jump(this.measuredOrigin,!1);const o=i.length-1,a=i[o];i[o]=ft[s](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,c])=>{n.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function Ue(t){return typeof t=="function"}let Ft;function Xo(){Ft=void 0}const K={now:()=>(Ft===void 0&&K.set(R.isProcessing||lo.useManualTiming?R.timestamp:performance.now()),Ft),set:t=>{Ft=t,queueMicrotask(Xo)}},vn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(J.test(t)||t==="0")&&!t.startsWith("url("));function Yo(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function qo(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],o=vn(i,e),a=vn(r,e);return!o||!a?!1:Yo(t)||(n==="spring"||Ue(n))&&s}const Zo=40;class qs{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=K.now(),this.options={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Zo?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Do(),this._resolved}onKeyframesResolved(e,n){this.resolvedAt=K.now(),this.hasAttemptedResolve=!0;const{name:s,type:i,velocity:r,delay:o,onComplete:a,onUpdate:l,isGenerator:c}=this.options;if(!c&&!qo(e,s,i,r))if(o)this.options.duration=0;else{l==null||l($t(e,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const u=this.initPlayback(e,n);u!==!1&&(this._resolved={keyframes:e,finalKeyframe:n,...u},this.onPostResolved())}onPostResolved(){}then(e,n){return this.currentFinishedPromise.then(e,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}function Zs(t,e){return e?t*(1e3/e):0}const Jo=5;function Js(t,e,n){const s=Math.max(e-Jo,0);return Zs(n-t(s),e-s)}const te=.001,Qo=.01,tr=10,er=.05,nr=1;function sr({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,r,o=1-e;o=Z(er,nr,o),t=Z(Qo,tr,z(t)),o<1?(i=c=>{const u=c*o,d=u*t,h=u-n,f=ye(c,o),m=Math.exp(-d);return te-h/f*m},r=c=>{const d=c*o*t,h=d*n+n,f=Math.pow(o,2)*Math.pow(c,2)*t,m=Math.exp(-d),g=ye(Math.pow(c,2),o);return(-i(c)+te>0?-1:1)*((h-f)*m)/g}):(i=c=>{const u=Math.exp(-c*t),d=(c-n)*t+1;return-te+u*d},r=c=>{const u=Math.exp(-c*t),d=(n-c)*(t*t);return u*d});const a=5/t,l=or(i,r,a);if(t=$(t),isNaN(l))return{stiffness:100,damping:10,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:o*2*Math.sqrt(s*c),duration:t}}}const ir=12;function or(t,e,n){let s=n;for(let i=1;i<ir;i++)s=s-t(s)/e(s);return s}function ye(t,e){return t*Math.sqrt(1-e*e)}const rr=["duration","bounce"],ar=["stiffness","damping","mass"];function xn(t,e){return e.some(n=>t[n]!==void 0)}function lr(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!xn(t,ar)&&xn(t,rr)){const n=sr(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}function Qs({keyframes:t,restDelta:e,restSpeed:n,...s}){const i=t[0],r=t[t.length-1],o={done:!1,value:i},{stiffness:a,damping:l,mass:c,duration:u,velocity:d,isResolvedFromDuration:h}=lr({...s,velocity:-z(s.velocity||0)}),f=d||0,m=l/(2*Math.sqrt(a*c)),g=r-i,x=z(Math.sqrt(a/c)),p=Math.abs(g)<5;n||(n=p?.01:2),e||(e=p?.005:.5);let y;if(m<1){const v=ye(x,m);y=b=>{const A=Math.exp(-m*x*b);return r-A*((f+m*x*g)/v*Math.sin(v*b)+g*Math.cos(v*b))}}else if(m===1)y=v=>r-Math.exp(-x*v)*(g+(f+x*g)*v);else{const v=x*Math.sqrt(m*m-1);y=b=>{const A=Math.exp(-m*x*b),C=Math.min(v*b,300);return r-A*((f+m*x*g)*Math.sinh(C)+v*g*Math.cosh(C))/v}}return{calculatedDuration:h&&u||null,next:v=>{const b=y(v);if(h)o.done=v>=u;else{let A=0;m<1&&(A=v===0?$(f):Js(y,v,b));const C=Math.abs(A)<=n,U=Math.abs(r-b)<=e;o.done=C&&U}return o.value=o.done?r:b,o}}}function Tn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:u}){const d=t[0],h={done:!1,value:d},f=w=>a!==void 0&&w<a||l!==void 0&&w>l,m=w=>a===void 0?l:l===void 0||Math.abs(a-w)<Math.abs(l-w)?a:l;let g=n*e;const x=d+g,p=o===void 0?x:o(x);p!==x&&(g=p-d);const y=w=>-g*Math.exp(-w/s),v=w=>p+y(w),b=w=>{const B=y(w),I=v(w);h.done=Math.abs(B)<=c,h.value=h.done?p:I};let A,C;const U=w=>{f(h.value)&&(A=w,C=Qs({keyframes:[h.value,m(h.value)],velocity:Js(v,w,h.value),damping:i,stiffness:r,restDelta:c,restSpeed:u}))};return U(0),{calculatedDuration:null,next:w=>{let B=!1;return!C&&A===void 0&&(B=!0,b(w),U(w)),A!==void 0&&w>=A?C.next(w-A):(!B&&b(w),h)}}}const cr=Dt(.42,0,1,1),ur=Dt(0,0,.58,1),ti=Dt(.42,0,.58,1),hr=t=>Array.isArray(t)&&typeof t[0]!="number",_e=t=>Array.isArray(t)&&typeof t[0]=="number",Pn={linear:F,easeIn:cr,easeInOut:ti,easeOut:ur,circIn:Fe,circInOut:Fs,circOut:Ls,backIn:Le,backInOut:Rs,backOut:Ms,anticipate:Es},bn=t=>{if(_e(t)){de(t.length===4);const[e,n,s,i]=t;return Dt(e,n,s,i)}else if(typeof t=="string")return de(Pn[t]!==void 0),Pn[t];return t},dr=(t,e)=>n=>e(t(n)),H=(...t)=>t.reduce(dr),mt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},D=(t,e,n)=>t+(e-t)*n;function ee(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function fr({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=ee(l,a,t+1/3),r=ee(l,a,t),o=ee(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}function Bt(t,e){return n=>n>0?e:t}const ne=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},mr=[pe,st,lt],pr=t=>mr.find(e=>e.test(t));function Sn(t){const e=pr(t);if(!e)return!1;let n=e.parse(t);return e===lt&&(n=fr(n)),n}const An=(t,e)=>{const n=Sn(t),s=Sn(e);if(!n||!s)return Bt(t,e);const i={...n};return r=>(i.red=ne(n.red,s.red,r),i.green=ne(n.green,s.green,r),i.blue=ne(n.blue,s.blue,r),i.alpha=D(n.alpha,s.alpha,r),st.transform(i))},ve=new Set(["none","hidden"]);function gr(t,e){return ve.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function yr(t,e){return n=>D(t,e,n)}function Ke(t){return typeof t=="number"?yr:typeof t=="string"?je(t)?Bt:E.test(t)?An:Tr:Array.isArray(t)?ei:typeof t=="object"?E.test(t)?An:vr:Bt}function ei(t,e){const n=[...t],s=n.length,i=t.map((r,o)=>Ke(r)(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}}function vr(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ke(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function xr(t,e){var n;const s=[],i={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){const o=e.types[r],a=t.indexes[o][i[o]],l=(n=t.values[a])!==null&&n!==void 0?n:0;s[r]=l,i[o]++}return s}const Tr=(t,e)=>{const n=J.createTransformer(e),s=wt(t),i=wt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?ve.has(t)&&!i.values.length||ve.has(e)&&!s.values.length?gr(t,e):H(ei(xr(s,i),i.values),n):Bt(t,e)};function ni(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?D(t,e,n):Ke(t)(t,e)}function Pr(t,e,n){const s=[],i=n||ni,r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const l=Array.isArray(e)?e[o]||F:e;a=H(l,a)}s.push(a)}return s}function br(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(de(r===e.length),r===1)return()=>e[0];if(r===2&&t[0]===t[1])return()=>e[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=Pr(e,s,i),a=o.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const d=mt(t[u],t[u+1],c);return o[u](d)};return n?c=>l(Z(t[0],t[r-1],c)):l}function Sr(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=mt(0,e,s);t.push(D(n,1,i))}}function Ar(t){const e=[0];return Sr(e,t.length-1),e}function wr(t,e){return t.map(n=>n*e)}function Vr(t,e){return t.map(()=>e||ti).splice(0,t.length-1)}function It({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=hr(s)?s.map(bn):bn(s),r={done:!1,value:e[0]},o=wr(n&&n.length===e.length?n:Ar(e),t),a=br(o,e,{ease:Array.isArray(i)?i:Vr(e,i)});return{calculatedDuration:t,next:l=>(r.value=a(l),r.done=l>=t,r)}}const wn=2e4;function Cr(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<wn;)e+=n,s=t.next(e);return e>=wn?1/0:e}const Dr=t=>{const e=({timestamp:n})=>t(n);return{start:()=>V.update(e,!0),stop:()=>q(e),now:()=>R.isProcessing?R.timestamp:K.now()}},Mr={decay:Tn,inertia:Tn,tween:It,keyframes:It,spring:Qs},Rr=t=>t/100;class We extends qs{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:s,element:i,keyframes:r}=this.options,o=(i==null?void 0:i.KeyframeResolver)||ke,a=(l,c)=>this.onKeyframesResolved(l,c);this.resolver=new o(r,a,n,s,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:o=0}=this.options,a=Ue(n)?n:Mr[n]||It;let l,c;a!==It&&typeof e[0]!="number"&&(l=H(Rr,ni(e[0],e[1])),e=[0,100]);const u=a({...this.options,keyframes:e});r==="mirror"&&(c=a({...this.options,keyframes:[...e].reverse(),velocity:-o})),u.calculatedDuration===null&&(u.calculatedDuration=Cr(u));const{calculatedDuration:d}=u,h=d+i,f=h*(s+1)-i;return{generator:u,mirroredGenerator:c,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:h,totalDuration:f}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,n=!1){const{resolved:s}=this;if(!s){const{keyframes:w}=this.options;return{done:!0,value:w[w.length-1]}}const{finalKeyframe:i,generator:r,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:c,totalDuration:u,resolvedDuration:d}=s;if(this.startTime===null)return r.next(0);const{delay:h,repeat:f,repeatType:m,repeatDelay:g,onUpdate:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),n?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const p=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?p<0:p>u;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let v=this.currentTime,b=r;if(f){const w=Math.min(this.currentTime,u)/d;let B=Math.floor(w),I=w%1;!I&&w>=1&&(I=1),I===1&&B--,B=Math.min(B,f+1),!!(B%2)&&(m==="reverse"?(I=1-I,g&&(I-=g/d)):m==="mirror"&&(b=o)),v=Z(0,1,I)*d}const A=y?{done:!1,value:l[0]}:b.next(v);a&&(A.value=a(A.value));let{done:C}=A;!y&&c!==null&&(C=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const U=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&C);return U&&i!==void 0&&(A.value=$t(l,this.options,i)),x&&x(A.value),U&&this.finish(),A}get duration(){const{resolved:e}=this;return e?z(e.calculatedDuration):0}get time(){return z(this.currentTime)}set time(e){e=$(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=z(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=Dr,onPlay:n,startTime:s}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=s??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const Er=new Set(["opacity","clipPath","filter","transform"]),Lr=10,Fr=(t,e)=>{let n="";const s=Math.max(Math.round(e/Lr),2);for(let i=0;i<s;i++)n+=t(mt(0,s-1,i))+", ";return`linear(${n.substring(0,n.length-2)})`};function Ge(t){let e;return()=>(e===void 0&&(e=t()),e)}const jr={linearEasing:void 0};function kr(t,e){const n=Ge(t);return()=>{var s;return(s=jr[e])!==null&&s!==void 0?s:n()}}const Ot=kr(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function si(t){return!!(typeof t=="function"&&Ot()||!t||typeof t=="string"&&(t in xe||Ot())||_e(t)||Array.isArray(t)&&t.every(si))}const vt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,xe={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:vt([0,.65,.55,1]),circOut:vt([.55,0,1,.45]),backIn:vt([.31,.01,.66,-.59]),backOut:vt([.33,1.53,.69,.99])};function ii(t,e){if(t)return typeof t=="function"&&Ot()?Fr(t,e):_e(t)?vt(t):Array.isArray(t)?t.map(n=>ii(n,e)||xe.easeOut):xe[t]}function Br(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){const c={[e]:n};l&&(c.offset=l);const u=ii(a,i);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:s,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"})}function Vn(t,e){t.timeline=e,t.onfinish=null}const Ir=Ge(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Nt=10,Or=2e4;function Nr(t){return Ue(t.type)||t.type==="spring"||!si(t.ease)}function Ur(t,e){const n=new We({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:t[0]};const i=[];let r=0;for(;!s.done&&r<Or;)s=n.sample(r),i.push(s.value),r+=Nt;return{times:void 0,keyframes:i,duration:r-Nt,ease:"linear"}}const oi={anticipate:Es,backInOut:Rs,circInOut:Fs};function _r(t){return t in oi}class Cn extends qs{constructor(e){super(e);const{name:n,motionValue:s,element:i,keyframes:r}=this.options;this.resolver=new Ys(r,(o,a)=>this.onKeyframesResolved(o,a),n,s,i),this.resolver.scheduleResolve()}initPlayback(e,n){var s;let{duration:i=300,times:r,ease:o,type:a,motionValue:l,name:c,startTime:u}=this.options;if(!(!((s=l.owner)===null||s===void 0)&&s.current))return!1;if(typeof o=="string"&&Ot()&&_r(o)&&(o=oi[o]),Nr(this.options)){const{onComplete:h,onUpdate:f,motionValue:m,element:g,...x}=this.options,p=Ur(e,x);e=p.keyframes,e.length===1&&(e[1]=e[0]),i=p.duration,r=p.times,o=p.ease,a="keyframes"}const d=Br(l.owner.current,c,e,{...this.options,duration:i,times:r,ease:o});return d.startTime=u??this.calcStartTime(),this.pendingTimeline?(Vn(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{const{onComplete:h}=this.options;l.set($t(e,this.options,n)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:i,times:r,type:a,ease:o,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:n}=e;return z(n)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:n}=e;return z(n.currentTime||0)}set time(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.currentTime=$(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:n}=e;return n.playbackRate}set speed(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:n}=e;return n.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:n}=e;return n.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:n}=this;if(!n)return F;const{animation:s}=n;Vn(s,e)}return F}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:n,keyframes:s,duration:i,type:r,ease:o,times:a}=e;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:u,onComplete:d,element:h,...f}=this.options,m=new We({...f,keyframes:s,duration:i,type:r,ease:o,times:a,isGenerator:!0}),g=$(this.time);c.setWithVelocity(m.sample(g-Nt).value,m.sample(g).value,Nt)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:n,name:s,repeatDelay:i,repeatType:r,damping:o,type:a}=e;return Ir()&&s&&Er.has(s)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!i&&r!=="mirror"&&o!==0&&a!=="inertia"}}const Kr=Ge(()=>window.ScrollTimeline!==void 0);class Wr{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}then(e,n){return Promise.all(this.animations).then(e).catch(n)}getAll(e){return this.animations[0][e]}setAll(e,n){for(let s=0;s<this.animations.length;s++)this.animations[s][e]=n}attachTimeline(e,n){const s=this.animations.map(i=>Kr()&&i.attachTimeline?i.attachTimeline(e):n(i));return()=>{s.forEach((i,r)=>{i&&i(),this.animations[r].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let n=0;n<this.animations.length;n++)e=Math.max(e,this.animations[n].duration);return e}runAll(e){this.animations.forEach(n=>n[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function Gr({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const $e=(t,e,n,s={},i,r)=>o=>{const a=Ee(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-$(l);let u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:h=>{e.set(h),a.onUpdate&&a.onUpdate(h)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:i};Gr(a)||(u={...u,...ao(t,u)}),u.duration&&(u.duration=$(u.duration)),u.repeatDelay&&(u.repeatDelay=$(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let d=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(d=!0)),d&&!r&&e.get()!==void 0){const h=$t(u.keyframes,a);if(h!==void 0)return V.update(()=>{u.onUpdate(h),u.onComplete()}),new Wr([])}return!r&&Cn.supports(u)?new Cn(u):new We(u)},$r=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),zr=t=>he(t)?t[t.length-1]||0:t;function ze(t,e){t.indexOf(e)===-1&&t.push(e)}function He(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Xe{constructor(){this.subscriptions=[]}add(e){return ze(this.subscriptions,e),()=>He(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Dn=30,Hr=t=>!isNaN(parseFloat(t));class Xr{constructor(e,n={}){this.version="11.11.17",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{const r=K.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=K.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Hr(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Xe);const s=this.events[e].add(n);return e==="change"?()=>{s(),V.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=K.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Dn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Dn);return Zs(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Vt(t,e){return new Xr(t,e)}function Yr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Vt(n))}function qr(t,e){const n=Gt(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const o in r){const a=zr(r[o]);Yr(t,o,a)}}const Ye=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Zr="framerAppearId",ri="data-"+Ye(Zr);function ai(t){return t.props[ri]}const L=t=>!!(t&&t.getVelocity);function Jr(t){return!!(L(t)&&t.add)}function Te(t,e){const n=t.getValue("willChange");if(Jr(n))return n.add(e)}function Qr({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function li(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(o=s);const c=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const d in l){const h=t.getValue(d,(r=t.latestValues[d])!==null&&r!==void 0?r:null),f=l[d];if(f===void 0||u&&Qr(u,d))continue;const m={delay:n,...Ee(o||{},d)};let g=!1;if(window.MotionHandoffAnimation){const p=ai(t);if(p){const y=window.MotionHandoffAnimation(p,d,V);y!==null&&(m.startTime=y,g=!0)}}Te(t,d),h.start($e(d,h,f,t.shouldReduceMotion&&ot.has(d)?{type:!1}:m,t,g));const x=h.animation;x&&c.push(x)}return a&&Promise.all(c).then(()=>{V.update(()=>{a&&qr(t,a)})}),c}function Pe(t,e,n={}){var s;const i=Gt(t,e,n.type==="exit"?(s=t.presenceContext)===null||s===void 0?void 0:s.custom:void 0);let{transition:r=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(r=n.transitionOverride);const o=i?()=>Promise.all(li(t,i,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:d,staggerDirection:h}=r;return ta(t,e,u+c,d,h,n)}:()=>Promise.resolve(),{when:l}=r;if(l){const[c,u]=l==="beforeChildren"?[o,a]:[a,o];return c().then(()=>u())}else return Promise.all([o(),a(n.delay)])}function ta(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(ea).forEach((c,u)=>{c.notify("AnimationStart",e),o.push(Pe(c,e,{...r,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(o)}function ea(t,e){return t.sortNodePosition(e)}function na(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Pe(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Pe(t,e,n);else{const i=typeof e=="function"?Gt(t,e,n.custom):e;s=Promise.all(li(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}const sa=Re.length;function ci(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?ci(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<sa;n++){const s=Re[n],i=t.props[s];(St(i)||i===!1)&&(e[s]=i)}return e}const ia=[...Me].reverse(),oa=Me.length;function ra(t){return e=>Promise.all(e.map(({animation:n,options:s})=>na(t,n,s)))}function aa(t){let e=ra(t),n=Mn(),s=!0;const i=l=>(c,u)=>{var d;const h=Gt(t,u,l==="exit"?(d=t.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(h){const{transition:f,transitionEnd:m,...g}=h;c={...c,...g,...m}}return c};function r(l){e=l(t)}function o(l){const{props:c}=t,u=ci(t.parent)||{},d=[],h=new Set;let f={},m=1/0;for(let x=0;x<oa;x++){const p=ia[x],y=n[p],v=c[p]!==void 0?c[p]:u[p],b=St(v),A=p===l?y.isActive:null;A===!1&&(m=x);let C=v===u[p]&&v!==c[p]&&b;if(C&&s&&t.manuallyAnimateOnMount&&(C=!1),y.protectedKeys={...f},!y.isActive&&A===null||!v&&!y.prevProp||Wt(v)||typeof v=="boolean")continue;const U=la(y.prevProp,v);let w=U||p===l&&y.isActive&&!C&&b||x>m&&b,B=!1;const I=Array.isArray(v)?v:[v];let rt=I.reduce(i(p),{});A===!1&&(rt={});const{prevResolvedValues:rn={}}=y,Ji={...rn,...rt},an=j=>{w=!0,h.has(j)&&(B=!0,h.delete(j)),y.needsAnimating[j]=!0;const W=t.getValue(j);W&&(W.liveStyle=!1)};for(const j in Ji){const W=rt[j],Yt=rn[j];if(f.hasOwnProperty(j))continue;let qt=!1;he(W)&&he(Yt)?qt=!As(W,Yt):qt=W!==Yt,qt?W!=null?an(j):h.add(j):W!==void 0&&h.has(j)?an(j):y.protectedKeys[j]=!0}y.prevProp=v,y.prevResolvedValues=rt,y.isActive&&(f={...f,...rt}),s&&t.blockInitialAnimation&&(w=!1),w&&(!(C&&U)||B)&&d.push(...I.map(j=>({animation:j,options:{type:p}})))}if(h.size){const x={};h.forEach(p=>{const y=t.getBaseTarget(p),v=t.getValue(p);v&&(v.liveStyle=!0),x[p]=y??null}),d.push({animation:x})}let g=!!d.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(d):Promise.resolve()}function a(l,c){var u;if(n[l].isActive===c)return Promise.resolve();(u=t.variantChildren)===null||u===void 0||u.forEach(h=>{var f;return(f=h.animationState)===null||f===void 0?void 0:f.setActive(l,c)}),n[l].isActive=c;const d=o(l);for(const h in n)n[h].protectedKeys={};return d}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n,reset:()=>{n=Mn(),s=!0}}}function la(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!As(e,t):!1}function tt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Mn(){return{animate:tt(!0),whileInView:tt(),whileHover:tt(),whileTap:tt(),whileDrag:tt(),whileFocus:tt(),exit:tt()}}class Q{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ca extends Q{constructor(e){super(e),e.animationState||(e.animationState=aa(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Wt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let ua=0;class ha extends Q{constructor(){super(...arguments),this.id=ua++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const da={animation:{Feature:ca},exit:{Feature:ha}},ui=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function zt(t,e="page"){return{point:{x:t[`${e}X`],y:t[`${e}Y`]}}}const fa=t=>e=>ui(e)&&t(e,zt(e));function G(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function X(t,e,n,s){return G(t,e,fa(n),s)}const Rn=(t,e)=>Math.abs(t-e);function ma(t,e){const n=Rn(t.x,e.x),s=Rn(t.y,e.y);return Math.sqrt(n**2+s**2)}class hi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=ie(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,f=ma(d.offset,{x:0,y:0})>=3;if(!h&&!f)return;const{point:m}=d,{timestamp:g}=R;this.history.push({...m,timestamp:g});const{onStart:x,onMove:p}=this.handlers;h||(x&&x(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,d)},this.handlePointerMove=(d,h)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=se(h,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(d,h)=>{this.end();const{onEnd:f,onSessionEnd:m,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ie(d.type==="pointercancel"?this.lastMoveEventInfo:se(h,this.transformPagePoint),this.history);this.startEvent&&f&&f(d,x),m&&m(d,x)},!ui(e))return;this.dragSnapToOrigin=r,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const o=zt(e),a=se(o,this.transformPagePoint),{point:l}=a,{timestamp:c}=R;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,ie(a,this.history)),this.removeListeners=H(X(this.contextWindow,"pointermove",this.handlePointerMove),X(this.contextWindow,"pointerup",this.handlePointerUp),X(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function se(t,e){return e?{point:e(t.point)}:t}function En(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ie({point:t},e){return{point:t,delta:En(t,di(e)),offset:En(t,pa(e)),velocity:ga(e,.1)}}function pa(t){return t[0]}function di(t){return t[t.length-1]}function ga(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=di(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>$(e)));)n--;if(!s)return{x:0,y:0};const r=z(i.timestamp-s.timestamp);if(r===0)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function fi(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const Ln=fi("dragHorizontal"),Fn=fi("dragVertical");function mi(t){let e=!1;if(t==="y")e=Fn();else if(t==="x")e=Ln();else{const n=Ln(),s=Fn();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function pi(){const t=mi(!0);return t?(t(),!1):!0}function ct(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}const gi=1e-4,ya=1-gi,va=1+gi,yi=.01,xa=0-yi,Ta=0+yi;function k(t){return t.max-t.min}function Pa(t,e,n){return Math.abs(t-e)<=n}function jn(t,e,n,s=.5){t.origin=s,t.originPoint=D(e.min,e.max,t.origin),t.scale=k(n)/k(e),t.translate=D(n.min,n.max,t.origin)-t.originPoint,(t.scale>=ya&&t.scale<=va||isNaN(t.scale))&&(t.scale=1),(t.translate>=xa&&t.translate<=Ta||isNaN(t.translate))&&(t.translate=0)}function Pt(t,e,n,s){jn(t.x,e.x,n.x,s?s.originX:void 0),jn(t.y,e.y,n.y,s?s.originY:void 0)}function kn(t,e,n){t.min=n.min+e.min,t.max=t.min+k(e)}function ba(t,e,n){kn(t.x,e.x,n.x),kn(t.y,e.y,n.y)}function Bn(t,e,n){t.min=e.min-n.min,t.max=t.min+k(e)}function bt(t,e,n){Bn(t.x,e.x,n.x),Bn(t.y,e.y,n.y)}function Sa(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?D(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?D(n,t,s.max):Math.min(t,n)),t}function In(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Aa(t,{top:e,left:n,bottom:s,right:i}){return{x:In(t.x,n,i),y:In(t.y,e,s)}}function On(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function wa(t,e){return{x:On(t.x,e.x),y:On(t.y,e.y)}}function Va(t,e){let n=.5;const s=k(t),i=k(e);return i>s?n=mt(e.min,e.max-s,t.min):s>i&&(n=mt(t.min,t.max-i,e.min)),Z(0,1,n)}function Ca(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const be=.35;function Da(t=be){return t===!1?t=0:t===!0&&(t=be),{x:Nn(t,"left","right"),y:Nn(t,"top","bottom")}}function Nn(t,e,n){return{min:Un(t,e),max:Un(t,n)}}function Un(t,e){return typeof t=="number"?t:t[e]||0}const _n=()=>({translate:0,scale:1,origin:0,originPoint:0}),ut=()=>({x:_n(),y:_n()}),Kn=()=>({min:0,max:0}),M=()=>({x:Kn(),y:Kn()});function N(t){return[t("x"),t("y")]}function vi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Ma({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Ra(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function oe(t){return t===void 0||t===1}function Se({scale:t,scaleX:e,scaleY:n}){return!oe(t)||!oe(e)||!oe(n)}function et(t){return Se(t)||xi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function xi(t){return Wn(t.x)||Wn(t.y)}function Wn(t){return t&&t!=="0%"}function Ut(t,e,n){const s=t-n,i=e*s;return n+i}function Gn(t,e,n,s,i){return i!==void 0&&(t=Ut(t,i,s)),Ut(t,n,s)+e}function Ae(t,e=0,n=1,s,i){t.min=Gn(t.min,e,n,s,i),t.max=Gn(t.max,e,n,s,i)}function Ti(t,{x:e,y:n}){Ae(t.x,e.translate,e.scale,e.originPoint),Ae(t.y,n.translate,n.scale,n.originPoint)}const $n=.999999999999,zn=1.0000000000001;function Ea(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let r,o;for(let a=0;a<i;a++){r=n[a],o=r.projectionDelta;const{visualElement:l}=r.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&dt(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,Ti(t,o)),s&&et(r.latestValues)&&dt(t,r.latestValues))}e.x<zn&&e.x>$n&&(e.x=1),e.y<zn&&e.y>$n&&(e.y=1)}function ht(t,e){t.min=t.min+e,t.max=t.max+e}function Hn(t,e,n,s,i=.5){const r=D(t.min,t.max,i);Ae(t,e,n,r,s)}function dt(t,e){Hn(t.x,e.x,e.scaleX,e.scale,e.originX),Hn(t.y,e.y,e.scaleY,e.scale,e.originY)}function Pi(t,e){return vi(Ra(t.getBoundingClientRect(),e))}function La(t,e,n){const s=Pi(t,n),{scroll:i}=e;return i&&(ht(s.x,i.offset.x),ht(s.y,i.offset.y)),s}const bi=({current:t})=>t?t.ownerDocument.defaultView:null,Fa=new WeakMap;class ja{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=M(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(zt(u,"page").point)},r=(u,d)=>{const{drag:h,dragPropagation:f,onDragStart:m}=this.getProps();if(h&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=mi(h),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),N(x=>{let p=this.getAxisMotionValue(x).get()||0;if(_.test(p)){const{projection:y}=this.visualElement;if(y&&y.layout){const v=y.layout.layoutBox[x];v&&(p=k(v)*(parseFloat(p)/100))}}this.originPoint[x]=p}),m&&V.postRender(()=>m(u,d)),Te(this.visualElement,"transform");const{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},o=(u,d)=>{const{dragPropagation:h,dragDirectionLock:f,onDirectionLock:m,onDrag:g}=this.getProps();if(!h&&!this.openGlobalLock)return;const{offset:x}=d;if(f&&this.currentDirection===null){this.currentDirection=ka(x),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",d.point,x),this.updateAxis("y",d.point,x),this.visualElement.render(),g&&g(u,d)},a=(u,d)=>this.stop(u,d),l=()=>N(u=>{var d;return this.getAnimationState(u)==="paused"&&((d=this.getAxisMotionValue(u).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new hi(e,{onSessionStart:i,onStart:r,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:bi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:r}=this.getProps();r&&V.postRender(()=>r(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Lt(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let o=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(o=Sa(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,r=this.constraints;n&&ct(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=Aa(i.layoutBox,n):this.constraints=!1,this.elastic=Da(s),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&N(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Ca(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ct(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=La(s,i.root,this.visualElement.getTransformPagePoint());let o=wa(i.layout.layoutBox,r);if(n){const a=n(Ma(o));this.hasMutatedConstraints=!!a,a&&(o=vi(a))}return o}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=N(u=>{if(!Lt(u,n,this.currentDirection))return;let d=l&&l[u]||{};o&&(d={min:0,max:0});const h=i?200:1e6,f=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:h,bounceDamping:f,timeConstant:750,restDelta:1,restSpeed:10,...r,...d};return this.startAxisValueAnimation(u,m)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Te(this.visualElement,e),s.start($e(e,s,0,n,this.visualElement,!1))}stopAnimation(){N(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){N(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){N(n=>{const{drag:s}=this.getProps();if(!Lt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];r.set(e[n]-D(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!ct(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};N(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const l=a.get();i[o]=Va({min:l,max:l},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),N(o=>{if(!Lt(o,e,null))return;const a=this.getAxisMotionValue(o),{min:l,max:c}=this.constraints[o];a.set(D(l,c,i[o]))})}addListeners(){if(!this.visualElement.current)return;Fa.set(this.visualElement,this);const e=this.visualElement.current,n=X(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();ct(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.read(s);const o=G(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(N(u=>{const d=this.getAxisMotionValue(u);d&&(this.originPoint[u]+=l[u].translate,d.set(d.get()+l[u].translate))}),this.visualElement.render())});return()=>{o(),n(),r(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=be,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function Lt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function ka(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Ba extends Q{constructor(e){super(e),this.removeGroupControls=F,this.removeListeners=F,this.controls=new ja(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||F}unmount(){this.removeGroupControls(),this.removeListeners()}}const Xn=t=>(e,n)=>{t&&V.postRender(()=>t(e,n))};class Ia extends Q{constructor(){super(...arguments),this.removePointerDownListener=F}onPointerDown(e){this.session=new hi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:bi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:Xn(e),onStart:Xn(n),onMove:s,onEnd:(r,o)=>{delete this.session,i&&V.postRender(()=>i(r,o))}}}mount(){this.removePointerDownListener=X(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const qe=P.createContext(null);function Oa(){const t=P.useContext(qe);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=P.useId();P.useEffect(()=>s(i),[]);const r=P.useCallback(()=>n&&n(i),[i,n]);return!e&&n?[!1,r]:[!0]}const Si=P.createContext({}),Ai=P.createContext({}),jt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Yn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const yt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(T.test(t))t=parseFloat(t);else return t;const n=Yn(t,e.target.x),s=Yn(t,e.target.y);return`${n}% ${s}%`}},Na={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=J.parse(t);if(i.length>5)return s;const r=J.createTransformer(t),o=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+o]/=a,i[1+o]/=l;const c=D(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=c),typeof i[3+o]=="number"&&(i[3+o]/=c),r(i)}},_t={};function Ua(t){Object.assign(_t,t)}const{schedule:Ze,cancel:Bc}=ws(queueMicrotask,!1);class _a extends P.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;Ua(Ka),r&&(n.group&&n.group.add(r),s&&s.register&&i&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),jt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:r}=this.props,o=s.projection;return o&&(o.isPresent=r,i||e.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?o.promote():o.relegate()||V.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Ze.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function wi(t){const[e,n]=Oa(),s=P.useContext(Si);return S.jsx(_a,{...t,layoutGroup:s,switchLayoutGroup:P.useContext(Ai),isPresent:e,safeToRemove:n})}const Ka={borderRadius:{...yt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:yt,borderTopRightRadius:yt,borderBottomLeftRadius:yt,borderBottomRightRadius:yt,boxShadow:Na},Vi=["TopLeft","TopRight","BottomLeft","BottomRight"],Wa=Vi.length,qn=t=>typeof t=="string"?parseFloat(t):t,Zn=t=>typeof t=="number"||T.test(t);function Ga(t,e,n,s,i,r){i?(t.opacity=D(0,n.opacity!==void 0?n.opacity:1,$a(s)),t.opacityExit=D(e.opacity!==void 0?e.opacity:1,0,za(s))):r&&(t.opacity=D(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let o=0;o<Wa;o++){const a=`border${Vi[o]}Radius`;let l=Jn(e,a),c=Jn(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Zn(l)===Zn(c)?(t[a]=Math.max(D(qn(l),qn(c),s),0),(_.test(c)||_.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=D(e.rotate||0,n.rotate||0,s))}function Jn(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const $a=Ci(0,.5,Ls),za=Ci(.5,.95,F);function Ci(t,e,n){return s=>s<t?0:s>e?1:n(mt(t,e,s))}function Qn(t,e){t.min=e.min,t.max=e.max}function O(t,e){Qn(t.x,e.x),Qn(t.y,e.y)}function ts(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function es(t,e,n,s,i){return t-=e,t=Ut(t,1/n,s),i!==void 0&&(t=Ut(t,1/i,s)),t}function Ha(t,e=0,n=1,s=.5,i,r=t,o=t){if(_.test(e)&&(e=parseFloat(e),e=D(o.min,o.max,e/100)-o.min),typeof e!="number")return;let a=D(r.min,r.max,s);t===r&&(a-=e),t.min=es(t.min,e,n,a,i),t.max=es(t.max,e,n,a,i)}function ns(t,e,[n,s,i],r,o){Ha(t,e[n],e[s],e[i],e.scale,r,o)}const Xa=["x","scaleX","originX"],Ya=["y","scaleY","originY"];function ss(t,e,n,s){ns(t.x,e,Xa,n?n.x:void 0,s?s.x:void 0),ns(t.y,e,Ya,n?n.y:void 0,s?s.y:void 0)}function is(t){return t.translate===0&&t.scale===1}function Di(t){return is(t.x)&&is(t.y)}function os(t,e){return t.min===e.min&&t.max===e.max}function qa(t,e){return os(t.x,e.x)&&os(t.y,e.y)}function rs(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Mi(t,e){return rs(t.x,e.x)&&rs(t.y,e.y)}function as(t){return k(t.x)/k(t.y)}function ls(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Za{constructor(){this.members=[]}add(e){ze(this.members,e),e.scheduleRender()}remove(e){if(He(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Ja(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y,o=(n==null?void 0:n.z)||0;if((i||r||o)&&(s=`translate3d(${i}px, ${r}px, ${o}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:d,rotateY:h,skewX:f,skewY:m}=n;c&&(s=`perspective(${c}px) ${s}`),u&&(s+=`rotate(${u}deg) `),d&&(s+=`rotateX(${d}deg) `),h&&(s+=`rotateY(${h}deg) `),f&&(s+=`skewX(${f}deg) `),m&&(s+=`skewY(${m}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const Qa=(t,e)=>t.depth-e.depth;class tl{constructor(){this.children=[],this.isDirty=!1}add(e){ze(this.children,e),this.isDirty=!0}remove(e){He(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Qa),this.isDirty=!1,this.children.forEach(e)}}function kt(t){const e=L(t)?t.get():t;return $r(e)?e.toValue():e}function el(t,e){const n=K.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(q(s),t(r-e))};return V.read(s,!0),()=>q(s)}function nl(t){return t instanceof SVGElement&&t.tagName!=="svg"}function sl(t,e,n){const s=L(t)?t:Vt(t);return s.start($e("",s,e,n)),s.animation}const nt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},xt=typeof window<"u"&&window.MotionDebug!==void 0,re=["","X","Y","Z"],il={visibility:"hidden"},cs=1e3;let ol=0;function ae(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function Ri(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=ai(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",V,!(i||r))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&Ri(s)}function Ei({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(o={},a=e==null?void 0:e()){this.id=ol++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,xt&&(nt.totalNodes=nt.resolvedTargetDeltas=nt.recalculatedProjection=0),this.nodes.forEach(ll),this.nodes.forEach(fl),this.nodes.forEach(ml),this.nodes.forEach(cl),xt&&window.MotionDebug.record(nt)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new tl)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Xe),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=nl(o),this.instance=o;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let d;const h=()=>this.root.updateBlockedByResize=!1;t(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=el(h,250),jt.hasAnimatedSinceResize&&(jt.hasAnimatedSinceResize=!1,this.nodes.forEach(hs))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:h,hasRelativeTargetChanged:f,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const g=this.options.transition||u.getDefaultTransition()||xl,{onLayoutAnimationStart:x,onLayoutAnimationComplete:p}=u.getProps(),y=!this.targetLayout||!Mi(this.targetLayout,m)||f,v=!h&&f;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||v||h&&(y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,v);const b={...Ee(g,"layout"),onPlay:x,onComplete:p};(u.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b)}else h||hs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(pl),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ri(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const d=this.path[u];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(us);return}this.isUpdating||this.nodes.forEach(hl),this.isUpdating=!1,this.nodes.forEach(dl),this.nodes.forEach(rl),this.nodes.forEach(al),this.clearAllSnapshots();const a=K.now();R.delta=Z(0,1e3/60,a-R.timestamp),R.timestamp=a,R.isProcessing=!0,Jt.update.process(R),Jt.preRender.process(R),Jt.render.process(R),R.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ze.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ul),this.sharedNodes.forEach(gl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=M(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Di(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;o&&(a||et(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),Tl(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return M();const l=a.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(Pl))){const{scroll:u}=this.root;u&&(ht(l.x,u.offset.x),ht(l.y,u.offset.y))}return l}removeElementScroll(o){var a;const l=M();if(O(l,o),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:d,options:h}=u;u!==this.root&&d&&h.layoutScroll&&(d.wasRoot&&O(l,o),ht(l.x,d.offset.x),ht(l.y,d.offset.y))}return l}applyTransform(o,a=!1){const l=M();O(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&dt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),et(u.latestValues)&&dt(l,u.latestValues)}return et(this.latestValues)&&dt(l,this.latestValues),l}removeTransform(o){const a=M();O(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!et(c.latestValues))continue;Se(c.latestValues)&&c.updateSnapshot();const u=M(),d=c.measurePageBox();O(u,d),ss(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return et(this.latestValues)&&ss(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==R.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(o||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:h}=this.options;if(!(!this.layout||!(d||h))){if(this.resolvedRelativeTargetAt=R.timestamp,!this.targetDelta&&!this.relativeTarget){const f=this.getClosestProjectingParent();f&&f.layout&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=M(),this.relativeTargetOrigin=M(),bt(this.relativeTargetOrigin,this.layout.layoutBox,f.layout.layoutBox),O(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=M(),this.targetWithTransforms=M()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),ba(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):O(this.target,this.layout.layoutBox),Ti(this.target,this.targetDelta)):O(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=M(),this.relativeTargetOrigin=M(),bt(this.relativeTargetOrigin,this.target,f.target),O(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}xt&&nt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Se(this.parent.latestValues)||xi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===R.timestamp&&(c=!1),c)return;const{layout:u,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||d))return;O(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;Ea(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=M());const{target:m}=a;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ts(this.prevProjectionDelta.x,this.projectionDelta.x),ts(this.prevProjectionDelta.y,this.projectionDelta.y)),Pt(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!ls(this.projectionDelta.x,this.prevProjectionDelta.x)||!ls(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m)),xt&&nt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ut(),this.projectionDelta=ut(),this.projectionDeltaWithTransform=ut()}setAnimationOrigin(o,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},d=ut();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const h=M(),f=l?l.source:void 0,m=this.layout?this.layout.source:void 0,g=f!==m,x=this.getStack(),p=!x||x.members.length<=1,y=!!(g&&!p&&this.options.crossfade===!0&&!this.path.some(vl));this.animationProgress=0;let v;this.mixTargetDelta=b=>{const A=b/1e3;ds(d.x,o.x,A),ds(d.y,o.y,A),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(bt(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),yl(this.relativeTarget,this.relativeTargetOrigin,h,A),v&&qa(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=M()),O(v,this.relativeTarget)),g&&(this.animationValues=u,Ga(u,c,this.latestValues,A,y,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=A},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{jt.hasAnimatedSinceResize=!0,this.currentAnimation=sl(0,cs,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(cs),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=o;if(!(!a||!l||!c)){if(this!==o&&this.layout&&c&&Li(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||M();const d=k(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const h=k(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+h}O(a,l),dt(a,u),Pt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Za),this.sharedNodes.get(o).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&ae("z",o,c,this.animationValues);for(let u=0;u<re.length;u++)ae(`rotate${re[u]}`,o,c,this.animationValues),ae(`skew${re[u]}`,o,c,this.animationValues);o.render();for(const u in c)o.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return il;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=kt(o==null?void 0:o.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=kt(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!et(this.latestValues)&&(g.transform=u?u({},""):"none",this.hasProjected=!1),g}const h=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=Ja(this.projectionDeltaWithTransform,this.treeScale,h),u&&(c.transform=u(h,c.transform));const{x:f,y:m}=this.projectionDelta;c.transformOrigin=`${f.origin*100}% ${m.origin*100}% 0`,d.animationValues?c.opacity=d===this?(l=(a=h.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:c.opacity=d===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const g in _t){if(h[g]===void 0)continue;const{correct:x,applyTo:p}=_t[g],y=c.transform==="none"?h[g]:x(h[g],d);if(p){const v=p.length;for(let b=0;b<v;b++)c[p[b]]=y}else c[g]=y}return this.options.layoutId&&(c.pointerEvents=d===this?kt(o==null?void 0:o.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(us),this.root.sharedNodes.clear()}}}function rl(t){t.updateLayout()}function al(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:r}=t.options,o=n.source!==t.layout.source;r==="size"?N(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],f=k(h);h.min=s[d].min,h.max=h.min+f}):Li(r,n.layoutBox,s)&&N(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],f=k(s[d]);h.max=h.min+f,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[d].max=t.relativeTarget[d].min+f)});const a=ut();Pt(a,s,n.layoutBox);const l=ut();o?Pt(l,t.applyTransform(i,!0),n.measuredBox):Pt(l,s,n.layoutBox);const c=!Di(a);let u=!1;if(!t.resumeFrom){const d=t.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:h,layout:f}=d;if(h&&f){const m=M();bt(m,n.layoutBox,h.layoutBox);const g=M();bt(g,s,f.layoutBox),Mi(m,g)||(u=!0),d.options.layoutRoot&&(t.relativeTarget=g,t.relativeTargetOrigin=m,t.relativeParent=d)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function ll(t){xt&&nt.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function cl(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function ul(t){t.clearSnapshot()}function us(t){t.clearMeasurements()}function hl(t){t.isLayoutDirty=!1}function dl(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function hs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function fl(t){t.resolveTargetDelta()}function ml(t){t.calcProjection()}function pl(t){t.resetSkewAndRotation()}function gl(t){t.removeLeadSnapshot()}function ds(t,e,n){t.translate=D(e.translate,0,n),t.scale=D(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function fs(t,e,n,s){t.min=D(e.min,n.min,s),t.max=D(e.max,n.max,s)}function yl(t,e,n,s){fs(t.x,e.x,n.x,s),fs(t.y,e.y,n.y,s)}function vl(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const xl={duration:.45,ease:[.4,0,.1,1]},ms=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ps=ms("applewebkit/")&&!ms("chrome/")?Math.round:F;function gs(t){t.min=ps(t.min),t.max=ps(t.max)}function Tl(t){gs(t.x),gs(t.y)}function Li(t,e,n){return t==="position"||t==="preserve-aspect"&&!Pa(as(e),as(n),.2)}function Pl(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const bl=Ei({attachResizeListener:(t,e)=>G(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),le={current:void 0},Fi=Ei({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!le.current){const t=new bl({});t.mount(window),t.setOptions({layoutScroll:!0}),le.current=t}return le.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Sl={pan:{Feature:Ia},drag:{Feature:Ba,ProjectionNode:Fi,MeasureLayout:wi}};function ys(t,e){const n=e?"pointerenter":"pointerleave",s=e?"onHoverStart":"onHoverEnd",i=(r,o)=>{if(r.pointerType==="touch"||pi())return;const a=t.getProps();t.animationState&&a.whileHover&&t.animationState.setActive("whileHover",e);const l=a[s];l&&V.postRender(()=>l(r,o))};return X(t.current,n,i,{passive:!t.getProps()[s]})}class Al extends Q{mount(){this.unmount=H(ys(this.node,!0),ys(this.node,!1))}unmount(){}}class wl extends Q{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=H(G(this.node.current,"focus",()=>this.onFocus()),G(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const ji=(t,e)=>e?t===e?!0:ji(t,e.parentElement):!1;function ce(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,zt(n))}class Vl extends Q{constructor(){super(...arguments),this.removeStartListeners=F,this.removeEndListeners=F,this.removeAccessibleListeners=F,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),r=X(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:d}=this.node.getProps(),h=!d&&!ji(this.node.current,a.target)?u:c;h&&V.update(()=>h(a,l))},{passive:!(s.onTap||s.onPointerUp)}),o=X(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=H(r,o),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=r=>{if(r.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||ce("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&V.postRender(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=G(this.node.current,"keyup",o),ce("down",(a,l)=>{this.startPress(a,l)})},n=G(this.node.current,"keydown",e),s=()=>{this.isPressing&&ce("cancel",(r,o)=>this.cancelPress(r,o))},i=G(this.node.current,"blur",s);this.removeAccessibleListeners=H(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&V.postRender(()=>s(e,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!pi()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&V.postRender(()=>s(e,n))}mount(){const e=this.node.getProps(),n=X(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=G(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=H(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const we=new WeakMap,ue=new WeakMap,Cl=t=>{const e=we.get(t.target);e&&e(t)},Dl=t=>{t.forEach(Cl)};function Ml({root:t,...e}){const n=t||document;ue.has(n)||ue.set(n,{});const s=ue.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Dl,{root:t,...e})),s[i]}function Rl(t,e,n){const s=Ml(e);return we.set(t,n),s.observe(t),()=>{we.delete(t),s.unobserve(t)}}const El={some:0,all:1};class Ll extends Q{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,o={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:El[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,r&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:d}=this.node.getProps(),h=c?u:d;h&&h(l)};return Rl(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Fl(e,n))&&this.startObserver()}unmount(){}}function Fl({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const jl={inView:{Feature:Ll},tap:{Feature:Vl},focus:{Feature:wl},hover:{Feature:Al}},kl={layout:{ProjectionNode:Fi,MeasureLayout:wi}},ki=P.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Ht=P.createContext({}),Je=typeof window<"u",Bl=Je?P.useLayoutEffect:P.useEffect,Bi=P.createContext({strict:!1});function Il(t,e,n,s,i){var r,o;const{visualElement:a}=P.useContext(Ht),l=P.useContext(Bi),c=P.useContext(qe),u=P.useContext(ki).reducedMotion,d=P.useRef();s=s||l.renderer,!d.current&&s&&(d.current=s(t,{visualState:e,parent:a,props:n,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:u}));const h=d.current,f=P.useContext(Ai);h&&!h.projection&&i&&(h.type==="html"||h.type==="svg")&&Ol(d.current,n,i,f);const m=P.useRef(!1);P.useInsertionEffect(()=>{h&&m.current&&h.update(n,c)});const g=n[ri],x=P.useRef(!!g&&!(!((r=window.MotionHandoffIsComplete)===null||r===void 0)&&r.call(window,g))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,g)));return Bl(()=>{h&&(m.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),Ze.render(h.render),x.current&&h.animationState&&h.animationState.animateChanges())}),P.useEffect(()=>{h&&(!x.current&&h.animationState&&h.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,g)}),x.current=!1))}),h}function Ol(t,e,n,s){const{layoutId:i,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ii(t.parent)),t.projection.setOptions({layoutId:i,layout:r,alwaysMeasureLayout:!!o||a&&ct(a),visualElement:t,animationType:typeof r=="string"?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:c})}function Ii(t){if(t)return t.options.allowProjection!==!1?t.projection:Ii(t.parent)}function Nl(t,e,n){return P.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):ct(n)&&(n.current=s))},[e])}function Xt(t){return Wt(t.animate)||Re.some(e=>St(t[e]))}function Oi(t){return!!(Xt(t)||t.variants)}function Ul(t,e){if(Xt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||St(n)?n:void 0,animate:St(s)?s:void 0}}return t.inherit!==!1?e:{}}function _l(t){const{initial:e,animate:n}=Ul(t,P.useContext(Ht));return P.useMemo(()=>({initial:e,animate:n}),[vs(e),vs(n)])}function vs(t){return Array.isArray(t)?t.join(" "):t}const xs={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},pt={};for(const t in xs)pt[t]={isEnabled:e=>xs[t].some(n=>!!e[n])};function Kl(t){for(const e in t)pt[e]={...pt[e],...t[e]}}const Wl=Symbol.for("motionComponentSymbol");function Gl({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&Kl(t);function r(a,l){let c;const u={...P.useContext(ki),...a,layoutId:$l(a)},{isStatic:d}=u,h=_l(a),f=s(a,d);if(!d&&Je){zl();const m=Hl(u);c=m.MeasureLayout,h.visualElement=Il(i,f,u,e,m.ProjectionNode)}return S.jsxs(Ht.Provider,{value:h,children:[c&&h.visualElement?S.jsx(c,{visualElement:h.visualElement,...u}):null,n(i,a,Nl(f,h.visualElement,l),f,d,h.visualElement)]})}const o=P.forwardRef(r);return o[Wl]=i,o}function $l({layoutId:t}){const e=P.useContext(Si).id;return e&&t!==void 0?e+"-"+t:t}function zl(t,e){P.useContext(Bi).strict}function Hl(t){const{drag:e,layout:n}=pt;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Xl=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Qe(t){return typeof t!="string"||t.includes("-")?!1:!!(Xl.indexOf(t)>-1||/[A-Z]/u.test(t))}function Ni(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const r in n)t.style.setProperty(r,n[r])}const Ui=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function _i(t,e,n,s){Ni(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Ui.has(i)?i:Ye(i),e.attrs[i])}function Ki(t,{layout:e,layoutId:n}){return ot.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!_t[t]||t==="opacity")}function tn(t,e,n){var s;const{style:i}=t,r={};for(const o in i)(L(i[o])||e.style&&L(e.style[o])||Ki(o,t)||((s=n==null?void 0:n.getValue(o))===null||s===void 0?void 0:s.liveStyle)!==void 0)&&(r[o]=i[o]);return r}function Wi(t,e,n){const s=tn(t,e,n);for(const i in t)if(L(t[i])||L(e[i])){const r=Ct.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[r]=t[i]}return s}function Yl(t){const e=P.useRef(null);return e.current===null&&(e.current=t()),e.current}function ql({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,r){const o={latestValues:Zl(s,i,r,t),renderState:e()};return n&&(o.mount=a=>n(s,a,o)),o}const Gi=t=>(e,n)=>{const s=P.useContext(Ht),i=P.useContext(qe),r=()=>ql(t,e,s,i);return n?r():Yl(r)};function Zl(t,e,n,s){const i={},r=s(t,{});for(const h in r)i[h]=kt(r[h]);let{initial:o,animate:a}=t;const l=Xt(t),c=Oi(t);e&&c&&!l&&t.inherit!==!1&&(o===void 0&&(o=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||o===!1;const d=u?a:o;if(d&&typeof d!="boolean"&&!Wt(d)){const h=Array.isArray(d)?d:[d];for(let f=0;f<h.length;f++){const m=De(t,h[f]);if(m){const{transitionEnd:g,transition:x,...p}=m;for(const y in p){let v=p[y];if(Array.isArray(v)){const b=u?v.length-1:0;v=v[b]}v!==null&&(i[y]=v)}for(const y in g)i[y]=g[y]}}}return i}const en=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),$i=()=>({...en(),attrs:{}}),zi=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Jl={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ql=Ct.length;function tc(t,e,n){let s="",i=!0;for(let r=0;r<Ql;r++){const o=Ct[r],a=t[o];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(o.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=zi(a,Oe[o]);if(!l){i=!1;const u=Jl[o]||o;s+=`${u}(${c}) `}n&&(e[o]=c)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function nn(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let o=!1,a=!1;for(const l in e){const c=e[l];if(ot.has(l)){o=!0;continue}else if(Is(l)){i[l]=c;continue}else{const u=zi(c,Oe[l]);l.startsWith("origin")?(a=!0,r[l]=u):s[l]=u}}if(e.transform||(o||n?s.transform=tc(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=r;s.transformOrigin=`${l} ${c} ${u}`}}function Ts(t,e,n){return typeof t=="string"?t:T.transform(e+n*t)}function ec(t,e,n){const s=Ts(e,t.x,t.width),i=Ts(n,t.y,t.height);return`${s} ${i}`}const nc={offset:"stroke-dashoffset",array:"stroke-dasharray"},sc={offset:"strokeDashoffset",array:"strokeDasharray"};function ic(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?nc:sc;t[r.offset]=T.transform(-s);const o=T.transform(e),a=T.transform(n);t[r.array]=`${o} ${a}`}function sn(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...c},u,d){if(nn(t,c,d),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:f,dimensions:m}=t;h.transform&&(m&&(f.transform=h.transform),delete h.transform),m&&(i!==void 0||r!==void 0||f.transform)&&(f.transformOrigin=ec(m,i!==void 0?i:.5,r!==void 0?r:.5)),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),o!==void 0&&ic(h,o,a,l,!1)}const on=t=>typeof t=="string"&&t.toLowerCase()==="svg",oc={useVisualState:Gi({scrapeMotionValuesFromProps:Wi,createRenderState:$i,onMount:(t,e,{renderState:n,latestValues:s})=>{V.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),V.render(()=>{sn(n,s,on(e.tagName),t.transformTemplate),_i(e,n)})}})},rc={useVisualState:Gi({scrapeMotionValuesFromProps:tn,createRenderState:en})};function Hi(t,e,n){for(const s in e)!L(e[s])&&!Ki(s,n)&&(t[s]=e[s])}function ac({transformTemplate:t},e){return P.useMemo(()=>{const n=en();return nn(n,e,t),Object.assign({},n.vars,n.style)},[e])}function lc(t,e){const n=t.style||{},s={};return Hi(s,n,t),Object.assign(s,ac(t,e)),s}function cc(t,e){const n={},s=lc(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const uc=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Kt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||uc.has(t)}let Xi=t=>!Kt(t);function hc(t){t&&(Xi=e=>e.startsWith("on")?!Kt(e):t(e))}try{hc(require("@emotion/is-prop-valid").default)}catch{}function dc(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Xi(i)||n===!0&&Kt(i)||!e&&!Kt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function fc(t,e,n,s){const i=P.useMemo(()=>{const r=$i();return sn(r,e,on(s),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){const r={};Hi(r,t.style,t),i.style={...r,...i.style}}return i}function mc(t=!1){return(n,s,i,{latestValues:r},o)=>{const l=(Qe(n)?fc:cc)(s,r,o,n),c=dc(s,typeof n=="string",t),u=n!==P.Fragment?{...c,...l,ref:i}:{},{children:d}=s,h=P.useMemo(()=>L(d)?d.get():d,[d]);return P.createElement(n,{...u,children:h})}}function pc(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const o={...Qe(s)?oc:rc,preloadedFeatures:t,useRender:mc(i),createVisualElement:e,Component:s};return Gl(o)}}const Ve={current:null},Yi={current:!1};function gc(){if(Yi.current=!0,!!Je)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ve.current=t.matches;t.addListener(e),e()}else Ve.current=!1}function yc(t,e,n){for(const s in e){const i=e[s],r=n[s];if(L(i))t.addValue(s,i);else if(L(r))t.addValue(s,Vt(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const o=t.getValue(s);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=t.getStaticValue(s);t.addValue(s,Vt(o!==void 0?o:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Ps=new WeakMap,vc=[...Us,E,J],xc=t=>vc.find(Ns(t)),bs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Tc{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ke,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const h=K.now();this.renderScheduledAt<h&&(this.renderScheduledAt=h,V.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=Xt(n),this.isVariantNode=Oi(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...d}=this.scrapeMotionValuesFromProps(n,{},this);for(const h in d){const f=d[h];l[h]!==void 0&&L(f)&&f.set(l[h],!1)}}mount(e){this.current=e,Ps.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Yi.current||gc(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ve.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ps.delete(this.current),this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=ot.has(e),i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&V.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r(),o&&o(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in pt){const n=pt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):M()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<bs.length;s++){const i=bs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,o=e[r];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=yc(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=Vt(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){var s;let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(s=this.getBaseTargetFromProps(this.props,e))!==null&&s!==void 0?s:this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(ks(i)||js(i))?i=parseFloat(i):!xc(i)&&J.test(n)&&(i=Xs(e,n)),this.setBaseTarget(e,L(i)?i.get():i)),L(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props;let i;if(typeof s=="string"||typeof s=="object"){const o=De(this.props,s,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(i=o[e])}if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!L(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Xe),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class qi extends Tc{constructor(){super(...arguments),this.KeyframeResolver=Ys}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}}function Pc(t){return window.getComputedStyle(t)}class bc extends qi{constructor(){super(...arguments),this.type="html",this.renderInstance=Ni}readValueFromInstance(e,n){if(ot.has(n)){const s=Ne(n);return s&&s.default||0}else{const s=Pc(e),i=(Is(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Pi(e,n)}build(e,n,s){nn(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return tn(e,n,s)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}class Sc extends qi{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=M}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(ot.has(n)){const s=Ne(n);return s&&s.default||0}return n=Ui.has(n)?n:Ye(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Wi(e,n,s)}build(e,n,s){sn(e,n,this.isSVGTag,s.transformTemplate)}renderInstance(e,n,s,i){_i(e,n,s,i)}mount(e){this.isSVGTag=on(e.tagName),super.mount(e)}}const Ac=(t,e)=>Qe(t)?new Sc(e):new bc(e,{allowProjection:t!==P.Fragment}),wc=pc({...da,...jl,...Sl,...kl},Ac),Ce=no(wc),Vc=50,Ss="card-id",at=[{name:"Backlog",badgeColor:"bg-secondary text-white",titleColor:"text-secondary",id:"0192f28a-5539-7704-bf7f-29b7a80617e8"},{name:"A fazer",badgeColor:"bg-warn text-warn-foreground",titleColor:"text-warn",id:"0192f28b-a7c8-7da6-a82a-6c1d2ec6da91"},{name:"Em progresso",badgeColor:"bg-info text-info-foreground",titleColor:"text-info",id:"0192f28b-ba96-73dd-af7b-fe1fea6557c8"},{name:"Concluída",badgeColor:"bg-success text-white",titleColor:"text-success",id:"0192f28b-cb62-7087-a4c7-11b0fcaac444"}],Cc=[{title:"Comunicar com o cliente X",id:"1",statusId:at[0].id},{title:"Adicionar advogados a plataforma Data Venia",id:"2",statusId:at[0].id},{title:"Ligação para o escritório de justiça",id:"5",statusId:at[1].id},{title:"Prazo fatal",id:"8",statusId:at[2].id},{title:"Processo do cliente Y",id:"10",statusId:at[3].id}],Dc=t=>{const[e,n]=P.useState(Cc);return S.jsx("div",{className:"flex h-full w-full gap-4 overflow-auto",children:at.map(s=>S.jsx(Mc,{cards:e,column:s,setCards:n,onClick:t.onClick},s.id))})},Mc=t=>{const[e,n]=P.useState(!1),s=(h,f)=>h.dataTransfer.setData(Ss,f.id),i=h=>{(h||c()).forEach(m=>m.style.opacity="0")},r=h=>{const f=c();i(f);const m=l(h,f);m.element&&(m.element.style.opacity="1")},o=h=>{var p;const f=h.dataTransfer.getData(Ss);n(!1),i();const m=c(),x=((p=l(h,m).element)==null?void 0:p.dataset.before)||"-1";if(x!==f){let y=[...t.cards],v=y.find(C=>C.id===f);if(!v)return;if(v={...v,statusId:t.column.id},y=y.filter(C=>C.id!==f),x==="-1")return t.setCards(y.concat(v));const A=y.findIndex(C=>C.id===x);return A===void 0?void 0:(y.splice(A,0,v),t.setCards(y))}},a=h=>{h.preventDefault(),r(h),n(!0)},l=(h,f)=>f.reduce((m,g)=>{const x=g.getBoundingClientRect(),p=h.clientY-(x.top+Vc);return p<0&&p>m.offset?{offset:p,element:g}:m},{offset:Number.NEGATIVE_INFINITY,element:f[f.length-1]}),c=()=>Array.from(document.querySelectorAll(`[data-column="${t.column.id}"]`)),u=()=>{i(),n(!1)},d=t.cards.filter(h=>h.statusId===t.column.id);return S.jsxs("div",{className:"h-full w-full max-w-64 shrink-0",children:[S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsx("span",{className:`flex size-5 items-center justify-center rounded-full proportional-nums antialiased ${t.column.badgeColor} text-sm`,children:d.length}),S.jsx("h3",{className:`font-medium ${t.column.titleColor}`,children:t.column.name})]}),S.jsxs("div",{onDrop:o,onDragOver:a,onDragLeave:u,className:`h-auto w-full pb-20 transition-colors ${e?"bg-card-background/50":"bg-transparent"}`,children:[d.map(h=>P.createElement(Rc,{...h,item:h,onClick:t.onClick,key:h.id,handleDragStart:s})),S.jsx(Zi,{beforeId:null,status:t.column.id}),S.jsx(Ec,{statusId:t.column.id,setCards:t.setCards})]})]})},Rc=({onClick:t,handleDragStart:e,item:n})=>S.jsxs(P.Fragment,{children:[S.jsx(Zi,{beforeId:n.id,status:n.statusId}),S.jsxs(Ce.div,{layout:!0,draggable:!0,layoutId:n.id,onDragStart:s=>e(s,n),className:"relative cursor-grab rounded-card border border-card-border bg-card-background p-4 text-foreground shadow active:cursor-grabbing",children:[S.jsx("button",{onClick:()=>t==null?void 0:t(n),className:"w-fit text-balance text-left text-sm hover:text-primary hover:underline",children:n.title}),S.jsx("div",{className:"absolute right-2 top-1 flex items-center gap-2",children:S.jsxs(to,{hover:!1,label:S.jsx("span",{children:S.jsx(eo,{size:14})}),children:[S.jsx(ln,{label:"Editar"}),S.jsx(ln,{label:"Excluir"})]})})]})]}),Zi=({beforeId:t,status:e})=>S.jsx("div",{"data-column":e,"data-before":t||"-1",className:"my-0.5 h-0.5 w-full bg-primary opacity-0"}),Ec=({statusId:t,setCards:e})=>{const[n,s]=P.useState(""),[i,r]=P.useState(!1),o=a=>{if(a.preventDefault(),!n.trim().length)return;const l={statusId:t,title:n.trim(),id:Math.random().toString()};e(c=>c.concat(l)),r(!1)};return S.jsx(P.Fragment,{children:i?S.jsxs(Ce.form,{layout:!0,onSubmit:o,children:[S.jsx("textarea",{onChange:a=>s(a.target.value),autoFocus:!0,placeholder:"Add new task...",className:"w-full rounded border border-primary bg-primary-subtle/40 p-4 text-sm text-foreground"}),S.jsxs("div",{className:"mt-1.5 flex items-center justify-end gap-1.5",children:[S.jsx(Zt,{size:"small",theme:"raw",onClick:()=>r(!1),children:"Cancelar"}),S.jsx(Zt,{size:"small",type:"submit",icon:S.jsx(cn,{size:16}),children:S.jsx("span",{children:"Confirmar"})})]})]}):S.jsxs(Zt,{layout:!0,theme:"raw",size:"small",as:Ce.button,onClick:()=>r(!0),children:[S.jsx(cn,{size:16}),S.jsx("span",{children:"Nova tarefa"})]})})};function Oc(){return S.jsx(Dc,{onClick:t=>alert(JSON.stringify(t,null,4))})}export{Oc as default};
