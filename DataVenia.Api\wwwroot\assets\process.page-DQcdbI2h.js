import{e as o,d as t,k as i,j as a,r as d,O as p,l as c,a as m,u as g,b as u,m as x,F as f,N as h}from"./index-DxHSBLqJ.js";import{C as j}from"./card-header-title-MEld3mYM.js";import{E as I}from"./external-link-TCLM46lm.js";/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=o("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var r;(s=>{s.create=e=>({tags:e.tags||[],id:e.id||"",processId:e.processId||"",legalFolder:e.legalFolder||"",updatedAt:new Date().toISOString(),legalInstance:e.legalInstance||""})})(r||(r={}));const k=[r.create({id:"6aa9183f-4f8d-4c5f-b9c1-ef2ef2e30a12",legalFolder:"Processo X",processId:"12345",legalInstance:"1ª instância",tags:["Bancário","Tributário"]}),r.create({processId:"88888",legalFolder:"Processo WWW",legalInstance:"2ª instância",id:"75129047-1363-41b4-83e0-8ba4f70d6a08",tags:["Trabalhista"]})],P=async s=>(await u(800),t.jsonResponse({items:k})),v=x(s=>{s.add("processId","Nº do processo",{Element:e=>a.jsxs(t.Link,{href:c.processById,paths:{id:e.value},className:"link flex w-fit items-center gap-2",children:[a.jsx(I,{size:16}),e.value]})}),s.add("legalFolder","Nome do processo"),s.add("updatedAt","Última atualização",{Element:e=>a.jsx("span",{className:"capitalize",children:f.date(new Date(e.value))})}),s.add("tags","Tags",{Element:e=>a.jsx("span",{className:"flex items-center gap-2",children:e.value.map(l=>a.jsx(h,{theme:"neutral",size:"small",children:l}))})})});function E(){const s=t.useDataLoader(),e=!s,l=(s==null?void 0:s.items)??[],n=i("@lentium/processes",{cols:v});return a.jsx(d.Fragment,{children:a.jsx(p,{title:a.jsx(j,{title:"Meus processos",side:a.jsx(t.Link,{href:c.newProcess,children:a.jsxs(m,{size:"small",children:[a.jsx(N,{size:16}),"Novo processo"]})})}),children:a.jsx(g,{loading:e,...n,operations:!1,rows:l})})})}export{E as default,P as loader};
