﻿using DataVenia.Common.Domain;
using MediatR;

namespace DataVenia.Common.Application.Messaging;

public interface ICommand : IRequest<Result>, IBaseCommand;
public interface ICommandFr : IRequest<FluentResults.Result>, IBaseCommand;

public interface ICommand<TResponse> : IRequest<Result<TResponse>>, IBaseCommand;
public interface ICommandFr<TResponse> : IRequest<FluentResults.Result<TResponse>>, IBaseCommand;

public interface IBaseCommand;
