﻿using DataVenia.Modules.Lawsuits.Domain.Outbox;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using OutboxDomain = DataVenia.Modules.Lawsuits.Domain.Outbox.Outbox;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Lawsuits.Infrastructure.Outbox;

public sealed class OutboxRepository : IOutboxRepository
{
    private readonly LawsuitsDbContext _context;

    public OutboxRepository(LawsuitsDbContext context)
    {
        _context = context;
    }

    public void Insert(OutboxDomain message)
    {
        _context.Set<OutboxDomain>().Add(message);
    }

    public async Task<IEnumerable<OutboxDomain>> GetUnprocessedMessagesAsync(CancellationToken cancellationToken)
    {
        return await _context.Set<OutboxDomain>()
            .Where(m => !m.Processed)
            .ToListAsync(cancellationToken);
    }

    public void MarkAsProcessed(OutboxDomain message)
    {
        message.Processed = true;
        message.ProcessedAt = DateTime.UtcNow;
        _context.Set<OutboxDomain>().Update(message);
    }
}
