﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Client.UpdateClient;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Domain.Company;
using DataVenia.Modules.Users.Domain.Lawyers;
using Microsoft.Extensions.Logging;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;

namespace DataVenia.Modules.Users.Application.Company.UpdateCompanyPatch;

public class UpdateCompanyPatchCommandHandler(ICompanyRepository companyRepository, IUnitOfWork unitOfWork, ILogger<UpdateCompanyPatchCommandHandler> logger)
: ICommandHandler<UpdateCompanyPatchCommand>
{
public async Task<Result> Handle(UpdateCompanyPatchCommand request, CancellationToken cancellationToken)
{
    Domain.Company.Company? company = await companyRepository.GetSingleAsync(x => x.Id == request.CompanyId, cancellationToken);

    if (company is null)
    {
        logger.LogError("Company not found: {CompanyId}", request.CompanyId);
        return Result.Failure(LawyerErrors.NotFound(request.CompanyId));
    }

    company.Update(request.Name, request.Cnpj);

    try
    {
        await unitOfWork.SaveChangesAsync(cancellationToken);
    } catch (Exception ex)
    {
        logger.LogError(ex, "Error saving company to database.");
        return Result.Failure<Guid>(Error.InternalServerError());
    }

    return Result.Success();
}
}
