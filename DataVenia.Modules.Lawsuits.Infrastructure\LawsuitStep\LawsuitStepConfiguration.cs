﻿using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.LawsuitSteps;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitStep;

internal sealed class LawsuitStepConfiguration : IEntityTypeConfiguration<Domain.LawsuitSteps.LawsuitStep>
{
    public void Configure(EntityTypeBuilder<Domain.LawsuitSteps.LawsuitStep> builder)
    {
        builder.ToTable("lawsuit_step");

        // Chave primária e tipo UUID
        builder.HasKey(ls => ls.Id);
        builder.Property(ls => ls.Id).HasColumnType("uuid");

        // Foreign key para Lawsuit
        builder.Property(ls => ls.Cnj)
            .IsRequired()
            .HasColumnType("text")
            .HasMaxLength(50);

        // Coluna para o ID da instância legal (catálogo)
        builder.Property(ls => ls.LegalInstanceId)
            .IsRequired()
            .HasColumnType("text")
            .HasMaxLength(50);

        // Título e descrição, opcionais
        builder.Property(ls => ls.Title)
            .HasColumnType("text");
            
        builder.Property(ls => ls.Description)
            .HasColumnType("text");

        // Data em que o evento ocorreu
        builder.Property(ls => ls.OccurredAt)
            .IsRequired()
            .HasColumnType("timestamp with time zone");

        // Ação realizada (opcional)
        builder.Property(ls => ls.ActionBy)
            .HasColumnType("text");

        // Campo para indicar se o step é secreto
        builder.Property(ls => ls.Secret)
            .IsRequired()
            .HasColumnType("boolean");

        // Data de atualização (opcional)
        builder.Property(ls => ls.UpdatedAt)
            .HasColumnType("timestamp with time zone");

        // Data de criação com valor default NOW()
        builder.Property(ls => ls.CreatedAt)
            .IsRequired()
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("NOW()");
    }
}
