import{e as _e,r as k,j as c,z as G,G as R,F as M,n as E,a as ee,C as He,g as Le}from"./index-DxHSBLqJ.js";/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=_e("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),ne=86399999,ce=604799999,oe=10080,Ge=60*1e3,re=24*60,ge=k.createContext(null),V=()=>{const t=k.useContext(ge);if(t===null)throw new Error("Context Error");return t},be=6048e5,Re=864e5,Ve=6e4,le=Symbol.for("constructDateFrom");function D(t,e){return typeof t=="function"?t(e):t&&typeof t=="object"&&le in t?t[le](e):t instanceof Date?new t.constructor(e):new Date(e)}function v(t,e){return D(e||t,t)}function ve(t,e,n){const r=v(t,n==null?void 0:n.in);return isNaN(e)?D((n==null?void 0:n.in)||t,NaN):(e&&r.setDate(r.getDate()+e),r)}function ke(t,e,n){const r=v(t,n==null?void 0:n.in);if(isNaN(e))return D((n==null?void 0:n.in)||t,NaN);if(!e)return r;const a=r.getDate(),s=D((n==null?void 0:n.in)||t,r.getTime());s.setMonth(r.getMonth()+e+1,0);const i=s.getDate();return a>=i?s:(r.setFullYear(s.getFullYear(),s.getMonth(),a),r)}function g(t,e,n){const{years:r=0,months:a=0,weeks:s=0,days:i=0,hours:o=0,minutes:l=0,seconds:d=0}=e,u=v(t,n==null?void 0:n.in),h=a||r?ke(u,a+r*12):u,w=i||s?ve(h,i+s*7):h,x=l+o*60,m=(d+x*60)*1e3;return D((n==null?void 0:n.in)||t,+w+m)}let $e={};function $(){return $e}function S(t,e){var o,l,d,u;const n=$(),r=(e==null?void 0:e.weekStartsOn)??((l=(o=e==null?void 0:e.locale)==null?void 0:o.options)==null?void 0:l.weekStartsOn)??n.weekStartsOn??((u=(d=n.locale)==null?void 0:d.options)==null?void 0:u.weekStartsOn)??0,a=v(t,e==null?void 0:e.in),s=a.getDay(),i=(s<r?7:0)+s-r;return a.setDate(a.getDate()-i),a.setHours(0,0,0,0),a}function X(t,e){return S(t,{...e,weekStartsOn:1})}function De(t,e){const n=v(t,e==null?void 0:e.in),r=n.getFullYear(),a=D(n,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);const s=X(a),i=D(n,0);i.setFullYear(r,0,4),i.setHours(0,0,0,0);const o=X(i);return n.getTime()>=s.getTime()?r+1:n.getTime()>=o.getTime()?r:r-1}function de(t){const e=v(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function B(t,...e){const n=D.bind(null,e.find(r=>typeof r=="object"));return e.map(n)}function N(t,e){const n=v(t,e==null?void 0:e.in);return n.setHours(0,0,0,0),n}function Be(t,e,n){const[r,a]=B(n==null?void 0:n.in,t,e),s=N(r),i=N(a),o=+s-de(s),l=+i-de(i);return Math.round((o-l)/Re)}function Qe(t,e){const n=De(t,e),r=D(t,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),X(r)}function Xe(t){return D(t,Date.now())}function A(t,e,n){const[r,a]=B(n==null?void 0:n.in,t,e);return+N(r)==+N(a)}function ze(t){return t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"}function Je(t){return!(!ze(t)&&typeof t!="number"||isNaN(+v(t)))}function Ke(t){return e=>{const n=Math.trunc,r=n(e);return r===0?0:r}}function U(t,e){return+v(t)-+v(e)}function C(t,e,n){const r=U(t,e)/Ve;return Ke()(r)}function Y(t,e){const n=v(t,e==null?void 0:e.in);return n.setHours(23,59,59,999),n}function Ne(t,e){const n=v(t,e==null?void 0:e.in),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(23,59,59,999),n}function Se(t,e){const[n,r]=B(t,e.start,e.end);return{start:n,end:r}}function ae(t,e){const{start:n,end:r}=Se(e==null?void 0:e.in,t);let a=+n>+r;const s=a?+n:+r,i=a?r:n;i.setHours(0,0,0,0);let o=1;const l=[];for(;+i<=s;)l.push(D(n,i)),i.setDate(i.getDate()+o),i.setHours(0,0,0,0);return a?l.reverse():l}function se(t,e){const{start:n,end:r}=Se(e==null?void 0:e.in,t);let a=+n>+r;const s=a?+n:+r,i=a?r:n;i.setMinutes(0,0,0);let o=1;const l=[];for(;+i<=s;)l.push(D(n,i)),i.setHours(i.getHours()+o);return a?l.reverse():l}function ie(t,e){const n=v(t,e==null?void 0:e.in);return n.setDate(1),n.setHours(0,0,0,0),n}function Ue(t,e){const n=v(t,e==null?void 0:e.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}function F(t,e){var o,l;const n=$(),r=n.weekStartsOn??((l=(o=n.locale)==null?void 0:o.options)==null?void 0:l.weekStartsOn)??0,a=v(t,e==null?void 0:e.in),s=a.getDay(),i=(s<r?-7:0)+6-(s-r);return a.setDate(a.getDate()+i),a.setHours(23,59,59,999),a}function I(t){return Y(Date.now(),t)}const Ze={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},pe=(t,e,n)=>{let r;const a=Ze[t];return typeof a=="string"?r=a:e===1?r=a.one:r=a.other.replace("{{count}}",e.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function te(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const et={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},tt={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},nt={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},rt={date:te({formats:et,defaultWidth:"full"}),time:te({formats:tt,defaultWidth:"full"}),dateTime:te({formats:nt,defaultWidth:"full"})},at={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},st=(t,e,n,r)=>at[t];function H(t){return(e,n)=>{const r=n!=null&&n.context?String(n.context):"standalone";let a;if(r==="formatting"&&t.formattingValues){const i=t.defaultFormattingWidth||t.defaultWidth,o=n!=null&&n.width?String(n.width):i;a=t.formattingValues[o]||t.formattingValues[i]}else{const i=t.defaultWidth,o=n!=null&&n.width?String(n.width):t.defaultWidth;a=t.values[o]||t.values[i]}const s=t.argumentCallback?t.argumentCallback(e):e;return a[s]}}const it={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},ct={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},ot={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},lt={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},dt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},ut={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},ht=(t,e)=>{const n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},ft={ordinalNumber:ht,era:H({values:it,defaultWidth:"wide"}),quarter:H({values:ct,defaultWidth:"wide",argumentCallback:t=>t-1}),month:H({values:ot,defaultWidth:"wide"}),day:H({values:lt,defaultWidth:"wide"}),dayPeriod:H({values:dt,defaultWidth:"wide",formattingValues:ut,defaultFormattingWidth:"wide"})};function L(t){return(e,n={})=>{const r=n.width,a=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],s=e.match(a);if(!s)return null;const i=s[0],o=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(o)?wt(o,h=>h.test(i)):mt(o,h=>h.test(i));let d;d=t.valueCallback?t.valueCallback(l):l,d=n.valueCallback?n.valueCallback(d):d;const u=e.slice(i.length);return{value:d,rest:u}}}function mt(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}function wt(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}function yt(t){return(e,n={})=>{const r=e.match(t.matchPattern);if(!r)return null;const a=r[0],s=e.match(t.parsePattern);if(!s)return null;let i=t.valueCallback?t.valueCallback(s[0]):s[0];i=n.valueCallback?n.valueCallback(i):i;const o=e.slice(a.length);return{value:i,rest:o}}}const xt=/^(\d+)(th|st|nd|rd)?/i,gt=/\d+/i,bt={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},vt={any:[/^b/i,/^(a|c)/i]},kt={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Dt={any:[/1/i,/2/i,/3/i,/4/i]},Nt={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},St={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},jt={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Mt={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Ot={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},At={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Et={ordinalNumber:yt({matchPattern:xt,parsePattern:gt,valueCallback:t=>parseInt(t,10)}),era:L({matchPatterns:bt,defaultMatchWidth:"wide",parsePatterns:vt,defaultParseWidth:"any"}),quarter:L({matchPatterns:kt,defaultMatchWidth:"wide",parsePatterns:Dt,defaultParseWidth:"any",valueCallback:t=>t+1}),month:L({matchPatterns:Nt,defaultMatchWidth:"wide",parsePatterns:St,defaultParseWidth:"any"}),day:L({matchPatterns:jt,defaultMatchWidth:"wide",parsePatterns:Mt,defaultParseWidth:"any"}),dayPeriod:L({matchPatterns:Ot,defaultMatchWidth:"any",parsePatterns:At,defaultParseWidth:"any"})},Pt={code:"en-US",formatDistance:pe,formatLong:rt,formatRelative:st,localize:ft,match:Et,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Tt(t,e){const n=v(t,e==null?void 0:e.in);return Be(n,Ue(n))+1}function Ft(t,e){const n=v(t,e==null?void 0:e.in),r=+X(n)-+Qe(n);return Math.round(r/be)+1}function je(t,e){var u,h,w,x;const n=v(t,e==null?void 0:e.in),r=n.getFullYear(),a=$(),s=(e==null?void 0:e.firstWeekContainsDate)??((h=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:h.firstWeekContainsDate)??a.firstWeekContainsDate??((x=(w=a.locale)==null?void 0:w.options)==null?void 0:x.firstWeekContainsDate)??1,i=D((e==null?void 0:e.in)||t,0);i.setFullYear(r+1,0,s),i.setHours(0,0,0,0);const o=S(i,e),l=D((e==null?void 0:e.in)||t,0);l.setFullYear(r,0,s),l.setHours(0,0,0,0);const d=S(l,e);return+n>=+o?r+1:+n>=+d?r:r-1}function Wt(t,e){var o,l,d,u;const n=$(),r=(e==null?void 0:e.firstWeekContainsDate)??((l=(o=e==null?void 0:e.locale)==null?void 0:o.options)==null?void 0:l.firstWeekContainsDate)??n.firstWeekContainsDate??((u=(d=n.locale)==null?void 0:d.options)==null?void 0:u.firstWeekContainsDate)??1,a=je(t,e),s=D((e==null?void 0:e.in)||t,0);return s.setFullYear(a,0,r),s.setHours(0,0,0,0),S(s,e)}function Ct(t,e){const n=v(t,e==null?void 0:e.in),r=+S(n,e)-+Wt(n,e);return Math.round(r/be)+1}function b(t,e){const n=t<0?"-":"",r=Math.abs(t).toString().padStart(e,"0");return n+r}const P={y(t,e){const n=t.getFullYear(),r=n>0?n:1-n;return b(e==="yy"?r%100:r,e.length)},M(t,e){const n=t.getMonth();return e==="M"?String(n+1):b(n+1,2)},d(t,e){return b(t.getDate(),e.length)},a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(t,e){return b(t.getHours()%12||12,e.length)},H(t,e){return b(t.getHours(),e.length)},m(t,e){return b(t.getMinutes(),e.length)},s(t,e){return b(t.getSeconds(),e.length)},S(t,e){const n=e.length,r=t.getMilliseconds(),a=Math.trunc(r*Math.pow(10,n-3));return b(a,e.length)}},_={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},ue={G:function(t,e,n){const r=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if(e==="yo"){const r=t.getFullYear(),a=r>0?r:1-r;return n.ordinalNumber(a,{unit:"year"})}return P.y(t,e)},Y:function(t,e,n,r){const a=je(t,r),s=a>0?a:1-a;if(e==="YY"){const i=s%100;return b(i,2)}return e==="Yo"?n.ordinalNumber(s,{unit:"year"}):b(s,e.length)},R:function(t,e){const n=De(t);return b(n,e.length)},u:function(t,e){const n=t.getFullYear();return b(n,e.length)},Q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return b(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return b(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){const r=t.getMonth();switch(e){case"M":case"MM":return P.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){const r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return b(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){const a=Ct(t,r);return e==="wo"?n.ordinalNumber(a,{unit:"week"}):b(a,e.length)},I:function(t,e,n){const r=Ft(t);return e==="Io"?n.ordinalNumber(r,{unit:"week"}):b(r,e.length)},d:function(t,e,n){return e==="do"?n.ordinalNumber(t.getDate(),{unit:"date"}):P.d(t,e)},D:function(t,e,n){const r=Tt(t);return e==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):b(r,e.length)},E:function(t,e,n){const r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){const a=t.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(s);case"ee":return b(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});case"eeee":default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){const a=t.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(s);case"cc":return b(s,e.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});case"cccc":default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){const r=t.getDay(),a=r===0?7:r;switch(e){case"i":return String(a);case"ii":return b(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){const a=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,e,n){const r=t.getHours();let a;switch(r===12?a=_.noon:r===0?a=_.midnight:a=r/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,e,n){const r=t.getHours();let a;switch(r>=17?a=_.evening:r>=12?a=_.afternoon:r>=4?a=_.morning:a=_.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,e,n){if(e==="ho"){let r=t.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return P.h(t,e)},H:function(t,e,n){return e==="Ho"?n.ordinalNumber(t.getHours(),{unit:"hour"}):P.H(t,e)},K:function(t,e,n){const r=t.getHours()%12;return e==="Ko"?n.ordinalNumber(r,{unit:"hour"}):b(r,e.length)},k:function(t,e,n){let r=t.getHours();return r===0&&(r=24),e==="ko"?n.ordinalNumber(r,{unit:"hour"}):b(r,e.length)},m:function(t,e,n){return e==="mo"?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):P.m(t,e)},s:function(t,e,n){return e==="so"?n.ordinalNumber(t.getSeconds(),{unit:"second"}):P.s(t,e)},S:function(t,e){return P.S(t,e)},X:function(t,e,n){const r=t.getTimezoneOffset();if(r===0)return"Z";switch(e){case"X":return fe(r);case"XXXX":case"XX":return W(r);case"XXXXX":case"XXX":default:return W(r,":")}},x:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"x":return fe(r);case"xxxx":case"xx":return W(r);case"xxxxx":case"xxx":default:return W(r,":")}},O:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+he(r,":");case"OOOO":default:return"GMT"+W(r,":")}},z:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+he(r,":");case"zzzz":default:return"GMT"+W(r,":")}},t:function(t,e,n){const r=Math.trunc(+t/1e3);return b(r,e.length)},T:function(t,e,n){return b(+t,e.length)}};function he(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),a=Math.trunc(r/60),s=r%60;return s===0?n+String(a):n+String(a)+e+b(s,2)}function fe(t,e){return t%60===0?(t>0?"-":"+")+b(Math.abs(t)/60,2):W(t,e)}function W(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),a=b(Math.trunc(r/60),2),s=b(r%60,2);return n+a+e+s}const me=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},Me=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},Yt=(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],r=n[1],a=n[2];if(!a)return me(t,e);let s;switch(r){case"P":s=e.dateTime({width:"short"});break;case"PP":s=e.dateTime({width:"medium"});break;case"PPP":s=e.dateTime({width:"long"});break;case"PPPP":default:s=e.dateTime({width:"full"});break}return s.replace("{{date}}",me(r,e)).replace("{{time}}",Me(a,e))},It={p:Me,P:Yt},_t=/^D+$/,Ht=/^Y+$/,Lt=["D","DD","YY","YYYY"];function qt(t){return _t.test(t)}function Gt(t){return Ht.test(t)}function Rt(t,e,n){const r=Vt(t,e,n);if(console.warn(r),Lt.includes(t))throw new RangeError(r)}function Vt(t,e,n){const r=t[0]==="Y"?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const $t=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Bt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Qt=/^'([^]*?)'?$/,Xt=/''/g,zt=/[a-zA-Z]/;function O(t,e,n){var u,h,w,x;const r=$(),a=r.locale??Pt,s=r.firstWeekContainsDate??((h=(u=r.locale)==null?void 0:u.options)==null?void 0:h.firstWeekContainsDate)??1,i=r.weekStartsOn??((x=(w=r.locale)==null?void 0:w.options)==null?void 0:x.weekStartsOn)??0,o=v(t,n==null?void 0:n.in);if(!Je(o))throw new RangeError("Invalid time value");let l=e.match(Bt).map(f=>{const m=f[0];if(m==="p"||m==="P"){const j=It[m];return j(f,a.formatLong)}return f}).join("").match($t).map(f=>{if(f==="''")return{isToken:!1,value:"'"};const m=f[0];if(m==="'")return{isToken:!1,value:Jt(f)};if(ue[m])return{isToken:!0,value:f};if(m.match(zt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+m+"`");return{isToken:!1,value:f}});a.localize.preprocessor&&(l=a.localize.preprocessor(o,l));const d={firstWeekContainsDate:s,weekStartsOn:i,locale:a};return l.map(f=>{if(!f.isToken)return f.value;const m=f.value;(Gt(m)||qt(m))&&Rt(m,e,String(t));const j=ue[m[0]];return j(o,m,a.localize,d)}).join("")}function Jt(t){const e=t.match(Qt);return e?e[1].replace(Xt,"'"):t}function z(t,e){return+v(t)>+v(e)}function J(t,e){return+v(t)<+v(e)}function K(t,e,n){const[r,a]=B(n==null?void 0:n.in,t,e);return+S(r,n)==+S(a,n)}function Kt(t,e,n){const[r,a]=B(n==null?void 0:n.in,t,e);return r.getFullYear()===a.getFullYear()&&r.getMonth()===a.getMonth()}function Z(t,e){return A(D(t,t),Xe(t))}function q(t,e,n){const r=+v(t,n==null?void 0:n.in),[a,s]=[+v(e.start,n==null?void 0:n.in),+v(e.end,n==null?void 0:n.in)].sort((i,o)=>i-o);return r>=a&&r<=s}function Ut(t,e,n){return ve(t,-e,n)}function y(t){return N(Date.now(),t)}function Zt(t,e,n){return ke(t,-e,n)}function T(t,e,n){const{years:r=0,months:a=0,weeks:s=0,days:i=0,hours:o=0,minutes:l=0,seconds:d=0}=e,u=Zt(t,a+r*12,n),h=Ut(u,i+s*7,n),w=l+o*60,f=(d+w*60)*1e3;return D((n==null?void 0:n.in)||t,+h-f)}const Oe=({className:t,height:e})=>{const n=new Date,r=N(n),[a,s]=k.useState(0);return k.useEffect(()=>{const i=()=>{const d=C(n,r)/re;s(d*e)};i();const o=setInterval(()=>i(),Ge);return()=>clearInterval(o)},[e]),c.jsx("div",{"aria-hidden":!0,style:{top:a},"aria-label":"Horário do dia",className:G("absolute left-24 isolate h-1 w-full -translate-y-1/2",t),children:c.jsxs("div",{className:"relative h-full w-full",children:[c.jsx("div",{"aria-label":"current time dot",className:"absolute -left-2 top-1/2 aspect-square w-4 -translate-y-1/2 rounded-full bg-danger"}),c.jsx("div",{"aria-label":"current time line",className:"absolute top-1/2 h-[2px] w-full -translate-y-1/2 bg-danger"})]})})},we=24*60,pt=({day:t,event:e,index:n,groupSize:r,containerHeight:a})=>{const s=N(t),i=V(),o=C(e.willFinishAt,e.startAt),l=()=>{var h;return(h=i.onEventClick)==null?void 0:h.call(i,e,"days")},d=()=>{const x=C(e.startAt,s)/we*a,f=o/we*a,m=n===r-1;let j=r===1?1:1/r*1.7;m&&(j=1/r);const Q={top:x,height:f,width:`calc((100% - 96px) * ${j})`};return m?{...Q,right:0}:{...Q,left:`calc(100px + 100% * ${1/r*n})`}},u=`${e.title}, ${O(e.startAt,"h:mm a")} - ${O(e.willFinishAt,"h:mm a")}`;return c.jsx("div",{style:d(),className:"absolute cursor-pointer rounded border border-card-border bg-primary text-left",children:c.jsx(R,{enabled:!0,followCursor:!0,title:c.jsx("button",{onClick:l,type:"button",className:"flex h-full w-full px-2 py-1 text-left",children:c.jsx("p",{className:"text-xs text-white",children:u})}),children:u})})},en=86399999,Ae=(t,e=[])=>{if(t.length<=0)return e;const[n,...r]=t,a=r.filter(o=>n?q(o.startAt,{start:n.startAt,end:g(n==null?void 0:n.willFinishAt,{minutes:-1})}):!1),s=[n,...a],i=r.slice(a.length);return e.push(s),Ae(i,e)},tn=(t,e)=>{const n=e.filter(i=>{const o=J(i.startAt,t)&&A(i.willFinishAt,t),l=A(i.startAt,t)&&z(i.willFinishAt,t),d=A(i.startAt,t)&&A(i.willFinishAt,t),u=J(i.startAt,t)&&z(i.willFinishAt,t);return l||d||o||u}),[r,a]=n.reduce((i,o)=>{var w,x;const{willFinishAt:l,startAt:d}=o,u=A(d,l),h=U(l,d);return u&&h<en?(w=i[1])==null||w.push(o):(x=i[0])==null||x.push(o),i},[[],[]]);return{eventGroups:Ae(a),allDayEvents:r}},nn=t=>{const e=V(),n=()=>{var r;return(r=e.onEventClick)==null?void 0:r.call(e,t.event,"days")};return c.jsx("div",{className:"w-full cursor-pointer rounded bg-primary text-primary-foreground",children:c.jsx(R,{enabled:!0,followCursor:!0,onClick:()=>{var r;return(r=t.onEventClick)==null?void 0:r.call(t,t.event,"days")},title:c.jsx("button",{type:"button",className:"h-full w-full px-2 py-1 text-left",onClick:n,children:c.jsx("p",{className:"text-sm",children:t.event.title})}),children:c.jsxs("p",{children:[c.jsx("span",{className:"font-medium",children:t.event.title}),". ",M.hour(t.event.startAt)," -"," ",M.hour(t.event.willFinishAt)]})})})},rn=({date:t,events:e=[]})=>{const[n,r]=k.useState(null),a=Z(t),s=se({start:N(t),end:Y(t)}),{eventGroups:i,allDayEvents:o}=tn(t,e);return c.jsxs("section",{className:"flex-1",children:[c.jsxs("div",{className:"scrollbar-gutter-stable flex border-b border-card-border",children:[c.jsx("div",{className:"flex h-14 min-w-24 items-center justify-center",children:c.jsx("span",{className:"text-xs",children:O(new Date,"z")})}),c.jsx("div",{className:"flex flex-1 flex-col items-center justify-center gap-1.5 border-l border-card-border pb-1.5 pl-2",children:o.map(l=>c.jsx(nn,{event:l},l.id))})]}),c.jsx("div",{className:"flex-1",children:c.jsxs("div",{className:"relative",ref:l=>r(l),children:[i.map(l=>l.map((d,u)=>c.jsx(pt,{day:t,event:d,index:u,groupSize:l.length,containerHeight:(n==null?void 0:n.offsetHeight)||1},d.id))),s.map((l,d)=>c.jsxs("div",{className:"flex h-14",children:[c.jsx("div",{className:"flex h-full w-24 items-start justify-center",children:c.jsx("time",{className:"-m-3 select-none text-xs",dateTime:O(l,"yyyy-MM-dd"),children:d===0?"":M.hour(l)})}),c.jsx("div",{className:G("relative flex-1 border-b border-l border-r border-card-border",d!==s.length-1&&"border-b")})]},l.toISOString()+d)),a&&c.jsx(Oe,{height:(n==null?void 0:n.offsetHeight)||1})]})})]})},an=(t,e)=>{var r,a,s,i;const n={};for(let o of e){const l=Y(o[o.length-1]),u=N(o[0]).toISOString()+"-"+l.toISOString();n[u]={weekEvents:[],weekDayEvents:o.reduce((h,w)=>(h[w.toISOString()]=[],h),{})}}for(let o of t){const{startAt:l,willFinishAt:d}=o,u=A(l,d),h=U(d,l),w=F(d).toISOString(),x=S(l).toISOString();if(u&&h<ne){const f=`${x}-${w}`,m=N(l).toISOString();(a=(r=n[f])==null?void 0:r.weekDayEvents[m])==null||a.push(o)}if(h>=ne&&h<=ce){const f=`${x}-${w}`,m={...o,displayEndDate:d,displayStartDate:l};(s=n[f])==null||s.weekEvents.push(m)}if(h>ce)for(let f of e){const m=N(f[0]),j=Y(f[f.length-1]),Q=m.toISOString()+"-"+j.toISOString(),We=K(d,j),Ce=K(l,m),Ye=q(j,{start:l,end:d}),Ie=q(m,{start:l,end:d}),p={...o,displayEndDate:d,displayStartDate:l};!We&&Ye&&(p.displayEndDate=j),!Ce&&Ie&&(p.displayStartDate=m),(i=n[Q])==null||i.weekEvents.push(p)}}return n};function Ee(t){var e,n,r="";if(typeof t=="string"||typeof t=="number")r+=t;else if(typeof t=="object")if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(n=Ee(t[e]))&&(r&&(r+=" "),r+=n);else for(e in t)t[e]&&(r&&(r+=" "),r+=e);return r}function sn(){for(var t,e,n=0,r="";n<arguments.length;)(t=arguments[n++])&&(e=Ee(t))&&(r&&(r+=" "),r+=e);return r}const ye=t=>typeof t=="boolean"?"".concat(t):t===0?"0":t,xe=sn,cn=(t,e)=>n=>{var r;if((e==null?void 0:e.variants)==null)return xe(t,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:a,defaultVariants:s}=e,i=Object.keys(a).map(d=>{const u=n==null?void 0:n[d],h=s==null?void 0:s[d];if(u===null)return null;const w=ye(u)||ye(h);return a[d][w]}),o=n&&Object.entries(n).reduce((d,u)=>{let[h,w]=u;return w===void 0||(d[h]=w),d},{}),l=e==null||(r=e.compoundVariants)===null||r===void 0?void 0:r.reduce((d,u)=>{let{class:h,className:w,...x}=u;return Object.entries(x).every(f=>{let[m,j]=f;return Array.isArray(j)?j.includes({...s,...o}[m]):{...s,...o}[m]===j})?[...d,h,w]:d},[]);return xe(t,i,l,n==null?void 0:n.class,n==null?void 0:n.className)},on=t=>{const e=[],n={};for(let r of t){const{startAt:a,willFinishAt:s}=r,i=A(a,s),o=U(s,a);if(i&&o<ne){const l=N(a).toISOString();n[l]||(n[l]=[]),n[l].push(r)}else e.push(r)}return{dayGroups:n,weekGroups:e}},Pe=(t,e=[])=>{if(t.length<=0)return e;const[n,...r]=t,a=r.filter(o=>q(o.startAt,{start:n.startAt,end:g(n.willFinishAt,{minutes:-1})})),s=[n,...a],i=r.slice(a.length);return e.push(s),Pe(i,e)},Te=(t,e=new Date)=>{const n=[],r=F(e),a=S(e);for(let o of t){const{willFinishAt:l,startAt:d}=o,u=K(l,e),h=K(d,e),w=J(d,a)&&z(l,r);if(!(h||u||w))continue;const x=J(d,a)?a:d,f=z(l,r)?r:l;n.push({...o,displayEndDate:f,displayStartDate:x})}const s=n.sort((o,l)=>o.startAt.getTime()-l.startAt.getTime()),i=[];for(const o of s){let l=!1;for(const d of i)if(d[d.length-1].willFinishAt.getTime()<=o.startAt.getTime()){d.push(o),l=!0;break}l||i.push([o])}return i},ln=6,dn=({events:t=E.array,restEvents:e=E.array,day:n,weekEventsShown:r=0})=>{const a=V(),s=e.filter(h=>q(n,{end:h.willFinishAt,start:h.startAt})),i=ln-r,o=t.concat(s),l=o.length;let d=[],u=0;return i>1&&(d=o.slice(0,i),u=l-d.length),i===1&&l===1&&(d=o.slice(0,1),u=0),i===1&&l>1&&(u=l),c.jsxs("ul",{className:"flex-1 space-y-1 overflow-hidden px-4",children:[d.map(h=>{const w=()=>{var f;return(f=a.onEventClick)==null?void 0:f.call(a,h,"months")},x=`${O(h.startAt,"h:mmaaa")}, ${h.title}`;return c.jsx("li",{children:c.jsxs("button",{type:"button",className:"flex items-center",onClick:w,children:[c.jsx("svg",{className:"mr-2 size-2 min-w-2 fill-primary text-primary",children:c.jsx("circle",{cx:"4",cy:"4",r:"4"})}),c.jsx(R,{className:"text-ellipsis text-nowrap text-sm",title:c.jsx("p",{className:"text-ellipsis text-nowrap text-sm",children:x}),children:c.jsx("p",{className:"text-ellipsis text-nowrap text-sm",children:x})})]})},h.id)}),u>0&&c.jsx("li",{className:"flex items-center",children:c.jsxs("button",{onClick:()=>{var h;return(h=a.onClickMore)==null?void 0:h.call(a,o)},className:"text-ellipsis text-nowrap text-sm",children:[u," more"]})})]})},un=(t,e,n)=>{const r=S(t),a=C(e.displayEndDate,e.displayStartDate),i=C(e.displayStartDate,r)/oe*n,o=a/oe*n;return{left:i,width:`calc(${o}px - 1px)`}},Fe=t=>{const e=V();return c.jsx("div",{style:un(t.date,t.event,t.width),className:"absolute h-full cursor-pointer rounded bg-primary text-left",children:c.jsx("button",{type:"button","data-event":"true",className:"h-full w-full px-2 text-left",onClick:()=>{var n;return(n=e.onEventClick)==null?void 0:n.call(e,t.event,t.view)},children:c.jsxs(R,{enabled:!0,followCursor:!0,title:c.jsxs("div",{className:"overflow-hidden text-ellipsis text-sm text-primary-foreground",children:[M.hour(t.event.startAt)," ",t.event.title]}),children:[c.jsx("p",{className:"mb-2 font-medium",children:t.event.title}),c.jsx("p",{children:M.datetimeCalendar(t.event.startAt)}),c.jsx("p",{children:"->"}),c.jsx("p",{children:M.datetimeCalendar(t.event.willFinishAt)}),c.jsx("p",{className:"w-full max-w-60 text-balance"})]})})})},hn=t=>{const e=k.useRef(null),[n,r]=k.useState(1);k.useEffect(()=>{if(!e.current)return;const s=new ResizeObserver(i=>{for(let o of i)r(o.contentRect.width)});return s.observe(e.current),()=>s.disconnect()},[e]);const a=t.groups??E.array;return c.jsx("div",{className:"space-y-1 overflow-hidden",ref:e,children:a.map((s,i)=>c.jsx("div",{className:"relative h-6",children:s.map(o=>c.jsx(Fe,{view:"months",date:t.date,event:o,width:n},o.id))},"group-"+i))})},fn=cn("my-2 flex justify-center items-center text-sm font-semibold proportional-nums",{variants:{variant:{default:"",today:"bg-primary text-primary-foreground",other:"opacity-70"},size:{default:"w-6 h-6 rounded-full",startOfMonth:"px-2 rounded-xl"}},defaultVariants:{variant:"default",size:"default"}}),mn=t=>{const e=Te(t.weekEvents,t.week[3]),n=e.slice(0,5),r=e.slice(5).flat(1);return c.jsxs("div",{className:"relative h-full min-h-56 w-full",children:[c.jsx("div",{className:"flex h-full w-full",children:t.week.map(a=>{const s=ie(a),i=A(a,s),o=Kt(a,t.date),l=Z(a)?"today":o?"default":"other",d=i?"startOfMonth":"default",u=i?a.toLocaleString(void 0,{day:"numeric",month:"short"}):a.getDate(),h=G(fn({variant:l,size:d}));return c.jsx("div",{className:"flex flex-1 flex-col items-center border-b border-l border-card-border last:border-r",children:c.jsx("p",{className:h,children:u})},"day-label-"+a.toISOString())})}),c.jsxs("div",{className:"absolute inset-0 mb-5 mt-10 space-y-1 overflow-hidden",children:[c.jsx(hn,{date:t.week[3],groups:n}),c.jsx("div",{className:"flex min-h-6",children:t.week.map(a=>{const s=a.toISOString(),i=t.weekDayEvents[s];return c.jsx(dn,{day:a,events:i,restEvents:r,weekEventsShown:n.length},s)})})]})]})},wn=t=>{const e=k.useMemo(()=>ae({start:S(t.date),end:F(t.date)}),[t.date]),n=ae({start:S(ie(t.date)),end:F(Ne(t.date))}).reduce((a,s,i)=>{const o=Math.floor(i/7);return a[o]||(a[o]=[]),a[o].push(s),a},[]),r=an(t.events??E.array,n);return c.jsxs("section",{"data-name":"calendar-month-view",className:"flex flex-1 flex-col",children:[c.jsx("div",{className:"flex w-full",children:e.map(a=>c.jsx("div",{className:"flex flex-1 justify-center border-b border-l border-t border-card-border last:border-r",children:c.jsx("span",{className:"my-1 text-sm font-semibold",children:M.weekday(a)})},a.toISOString()))}),c.jsx("div",{className:"flex h-44 min-h-44 flex-1 flex-col",children:n.map(a=>{const s=Y(a[a.length-1]),o=N(a[0]).toISOString()+"-"+s.toISOString(),l={week:a,...r[o]};return c.jsx(mn,{date:t.date,week:l.week||a,weekEvents:l.weekEvents??E.array,weekDayEvents:l.weekDayEvents??E.record},o)})})]})},yn=({day:t})=>{const e=Z(t);return c.jsxs("div",{className:"flex min-w-36 flex-1 flex-col items-center",children:[c.jsx("span",{"aria-hidden":!0,className:"text-md",children:t.toLocaleDateString(void 0,{weekday:"short"})}),c.jsx("div",{"aria-label":t.toDateString(),className:G("flex size-11 items-center justify-center rounded-full text-2xl font-medium",e&&"bg-primary text-primary-foreground"),children:c.jsx("p",{className:"leading-snug",children:t.getDate()})})]})},xn=({day:t,event:e,index:n,groupSize:r,containerHeight:a})=>{const s=V(),i=()=>{const l=N(t),d=C(e.startAt,l),u=C(e.willFinishAt,e.startAt),h=d/re*a,w=u/re*a,x=n===r-1;let f=r===1?1:1/r*1.7;x&&(f=1/r);const m={top:h,height:w,width:`calc(100% * ${f})`};return x?{...m,right:0}:{...m,left:`calc(100% * ${1/r*n})`}},o=c.jsxs("span",{children:[c.jsx("span",{className:"font-medium",children:e.title}),". ",M.hour(e.startAt)," -"," ",M.hour(e.willFinishAt)]});return c.jsx("div",{style:i(),className:"absolute isolate cursor-pointer rounded border border-card-border bg-primary text-primary-foreground",children:c.jsx(R,{enabled:!0,followCursor:!0,onClick:()=>{var l;return(l=s.onEventClick)==null?void 0:l.call(s,e,s.view)},title:c.jsx("button",{type:"button",className:"flex h-full w-full truncate whitespace-pre-wrap break-words rounded px-2 py-1 text-sm",children:o}),children:o})})},gn=t=>{const[e,n]=k.useState(null),r=Z(t.day),a=se({start:N(t.day),end:Y(t.day)}),s=Pe(t.events??E.array);return c.jsxs("div",{"aria-label":"Eventos do dia "+t.day.toDateString(),className:"relative flex h-full min-w-36 flex-1",children:[c.jsx("div",{className:"absolute h-full w-[95%]",children:c.jsx("div",{className:"relative h-full w-full",ref:i=>n(i),children:s.map(i=>i.map((o,l)=>c.jsx(xn,{event:o,index:l,day:t.day,groupSize:i.length,containerHeight:(e==null?void 0:e.offsetHeight)||1},o.id)))})}),c.jsxs("div",{className:"flex w-full flex-col",children:[a.map((i,o)=>c.jsx("div",{className:G("h-14 w-full border-l border-card-border",o!==a.length-1&&"border-b")},i.toISOString())),r&&c.jsx(Oe,{className:"left-0",height:(e==null?void 0:e.offsetHeight)||1})]})]})},bn=t=>{const[e,n]=k.useState(1),r=k.useRef(null),a=Te(t.events??E.array,t.date);return k.useEffect(()=>{if(!r.current)return;const s=new ResizeObserver(i=>{for(let o of i)n(o.contentRect.width)});return s.observe(r.current),()=>s.disconnect()},[r]),c.jsx("div",{className:"mt-2 space-y-1 overflow-hidden",ref:r,children:a.map((s,i)=>c.jsx("div",{className:"relative h-6",children:s.map(o=>k.createElement(Fe,{...t,view:"weeks",date:t.date,event:o,key:o.id,width:e}))},"group-"+i))})},vn=t=>{const e=se({start:N(t.date),end:Y(t.date)}),n=ae({start:S(t.date),end:F(t.date)}),{weekGroups:r,dayGroups:a}=on(t.events??E.array);return c.jsxs("section",{className:"flex flex-1 flex-col overflow-x-auto",children:[c.jsxs("div",{className:"flex min-w-[calc(96px+(144px*7))] items-center border-b border-card-border",children:[c.jsx("div",{className:"flex h-14 min-w-24 items-center justify-center",children:c.jsx("span",{className:"text-xs",children:O(new Date,"z")})}),c.jsxs("div",{className:"flex flex-1 flex-col",children:[c.jsx("div",{className:"relative flex flex-1",children:n.map(s=>c.jsx(yn,{day:s},"week-day-label-"+s.toISOString()))}),c.jsxs("div",{className:"relative min-h-6",children:[c.jsxs("div",{className:"absolute inset-0 flex h-full flex-1",children:[c.jsx("div",{className:"min-w-36 flex-1 border-l border-card-border"}),c.jsx("div",{className:"min-w-36 flex-1 border-l border-card-border"}),c.jsx("div",{className:"min-w-36 flex-1 border-l border-card-border"}),c.jsx("div",{className:"min-w-36 flex-1 border-l border-card-border"}),c.jsx("div",{className:"min-w-36 flex-1 border-l border-card-border"}),c.jsx("div",{className:"min-w-36 flex-1 border-l border-card-border"}),c.jsx("div",{className:"min-w-36 flex-1 border-l border-card-border"})]}),c.jsx(bn,{date:t.date,events:r})]})]})]}),c.jsxs("div",{className:"flex min-w-[calc(96px+(144px*7))] overflow-y-auto",children:[c.jsx("div",{className:"flex h-fit flex-col",children:e.map((s,i)=>c.jsx("div",{"aria-label":O(s,"h a"),className:"flex min-h-14 w-24 items-start justify-center",children:c.jsx("time",{className:"-m-3 text-xs",dateTime:O(s,"yyyy-MM-dd"),children:i===0?"":M.hour(s)})},s.toISOString()+i))}),c.jsx("div",{className:"flex h-fit flex-1 border-b border-card-border",children:n.map(s=>{const i=s.toISOString();return c.jsx(gn,{day:s,events:a[i]},i)})})]})]})},kn=[{value:"days",label:"Dia"},{value:"weeks",label:"Semana"},{value:"months",label:"Mês"}],Dn=t=>{const[e,n]=k.useState(t.view||"days"),[r,a]=k.useState(new Date(t.date)),s=(u,h,w)=>w(x=>h(x,{[u]:1})),i=k.useCallback(()=>s(e,T,a),[e]),o=k.useCallback(()=>s(e,g,a),[e]),l=k.useCallback(u=>{if(e==="days")return M.monthYear(u);if(e==="weeks"){const h=S(u),w=F(u),x=O(h,"MMM"),f=O(w,"MMM"),m=O(h,"yyyy");return x!==f?`${x} – ${f} ${m}`:`${x} ${m}`}return u.toLocaleDateString(void 0,{month:"long",year:"numeric"})},[e]),d={...t,date:r,view:e};return c.jsx(ge.Provider,{value:d,children:c.jsxs("div",{className:"flex h-full w-full flex-1 flex-col overflow-hidden text-foreground",children:[c.jsxs("header",{className:"mb-6 flex w-full justify-between",children:[c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx(ee,{"aria-label":"Definir o dia atual",onClick:()=>a(new Date),theme:"raw",size:"small",children:"Today"}),c.jsx(ee,{theme:"raw",size:"small",icon:c.jsx(qe,{}),onClick:i,"aria-label":`${e} anterior`}),c.jsx(ee,{theme:"raw",size:"small",icon:c.jsx(He,{}),onClick:o,"aria-label":`próximo ${e}`}),c.jsx("span",{className:"ml-6 text-xl font-semibold capitalize",children:l(r)})]}),c.jsx("div",{className:"flex gap-2",children:c.jsx(Le,{hideLeft:!0,required:!0,value:e,options:kn,onChange:u=>n(u.target.value),placeholder:"Alterar visualização temporal"})})]}),e==="days"&&c.jsx(rn,{...d}),e==="weeks"&&c.jsx(vn,{...d}),e==="months"&&c.jsx(wn,{...d})]})})},Nn=[{id:"1",startAt:g(y(),{hours:12,minutes:30}),willFinishAt:g(y(),{hours:13,minutes:30}),title:"First"},{id:"2",startAt:g(y(),{hours:12,minutes:45}),willFinishAt:g(y(),{hours:13,minutes:30}),title:"Second"},{id:"3",startAt:g(y(),{hours:13}),willFinishAt:g(y(),{hours:13,minutes:45}),title:"Third"},{id:"4",startAt:g(y(),{hours:13,minutes:15}),willFinishAt:g(y(),{hours:14,minutes:15}),title:"Fourth"},{id:"5",startAt:g(y(),{hours:15,minutes:30}),willFinishAt:g(y(),{hours:15,minutes:55}),title:"Fifths"},{id:"9",startAt:g(y(),{days:1,hours:6}),willFinishAt:g(y(),{days:1,hours:6,minutes:30}),title:"Tomorrow event 1"},{id:"10",startAt:T(y(),{hours:12}),willFinishAt:T(y(),{hours:11}),title:"Yesterday event"},{id:"6",startAt:y(),willFinishAt:I(),title:"All day event 1"},{id:"11",startAt:S(new Date),willFinishAt:F(new Date),title:"Week event"},{id:"12",startAt:T(y(),{days:3}),willFinishAt:T(I(),{days:1}),title:"3 days event 1"},{id:"13",startAt:T(y(),{days:4}),willFinishAt:T(I(),{days:2}),title:"3 days event 2"},{id:"14",startAt:g(y(),{days:1}),willFinishAt:g(I(),{days:2}),title:"2 days event"},{id:"15",startAt:T(S(new Date),{days:2}),willFinishAt:F(new Date),title:"More than one week event"},{id:"16",startAt:ie(new Date),willFinishAt:Ne(new Date),title:"Month event"},{id:"17",startAt:y(),willFinishAt:I(),title:"All day event 2"},{id:"18",startAt:y(),willFinishAt:I(),title:"All day event 3"},{id:"19",startAt:g(y(),{days:1,hours:6}),willFinishAt:g(y(),{days:1,hours:6,minutes:30}),title:"Tomorrow event 2"},{id:"20",startAt:g(y(),{days:7}),willFinishAt:g(y(),{days:7}),title:"Next week 1"},{id:"21",startAt:g(y(),{days:7}),willFinishAt:g(y(),{days:7}),title:"Next week 2"},{id:"22",startAt:g(y(),{days:7}),willFinishAt:g(y(),{days:7}),title:"Next week 3"},{id:"23",startAt:g(y(),{days:7}),willFinishAt:g(y(),{days:7}),title:"Next week 4"},{id:"24",startAt:g(y(),{days:7}),willFinishAt:g(y(),{days:7}),title:"Next week 5"},{id:"25",startAt:g(y(),{days:7}),willFinishAt:g(y(),{days:7}),title:"Next week 6"}];function jn(){const t=(...e)=>alert(JSON.stringify(e,null,4));return c.jsx("div",{className:"w-full",children:c.jsx(Dn,{date:new Date,events:Nn,onEventClick:t,onClickMore:t,onBlankDateClick:t})})}export{jn as default,Nn as eventsMock};
