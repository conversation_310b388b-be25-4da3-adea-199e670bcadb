﻿using System.Text.Json;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Application.Lawyers.RegisterLawyer;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.Oab;
using DataVenia.Modules.Users.Domain.SignUpToken;
using DataVenia.Modules.Users.Domain.Outbox;
using DataVenia.Modules.Users.IntegrationEvents;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Lawyer.CompleteLawyerSignUp;

public class CompleteLawyerSignUpCommandHandler(
    ILawyerRepository lawyerRepository,
    IUnitOfWork unitOfWork,
    IOfficeUserRepository officeUserRepository,
    ISignUpTokenRepository signUpTokenRepository,
    IOutboxRepository outboxRepository,
    IOabRepository oabRepository,
    ILogger<CompleteLawyerSignUpCommandHandler> logger)
    : ICommandHandler<CompleteLawyerSignUpCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CompleteLawyerSignUpCommand request, CancellationToken cancellationToken)
    {
        Result<SignUpToken> signUpTokenResult = await signUpTokenRepository.GetSingleByTokenAsync(request.SignUpToken, cancellationToken);

        #region validate signUpToken
        if(signUpTokenResult.IsFailure)
            return Result.Failure<Guid>(signUpTokenResult.Error);

        if (signUpTokenResult.Value.Lawyer?.Email == null)
        {
            logger.LogError("Sign up token lawyer email is null");
            return Result.Failure<Guid>(new Error("Lawyer.NotFound", "Lawyer not found", ErrorType.NotFound));
        }
        #endregion
        
        signUpTokenResult.Value.SoftDelete();
        signUpTokenResult.Value.DeletedBy = signUpTokenResult.Value.LawyerId;
        
        Domain.Lawyers.Lawyer? lawyer = await lawyerRepository.GetSingleByFilterAsync(x => x.Id == signUpTokenResult.Value.LawyerId, cancellationToken);
        
        if(lawyer is null)
            return Result.Failure<Guid>(new Error("Lawyer.NotFound", "Lawyer not found", ErrorType.NotFound));
        
        # region enrich lawyer data
        
        Result updateLawyerResult = lawyer.Update(request.FirstName, request.LastName, request.Oabs, request.Contacts, request.Cpf, request.Rg, request.Cnh, request.Passport, request.Ctps, request.Pis, request.VoterId);
        
        if(updateLawyerResult.IsFailure)
            return Result.Failure<Guid>(updateLawyerResult.Error);
        
        if(lawyer.Oab is null)
            return Result.Failure<Guid>(new Error("Oab.Required", "Oab cannot be empty", ErrorType.Validation));
        
        oabRepository.Insert(lawyer.Oab);
        # endregion
        
        OfficeUser? officeUser = await officeUserRepository.GetSingleByFilterAsync(x => x.UserId == lawyer.Id && x.OfficeId == signUpTokenResult.Value.OfficeId, cancellationToken);

        if (officeUser is null)
        {
            logger.LogError("OfficeUser not found. UserId: {UserId}, OfficeId: {OfficeId}", lawyer.Id, signUpTokenResult.Value.OfficeId);
            return Result.Failure<Guid>(new Error("OfficeUser.NotFound", "OfficeUser not found", ErrorType.NotFound));
        }
        
        officeUser.Accept();
        
        officeUserRepository.Update(officeUser);

        
            var lawyerSignedUpEvent = new LawyerSignedUpEvent(
                lawyer.Id,
                signUpTokenResult.Value.Lawyer.Email,
                request.FirstName,
                request.LastName,
                request.Password
            );
    
            string payloadJson = JsonSerializer.Serialize(lawyerSignedUpEvent);
            
            var outboxMessage = new Outbox
            {
                MessageType = nameof(LawyerSignedUpEvent),
                Payload = payloadJson,
                CreatedAt = DateTime.UtcNow,
                ProcessedAt = null
            };
            
            outboxRepository.Insert(outboxMessage);
            
            
            try
            {
            await unitOfWork.SaveChangesAsync(cancellationToken);
            
            
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error completing lawyer sign up process in the database: {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }
        return lawyer.Id;
    }
}
