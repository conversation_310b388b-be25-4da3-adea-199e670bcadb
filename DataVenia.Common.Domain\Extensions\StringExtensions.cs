﻿using System.Globalization;
using System.Text.RegularExpressions;

namespace DataVenia.Common.Domain.Extensions;

public static class StringExtensions
{
    public static string ToCamelCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var matches = Regex.Matches(
            input,
            @"[A-Z]{2,}(?=[A-Z][a-z]+[0-9]|\b)|[A-Z]?[a-z]+[0-9]|[A-Z]|[0-9]+"
        );

        var words = matches
            .Select(m =>
            {
                var word = m.Value;
                return char.ToUpper(word[0], CultureInfo.InvariantCulture) +
                       word.Substring(1).ToLower(CultureInfo.InvariantCulture);
            });

        var result = string.Concat(words);
        return char.ToLower(result[0], CultureInfo.InvariantCulture) + result.Substring(1);
    }
}
