﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.UpdateClient;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Client;

internal sealed class UpdateClientPatch : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPatch("/offices/{officeId}/clients/{clientId}", async ([FromBody] Request request, Guid clientId, ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            Result result = await sender.Send(new UpdateClientPatchCommand(
                clientId,
                request.Name,
                request.Cpf,
                request.Email));

            return result.Match(Results.NoContent, ApiResults.Problem);
        })
        .RequireAuthorization("office:clients:update")
        .WithTags(Tags.Client);
    }

    public sealed class Request
    {
        public string? Name { get; init; }
        public string? Cpf { get; init; }
        public string? Email { get; init; }
    }
}
