using DataVenia.Common.Domain;

namespace DataVenia.Modules.AssociationHub.Domain.AssociationRequest;

public sealed class AssociationRequest : Entity
{
    public Guid Id { get; private set; }
    public string EntityType { get; private set; }
    public Guid EntityId { get; private set; }
    public string TargetEntityType { get; private set; }
    public Guid TargetEntityId { get; private set; }
    public AssociationOperation Operation { get; private set; }
    public AssociationRequestStatus Status { get; private set; }
    public string? ErrorMessage { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public int RetryCount { get; private set; }

    private AssociationRequest() { }

    public static AssociationRequest Create(
        string entityType,
        Guid entityId,
        string targetEntityType,
        Guid targetEntityId,
        AssociationOperation operation)
    {
        return new AssociationRequest
        {
            Id = Guid.CreateVersion7(),
            EntityType = entityType,
            EntityId = entityId,
            TargetEntityType = targetEntityType,
            TargetEntityId = targetEntityId,
            Operation = operation,
            Status = AssociationRequestStatus.Pending,
            CreatedAt = DateTime.UtcNow,
            RetryCount = 0
        };
    }

    public void MarkAsProcessing()
    {
        Status = AssociationRequestStatus.Processing;
    }

    public void MarkAsCompleted()
    {
        Status = AssociationRequestStatus.Completed;
        ProcessedAt = DateTime.UtcNow;
    }

    public void MarkAsFailed(string errorMessage)
    {
        Status = AssociationRequestStatus.Failed;
        ErrorMessage = errorMessage;
        ProcessedAt = DateTime.UtcNow;
        RetryCount++;
    }

    public bool CanRetry(int maxRetries = 3)
    {
        return RetryCount < maxRetries && Status == AssociationRequestStatus.Failed;
    }
}

public enum AssociationOperation
{
    Add,
    Remove
}

public enum AssociationRequestStatus
{
    Pending,
    Processing,
    Completed,
    Failed
}
