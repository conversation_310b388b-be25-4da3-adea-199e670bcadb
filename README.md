# legal-venture

Pense numa descrição maneira aqui!

# Table of content

<!-- TOC -->
* [legal-venture](#legal-venture)
* [Table of content](#table-of-content)
* [Diagram](#diagram)
* [Entities](#entities)
  * [Office](#office)
  * [Contact](#contact)
  * [User](#user)
  * [Oab](#oab)
  * [Lawyer](#lawyer)
  * [LawyerAssociationOffice](#lawyerassociationoffice)
  * [Agenda](#agenda)
  * [Lawsuit](#lawsuit)
  * [LegalInstance](#legalinstance)
  * [LawsuitEvent](#lawsuitevent)
  * [Case](#case)
  * [AccessType](#accesstype)
<!-- TOC -->

# Diagram

Esse é um diagrama geral, tendo uma representação mais detalhada para cada uma das entidades. Apesar de não estar
contemplado no diagrama, todos os itens devem possuir.

- `id`: identificador
- `createdBy`: autor da ação
- `createdAt`: data de criação
- `deletedAt`*: quando fizer sentido para deleção lógica

```mermaid
classDiagram
%% enum
    class LegalInstance {
        Guid id
        string name
    }

    class Case {
        string id
        string title
        Lawyer owner
        string description
        User[] clients
        User[] caseParticipants
        Lawsuit[]? Lawsuits
    }

%% enum
    class AccessType {
        Guid id
        string name
    }

    class Lawsuit {
        Guid id
        Guid LawsuitParentId
        Guid legalFolder
        Client[] clientes
        Client[] envolvidos
        Client[] testemunhas
        Tag[] tags
        Instance legalInstance
        string processId
        string juizo
        Guid Vara
        Guid Foro
        Guid Acao
        string courtHref
        string Description
        string Observations
        Guid[] Attatchments
        decimal ValorCausa
        decimal ValorCondenacao
        DateTime DistributedAt
        Lawyer[] Responsibles
        string AccessType
        LawsuitEvent[] events
    }

%% enum
    class LawsuitEventStatus {
        Guid id
        string name
    }

%% enum
    class LawsuitEventType {
        Guid id
        string name
    }

    class LawsuitEvent {
        Guid id
        Guid lawsuitEventParentId
        Guid lawsuitId
        boolean notify
        LegalInstance legalInstance
        string LawsuitEventStatus
        string LawsuitEventType
        Instancia instance
        DateTime startedAt
        DateTime? finishedAt
        string description
        Guid[] Attachments
        User createdBy
    }

    class User {
        Guid id
        Contact[] contacts
        string name
        string authId
        string cpf
        string rg
        string cnh
        string passport
        string ctps
        string pis
        string voterId
    }

    class Office {
        Guid id
        string name
        string website
        string cnpj
        User[] owners
        Contact[] contacts
        Address[] address
    }

    User <|-- Lawyer
    User <|-- Client

    class LawyerAssociationOffice {
        Office officeId
        Lawyer lawyerId
        string type %% admin/user
    }

    class Address {
        Guid id
        string neighborhood
        string city
        string street
        string postalCode
        string state
        string complement
    }

    class Contact {
        Guid id
        string type
        string value
    }

    class Client {
        Address[] addresses
    }

    class Oab {
        Guid id
        string document
        string filiation
        DateTime emittedAt
    }

    class Lawyer {
        Office[] office
        string oab
    }

    class AgendaAlert {
        Guid id
        Cron interval
    }

    class Recurrence {
        Guid id
    }

    class Agenda {
        Guid id
        string type
        Datetime from
        Datetime to
        Lawyer responsible
        Recurrence recurrence
        Lawyer owner
        AgendaAlert? alert
        Lawyer[] participants
        Cron? cron
    }
```

# Entities

Descrição das entidades do diagrama

## Office

Entidade que representa o escritório de advocacia

- `id`: identificador
- `name`: nome fantasia
- `website`: URL para o site da empresa
- `cnpj`: documento de identificação brasileira
- `owners`: administradores da empresa
- `contacts`: contatos da empresa
- `address`: endereços

## Contact

Representação das formas de contato do cliente, podendo ser telefone, celular, email e afins

- `id`: identificador
- `type`: e-mail, celular, telefone, instagram, tiktok...
- `value`: string

## User

- `id`: identificador
- `name`: nome completo
- `authId`: id da plataforma de autenticação
- `contacts`: lista de contatos do usuário
- `cpf`: documento do usuário, podendo ser CPF/CNPJ
- `rg`: documento do usuário, podendo ser CPF/CNPJ
- `cnh`: carteira de motorista
- `passport`: passaporte
- `ctps`: carteira de trabalho
- `pis`: Programa de Integração Social
- `voterId`: título de eleitor

## Oab

- `id`
- `document`
- `filiation`
- `emittedAt`

## Lawyer

Usuário do tipo advogado, possuindo algumas mais informações necessárias além de [User](#user)

- `id`: identificador
- `companies`: empresas o qual o advogado pertence
- `oab`: documento identificador dos advogados [Ordem dos Advogados](https://www.oab.org.br/)

## LawyerAssociationOffice

Associação entre advogado x escritório

- `id`: identificador
- `companyId`: id do escritório
- `lawyerId`: id do advogado
- `type`: tipo de acesso do advogado ao escritório. `admin`, `user`, `viewer`

## Agenda

Classe que representa a agenda do advogado, sendo responsável por organizar prazos, eventos e marcos

- `id`: identificador
- `type`: tipo do evento agendado. `meeting`, `interview`, `focus-mode`, `service`
- `from`: data de início do evento
- `to`: data final do evento
- `responsible`: responsável do evento, instância de [Lawyer](#lawyer)
- `owner`: criador do evento
- `alert`: objeto de alerta, utilizado para cron de envios de e-mail/notificações do sistema
- `recurrence`: tipo de recorrência do evento
- `participants`: convidados a participar do evento
- `cron`: notação [cron](https://crontab.guru/) para emissão de notificações

## Lawsuit

- `id`: identificador
- `legalFolder`: nome para agregar processos
- `clientes`: clientes envolvidos no processo
- `envolvidos`: ???
- `testemunhas`: nome auto explicativo
- `tags`: identificadores para filtros avançados
- `legalInstance`: instância. Ex.: 1/2 instância
- `processId`: número do processo
- `juizo`: ???
- `vara`: unidade jurisdicional de primeira instância
- `foro`: local onde são processados assuntos relacionados com a justiça
- `acao`: id da ação movida pelo processo
- `courtHref`: site do tribunal para identificação
- `description`: descrição ???
- `observations`: descrição também ???
- `attachments`: lista de anexos atrelados ao processo
- `valorCausa`: valor em reais cobrado pela causa
- `valorCondenacao`: valor em reais cobrado pela condenação
- `distributedAt`: data da distribuição do processo
- `responsibles`: responsáveis legais pelo processo
- `accessType`: tipo de permissão de visualização do processo na plataforma
- `events`: eventos homologados ao processo

## LegalInstance

Grau de jurisdição ou de hierarquia judiciária: juízo de primeira instância. O espaço de tempo dentro do qual a causa
permanece no mesmo juízo onde é proposta, discutida e julgada definitivamente é chamada a 'instância'.

- `id`: identificador
- `name`: Primeira Instância, segunda instância...

## LawsuitEvent

Entidade responsável por criar a linha de acontecimentos ligadas a [Lawsuit](#lawsuit).

- `id`: identificador
- `lawsuitEventParentId`: id de correlação com algum evento anterior
- `lawsuitId`: entidade pai [Lawsuit](#lawsuit)
- `notify`: emite notificação quando criado
- `legalInstance`: entidade [LegalInstance](#legalinstance)
- `LawsuitEventStatus`: status o qual se encontra o processo - [Lawsuit](#lawsuit)
- `LawsuitEventType`: tipo do evento - **investigar !!!**
- `startedAt`: data de início do evento
- `finishedAt`: data de finalização do evento, se houver
- `description`: descrição do evento
- `attachments`: anexos ligados ao evento
- `createdBy`: criador do evento

## Case

Entidade de caso, que pode englobar processos ou futuramente se tornar um processo

- `id`: identificador
- `title`: título do processo
- `owner`: advogado responsável pelo caso
- `clients`: clientes envolvidos ???
- `caseParticipants`: clientes envolvidos ???
- `lawsuits`: processos ligados ao determinado caso

## AccessType

Tipos de acesso dos processos/casos

- `public`: todos na plataforma podem ter acesso
- `internal`: todos os envolvidos podem ter acesso
- `private`: apenas advogados podem ter acesso ao processo

# Nginx


```shell
docker build -t 0.0.1 .
docker run -d -p 80:80 -v /home/<USER>/frontend:/usr/share/nginx/html/assets 0.0.1
```
