﻿using System.Text.Json;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.Events.Lawsuit;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Domain.Outbox;
using FluentResults;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateLawsuitByCnj;

public sealed class CreateLawsuitByCnjCommandHandler(
    ILawsuitRepository lawsuitRepository,
    ILawsuitDataRepository lawsuitDataRepository,
    IOfficeLawyerFacade officeLawyerFacade,
    IOutboxRepository outboxRepository,
    IUnitOfWork unitOfWork,
    ILogger<CreateLawsuitByCnjCommandHandler> logger) : ICommandHandlerFr<CreateLawsuitByCnjCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateLawsuitByCnjCommand request, CancellationToken cancellationToken)
    {
        using (logger.BeginScope(new
                   { Class = nameof(CreateLawsuitByCnjCommandHandler), request.OfficeId, request.UserId, request.Cnj }))
        {
            try
            {
                IReadOnlyCollection<OfficeLawyerDto> responsibles =
                    await officeLawyerFacade.GetActiveByOfficeAndLawyers(request.OfficeId, request.ResponsibleIds);

                if (responsibles.Count != request.ResponsibleIds.Count)
                    return Result.Fail<Guid>(new Error("Nonexistent.Responsibles").WithMetadata("StatusCode", 400));

                var lawsuit = Lawsuit.CreateByCnj(
                    request.Cnj,
                    request.OfficeId,
                    request.LawsuitTypeId,
                    request.UserId
                );

                var lawsuitData = LawsuitData.CreateByCnj(
                    request.Cnj,
                    request.Title,
                    lawsuit.Id,
                    request.Description,
                    request.ResponsibleIds,
                    request.EvolvedFromCaseId,
                    request.GroupingCaseId);

                if (lawsuitData.IsFailed)
                    return Result.Fail<Guid>(lawsuitData.Errors);

                lawsuitRepository.Insert(lawsuit);
                lawsuitDataRepository.Insert(lawsuitData.Value);

                var registerLawsuitByCnj = new RegisterLawsuitByCnj(request.Cnj, request.OfficeId, true);

                outboxRepository.Insert(new Outbox()
                {
                    MessageType = nameof(RegisterLawsuitByCnj),
                    Payload = JsonSerializer.Serialize(registerLawsuitByCnj),
                    CreatedAt = DateTime.UtcNow,
                    ProcessedAt = null
                });

                await unitOfWork.SaveChangesAsync(cancellationToken);

                return Result.Ok(lawsuit.Id);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error saving lawsuit by CNJ to database: {@Request}", request);
                return Result.Fail<Guid>(new Error("Internal.Server.Error").WithMetadata("StatusCode", 500));
            }
        }
    }
}
