﻿using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using LawsuitDataDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData;

namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitData;
public sealed class LawsuitDataConfiguration : IEntityTypeConfiguration<LawsuitDataDomain>
{
    public void Configure(EntityTypeBuilder<LawsuitDataDomain> builder)
    {
        builder.ToTable("lawsuit_data");
        
        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(ld => ld.Id);

        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.Property(ld => ld.Title)
       .IsRequired()
       .HasMaxLength(200);

        builder.Property(ld => ld.Cnj)
       .IsRequired()
       .HasMaxLength(50);

        builder.Property(l => l.JudgingOrganId)
            .HasColumnName("judging_organ_id")
            .HasMaxLength(150)
            .IsRequired(false);

        builder.Property(ld => ld.JudgingOrganHref)
               .HasMaxLength(200);
        
        builder.Property(ld => ld.Description)
            .HasMaxLength(500);
        
        builder.Property(ld => ld.Observations)
            .HasMaxLength(500);
        
        builder.Property(ld => ld.IsInstanceCreatedByUser)
            .HasDefaultValue(false);
        
        var serializationOptions = new JsonSerializerOptions();
        
        builder.Property(l => l.TopicIds)
            .HasColumnName("topic_ids")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, serializationOptions),
                v => JsonSerializer.Deserialize<List<int>>(v, serializationOptions) ?? new List<int>()
            )
            .HasDefaultValueSql("'[]'::jsonb")
            .IsRequired(false);

        builder.HasOne(l => l.Folder)
               .WithMany()
               .HasForeignKey(l => l.FolderId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(l => l.Responsibles)
            .WithOne(lp => lp.LawsuitData)
            .HasForeignKey(ld => ld.LawsuitDataId)
            .OnDelete(DeleteBehavior.NoAction);
        
        builder.HasMany(l => l.LawsuitParties)
               .WithOne(lp => lp.LawsuitData)
               .HasForeignKey(ld => ld.LawsuitDataId)
               .OnDelete(DeleteBehavior.NoAction);
    }
}
