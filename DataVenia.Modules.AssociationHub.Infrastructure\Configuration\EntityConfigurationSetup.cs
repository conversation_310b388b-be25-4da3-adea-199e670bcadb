using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;

namespace DataVenia.Modules.AssociationHub.Infrastructure.Configuration;

public static class EntityConfigurationSetup
{
    public static EntityConfigurationRegistry CreateDefaultRegistry()
    {
        var registry = new EntityConfigurationRegistry();

        // Users Module Entities
        registry.Register(new EntityConfiguration
        {
            EntityType = "Client",
            GetEndpointTemplate = "/offices/{1}/clients/{0}",
            PatchEndpointTemplate = "/offices/{1}/clients/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Company", "Lawyer", "Lawsuit", "Case", "Appointment"]
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Company",
            GetEndpointTemplate = "/offices/{1}/companies/{0}",
            PatchEndpointTemplate = "/offices/{1}/companies/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Lawyer", "Lawsuit", "Case"]
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Lawyer",
            GetEndpointTemplate = "/lawyers/{0}",
            PatchEndpointTemplate = "/lawyers/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Company", "Office", "Lawsuit", "Case", "Appointment"]
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Office",
            GetEndpointTemplate = "/offices/{0}",
            PatchEndpointTemplate = "/offices/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Lawyer"] // Offices can only be associated with lawyers
        });

        // Lawsuits Module Entities
        registry.Register(new EntityConfiguration
        {
            EntityType = "Lawsuit",
            GetEndpointTemplate = "/lawsuits/{0}",
            PatchEndpointTemplate = "/lawsuits/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Company", "Lawyer", "Case", "Appointment"]
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Case",
            GetEndpointTemplate = "/cases/{0}",
            PatchEndpointTemplate = "/cases/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Company", "Lawyer", "Lawsuit", "Appointment"]
        });

        // Calendar Module Entities
        registry.Register(new EntityConfiguration
        {
            EntityType = "Appointment",
            GetEndpointTemplate = "/appointments/{0}",
            PatchEndpointTemplate = "/appointments/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Lawyer", "Lawsuit", "Case"]
        });

        return registry;
    }
}
