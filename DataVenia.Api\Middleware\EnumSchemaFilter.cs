﻿using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace DataVenia.Api.Middleware;

internal sealed class EnumSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type.IsEnum)
        {
            // Change the type to string
            schema.Type = "string";
            schema.Format = null;

            // Replace enum values with their string names
            string[] enumNames = Enum.GetNames(context.Type);
            schema.Enum.Clear();
            foreach (string enumName in enumNames)
            {
                schema.Enum.Add(new OpenApiString(enumName));
            }
        }
    }
}
