using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.AssociationHub.Infrastructure.AssociationRequest;

internal sealed class AssociationRequestConfiguration : IEntityTypeConfiguration<Domain.AssociationRequest.AssociationRequest>
{
    public void Configure(EntityTypeBuilder<Domain.AssociationRequest.AssociationRequest> builder)
    {
        builder.ToTable("association_requests");

        builder.HasQueryFilter(ar => ar.DeletedAt == null);

        builder.HasKey(ar => ar.Id);
        builder.Property(ar => ar.Id).HasColumnType("uuid");

        builder.Property(ar => ar.EntityType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(ar => ar.EntityId)
            .IsRequired()
            .HasColumnType("uuid");

        builder.Property(ar => ar.TargetEntityType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(ar => ar.TargetEntityId)
            .IsRequired()
            .HasColumnType("uuid");

        builder.Property(ar => ar.Operation)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(ar => ar.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(ar => ar.ErrorMessage)
            .HasMaxLength(1000);

        builder.Property(ar => ar.CreatedAt)
            .IsRequired();

        builder.Property(ar => ar.ProcessedAt);

        builder.Property(ar => ar.RetryCount)
            .IsRequired()
            .HasDefaultValue(0);

        // Indexes for performance
        builder.HasIndex(ar => ar.Status);
        builder.HasIndex(ar => new { ar.EntityType, ar.EntityId });
        builder.HasIndex(ar => new { ar.TargetEntityType, ar.TargetEntityId });
        builder.HasIndex(ar => ar.CreatedAt);
    }
}
