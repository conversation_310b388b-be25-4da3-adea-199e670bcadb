namespace DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;

public class AutomaticSearchResponse
{
    public bool Success { get; set; }
    public required Data Data { get; set; }
}

public class Data
{
    public required string Id { get; set; }
    public required string Key { get; set; }
    public required string Value { get; set; }
    public required IEnumerable<Request> Requests { get; set; }
    public required string CreatedAt { get; set; }
}

public class Request
{
    public required string Id { get; set; }
    public required string Status { get; set; }
    public required string Source { get; set; }
    public required string Platform { get; set; }
    public required string Query { get; set; }
    public required string Court { get; set; }
    public required string Search { get; set; }
    public required Param Param { get; set; }
    public required object RespondedAt { get; set; }
    public required string CreatedAt { get; set; }
}

public class Param
{
    public required string Key { get; set; }
    public required string Value { get; set; }
}
