﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OabDomain = DataVenia.Modules.Users.Domain.Oab.Oab;

namespace DataVenia.Modules.Users.Infrastructure.Oab;
public sealed class OabConfiguration : IEntityTypeConfiguration<OabDomain>
{
    public void Configure(EntityTypeBuilder<OabDomain> builder)
    {
        builder.ToTable("oab");
        
        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Value)
              .IsRequired()
              .HasMaxLength(100);

        builder.HasIndex(e => e.Value)
              .IsUnique();

        builder.HasOne(o => o.Lawyer)
            .WithOne(l => l.Oab)
            .HasForeignKey<OabDomain>(o => o.LawyerId)
            .OnDelete(DeleteBehavior.NoAction);

    }
}
