﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Company.GetCompanies;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Company;
public sealed class GetCompanies: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/companies", async (Guid officeId, [FromServices] ISender sender) =>
        {
            Result<IReadOnlyCollection<GetCompaniesResponse>> result = await sender.Send(new GetCompaniesQuery(
                officeId));

            return result.Match(
                success =>
                {
                    var responseObject = new
                    {
                        items = success
                    };

                    return Results.Ok(responseObject);
                },
                ApiResults.Problem);
        })
        .RequireAuthorization("office:clients:read")
        .WithTags(Tags.Company);
    }
}
