﻿using Bogus;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.Office;
using DataVenia.Modules.Users.Domain.SharedModels;
using DataVenia.Modules.Users.Infrastructure.Database;

namespace DataVenia.Common.SeedDatabase.UsersModule;
public sealed class OfficeSeed
{
    private readonly UsersDbContext _usersContext;

    public OfficeSeed(UsersDbContext usersContext)
    {
        _usersContext = usersContext;
    }

    public async Task<Guid> Seed(string? officeName = null)
    {
        Faker<Office> officeFaker = new Faker<Office>()
            .CustomInstantiator(f => Office.Create(
                name: officeName ?? f.Company.CompanyName(),
                website: f.Internet.Url(),
                cnpj: Generator.GenerateValidCnpj(),
                contacts: new List<Contact>(),
                addresses: new List<Address>()
                ));

        Office newOffice = officeFaker.Generate();

        _usersContext.Offices.Add(newOffice);
        await _usersContext.SaveChangesAsync();

        return newOffice.Id;
    }
}
