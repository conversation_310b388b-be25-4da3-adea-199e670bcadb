﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Calendar.Application.Abstractions.Data;
using DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointment;
using DataVenia.Modules.Calendar.Domain.Appointments;
using Microsoft.Extensions.Logging;
using AppointmentDomain = DataVenia.Modules.Calendar.Domain.Appointments.Appointment;
namespace DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointmentPartial.cs;

public sealed class UpdateAppointmentPatchCommandHandler(IAppointmentRepository appointmentRepository, IUnitOfWork unitOfWork, ILogger<UpdateAppointmentPatchCommandHandler> logger) : ICommandHandler<UpdateAppointmentPatchCommand>
{
    public async Task<Result> Handle(UpdateAppointmentPatchCommand request, CancellationToken cancellationToken)
    {
        AppointmentDomain? appointment =
            await appointmentRepository.GetSingleAsync(a => a.Id == request.AppointmentId, cancellationToken);

        if (appointment is null)
            return Result.Failure(AppointmentErrors.NotFound(request.AppointmentId));

        if (request.UserId != appointment.OwnerLawyerId)
            return Result.Failure(AppointmentErrors.Unauthorized);

        Recurrence? recurrence = null;
        if (request.Recurrence is not null)
        {
            Result<Recurrence> recurrenceResult = Recurrence.Create(
                request.Recurrence.Frequency,
                request.Recurrence.DaysOfWeek,
                request.Recurrence.DaysOfMonth,
                request.Recurrence.StartDate,
                request.Recurrence.EndDate,
                request.Recurrence.StartTime,
                request.Recurrence.EndTime
            );

            if (recurrenceResult.IsFailure)
                return Result.Failure<Recurrence>(recurrenceResult.Error);

            recurrence = recurrenceResult.Value;
        }

        appointment.UpdatePartial(
            request.Type,
            request.Name,
            request.Description,
            request.ResponsibleLawyerId,
            recurrence,
            request.ParticipantLawyersIds,
            request.Alerts,
            request.StatusId
        );

        appointmentRepository.Update(appointment);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error partially updating appointment");
            return Result.Failure(new Error("Internal.Server.Error", "Something weird happened.", ErrorType.InternalServerError));
        }

        return Result.Success();
    }
}
