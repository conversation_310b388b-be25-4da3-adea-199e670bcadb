import{z as t,S as a}from"./schemas-CWY_3UB6.js";import{y as i}from"./index-DxHSBLqJ.js";var s;(o=>{o.create=r=>({id:i(),name:r.name||"",oab:r.oab||""}),o.schemaCreate=t.object({email:a.email,password:a.string.optional(),firstName:a.string,lastName:a.string,oabs:t.array(a.oab),cpf:a.cpf,rg:a.string.optional(),cnh:a.string.optional(),passport:a.string.optional(),ctps:a.string.optional(),pis:a.string.optional(),voterId:a.string.optional(),contacts:t.array(t.object({type:a.string,value:a.string}))})})(s||(s={}));export{s as L};
