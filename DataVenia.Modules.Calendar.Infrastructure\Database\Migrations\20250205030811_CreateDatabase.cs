﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Calendar.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class CreateDatabase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "calendar");

            migrationBuilder.CreateTable(
                name: "recurrence",
                schema: "calendar",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    frequency = table.Column<string>(type: "text", nullable: false),
                    start_date = table.Column<DateOnly>(type: "date", nullable: false),
                    end_date = table.Column<DateOnly>(type: "date", nullable: false),
                    start_time = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    end_time = table.Column<TimeOnly>(type: "time without time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_recurrence", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "status",
                schema: "calendar",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    display_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    description = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: true),
                    order_index = table.Column<int>(type: "integer", nullable: false),
                    office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_status", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "appointment",
                schema: "calendar",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    responsible_lawyer_id = table.Column<Guid>(type: "uuid", nullable: false),
                    recurrence_id = table.Column<Guid>(type: "uuid", nullable: true),
                    owner_lawyer_id = table.Column<Guid>(type: "uuid", nullable: false),
                    owner_office_id = table.Column<Guid>(type: "uuid", nullable: false),
                    status_id = table.Column<Guid>(type: "uuid", nullable: true),
                    Alerts = table.Column<string>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_appointment", x => x.id);
                    table.ForeignKey(
                        name: "fk_appointment_recurrence_recurrence_id",
                        column: x => x.recurrence_id,
                        principalSchema: "calendar",
                        principalTable: "recurrence",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "fk_appointment_statuses_status_id",
                        column: x => x.status_id,
                        principalSchema: "calendar",
                        principalTable: "status",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "appointment_participant",
                schema: "calendar",
                columns: table => new
                {
                    appointment_id = table.Column<Guid>(type: "uuid", nullable: false),
                    lawyer_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_appointment_participant", x => new { x.appointment_id, x.lawyer_id });
                    table.ForeignKey(
                        name: "fk_appointment_participant_appointment_appointment_id",
                        column: x => x.appointment_id,
                        principalSchema: "calendar",
                        principalTable: "appointment",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_appointment_recurrence_id",
                schema: "calendar",
                table: "appointment",
                column: "recurrence_id");

            migrationBuilder.CreateIndex(
                name: "ix_appointment_status_id",
                schema: "calendar",
                table: "appointment",
                column: "status_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "appointment_participant",
                schema: "calendar");

            migrationBuilder.DropTable(
                name: "appointment",
                schema: "calendar");

            migrationBuilder.DropTable(
                name: "recurrence",
                schema: "calendar");

            migrationBuilder.DropTable(
                name: "status",
                schema: "calendar");
        }
    }
}
