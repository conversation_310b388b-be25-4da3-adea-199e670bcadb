using FluentResults;

namespace DataVenia.Common.Infrastructure.Facades;

public interface IBlobStorage
{
    Task<Result> UploadAsync(string bucketName, Stream inputStream, string objectName, string contentType = "application/octet-stream");
    Task<Result> DownloadAsync(string bucketName, string objectName, Stream outputStream, CancellationToken cancellationToken);
    Task<Result> DeleteAsync(string bucketName, string objectName);
    Task<Result<bool>> ExistsAsync(string bucketName, string objectName);
    Task<Result<IList<string>>> ListAsync(string bucketName, string? prefix = null);
}

