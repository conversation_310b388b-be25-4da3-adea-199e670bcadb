﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemoveMistakenlyCreatedTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "lawsuit_response",
                schema: "lawsuit");

            migrationBuilder.DropTable(
                name: "party_dto",
                schema: "lawsuit");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "lawsuit_response",
                schema: "lawsuit",
                columns: table => new
                {
                    access = table.Column<string>(type: "text", nullable: true),
                    cause_value = table.Column<decimal>(type: "numeric", nullable: true),
                    cnj = table.Column<string>(type: "text", nullable: false),
                    conviction_value = table.Column<decimal>(type: "numeric", nullable: true),
                    court = table.Column<string>(type: "text", nullable: false),
                    court_division_id = table.Column<string>(type: "text", nullable: false),
                    court_href = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    distributed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    evolved_from_case_id = table.Column<Guid>(type: "uuid", nullable: true),
                    folder_id = table.Column<Guid>(type: "uuid", nullable: true),
                    folder_name = table.Column<string>(type: "text", nullable: true),
                    forum_id = table.Column<string>(type: "text", nullable: false),
                    grouping_case_id = table.Column<Guid>(type: "uuid", nullable: true),
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    lawsuit_status_id = table.Column<string>(type: "text", nullable: false),
                    lawsuit_type_id = table.Column<string>(type: "text", nullable: false),
                    legal_category_id = table.Column<string>(type: "text", nullable: false),
                    legal_instance_id = table.Column<string>(type: "text", nullable: false),
                    observations = table.Column<string>(type: "text", nullable: true),
                    responsible_ids = table.Column<List<Guid>>(type: "uuid[]", nullable: false),
                    title = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "party_dto",
                schema: "lawsuit",
                columns: table => new
                {
                    is_client = table.Column<bool>(type: "boolean", nullable: false),
                    party_type = table.Column<string>(type: "text", nullable: false),
                    user_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                });
        }
    }
}
