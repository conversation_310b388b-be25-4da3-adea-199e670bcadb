﻿using Bogus;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.SeedDatabase.UsersModule;

public sealed class SeedUsersModule
{
    private readonly Faker _faker;
    private readonly LawyerSeed _lawyerSeed;
    private readonly OfficeSeed _officeSeed;
    private readonly OfficeUserSeed _officeUserSeed;
    private readonly UserGlobalRoleSeed _userGlobalRoleSeed;
    private readonly ILogger<SeedUsersModule> _logger;

    public SeedUsersModule(IIdentityProviderService identityProviderService, UsersDbContext usersDbContext, ILogger<SeedUsersModule> logger)
    {
        var loggerFactory = new LoggerFactory();
        ILogger<UserGlobalRoleSeed> loggerGlobal = loggerFactory.CreateLogger<UserGlobalRoleSeed>();
        ILogger<LawyerSeed> lawyerSeed = loggerFactory.CreateLogger<LawyerSeed>();

        loggerFactory.Dispose();

        _faker = new Faker();
        _lawyerSeed = new LawyerSeed(identityProviderService, usersDbContext, lawyerSeed);
        _officeSeed = new OfficeSeed(usersDbContext);
        _officeUserSeed = new OfficeUserSeed(usersDbContext);
        _userGlobalRoleSeed = new UserGlobalRoleSeed(usersDbContext, loggerGlobal);
        _logger = logger;
    }

    public async Task Seed()
    {
        try
        {
            #region seed prod
            
            Guid? raphaelLawyerId = await _lawyerSeed.Seed("<EMAIL>", "#Trocar123", "Raphael", "Tavares", Role.OfficeAdministrator.Name);
            Guid? flavioLawyerId = await _lawyerSeed.Seed("<EMAIL>", "#Trocar123", "Flávio", "Barci", Role.OfficeAdministrator.Name);
            Guid? renanLawyerId = await _lawyerSeed.Seed("<EMAIL>", "#Trocar123", "Renan", "Spatin", Role.OfficeAdministrator.Name);
            Guid? jesseLawyerId = await _lawyerSeed.Seed("<EMAIL>", "#Trocar123", "Filipe", "Jesse", Role.OfficeAdministrator.Name);
            Guid? allanLawyerId = await _lawyerSeed.Seed("<EMAIL>", "#Trocar123", "Allan", "Garcez", Role.OfficeAdministrator.Name);

            Guid lentiumOfficeId = await _officeSeed.Seed(_faker.Company.CompanyName());

            OfficeUser raphaelOfficeUser = await _officeUserSeed.Seed(lentiumOfficeId, (Guid)raphaelLawyerId!, (Guid)raphaelLawyerId, Role.OfficeAdministrator.Name);
            await _officeUserSeed.AcceptInvitation(raphaelOfficeUser);
            
            OfficeUser flavioOfficeUser = await _officeUserSeed.Seed(lentiumOfficeId, (Guid)flavioLawyerId!, (Guid)flavioLawyerId, Role.OfficeAdministrator.Name);
            await _officeUserSeed.AcceptInvitation(flavioOfficeUser);
            
            OfficeUser renanOfficeUser = await _officeUserSeed.Seed(lentiumOfficeId, (Guid)renanLawyerId!, (Guid)renanLawyerId, Role.OfficeAdministrator.Name);
            await _officeUserSeed.AcceptInvitation(renanOfficeUser);
            
            OfficeUser jesseOfficeUser = await _officeUserSeed.Seed(lentiumOfficeId, (Guid)jesseLawyerId!, (Guid)jesseLawyerId, Role.OfficeAdministrator.Name);
            await _officeUserSeed.AcceptInvitation(jesseOfficeUser);
            
            OfficeUser allanOfficeUser = await _officeUserSeed.Seed(lentiumOfficeId, (Guid)allanLawyerId!, (Guid)allanLawyerId, Role.OfficeAdministrator.Name);
            await _officeUserSeed.AcceptInvitation(allanOfficeUser);
            
            await _userGlobalRoleSeed.Seed((Guid)raphaelLawyerId, Role.SystemMember.Name);
            await _userGlobalRoleSeed.Seed((Guid)flavioLawyerId, Role.SystemMember.Name);
            await _userGlobalRoleSeed.Seed((Guid)renanLawyerId, Role.SystemMember.Name);
            await _userGlobalRoleSeed.Seed((Guid)jesseLawyerId, Role.SystemMember.Name);
            await _userGlobalRoleSeed.Seed((Guid)allanLawyerId, Role.SystemMember.Name);
            
            
            // Guid? larissaLawyerId = await _lawyerSeed.Seed("<EMAIL>", "#Trocar123", "Larissa", "Rezende", Role.OfficeAdministrator.Name);
            // Guid larissaRezendeAdvOfficeId = await _officeSeed.Seed("Larissa Rezende Advocacia");
            // OfficeUser larissaOfficeUser = await _officeUserSeed.Seed(larissaRezendeAdvOfficeId, (Guid)larissaLawyerId!, (Guid)larissaLawyerId, Role.OfficeAdministrator.Name);
            // await _officeUserSeed.AcceptInvitation(larissaOfficeUser);
            // await _userGlobalRoleSeed.Seed((Guid)larissaLawyerId, Role.SystemMember.Name);
            //
            // Guid? amandaLawyerId = await _lawyerSeed.Seed("<EMAIL>", "#Trocar123", "Amanda", "Hisse", Role.OfficeAdministrator.Name);
            // Guid larissaRezendeAdvOfficeId = await _officeSeed.Seed("Larissa Rezende Advocacia");
            // OfficeUser larissaOfficeUser = await _officeUserSeed.Seed(larissaRezendeAdvOfficeId, (Guid)larissaLawyerId!, (Guid)larissaLawyerId, Role.OfficeAdministrator.Name);
            // await _officeUserSeed.AcceptInvitation(larissaOfficeUser);
            // await _userGlobalRoleSeed.Seed((Guid)larissaLawyerId, Role.SystemMember.Name);
            
            #endregion
            
            // #region seed lawyer
            // Guid? adminLawyerId = await _lawyerSeed.Seed("<EMAIL>", "123", "Raphael", "Tavares", Role.OfficeAdministrator.Name);
            //
            // if (adminLawyerId == null)
            //     adminLawyerId = await _lawyerSeed.GetLawyerIdByEmail("<EMAIL>");
            //
            // var lawyers = new List<Guid>();
            //
            // for (int i = 0; i < 10; i++)
            // {
            //     Guid? lawyerId = await _lawyerSeed.Seed(_faker.Internet.Email(), "123", _faker.Name.FirstName(), _faker.Name.LastName(), Role.OfficeMember.Name);
            //
            //     if (lawyerId.HasValue)
            //         lawyers.Add(lawyerId.Value);
            // }
            //
            // #endregion
            //
            // #region seed office
            //
            // var offices = new List<Guid>();
            //
            // for (int i = 0; i < 2; i++)
            // {
            //     Guid officeId = await _officeSeed.Seed(_faker.Company.CompanyName());
            //
            //     offices.Add(officeId);
            // }
            //
            // #endregion
            //
            //
            // if (adminLawyerId.HasValue)
            // {
            //     #region seed office_user
            //     Guid chosenOffice = offices.FirstOrDefault();
            //
            //     OfficeUser adminOfficeUser = await _officeUserSeed.Seed(chosenOffice, (Guid)adminLawyerId, (Guid)adminLawyerId, Role.OfficeAdministrator.Name);
            //
            //     await _officeUserSeed.AcceptInvitation(adminOfficeUser);
            //
            //     for (int i = 0; i < 5 && i < lawyers.Count; i++)
            //     {
            //         OfficeUser newOfficeUser = await _officeUserSeed.Seed(chosenOffice, lawyers[i], (Guid)adminLawyerId, Role.OfficeMember.Name);
            //
            //         if (i % 2 == 0)
            //         {
            //             await _officeUserSeed.AcceptInvitation(newOfficeUser);
            //         }
            //     }
            //     #endregion
            //
            //     #region seed global_user_role
            //
            //     await _userGlobalRoleSeed.Seed((Guid)adminLawyerId, Role.SystemMember.Name);
            //
            //     for (int i = 0; i < lawyers.Count; i++)
            //     {
            //         await _userGlobalRoleSeed.Seed(lawyers[i], Role.SystemMember.Name);
            //     }
            //
            //     #endregion
            // }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error seeding users");
        }
    }
}
