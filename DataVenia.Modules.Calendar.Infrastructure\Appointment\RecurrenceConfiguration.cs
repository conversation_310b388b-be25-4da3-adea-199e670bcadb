﻿using DataVenia.Modules.Calendar.Domain.Appointments;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Calendar.Infrastructure.Appointments;
public sealed class RecurrenceConfiguration : IEntityTypeConfiguration<Recurrence>
{
    public void Configure(EntityTypeBuilder<Recurrence> builder)
    {
        builder.ToTable("recurrence");

        // Conversão do Enum para string
        builder.Property(r => r.Frequency)
               .HasConversion(
                   v => v.ToString(), // Para salvar no banco como string
                   v => Enum.Parse<RecurrenceFrequency>(v)); // Para carregar do banco como enum

        builder.HasKey(u => u.Id);

        builder.Property(a => a.StartTime)
    .IsRequired();

        builder.Property(a => a.EndTime)
            .IsRequired();

        builder.Property(a => a.StartDate)
    .IsRequired();

        builder.Property(a => a.EndDate)
            .IsRequired();
    }
}
