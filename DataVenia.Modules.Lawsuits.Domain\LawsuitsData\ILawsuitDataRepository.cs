﻿
using FluentResults;

namespace DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
public interface ILawsuitDataRepository
{
    Task<Result<LawsuitData?>> GetLatestByLawsuitIdAsync(Guid lawsuitId, CancellationToken cancellationToken);

    Task<FluentResults.Result<List<LawsuitData>>> GetLatestOfEachInstanceByLawsuitIdsAsync(List<Guid> lawsuitIds,
        CancellationToken cancellationToken);
    
    Task<FluentResults.Result<LawsuitData>> GetLatestOfInstanceByLawsuitIdAsync(Guid lawsuitId, string instance,
        CancellationToken cancellationToken);
    
    Task<Result<List<Guid>>> GetLawyerIdsByLawsuitDataIdAsync(Guid lawsuitDataId, CancellationToken cancellationToken = default);
    
    void Insert(LawsuitData lawsuitData);
    void Update(LawsuitData lawsuitData);
}
