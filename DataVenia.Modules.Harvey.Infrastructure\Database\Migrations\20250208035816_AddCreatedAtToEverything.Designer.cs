﻿// <auto-generated />
using System;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    [DbContext(typeof(HarveyDbContext))]
    [Migration("20250208035816_AddCreatedAtToEverything")]
    partial class AddCreatedAtToEverything
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("harvey")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.Action.LawsuitType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_type");

                    b.ToTable("lawsuit_type", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.CourtDivision.CourtDivision", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_court_division");

                    b.ToTable("court_division", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.Forum.Forum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_forum");

                    b.ToTable("forum", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LawsuitStatus.LawsuitStatus", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_status");

                    b.ToTable("lawsuit_status", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "Pending",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 441, DateTimeKind.Utc).AddTicks(125),
                            DisplayName = "Pendente"
                        },
                        new
                        {
                            Id = "InProgress",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3683),
                            DisplayName = "Em progresso"
                        },
                        new
                        {
                            Id = "Suspended",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3693),
                            DisplayName = "Suspendido"
                        },
                        new
                        {
                            Id = "AwaitingAppeal",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3697),
                            DisplayName = "Aguardando Apelação"
                        },
                        new
                        {
                            Id = "Completed",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3699),
                            DisplayName = "Completo"
                        },
                        new
                        {
                            Id = "Closed",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3702),
                            DisplayName = "Fechado"
                        },
                        new
                        {
                            Id = "Cancelled",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3705),
                            DisplayName = "Cancelado"
                        },
                        new
                        {
                            Id = "AwaitingClient",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3708),
                            DisplayName = "Aguardando cliente"
                        },
                        new
                        {
                            Id = "AwaitingDecision",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3711),
                            DisplayName = "Aguardando decisão"
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LegalCategory.LegalCategory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_legal_category");

                    b.ToTable("legal_category", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "CollectionAction",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3456),
                            DisplayName = "Ação coletiva",
                            Order = 1
                        },
                        new
                        {
                            Id = "DivorceAction",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3741),
                            DisplayName = "Ação de divórcio",
                            Order = 2
                        },
                        new
                        {
                            Id = "HabeasCorpus",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3746),
                            DisplayName = "Habeas Corpus",
                            Order = 6
                        },
                        new
                        {
                            Id = "LaborAction",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3742),
                            DisplayName = "Ação trabalhista",
                            Order = 3
                        },
                        new
                        {
                            Id = "TaxEnforcementAction",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3743),
                            DisplayName = "Supremo Tribunal Federal",
                            Order = 4
                        },
                        new
                        {
                            Id = "WritOfMandamus",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3744),
                            DisplayName = "Mandado de segurança",
                            Order = 5
                        },
                        new
                        {
                            Id = "Other",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3746),
                            DisplayName = "Other",
                            Order = 7
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LegalInstance.LegalInstance", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_legal_instance");

                    b.ToTable("legal_instance", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "FirstInstance",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8413),
                            DisplayName = "Primeira Instância",
                            Order = 1
                        },
                        new
                        {
                            Id = "SecondInstance",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8583),
                            DisplayName = "Segunda Instância",
                            Order = 2
                        },
                        new
                        {
                            Id = "STF",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8586),
                            DisplayName = "Supremo Tribunal Federal",
                            Order = 4
                        },
                        new
                        {
                            Id = "STJ",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8584),
                            DisplayName = "Superior Tribunal de Justiça",
                            Order = 3
                        },
                        new
                        {
                            Id = "Other",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8586),
                            DisplayName = "Outras",
                            Order = 5
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.PartyType.PartyType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_party_type");

                    b.ToTable("party_type", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "Lawyer",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3501),
                            DisplayName = "Advogado",
                            Order = 1
                        },
                        new
                        {
                            Id = "Defendant",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3663),
                            DisplayName = "Réu",
                            Order = 3
                        },
                        new
                        {
                            Id = "Plaintiff",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3661),
                            DisplayName = "Autor",
                            Order = 2
                        },
                        new
                        {
                            Id = "Witness",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3664),
                            DisplayName = "Testemunha",
                            Order = 4
                        },
                        new
                        {
                            Id = "Other",
                            CreatedAt = new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3664),
                            DisplayName = "Outro",
                            Order = 5
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
