﻿// <auto-generated />
using System;
using System.Collections.Generic;
using DataVenia.Modules.LawsuitSync.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.LawsuitSync.Infrastructure.Database.Migrations
{
    [DbContext(typeof(LawsuitSyncDbContext))]
    [Migration("20250421152404_AddSubscriptions")]
    partial class AddSubscriptions
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("lawsuit-sync")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DataVenia.Modules.LawsuitSync.Domain.Entities.LawsuitEventsHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Cnj")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cnj");

                    b.Property<string>("Event")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("event");

                    b.Property<DateTimeOffset>("EventDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("event_date");

                    b.Property<Guid>("LawsuitMonitoringConfigurationExternalId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawsuit_monitoring_configuration_external_id");

                    b.Property<string>("LawsuitStatus")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("lawsuit_status");

                    b.Property<string>("MonitoringType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("monitoring_type");

                    b.HasKey("Id")
                        .HasName("pk_tb_lawsuit_events_history");

                    b.ToTable("tb_lawsuit_events_history", "lawsuit-sync");
                });

            modelBuilder.Entity("DataVenia.Modules.LawsuitSync.Domain.Entities.LawsuitMonitoringConfiguration", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Cnj")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cnj");

                    b.Property<string>("CourtDivision")
                        .HasColumnType("text")
                        .HasColumnName("court_division");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("now()");

                    b.Property<string>("CurrentStatus")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("current_status");

                    b.Property<Guid>("ExternalId")
                        .HasColumnType("uuid")
                        .HasColumnName("external_id");

                    b.Property<DateTimeOffset>("LastUpdate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_update");

                    b.Property<string>("LawsuitSyncSource")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("lawsuit_sync_source");

                    b.Property<string>("LegalInstance")
                        .HasColumnType("text")
                        .HasColumnName("legal_instance");

                    b.Property<string>("MonitoringType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("monitoring_type");

                    b.Property<string>("Platform")
                        .HasColumnType("text")
                        .HasColumnName("platform");

                    b.Property<string>("ScheduleCronExpression")
                        .HasColumnType("text")
                        .HasColumnName("schedule_cron_expression");

                    b.Property<List<string>>("Subscriptions")
                        .IsRequired()
                        .HasColumnType("text[]")
                        .HasColumnName("subscriptions");

                    b.HasKey("Id")
                        .HasName("pk_tb_lawsuit_monitoring_configuration");

                    b.HasIndex("Cnj")
                        .HasDatabaseName("ix_tb_lawsuit_monitoring_configuration_cnj");

                    b.HasIndex("ExternalId")
                        .IsUnique()
                        .HasDatabaseName("ix_tb_lawsuit_monitoring_configuration_external_id");

                    b.ToTable("tb_lawsuit_monitoring_configuration", "lawsuit-sync");
                });
#pragma warning restore 612, 618
        }
    }
}
