using DataVenia.Common.Contracts.Events.Lawsuit;
using DataVenia.Common.Contracts.Events.LawsuitSync;
using DataVenia.Modules.LawsuitSync.Application.AppServices;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.LawsuitSync.Worker.Workers;

public class InactivateLawsuitSyncConsumer(
    IStopLawsuitMonitoringAppService stopLawsuitMonitoring,
    ILogger<InactivateLawsuitSyncConsumer> logger) : IConsumer<DeactivateLawsuitSync>
{
    public async Task Consume(ConsumeContext<DeactivateLawsuitSync> context)
    {
        logger.LogInformation("Start processing deactivate monitoring logic");

        context = context ?? throw new ArgumentNullException(nameof(context));

        logger.LogInformation("Start processing deactivate monitoring cnj {Cnj}", context.Message.Cnj);

        await stopLawsuitMonitoring.ExecuteAsync(context.Message.Cnj, context.Message.SubscriberId);

        logger.LogTrace("Stop monitoring cnj {Number}", context.Message.Cnj);
    }
}
