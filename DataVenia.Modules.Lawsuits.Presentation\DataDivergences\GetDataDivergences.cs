using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.DataDivergences.GetDataDivergences;
using DataVenia.Modules.Users.Presentation;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.DataDivergences;

public sealed class GetDataDivergences(ILogger<GetDataDivergences> logger) : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/offices/{officeId}/lawsuit/{lawsuitId}/divergences", async (
            Guid officeId, 
            Guid lawsuitId,
            [FromServices] ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<DataDivergenceResponse>> result = 
                    await sender.Send(new GetDataDivergencesQuery(officeId, lawsuitId));

                return result.ToHttpResult(success =>
                {
                    var responseObject = new
                    {
                        items = success
                    };

                    return Results.Ok(responseObject);
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error getting data divergences for office {OfficeId}", officeId);
                return Results.Problem("An error occurred while retrieving data divergences");
            }
        })
        .RequireAuthorization("office:lawsuits:read")
        .WithTags(Tags.LawsuitDivergences);
    }
}
