﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemoveUselessFieldsFromCase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "case_type_id",
                schema: "lawsuit",
                table: "case");

            migrationBuilder.DropColumn(
                name: "legal_category_id",
                schema: "lawsuit",
                table: "case");

            migrationBuilder.RenameColumn(
                name: "responsibles",
                schema: "lawsuit",
                table: "case_response",
                newName: "responsible_ids");

            migrationBuilder.AddColumn<string>(
                name: "access",
                schema: "lawsuit",
                table: "case_response",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "description",
                schema: "lawsuit",
                table: "case_response",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "observations",
                schema: "lawsuit",
                table: "case_response",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "access",
                schema: "lawsuit",
                table: "case_response");

            migrationBuilder.DropColumn(
                name: "description",
                schema: "lawsuit",
                table: "case_response");

            migrationBuilder.DropColumn(
                name: "observations",
                schema: "lawsuit",
                table: "case_response");

            migrationBuilder.RenameColumn(
                name: "responsible_ids",
                schema: "lawsuit",
                table: "case_response",
                newName: "responsibles");

            migrationBuilder.AddColumn<string>(
                name: "case_type_id",
                schema: "lawsuit",
                table: "case",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "legal_category_id",
                schema: "lawsuit",
                table: "case",
                type: "text",
                nullable: false,
                defaultValue: "");
        }
    }
}
