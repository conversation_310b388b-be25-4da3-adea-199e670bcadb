﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Client.UpdateClient;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Domain.Lawyers;
using Microsoft.Extensions.Logging;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;

namespace DataVenia.Modules.Users.Application.Client.DeleteClient;

public class DeleteClientCommandHandler(IClientRepository clientRepository, IUnitOfWork unitOfWork, ILogger<DeleteClientCommandHandler> logger) : ICommandHandler<DeleteClientCommand>
{
    public async Task<Result> Handle(DeleteClientCommand request, CancellationToken cancellationToken)
    {
        ClientDomain? client = await clientRepository.GetSingleAsync(x => x.Id == request.ClientId, cancellationToken);

        if (client is null)
        {
            logger.LogError("Client not found: {ClientId}", request.ClientId);
            return Result.Failure(Error.NotFound("Not.Found", $"Client with Id {request.ClientId} not found"));
        }

        client.SoftDelete();

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        } catch (Exception ex)
        {
            logger.LogError(ex, "Error soft deleting client in the database.");
            return Result.Failure<Guid>(Error.InternalServerError());
        }
        
        return Result.Success();
    }
}
