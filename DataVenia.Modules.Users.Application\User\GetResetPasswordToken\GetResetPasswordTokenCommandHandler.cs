﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.Events.Notification;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.SignUpToken;
using MassTransit;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.User.GetResetPasswordToken;

public sealed class GetResetPasswordTokenCommandHandler(
    ILawyerRepository lawyerRepository,
    ISignUpTokenRepository signUpTokenRepository,
    IUnitOfWork unitOfWork, 
    IPublishEndpoint publisher,
    ILogger<GetResetPasswordTokenCommandHandler> logger,
    IConfiguration configuration
) : ICommandHandler<GetResetPasswordTokenCommand>
{
    public async Task<Result> Handle(GetResetPasswordTokenCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            Domain.Lawyers.Lawyer? lawyerToBeAdded =
                await lawyerRepository.GetByEmailAsync(request.email, cancellationToken);

            if (lawyerToBeAdded == null)
                return Result.Failure(Error.NotFound("Email.Not.Found",
                    "Email could not be found"));

            var signUpToken = SignUpToken.Create(lawyerToBeAdded.Id, tokenType: TokenType.ResetPassword);
        
            signUpTokenRepository.Insert(signUpToken);
 
            await unitOfWork.SaveChangesAsync(cancellationToken);
            // enviar o email no notification module
            string? resetPasswordUrl = configuration["Users:ResetPasswordUrl"];

            if (string.IsNullOrWhiteSpace(resetPasswordUrl))
            {
                logger.LogError("SignUpUrl env variable is not configured.");
                return Result.Failure(Error.InternalServerError());
            }

            var eventMessage = new MailNotificationEvent(
                $"{resetPasswordUrl}{signUpToken.Token}",
                lawyerToBeAdded.Email,
                lawyerToBeAdded.FirstName,
                NotificationMailType.ForgetPassword,
                $"[DataVenia] Please reset your password"
            );

            await publisher.Publish(eventMessage, cancellationToken);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error to generate reset password token for email. {Email}", request.email);
            return Result.Failure(Error.NotFound("Email.Not.Found",
                "Email could not be found"));
        }
    }
}
