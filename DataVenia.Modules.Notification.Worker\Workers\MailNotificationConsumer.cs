using DataVenia.Common.Contracts.Events.Notification;
using DataVenia.Modules.Notification.Application.Factories;
using DataVenia.Modules.Notification.Application.Strategy;
using DataVenia.Modules.Notification.Domain.Enums;
using DataVenia.Modules.Notification.Worker.Helpers;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Notification.Worker.Workers;

public sealed class MailNotificationConsumer(
    NotificationStrategyFactory strategyFactory,
    ILogger<MailNotificationConsumer> logger,
    IConfiguration configuration) : IConsumer<MailNotificationEvent>
{
    private static readonly Dictionary<NotificationMailType, string> MailHtmlName =
        new()
        {
            { NotificationMailType.MailConfirmation, "confirm-email.html" },
            { NotificationMailType.ForgetPassword, "forget-password.html" },
            { NotificationMailType.MagicLink, "magic-link.html" }
        };

    private readonly string _companyLogo = configuration["Notification:CompanyLogo"] ?? throw new ArgumentException(nameof(_companyLogo));
    private readonly string _companyName = configuration["Notification:CompanyName"] ?? throw new ArgumentException(nameof(_companyName));

    public async Task Consume(ConsumeContext<MailNotificationEvent> context)
    {
        context = context ?? throw new ArgumentNullException(nameof(context));
        
        MailNotificationEvent notificationEvent = context.Message;

        INotificationStrategy strategy = strategyFactory.Create(NotificationEventType.Email);
        if (strategy != null)
        {
            string htmlName = MailHtmlName[notificationEvent.MailType];
            string mailMessage =
                MailHelper.CreateMailMessage(htmlName, notificationEvent.Message, _companyLogo, _companyName);

            await strategy.SendNotificationAsync(mailMessage, notificationEvent.RecipientMail,
                notificationEvent.Subject).ConfigureAwait(false);
        }

        logger.LogTrace("Processed mail notification for {Recipient}", notificationEvent.RecipientMail);
    }
}
