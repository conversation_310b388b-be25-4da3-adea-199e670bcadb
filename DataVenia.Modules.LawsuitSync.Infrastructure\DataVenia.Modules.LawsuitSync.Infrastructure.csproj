﻿<Project Sdk="Microsoft.NET.Sdk">


  <ItemGroup>
    <ProjectReference Include="..\DataVenia.Common.Infrastructure\DataVenia.Common.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Common.Presentation\DataVenia.Common.Presentation.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.LawsuitSync.Application\DataVenia.Modules.LawsuitSync.Application.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.LawsuitSync.Domain\DataVenia.Modules.LawsuitSync.Domain.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.LawsuitSync.Presentation\DataVenia.Modules.LawsuitSync.Presentation.csproj" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="FluentResults" Version="3.16.0" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
  </ItemGroup>

</Project>
