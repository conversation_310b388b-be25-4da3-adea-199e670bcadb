﻿using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.LawsuitTopic;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Harvey.Presentation.LawsuitTopic;

public sealed class GetLawsuitTopics(ILogger<GetLawsuitTopics> _logger) : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/lawsuit-topics", async ([FromServices] ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<GetLawsuitTopicsResponse>> result = await sender.Send(new GetLawsuitTopicsQuery());

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lawsuit topics");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}
