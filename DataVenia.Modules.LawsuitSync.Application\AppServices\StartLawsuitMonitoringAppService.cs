using DataVenia.Common.Domain.Lawsuit;
using DataVenia.Modules.LawsuitSync.Domain.Entities;
using DataVenia.Modules.LawsuitSync.Domain.Enums;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Requests;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Settings;
using FluentResults;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Info = DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses.Info;

namespace DataVenia.Modules.LawsuitSync.Application.AppServices;

public sealed class StartLawsuitMonitoringAppService(
    ICodiloRepository codiloRepository,
    ILawsuitMonitoringRepository lawsuitMonitoringRepository,
    IOptions<CallbackSettings> callbackSettings,
    IMemoryCache memoryCache,
    ILogger<StartLawsuitMonitoringAppService> logger)
    : IStartLawsuitMonitoringAppService
{
    private readonly CallbackSettings _callbackSettings = callbackSettings.Value;

    public async Task<Result> ExecuteAsync(string cnj, Guid subscriberId)
    {
        var cacheKey = $"{cnj}-{subscriberId}";
        try
        {
            if (memoryCache.TryGetValue(cacheKey, out var _))
                return Result.Ok();

            memoryCache.Set($"{cnj}-{subscriberId}", "locked", DateTimeOffset.Now.AddMinutes(1));

            using var _ = logger.BeginScope("StartLawsuitMonitoringAppService.ExecuteAsync");

            var lawsuitMonitoring =
                await lawsuitMonitoringRepository.GetLawsuitMonitoring(cnj).ConfigureAwait(false);

            if (lawsuitMonitoring.IsFailed)
                return Result.Fail(new Error("LawsuitMonitoring not found").WithMetadata("StatusCode", 404));

            if (lawsuitMonitoring.Value?.Any() == true)
            {
                if (lawsuitMonitoring.Value[0].Subscriptions.Contains(subscriberId.ToString()))
                    return Result.Ok();

                foreach (var monitoring in lawsuitMonitoring.Value)
                {
                    monitoring.Subscriptions.Add(subscriberId.ToString());

                    await lawsuitMonitoringRepository.UpdateLawsuitMonitoringConfiguration(monitoring);
                }

                return Result.Ok();
            }

            var request = CreateStartMonitoringRequest(cnj);

            var startLawsuitMonitoring = await codiloRepository.StartLawsuitMonitoring(request).ConfigureAwait(false);
            if (startLawsuitMonitoring.IsFailed)
            {
                logger.LogError("startLawsuitMonitoring => Failed to start monitoring lawsuit for cnj: {Cnj}", cnj);
                return startLawsuitMonitoring.ToResult();
            }

            var codiloResponse = startLawsuitMonitoring.Value;

            var monitoredLawsuits = new List<LawsuitMonitoringConfiguration>();
            var id = codiloResponse.Data.Id;
            foreach (var lawsuit in codiloResponse.Data.Info)
            {
                var monitoringConfiguration =
                    CreateLawsuitMonitoringConfiguration(cnj, lawsuit, codiloResponse, subscriberId, id);
                monitoredLawsuits.Add(monitoringConfiguration);
            }

            var saveLawsuitMonitoringConfiguration =
                await lawsuitMonitoringRepository.AddLawsuitMonitoringConfigurationInBatch(monitoredLawsuits)
                    .ConfigureAwait(false);

            if (saveLawsuitMonitoringConfiguration.IsFailed)
                return saveLawsuitMonitoringConfiguration;


            return Result.Ok();
        }
        finally
        {
            memoryCache.Remove(cacheKey);
        }
    }

    private static LawsuitMonitoringConfiguration CreateLawsuitMonitoringConfiguration(string cnj, Info lawsuit,
        StartMonitoringResponse codiloResponse, Guid subscriberId, Guid monitorExternalId)
    {
        return new LawsuitMonitoringConfiguration()
        {
            ExternalId = lawsuit.Id,
            MonitorExternalId = monitorExternalId,
            Cnj = cnj,
            CreatedAt = codiloResponse.Data.CreatedAt,
            LastUpdate = codiloResponse.Data.CreatedAt,
            CourtDivision = lawsuit.SearchTag,
            LegalInstance = lawsuit.QueryTag,
            Platform = lawsuit.PlatformTag,
            MonitoringType = MonitoringType.Webhook,
            CurrentStatus = LawsuitStatus.None,
            Subscriptions = new List<string>() { subscriberId.ToString() }
        };
    }

    private StartMonitoringRequest CreateStartMonitoringRequest(string cnj)
    {
        var callbacks = _callbackSettings.CallbackAddress.Select(x =>
            new LawsuitMonitoringCallback
            {
                Method = "POST",
                Url = new Uri(x)
            });
        return new StartMonitoringRequest
        {
            Cnj = cnj,
            Callbacks = callbacks
        };
    }
}
