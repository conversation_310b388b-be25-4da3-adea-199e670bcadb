﻿using System.Security.Claims;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitStepsByCnj;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;

public class GetLawsuitStepsByLawsuitId: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/lawsuits/{lawsuitId}/steps", async (Guid officeId, Guid lawsuitId, string? instance, ClaimsPrincipal claims, [FromServices] ISender sender) =>
            {
                var result = await sender.Send(new GetLawsuitStepsByLawsuitIdQuery(
                    claims.GetUserId(),
                    officeId,
                    lawsuitId));

                return result.Match(
                    success => Results.Ok(new { items = success }),
                    ApiResults.Problem
                );

            })
            .RequireAuthorization("office:lawsuits:read")
            .WithTags(Tags.Lawsuits);
    }
}
