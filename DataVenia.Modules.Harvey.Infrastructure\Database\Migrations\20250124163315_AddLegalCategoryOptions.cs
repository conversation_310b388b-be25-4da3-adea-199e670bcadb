﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddLegalCategoryOptions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                schema: "harvey",
                table: "legal_category",
                columns: new[] { "id", "display_name", "order" },
                values: new object[,]
                {
                    { "CollectionAction", "Ação coletiva", 1 },
                    { "DivorceAction", "Ação de divórcio", 2 },
                    { "HabeasCorpus", "Habeas Corpus", 6 },
                    { "LaborAction", "Ação trabalhista", 3 },
                    { "Other", "Other", 7 },
                    { "TaxEnforcementAction", "Supremo Tribunal Federal", 4 },
                    { "WritOfMandamus", "Mandado de segurança", 5 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "CollectionAction");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "DivorceAction");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "HabeasCorpus");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "LaborAction");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "Other");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "TaxEnforcementAction");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "WritOfMandamus");
        }
    }
}
