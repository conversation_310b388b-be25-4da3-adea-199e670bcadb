﻿using Microsoft.AspNetCore.Authorization;

namespace DataVenia.Common.Infrastructure.Authorization;

internal sealed class PermissionRequirement : IAuthorizationRequirement
{
    public PermissionRequirement(string permission)
    {
        Permission = permission;
    }

    public string Permission { get; }

    public bool IsGlobalPermission()
    {
        string[] splitPermission = Permission.Split(':');

        if (splitPermission.Length > 0 && splitPermission.FirstOrDefault() == "system")
            return true;

        return false;
    }
}
