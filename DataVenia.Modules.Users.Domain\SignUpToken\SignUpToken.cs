﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Lawyers;

namespace DataVenia.Modules.Users.Domain.SignUpToken;

public class SignUpToken : Entity
{
    public Guid Id { get; private set; }
    public Guid LawyerId { get; private set; }
    public Lawyer Lawyer { get; private set; }
    public Guid Token { get; private set; }
    public TokenType Type { get; private set; }
    public DateTime? ExpirationDate { get; private set; }
    public Guid? OfficeId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public Guid DeletedBy { get; set; }
    public static SignUpToken Create(Guid userId, Guid? officeId = null, TokenType tokenType = TokenType.SignUp)
    {
        var signUpToken = new SignUpToken
        {
            LawyerId = userId,
            Token = Guid.CreateVersion7(),
            CreatedAt = DateTime.UtcNow,
            OfficeId = officeId,
            Type = tokenType,
            ExpirationDate = tokenType == TokenType.ResetPassword ? DateTime.UtcNow.AddDays(1) : null,
        };

        return signUpToken;
    }
}
