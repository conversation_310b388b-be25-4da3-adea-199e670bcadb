﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Cases.GetCases;
using DataVenia.Modules.Lawsuits.Domain.Cases;

namespace DataVenia.Modules.Lawsuits.Application.Cases;

public interface ICaseRepository
{
    Task<Case?> GetCaseByIdAsync(Guid caseId, CancellationToken cancellationToken = default);
    Task<Result<IReadOnlyCollection<CaseResponse>>> GetCasesAsync(Guid userId, Guid officeId, Guid? caseId = null, CancellationToken cancellationToken = default);
    void Insert(Case @case);
}
