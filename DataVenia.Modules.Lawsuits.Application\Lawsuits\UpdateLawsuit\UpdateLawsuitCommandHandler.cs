﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.UpdateLawsuit;
public sealed class UpdateLawsuitCommandHandler(
    ILawsuitRepository lawsuitRepository,
    ILawsuitDataRepository lawsuitDataRepository,
    IOfficeLawyerFacade officeLawyerFacade,
    IUnitOfWork unitOfWork,
    ILogger<UpdateLawsuitCommandHandler> logger) : ICommandHandler<UpdateLawsuitCommand>
{
    public async Task<Result> Handle(UpdateLawsuitCommand request, CancellationToken cancellationToken)
    {
        Lawsuit? lawsuit = await lawsuitRepository.GetLawsuitByIdAsync(request.LawsuitId, cancellationToken);
        if (lawsuit == null)
            return Result.Failure(Error.NotFound("Lawsuit.Not.Found", "The specified lawsuit does not exist."));
        
        if (!request.ResponsibleIds.Any())
            return Result.Failure(new Error("No.Responsible", "You must assign at least one responsible for the lawsuit", ErrorType.Validation));

        IReadOnlyCollection<OfficeLawyerDto> responsibles = await officeLawyerFacade.GetActiveByOfficeAndLawyers(request.OfficeId, request.ResponsibleIds);
        if (responsibles.Count != request.ResponsibleIds.Count)
            return Result.Failure(Error.Conflict("Not.Authorized", "There are nonexistent users being attached to the lawsuit"));

        
        var latestLawsuitData = await lawsuitDataRepository.GetLatestOfInstanceByLawsuitIdAsync(request.LawsuitId, request.LegalInstanceId, cancellationToken);
        if (latestLawsuitData.IsFailed)
            return Result.Failure(new Error(
                latestLawsuitData.Errors.FirstOrDefault()?.Message ?? "Internal.Server.Error", "Could not find the lawsuit data",
                ErrorType.NotFound));
                
        var newLawsuitData = LawsuitData.Create(
        request.Title,
        lawsuit.Cnj,
        lawsuit.Id,
        request.FolderId,
        request.LegalInstanceId,
        request.LawsuitStatusId, 
        request.TopicIds,
        request.JudgingOrganId,
        request.CauseValue,
        request.ConvictionValue,
        request.JudgingOrganHref,
        request.Description,
        request.Observations,
        request.Access,
        request.ResponsibleIds,
        request.EvolvedFromCaseId,
        request.GroupingCaseId,
        latestLawsuitData.Value.IsInstanceCreatedByUser
        );

        lawsuitDataRepository.Insert(newLawsuitData.Value);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, nameof(UpdateLawsuitCommandHandler));
            return Result.Failure(new Error("Internal.Server.Error", "Something weird happened.", ErrorType.InternalServerError));
        }

        return Result.Success();
    }
}
