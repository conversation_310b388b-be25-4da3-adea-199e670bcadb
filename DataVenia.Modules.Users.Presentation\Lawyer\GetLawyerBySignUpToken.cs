﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyer.GetLawyerBySignUpToken;
using DataVenia.Modules.Users.Application.Lawyer.GetLawyers;
using DataVenia.Modules.Users.Application.Lawyers.GetLawyer;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Lawyer;

public class GetLawyerBySignUpToken : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("lawyers/sign-up-token/{token}", async (Guid token, ClaimsPrincipal claims, [FromServices] ISender sender) =>
            {
                Result<GetLawyersResponse> result = await sender.Send(new GetLawyerBySignUpTokenQuery(
                    claims.GetUserId(),
                    token));

                return result.Match(Results.Ok, ApiResults.Problem);
            })
            .AllowAnonymous()
            .WithTags(Tags.Users);
    }
}
