﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Harvey.Domain.PartyType;
using PartyTypeDomain = DataVenia.Modules.Harvey.Domain.PartyType.PartyType;

namespace DataVenia.Modules.Harvey.Application.PartyType;
public sealed class GetPartyTypesQueryHandler(
    IPartyTypeRepository partyTypeRepository) : IQueryHandler<GetPartyTypesQuery, IReadOnlyCollection<GetPartyTypesResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetPartyTypesResponse>>> <PERSON>le(GetPartyTypesQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<PartyTypeDomain> partyTypes = await partyTypeRepository.GetAllAsync(request.displayName, cancellationToken);

        var partyTypesResponse = partyTypes.Select(partyType => new GetPartyTypesResponse(
            partyType.Id,
            partyType.DisplayName)).ToList();

        return partyTypesResponse;
    }
}
