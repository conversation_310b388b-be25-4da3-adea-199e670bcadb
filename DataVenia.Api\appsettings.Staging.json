{"Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:80"}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"Database": "Host=***********;Port=5432;Database=datavenia;Username=postgres;Password=************;Include Error Detail=true", "Cache": "datavenia.redis:6379"}, "Authentication": {"Audience": "account", "TokenValidationParameters": {"ValidIssuers": ["https://keycloak-stg-************.us-central1.run.app/realms/datavenia"]}, "MetadataAddress": "https://keycloak-stg-************.us-central1.run.app/realms/datavenia/.well-known/openid-configuration", "RequireHttpsMetadata": false}, "KeyCloak": {"HealthUrl": "https://keycloak-stg-************.us-central1.run.app/health/"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"serverUrl": "http://datavenia.seq:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "DataVenia.Api"}}}