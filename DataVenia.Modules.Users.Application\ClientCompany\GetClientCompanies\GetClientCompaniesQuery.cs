﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Users.Application.Client.GetClients;
using DataVenia.Modules.Users.Domain.ClientCompany;

namespace DataVenia.Modules.Users.Application.ClientCompany.GetClientCompanies;

public sealed record GetClientCompaniesQuery(Guid OfficeId, Guid? ClientId, Guid? CompanyId, ClientCompanyRole? Role) : IQuery<IReadOnlyCollection<GetClientCompaniesResponse>>;
