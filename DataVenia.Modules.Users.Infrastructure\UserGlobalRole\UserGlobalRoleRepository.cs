﻿using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Infrastructure.Database;
using UserGlobalRoleDomain = DataVenia.Modules.Users.Domain.Authorization.UserGlobalRole;

namespace DataVenia.Modules.Users.Infrastructure.UserGlobalRole;
public sealed class UserGlobalRoleRepository(UsersDbContext context) : IUserGlobalRoleRepository
{
    public void Insert(UserGlobalRoleDomain userGlobalRole)
    {
        context.UserGlobalRoles.Add(userGlobalRole);
    }
}
