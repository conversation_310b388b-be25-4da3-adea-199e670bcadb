﻿using DataVenia.Modules.Lawsuits.Application.Folders;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Lawsuits.Application.Cases.GetCases;

[Keyless]
public sealed record CaseResponse(
    Guid Id,
    string Title,
    Guid? FolderId,
    string? FolderName,
    decimal? CauseValue,
    decimal? ConvictionValue,
    DateTime CreatedAt,
    List<Guid> ResponsibleIds,
    string? Description,
    string? Observations,
    string? Access);

public sealed record EnrichedCaseResponse(
    Guid Id,
    string Title,
    Guid? FolderId,
    string? FolderName,
    // List<CasePartyResponse> CaseParties,
    decimal? CauseValue,
    decimal? ConvictionValue,
    List<Guid> ResponsibleIds,
    DateTime CreatedAt,
    string? Description,
    string? Observations,
    string? Access)
{
    public FolderResponse? Folder => FolderId.HasValue && !string.IsNullOrEmpty(FolderName)
        ? new FolderResponse(FolderId.Value, FolderName!)
        : null;
}

public sealed record CasePartyResponse(Guid Id, Guid PartyId, Guid CaseDataId, string PartyType, bool IsClient);

