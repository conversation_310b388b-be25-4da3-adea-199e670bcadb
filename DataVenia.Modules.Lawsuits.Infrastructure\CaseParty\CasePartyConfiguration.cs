﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.CaseParty;

public class CasePartyConfiguration: IEntityTypeConfiguration<Domain.CasesParties.CaseParty>
{
    public void Configure(EntityTypeBuilder<Domain.CasesParties.CaseParty> builder)
    {
        builder.ToTable("case_party");
        
        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.HasKey(lp => lp.Id);

        builder.Property(lp => lp.PartyType)
            .IsRequired();

        builder.Property(lp => lp.IsClient)
            .IsRequired();

        // Relacionamento com Case
        builder.HasOne(lp => lp.CaseData)
            .WithMany(l => l.CaseParties)
            .HasForeignKey(lp => lp.CaseDataId)
            .OnDelete(DeleteBehavior.NoAction);

        builder
            .HasIndex(lp => new
            {
                lp.CaseDataId,
                lp.PartyId
            })
            .IsUnique();
    }
    
}
