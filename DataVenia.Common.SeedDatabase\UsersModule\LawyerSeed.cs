﻿using Bogus;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.SharedModels;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.SeedDatabase.UsersModule;

public sealed class LawyerSeed
{
    private readonly IIdentityProviderService _identityProviderService;
    private readonly UsersDbContext _usersContext;
    private readonly ILogger<LawyerSeed> _logger;

    public LawyerSeed(IIdentityProviderService identityProviderService, UsersDbContext usersContext, ILogger<LawyerSeed> logger)
    {
        _identityProviderService = identityProviderService;
        _usersContext = usersContext;
        _logger = logger;
    }

    public async Task<Guid?> Seed(string userEmail, string userPassword, string firstName, string lastName, string expectedRole)
    {
        Result<string?> result = await _identityProviderService.GetUserIdByEmailAsync(userEmail);

        Guid? userId;

        if (result.IsSuccess && result.Value != null)
            userId = Guid.Parse(result.Value);
        else
        {
            var userModel = new UserModel(userEmail, userPassword, firstName, lastName);
            Result<string> identityRegistrationResult = await _identityProviderService.RegisterUserAsync(userModel);
            if (identityRegistrationResult.IsFailure)
                return null;

            userId = Guid.Parse(identityRegistrationResult.Value);
        }

        Role? role = _usersContext.Roles.FirstOrDefault(x => x.Name == expectedRole);
        UserGlobalRole? globalRole = _usersContext.UserGlobalRoles.FirstOrDefault(x => x.RoleName == expectedRole);
        
        if (role is null && globalRole is null)
            return null;

        Faker<Result<Lawyer>> lawyerFaker = new Faker<Result<Lawyer>>()
            .CustomInstantiator(f => Lawyer.Create(
                email: userEmail,
                firstName: firstName,
                lastName: lastName,
                oabs: Generator.GenerateValidOab(),
                contacts: new List<Contact>(),
                identityId: userId,
                cpf: Generator.GenerateRg(),
                cnh: Generator.GenerateCnh(),
                passport: Generator.GeneratePassport(),
                ctps: Generator.GenerateCtps(),
                pis: Generator.GeneratePis(),
                voterId: Generator.GenerateVoterId()
                ));

        Result<Lawyer> newLawyer = lawyerFaker.Generate();

        try
        {
            _usersContext.Lawyers.Add(newLawyer.Value);
            await _usersContext.SaveChangesAsync();
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "Error seeding lawyer");
            _usersContext.ChangeTracker.Clear();

            return null;
        }

        return newLawyer.Value.Id;
    }

    public async Task<Guid?> GetLawyerIdByEmail(string email)
    {
        return await _usersContext.Lawyers
            .Where(l => l.Email == email)
            .Select(l => (Guid?)l.Id)
            .FirstOrDefaultAsync();
    }
}
