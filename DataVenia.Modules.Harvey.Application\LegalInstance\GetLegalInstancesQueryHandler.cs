﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Harvey.Domain.LegalInstance;
using LegalInstanceDomain = DataVenia.Modules.Harvey.Domain.LegalInstance.LegalInstance;
namespace DataVenia.Modules.Harvey.Application.LegalInstance;
internal sealed class GetLegalInstancesQueryHandler(
    ILegalInstanceRepository legalInstanceRepository) : IQueryHandler<GetLegalInstancesQuery, IReadOnlyCollection<GetLegalInstancesResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetLegalInstancesResponse>>> Handle(GetLegalInstancesQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<LegalInstanceDomain> legalInstances = await legalInstanceRepository.GetAllAsync(request.displayName, cancellationToken);

        var legalInstancesResponse = legalInstances.Select(legalInstance => new GetLegalInstancesResponse(
            legalInstance.Id,
            legalInstance.DisplayName)).ToList();

        return legalInstancesResponse;
    }
}
