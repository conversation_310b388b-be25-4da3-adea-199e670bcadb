using DataVenia.Common.Domain.Lawsuit;
using DataVenia.Modules.LawsuitSync.Domain.Enums;

namespace DataVenia.Modules.LawsuitSync.Domain.Entities;

public sealed class LawsuitMonitoringConfiguration
{
    public long Id { get; set; }
    public required Guid ExternalId { get; set; }
    public Guid? MonitorExternalId { get; set; }
    public required string Cnj { get; set; }
    public List<string> Subscriptions { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset LastUpdate { get; set; }
    public LawsuitSyncSource LawsuitSyncSource { get; set; }
    public required MonitoringType MonitoringType { get; set; }
    public required LawsuitStatus CurrentStatus { get; set; }
    public string? Platform { get; set; }
    public string? CourtDivision { get; set; }
    public string? LegalInstance { get; set; }
    public string? ScheduleCronExpression { get; set; }
}
