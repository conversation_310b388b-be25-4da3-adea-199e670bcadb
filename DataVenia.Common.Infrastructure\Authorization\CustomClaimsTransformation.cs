﻿using System.Security.Claims;
using DataVenia.Common.Application.Exceptions;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Modules.Users.Domain.Users;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DataVenia.Common.Infrastructure.Authorization;

internal sealed class CustomClaimsTransformation : IClaimsTransformation
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<CustomClaimsTransformation> _logger;

    public CustomClaimsTransformation(IServiceScopeFactory serviceScopeFactory, ILogger<CustomClaimsTransformation> logger)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _logger = logger;
    }

    public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
    {
        if (principal.HasClaim(c => c.Type == CustomClaims.Sub))
        {
            return principal;
        }

        using IServiceScope scope = _serviceScopeFactory.CreateScope();

        IUserRepository userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();

        string identityId;
        try
        {
            identityId = principal.GetIdentityId();
        }
        catch (DataVeniaException)
        {
            return principal; 
        }

        var parsedIdentityId = Guid.Parse(identityId);
        User? user = await userRepository.GetAsync(x => x.IdentityId == parsedIdentityId);

        if (user?.Id == null)
        {
            _logger.LogError("user is in keycloak but not in database. IdentityId: {IdentityId}", identityId);
            throw new DataVeniaException("User ID not found for identity id");
        }

        var claimsIdentity = new ClaimsIdentity();

        claimsIdentity.AddClaim(new Claim(CustomClaims.Sub, user.Id.ToString()));

        principal.AddIdentity(claimsIdentity);

        return principal;
    }
}
