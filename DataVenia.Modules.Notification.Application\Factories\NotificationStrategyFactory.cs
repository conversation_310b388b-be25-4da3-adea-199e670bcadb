using System.Net.Mail;
using DataVenia.Modules.Notification.Application.Strategy;
using DataVenia.Modules.Notification.Domain.Enums;
using DataVenia.Modules.Notification.Domain.Infrastructure.Clients;
using DataVenia.Modules.Notification.Domain.Models.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DataVenia.Modules.Notification.Application.Factories;

public sealed class NotificationStrategyFactory(
    IOptions<NotificationSettings> notificationSettings,
    ILogger<NotificationStrategyFactory> logger,
    TelegramApiClient telegramApiClient,
    SmtpClient smtpClient)
{
    private readonly NotificationSettings _notificationSettings = notificationSettings.Value;

    public INotificationStrategy? Create(NotificationEventType eventType)
    {
        return eventType switch
        {
            NotificationEventType.Email => new EmailNotificationStrategy(smtpClient, _notificationSettings.SmtpGroup, _notificationSettings.SmtpGroupName, logger),
            NotificationEventType.Telegram => new TelegramNotificationStrategy(telegramApiClient, _notificationSettings.TelegramBotToken),
            _ => null
        };
    }
}
