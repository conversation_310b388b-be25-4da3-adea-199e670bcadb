﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddStructureChanges : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "party_type",
                type: "character varying(64)",
                maxLength: 64,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(200)",
                oldMaxLength: 200);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "party_type",
                type: "timestamp with time zone",
                nullable: false,
                defaultValueSql: "CURRENT_TIMESTAMP",
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone");

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "harvey",
                table: "party_type",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "legal_instance",
                type: "character varying(512)",
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(200)",
                oldMaxLength: 200);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "legal_instance",
                type: "timestamp with time zone",
                nullable: false,
                defaultValueSql: "CURRENT_TIMESTAMP",
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone");

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "harvey",
                table: "legal_instance",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "legal_category",
                type: "character varying(512)",
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(200)",
                oldMaxLength: 200);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "legal_category",
                type: "timestamp with time zone",
                nullable: false,
                defaultValueSql: "CURRENT_TIMESTAMP",
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone");

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "harvey",
                table: "legal_category",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "lawsuit_type",
                type: "character varying(512)",
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(450)",
                oldMaxLength: 450);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_type",
                type: "timestamp with time zone",
                nullable: false,
                defaultValueSql: "CURRENT_TIMESTAMP",
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone");

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "harvey",
                table: "lawsuit_type",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "lawsuit_status",
                type: "character varying(128)",
                maxLength: 128,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(450)",
                oldMaxLength: 450);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_status",
                type: "timestamp with time zone",
                nullable: false,
                defaultValueSql: "CURRENT_TIMESTAMP",
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone");

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "harvey",
                table: "lawsuit_status",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "forum",
                type: "character varying(512)",
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(450)",
                oldMaxLength: 450);

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "harvey",
                table: "forum",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "deleted_at",
                schema: "harvey",
                table: "court_division",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingAppeal",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingClient",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingDecision",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Cancelled",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Closed",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Completed",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "InProgress",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Pending",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Suspended",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "CollectionAction",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "DivorceAction",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "HabeasCorpus",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "LaborAction",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "Other",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "TaxEnforcementAction",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "WritOfMandamus",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "FirstInstance",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "Other",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "SecondInstance",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "STF",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "STJ",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Defendant",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Lawyer",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Other",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Plaintiff",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Witness",
                columns: new[] { "created_at", "deleted_at" },
                values: new object[] { new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null });

            migrationBuilder.CreateIndex(
                name: "ix_legal_instance_display_name",
                schema: "harvey",
                table: "legal_instance",
                column: "display_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_legal_category_display_name",
                schema: "harvey",
                table: "legal_category",
                column: "display_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_type_display_name",
                schema: "harvey",
                table: "lawsuit_type",
                column: "display_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_status_display_name",
                schema: "harvey",
                table: "lawsuit_status",
                column: "display_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_status_id",
                schema: "harvey",
                table: "lawsuit_status",
                column: "id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_forum_display_name",
                schema: "harvey",
                table: "forum",
                column: "display_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_forum_id",
                schema: "harvey",
                table: "forum",
                column: "id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_legal_instance_display_name",
                schema: "harvey",
                table: "legal_instance");

            migrationBuilder.DropIndex(
                name: "ix_legal_category_display_name",
                schema: "harvey",
                table: "legal_category");

            migrationBuilder.DropIndex(
                name: "ix_lawsuit_type_display_name",
                schema: "harvey",
                table: "lawsuit_type");

            migrationBuilder.DropIndex(
                name: "ix_lawsuit_status_display_name",
                schema: "harvey",
                table: "lawsuit_status");

            migrationBuilder.DropIndex(
                name: "ix_lawsuit_status_id",
                schema: "harvey",
                table: "lawsuit_status");

            migrationBuilder.DropIndex(
                name: "ix_forum_display_name",
                schema: "harvey",
                table: "forum");

            migrationBuilder.DropIndex(
                name: "ix_forum_id",
                schema: "harvey",
                table: "forum");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "harvey",
                table: "party_type");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "harvey",
                table: "legal_instance");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "harvey",
                table: "legal_category");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "harvey",
                table: "lawsuit_type");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "harvey",
                table: "lawsuit_status");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "harvey",
                table: "forum");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                schema: "harvey",
                table: "court_division");

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "party_type",
                type: "character varying(200)",
                maxLength: 200,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(64)",
                oldMaxLength: 64);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "party_type",
                type: "timestamp with time zone",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValueSql: "CURRENT_TIMESTAMP");

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "legal_instance",
                type: "character varying(200)",
                maxLength: 200,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "legal_instance",
                type: "timestamp with time zone",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValueSql: "CURRENT_TIMESTAMP");

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "legal_category",
                type: "character varying(200)",
                maxLength: 200,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "legal_category",
                type: "timestamp with time zone",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValueSql: "CURRENT_TIMESTAMP");

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "lawsuit_type",
                type: "character varying(450)",
                maxLength: 450,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_type",
                type: "timestamp with time zone",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValueSql: "CURRENT_TIMESTAMP");

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "lawsuit_status",
                type: "character varying(450)",
                maxLength: 450,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(128)",
                oldMaxLength: 128);

            migrationBuilder.AlterColumn<DateTime>(
                name: "created_at",
                schema: "harvey",
                table: "lawsuit_status",
                type: "timestamp with time zone",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValueSql: "CURRENT_TIMESTAMP");

            migrationBuilder.AlterColumn<string>(
                name: "display_name",
                schema: "harvey",
                table: "forum",
                type: "character varying(450)",
                maxLength: 450,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldMaxLength: 512);

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingAppeal",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3697));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingClient",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3708));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "AwaitingDecision",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3711));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Cancelled",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3705));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Closed",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3702));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Completed",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3699));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "InProgress",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3683));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Pending",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 441, DateTimeKind.Utc).AddTicks(125));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "lawsuit_status",
                keyColumn: "id",
                keyValue: "Suspended",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 447, DateTimeKind.Utc).AddTicks(3693));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "CollectionAction",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3456));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "DivorceAction",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3741));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "HabeasCorpus",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3746));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "LaborAction",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3742));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "Other",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3746));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "TaxEnforcementAction",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3743));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_category",
                keyColumn: "id",
                keyValue: "WritOfMandamus",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 449, DateTimeKind.Utc).AddTicks(3744));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "FirstInstance",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8413));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "Other",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8586));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "SecondInstance",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8583));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "STF",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8586));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "STJ",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 438, DateTimeKind.Utc).AddTicks(8584));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Defendant",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3663));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Lawyer",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3501));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Other",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3664));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Plaintiff",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3661));

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "party_type",
                keyColumn: "id",
                keyValue: "Witness",
                column: "created_at",
                value: new DateTime(2025, 2, 8, 3, 58, 16, 448, DateTimeKind.Utc).AddTicks(3664));
        }
    }
}
