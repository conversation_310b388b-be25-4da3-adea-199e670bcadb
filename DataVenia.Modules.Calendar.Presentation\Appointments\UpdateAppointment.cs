﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Appointment.CreateAppointment;
using DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointment;
using DataVenia.Modules.Calendar.Domain.Appointments;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Calendar.Presentation.Appointment;
public sealed class UpdateAppointment : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPut("/offices/{officeId}/appointments", async (Guid officeId, [FromBody] UpdateAppointmentRequest request, ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            Result result = await sender.Send(new UpdateAppointmentCommand(
                claims.GetUserId(),
                officeId,
                request.Id,
                request.Type,
                request.Name,
                request.Description,
                request.ResponsibleLawyerId,
                new RecurrenceCommand(
                    request.Recurrence.DaysOfWeek.ToList(),
                    request.Recurrence.DaysOfMonth.ToList(),
                    request.Recurrence.Frequency,
                    request.Recurrence.StartDate,
                    request.Recurrence.EndDate,
                    request.Recurrence.StartTime,
                    request.Recurrence.EndTime),
                request.ParticipantLawyersIds,
                request.Alerts,
                request.StatusId
                )) ;

            return result.Match(Results.NoContent, ApiResults.Problem);
        })
        .RequireAuthorization("office:appointments:update")
        .WithTags(Tags.Calendar);
    }

}
public sealed class UpdateAppointmentRequest
{
    public Guid Id { get; init; }
    public string Type { get; init; }

    public string Name { get; init; }
    public string Description { get; init; }
    public Guid ResponsibleLawyerId { get; init; }
    public Recurrence Recurrence { get; init; }
    public Guid OwnerLawyerId { get; init; }
    public List<Guid> ParticipantLawyersIds { get; init; }
    public List<TimeSpan> Alerts { get; init; }
    public Guid StatusId { get; init; }
}

