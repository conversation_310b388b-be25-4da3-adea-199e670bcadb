﻿using DataVenia.Modules.Users.Application.Client.GetClients;
using DataVenia.Modules.Users.Application.Company.GetCompanies;
using DataVenia.Modules.Users.Domain.ClientCompany;

namespace DataVenia.Modules.Users.Application.ClientCompany.GetClientCompanies;

public sealed record GetClientCompaniesResponse(Guid Id, Guid ClientId, string ClientName, Guid CompanyId, string CompanyName, ClientCompanyRole Role, DateTime CreatedAt);
