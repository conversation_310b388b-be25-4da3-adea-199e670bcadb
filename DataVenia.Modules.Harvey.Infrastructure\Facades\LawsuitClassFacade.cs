using DataVenia.Common.Contracts.Harvey;
using DataVenia.Modules.Harvey.Domain.LawsuitClass;

namespace DataVenia.Modules.Harvey.Infrastructure.Facades;

public sealed class LawsuitClassFacade(ILawsuitClassRepository lawsuitClassRepository) : ILawsuitClassFacade
{
    public async Task<bool> ExistsAsync(int classId, CancellationToken cancellationToken = default)
    {
        var lawsuitClass = await lawsuitClassRepository.GetByIdAsync(classId, cancellationToken);
        return lawsuitClass != null;
    }
}
