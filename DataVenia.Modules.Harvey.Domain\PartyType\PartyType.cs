﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Harvey.Domain.PartyType;
public sealed class PartyType : Entity
{
    public static readonly PartyType Lawyer = Create("Lawyer", "Advogado", 1);
    public static readonly PartyType Plaintiff = Create("Plaintiff", "Autor", 2);
    public static readonly PartyType Defendant = Create("Defendant", "Réu", 3);
    public static readonly PartyType Witness = Create("Witness", "Testemunha", 4);
    public static readonly PartyType Other = Create("Other", "Outro", 5);
    public string Id { get; private set; }
    public string DisplayName { get; private set; }
    public int Order { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private static PartyType Create(string id, string displayName, int order)
    {
        return new PartyType { Id = id, DisplayName = displayName, Order = order };
    }
}
