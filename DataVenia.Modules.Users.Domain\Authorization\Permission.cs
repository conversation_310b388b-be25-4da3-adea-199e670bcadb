﻿namespace DataVenia.Modules.Users.Domain.Authorization;

public sealed class Permission
{
    public static readonly Permission GetUser = new("system:users:read");
    public static readonly Permission ModifyUser = new("system:users:update");
    public static readonly Permission CreateUser = new("office:users:write");
    
    public static readonly Permission GetUsers = new("office:users:read");

    public static readonly Permission GetStatus = new("office:status:read");

    public static readonly Permission CreateClient = new("office:clients:create");
    public static readonly Permission GetClients = new("office:clients:read");
    public static readonly Permission UpdateClient = new("office:clients:update");
    public static readonly Permission DeleteClient = new("office:clients:delete");

    public static readonly Permission CreateLawsuits = new("office:lawsuits:create");
    public static readonly Permission GetLawsuits = new("office:lawsuits:read");
    public static readonly Permission ModifyLawsuits = new("office:lawsuits:update");

    public static readonly Permission CreateAppointments = new("office:appointments:write");
    public static readonly Permission GetAppointments = new("office:appointments:read");
    public static readonly Permission UpdateAppointments = new("office:appointments:update");

    public static readonly Permission CreateInvites = new("office:invites:create");
    public static readonly Permission ReadInvites = new("system:invites:read");
    public static readonly Permission UpdateInvites = new("system:invites:update");

    public static readonly Permission General = new("system:general:all");

    public static readonly Permission GetHarvey = new("system:harvey:read");

    public static readonly Permission SystemAdministrator = new("system:administrator:all");
    public Permission(string code)
    {
        Code = code;
    }

    public string Code { get; }
}
