﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.LawsuitSync.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class MonitorExternalId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "monitor_external_id",
                schema: "lawsuit-sync",
                table: "tb_lawsuit_monitoring_configuration",
                type: "uuid",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "monitor_external_id",
                schema: "lawsuit-sync",
                table: "tb_lawsuit_monitoring_configuration");
        }
    }
}
