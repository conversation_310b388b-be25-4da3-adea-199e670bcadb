﻿using DataVenia.Modules.Harvey.Domain.Forum;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using ForumDomain = DataVenia.Modules.Harvey.Domain.Forum.Forum;

namespace DataVenia.Modules.Harvey.Infrastructure.Forum;
public sealed class ForumRepository(HarveyDbContext context) : IForumRepository
{
    public async Task<IReadOnlyCollection<ForumDomain>> GetAllAsync(string? displayName, CancellationToken cancellationToken = default)
    {
        IQueryable<ForumDomain> query = context.Forums;

        if (!string.IsNullOrWhiteSpace(displayName))
            query = query.Where(f => f.DisplayName.Contains(displayName));

        return await query.ToListAsync(cancellationToken);
    }
}
