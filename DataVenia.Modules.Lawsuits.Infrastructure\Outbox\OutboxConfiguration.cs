﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OutboxDomain = DataVenia.Modules.Lawsuits.Domain.Outbox.Outbox;
namespace DataVenia.Modules.Lawsuits.Infrastructure.Outbox;

public class OutboxConfiguration : IEntityTypeConfiguration<OutboxDomain>
{
    public void Configure(EntityTypeBuilder<OutboxDomain> builder)
    {
        builder.ToTable("outbox");
        builder.HasKey(m => m.Id);
        builder.Property(m => m.Id)
            .HasColumnType("uuid")
            .IsRequired();

        builder.Property(m => m.MessageType)
            .IsRequired();

        builder.Property(m => m.Payload)
            .IsRequired();

        builder.Property(m => m.CreatedAt)
            .IsRequired();

        builder.Property(m => m.Processed)
            .IsRequired();

        builder.Property(m => m.ProcessedAt);
            
        // Optional: Index to quickly query unprocessed messages
        builder.HasIndex(m => m.Processed);
    }
}
