﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddCaseIdsToLawsuits : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "evolved_from_case_id",
                schema: "lawsuit",
                table: "lawsuit_response",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "grouping_case_id",
                schema: "lawsuit",
                table: "lawsuit_response",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "observations",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500);

            migrationBuilder.AlterColumn<string>(
                name: "description",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500);

            migrationBuilder.AlterColumn<string>(
                name: "access",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<Guid>(
                name: "evolved_from_case_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "grouping_case_id",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "case_response",
                schema: "lawsuit",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    title = table.Column<string>(type: "text", nullable: false),
                    folder_id = table.Column<Guid>(type: "uuid", nullable: true),
                    folder_name = table.Column<string>(type: "text", nullable: true),
                    case_type_id = table.Column<string>(type: "text", nullable: false),
                    legal_category_id = table.Column<string>(type: "text", nullable: false),
                    cause_value = table.Column<decimal>(type: "numeric", nullable: true),
                    conviction_value = table.Column<decimal>(type: "numeric", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    responsibles = table.Column<List<Guid>>(type: "uuid[]", nullable: false)
                },
                constraints: table =>
                {
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "case_response",
                schema: "lawsuit");

            migrationBuilder.DropColumn(
                name: "evolved_from_case_id",
                schema: "lawsuit",
                table: "lawsuit_response");

            migrationBuilder.DropColumn(
                name: "grouping_case_id",
                schema: "lawsuit",
                table: "lawsuit_response");

            migrationBuilder.DropColumn(
                name: "evolved_from_case_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.DropColumn(
                name: "grouping_case_id",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.AlterColumn<string>(
                name: "observations",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "description",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "access",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
