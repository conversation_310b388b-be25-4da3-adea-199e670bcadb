﻿using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Office.AcceptInviteToOffice;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using System.Security.Claims;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Users.Presentation.Office;
internal sealed class AcceptInviteToOffice : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPatch("offices/{officeId}/invites/accept", async (Guid officeId, ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            Result result = await sender.Send(new AcceptInviteToOfficeCommand(
                officeId,
                claims.GetUserId()));

            return result.Match(Results.NoContent, ApiResults.Problem);
        })
        .RequireAuthorization("system:invites:update")
        .WithTags(Tags.OfficeInvite);
    }

}
