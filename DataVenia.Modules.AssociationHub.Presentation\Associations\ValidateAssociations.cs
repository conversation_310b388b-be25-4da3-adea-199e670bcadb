using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.AssociationHub.Application.Associations.ValidateAssociations;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.AssociationHub.Presentation.Associations;

internal sealed class ValidateAssociations : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/associations/validate", async ([FromBody] ValidateAssociationsRequest request, [FromServices] ISender sender) =>
        {
            var result = await sender.Send(new ValidateAssociationsQuery(
                request.EntityType,
                request.TargetEntityTypes));

            return result.Match(Results.Ok, ApiResults.Problem);
        })
        .RequireAuthorization("system:associations:read")
        .WithTags(Tags.Associations)
        .WithSummary("Validate associations for an entity type")
        .WithDescription("Validates whether the specified entity type can associate with the target entity types");
    }
}

public sealed record ValidateAssociationsRequest
{
    public string EntityType { get; init; } = string.Empty;
    public IReadOnlyCollection<string> TargetEntityTypes { get; init; } = [];
}
