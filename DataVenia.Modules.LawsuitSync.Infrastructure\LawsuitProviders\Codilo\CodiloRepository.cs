using DataVenia.Modules.LawsuitSync.Domain.Entities;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Requests;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Responses;
using FluentResults;
using Microsoft.Extensions.Logging;
using Refit;

namespace DataVenia.Modules.LawsuitSync.Infrastructure.LawsuitProviders.Codilo;

public sealed class CodiloRepository(
    IMonitoringLawsuitsClient monitoringLawsuitsClient,
    ICodiloClient codiloClient,
    ILogger<CodiloRepository> logger) : ICodiloRepository
{
    public async Task<Result<StartMonitoringResponse>> StartLawsuitMonitoring(StartMonitoringRequest request)
    {
        try
        {
            var result = await monitoringLawsuitsClient.StartLawsuitMonitoring(request).ConfigureAwait(false);

            return result.Success ? result : Result.Fail("Failed to start Lawsuit Monitoring");
        }
        catch (ApiException ex)
        {
            logger.LogError(ex, "Error starting Lawsuit Monitoring. {@Lawsuit}", request);

            return Result.Fail<StartMonitoringResponse>(ex.Message);
        }
    }

    public async Task<Result> CancelLawsuitMonitoring(string lawsuitId)
    {
        try
        {
            var stopMonitoringResponse = await monitoringLawsuitsClient.CancelLawsuitMonitoring(lawsuitId).ConfigureAwait(false);

            return stopMonitoringResponse?.Success == true ? Result.Ok() : Result.Fail("Lawsuit Monitoring could not be cancelled.");
        }
        catch (ApiException ex)
        {
            logger.LogError(ex, "Error cancelling Lawsuit monitoring. {LawsuitId}", lawsuitId);

            return Result.Fail(ex.Message);
        }
    }

    public async Task<Result<AutomaticSearchResponse>> StartLawsuitSearch(AutomaticSearchRequest request)
    {
        try
        {
            return await codiloClient.StartLawsuitSearch(request).ConfigureAwait(false);
        }
        catch (ApiException ex)
        {
            logger.LogError(ex, "Error starting LawsuitSearch. {@Lawsuit}", request);

            return Result.Fail<AutomaticSearchResponse>(ex.Message);
        }
    }
}
