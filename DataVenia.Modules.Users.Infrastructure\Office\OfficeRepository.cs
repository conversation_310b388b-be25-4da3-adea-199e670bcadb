﻿using System.Linq.Expressions;
using DataVenia.Modules.Users.Domain.Office;
using DataVenia.Modules.Users.Infrastructure.Database;
using OfficeDomain = DataVenia.Modules.Users.Domain.Office.Office;

namespace DataVenia.Modules.Users.Infrastructure.Office;
public sealed class OfficeRepository(UsersDbContext context) : IOfficeRepository
{
    public async Task<OfficeDomain?> GetAsync(Expression<Func<OfficeDomain, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.GetSingleByFilterAsync(filter, cancellationToken);
    }

    public void Insert(OfficeDomain office)
    {
        context.Offices.Add(office);
    }
}
