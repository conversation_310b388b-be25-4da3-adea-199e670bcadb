﻿using System.Text.Json;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.Events.AssociationHub;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Domain.Outbox;
using Microsoft.Extensions.Logging;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
namespace DataVenia.Modules.Users.Application.Client.CreateClient;
internal sealed class CreateClientCommandHandler(
    IClientRepository clientRepository,
    IOutboxRepository outboxRepository,
    IUnitOfWork unitOfWork,
    ILogger<CreateClientCommandHandler> logger)
    : ICommandHandler<CreateClientCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateClientCommand request, CancellationToken cancellationToken)
    {
        Result<ClientDomain> clientResult = ClientDomain.Create(
            request.Cpf,
            request.Name,
            request.OfficeId,
            request.LawyerId,
            request.Email
        );

        if (clientResult.IsFailure)
        {
            logger.LogError("Error creating client. {@Error}", clientResult.Error);
            return Result.Failure<Guid>(clientResult.Error);
        }

        clientRepository.Insert(clientResult.Value);

        var associations = new CreateAssociationEvent(
            "Client",
            clientResult.Value.Id,
            new List<Associations>
            {
                new Associations("Company", request.Companies)
            });
        
        outboxRepository.Insert(new Outbox
        {
            MessageType = nameof(CreateAssociationEvent),
            Payload = JsonSerializer.Serialize(associations)
        });
        
        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving client to database.");
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return clientResult.Value.Id;
    }
}
