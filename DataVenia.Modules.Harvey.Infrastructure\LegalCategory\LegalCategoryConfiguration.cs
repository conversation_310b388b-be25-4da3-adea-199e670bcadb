﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using LegalCategoryDomain = DataVenia.Modules.Harvey.Domain.LegalCategory.LegalCategory;
namespace DataVenia.Modules.Harvey.Infrastructure.LegalCategory;

internal sealed class LegalCategoryConfiguration : IEntityTypeConfiguration<LegalCategoryDomain>
{
    public void Configure(EntityTypeBuilder<LegalCategoryDomain> builder)
    {
        // Table Mapping
        builder.ToTable("legal_category");

        // Primary Key
        builder.HasKey(li => li.Id);

        builder.Property(li => li.DisplayName)
            .IsRequired()
            .HasMaxLength(512);
        builder.HasIndex(x => x.DisplayName).IsUnique();

        builder.Property(li => li.Order)
            .IsRequired();
        
        builder.Property(f => f.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.HasData(
            LegalCategoryDomain.CollectionAction,
            LegalCategoryDomain.DivorceAction,
            LegalCategoryDomain.HabeasCorpus,
            LegalCategoryDomain.LaborAction,
            LegalCategoryDomain.TaxEnforcementAction,
            LegalCategoryDomain.WritOfMandamus,
            LegalCategoryDomain.Other
        );
    }
}
