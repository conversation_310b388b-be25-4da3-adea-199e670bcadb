using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;

namespace DataVenia.Modules.AssociationHub.Infrastructure.Configuration;

/// <summary>
/// Advanced configuration setup showing different association modes and complex rules
/// </summary>
public static class AdvancedEntityConfigurationSetup
{
    public static EntityConfigurationRegistry CreateAdvancedRegistry()
    {
        var registry = new EntityConfigurationRegistry();

        // Users Module Entities with strict whitelist
        registry.Register(new EntityConfiguration
        {
            EntityType = "Client",
            GetEndpointTemplate = "/offices/{1}/clients/{0}",
            PatchEndpointTemplate = "/offices/{1}/clients/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Company", "Lawyer", "Lawsuit", "Case", "Appointment"]
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Company",
            GetEndpointTemplate = "/offices/{1}/companies/{0}",
            PatchEndpointTemplate = "/offices/{1}/companies/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Blacklist, // Allow all except specific ones
            AllowedAssociations = ["Office"] // Companies cannot be associated with Offices directly
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Lawyer",
            GetEndpointTemplate = "/lawyers/{0}",
            PatchEndpointTemplate = "/lawyers/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.AllowAll, // Lawyers can associate with anything
            AllowedAssociations = [] // Not used in AllowAll mode
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Office",
            GetEndpointTemplate = "/offices/{0}",
            PatchEndpointTemplate = "/offices/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Lawyer"] // Offices can only be associated with lawyers
        });

        // Lawsuits Module Entities
        registry.Register(new EntityConfiguration
        {
            EntityType = "Lawsuit",
            GetEndpointTemplate = "/lawsuits/{0}",
            PatchEndpointTemplate = "/lawsuits/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Company", "Lawyer", "Case", "Appointment"]
        });

        registry.Register(new EntityConfiguration
        {
            EntityType = "Case",
            GetEndpointTemplate = "/cases/{0}",
            PatchEndpointTemplate = "/cases/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Company", "Lawyer", "Lawsuit", "Appointment"]
        });

        // Calendar Module Entities
        registry.Register(new EntityConfiguration
        {
            EntityType = "Appointment",
            GetEndpointTemplate = "/appointments/{0}",
            PatchEndpointTemplate = "/appointments/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Blacklist, // Allow all except specific ones
            AllowedAssociations = ["Office"] // Appointments cannot be directly associated with Offices
        });

        // Example of a disabled entity
        registry.Register(new EntityConfiguration
        {
            EntityType = "ArchivedDocument",
            GetEndpointTemplate = "/documents/{0}",
            PatchEndpointTemplate = "/documents/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = false, // Disabled - no associations allowed
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = []
        });

        return registry;
    }

    /// <summary>
    /// Creates a registry with role-based association rules
    /// </summary>
    public static EntityConfigurationRegistry CreateRoleBasedRegistry()
    {
        var registry = new EntityConfigurationRegistry();

        // Senior entities can associate with more types
        registry.Register(new EntityConfiguration
        {
            EntityType = "SeniorLawyer",
            GetEndpointTemplate = "/lawyers/{0}",
            PatchEndpointTemplate = "/lawyers/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.AllowAll,
            AllowedAssociations = []
        });

        // Junior entities have restricted associations
        registry.Register(new EntityConfiguration
        {
            EntityType = "JuniorLawyer",
            GetEndpointTemplate = "/lawyers/{0}",
            PatchEndpointTemplate = "/lawyers/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Whitelist,
            AllowedAssociations = ["Client", "Case"] // Limited scope
        });

        // Administrative entities
        registry.Register(new EntityConfiguration
        {
            EntityType = "Administrator",
            GetEndpointTemplate = "/admins/{0}",
            PatchEndpointTemplate = "/admins/{0}",
            AssociationPropertyName = "associations",
            IsEnabled = true,
            AssociationMode = AssociationMode.Blacklist,
            AllowedAssociations = ["Client", "Lawsuit"] // Cannot associate with sensitive entities
        });

        return registry;
    }
}
