using DataVenia.Modules.Lawsuits.Domain.DataDivergence;

namespace DataVenia.Modules.Lawsuits.Application.DataDivergences.GetDataDivergences;

public sealed record DataDivergenceResponse(
    Guid Id,
    Guid LawsuitId,
    Guid OfficeId,
    string InstanceId,
    IReadOnlyCollection<DivergentFieldResponse> Fields,
    DateTime CreatedAt,
    DateTime? UpdatedAt,
    DateTime? AnalyzedAt,
    Guid? AnalyzedBy,
    DataDivergenceStatus Status,
    string? Reason)
{
    public static DataDivergenceResponse FromDomain(DataDivergence dataDivergence)
    {
        var fields = dataDivergence.Fields
            .Select(field => new DivergentFieldResponse(
                ToCamelCase(field.Key),
                field.Value.CurrentValue,
                field.Value.SuggestedValue))
            .ToList();

        return new DataDivergenceResponse(
            dataDivergence.Id,
            dataDivergence.LawsuitId,
            dataDivergence.OfficeId,
            dataDivergence.InstanceId,
            fields,
            dataDivergence.CreatedAt,
            dataDivergence.UpdatedAt,
            dataDivergence.AnalyzedAt,
            dataDivergence.AnalyzedBy,
            dataDivergence.Status,
            dataDivergence.Reason);
    }

    private static string ToCamelCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return char.ToLowerInvariant(input[0]) + input[1..];
    }
}

public sealed record DivergentFieldResponse(
    string Field,
    string CurrentValue,
    string SuggestedValue);
