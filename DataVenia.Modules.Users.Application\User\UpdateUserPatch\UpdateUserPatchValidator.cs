﻿using DataVenia.Modules.Users.Application.User.AcceptTermsAndConditions;
using FluentValidation;

namespace DataVenia.Modules.Users.Application.User.UpdateUserPatch;

internal sealed class UpdateUserPatchValidator : AbstractValidator<UpdateUserPatchCommand>
{
    public UpdateUserPatchValidator()
    {
        RuleFor(c => c)
            .Must(c => c.JwtUserId == c.RequestUserId)
            .WithMessage("User is not authenticated.");
    }
}
