﻿using DataVenia.Common.Application.Authorization;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.User.GetUserGlobalPermissions;
using DataVenia.Modules.Users.Application.Users.GetUserPermissions;
using MediatR;

namespace DataVenia.Modules.Users.Infrastructure.Authorization;

internal sealed class PermissionService(ISender sender) : IPermissionService
{
    public async Task<Result<PermissionsResponse>> GetUserPermissionsAsync(Guid userId, Guid officeId)
    {
        return await sender.Send(new GetUserPermissionsQuery(userId, officeId));
    }

    public async Task<Result<PermissionsResponse>> GetUserGlobalPermissionsAsync(Guid userId)
    {
        return await sender.Send(new GetUserGlobalPermissionsQuery(userId));
    }
}
