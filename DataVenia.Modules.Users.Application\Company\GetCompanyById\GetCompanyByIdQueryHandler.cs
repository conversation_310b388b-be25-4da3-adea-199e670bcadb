﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Company.GetCompanies;
using DataVenia.Modules.Users.Domain.Company;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Company.GetCompanyById;

public sealed class GetCompanyByIdQueryHandler(
    ICompanyRepository companyRepository,
    ILogger<GetCompanyByIdQueryHandler> logger)
    : IQueryHandler<GetCompanyByIdQuery, GetCompaniesResponse?>
{
    public async Task<Result<GetCompaniesResponse?>> Handle(GetCompanyByIdQuery request,
        CancellationToken cancellationToken)
    {
        Domain.Company.Company? company = null;

        try
        {
            // pega o appointment pelo Id, validando escritório e se o lawyer que requisitou é vinculado ao appointment
            company = await companyRepository.GetSingleAsync(x => x.Id == request.CompanyId, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "an error occurred while retrieving the company with id {CompanyId}.",
                request.CompanyId);
        }

        if (company == null)
            return Result.Failure<GetCompaniesResponse?>(new Error("Not.Found", "The company was not found.",
                ErrorType.NotFound));

        return new GetCompaniesResponse(
            company.Id,
            company.Name,
            company.Cnpj,
            Clients: company.ClientCompanies.Select(cc => cc.ClientId).ToList());
    }
}
