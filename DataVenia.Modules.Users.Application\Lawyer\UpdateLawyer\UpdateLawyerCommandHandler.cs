﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Lawyers;
using Microsoft.Extensions.Logging;
using LawyerDomain = DataVenia.Modules.Users.Domain.Lawyers.Lawyer;

namespace DataVenia.Modules.Users.Application.Lawyer.UpdateLawyer;

internal sealed class UpdateLawyerCommandHandler(ILawyerRepository lawyerRepository, IUnitOfWork unitOfWork, ILogger<UpdateLawyerCommandHandler> logger)
    : ICommandHandler<UpdateLawyerCommand>
{
    public async Task<Result> Handle(UpdateLawyerCommand request, CancellationToken cancellationToken)
    {
        LawyerDomain? user = await lawyerRepository.GetSingleByFilterAsync(x => x.Id == request.UserId, cancellationToken);

        if (user is null)
        {
            return Result.Failure(LawyerErrors.NotFound(request.UserId));
        }

        Result update = user.Update(request.FirstName, request.LastName);

        if(update.IsFailure)
            return Result.Failure(update.Error);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        } catch(Exception ex)
        {
            logger.LogError(ex, "Error updating lawyer: {@UserId}", request.UserId);
            return Result.Failure<LawyerDomain>(Error.InternalServerError());
        }

        return Result.Success();
    }
}
