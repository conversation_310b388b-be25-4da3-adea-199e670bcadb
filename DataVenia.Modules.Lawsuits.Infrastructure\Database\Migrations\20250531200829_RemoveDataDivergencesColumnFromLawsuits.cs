﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemoveDataDivergencesColumnFromLawsuits : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "data_divergences",
                schema: "lawsuit",
                table: "lawsuit");

            migrationBuilder.CreateIndex(
                name: "ix_data_divergence_lawsuit_id",
                schema: "lawsuit",
                table: "data_divergence",
                column: "lawsuit_id");

            migrationBuilder.AddForeignKey(
                name: "fk_data_divergence_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "data_divergence",
                column: "lawsuit_id",
                principalSchema: "lawsuit",
                principalTable: "lawsuit",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_data_divergence_lawsuit_lawsuit_id",
                schema: "lawsuit",
                table: "data_divergence");

            migrationBuilder.DropIndex(
                name: "ix_data_divergence_lawsuit_id",
                schema: "lawsuit",
                table: "data_divergence");

            migrationBuilder.AddColumn<string>(
                name: "data_divergences",
                schema: "lawsuit",
                table: "lawsuit",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");
        }
    }
}
