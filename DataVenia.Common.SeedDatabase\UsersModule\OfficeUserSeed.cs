﻿using Bogus;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Office;
using DataVenia.Modules.Users.Infrastructure.Database;

namespace DataVenia.Common.SeedDatabase.UsersModule;
public sealed class OfficeUserSeed
{
    private readonly UsersDbContext _usersContext;

    public OfficeUserSeed(UsersDbContext usersContext)
    {
        _usersContext = usersContext;
    }

    public async Task<OfficeUser> Seed(Guid officeId, Guid userId, Guid ownerId, string role)
    {
        Faker<OfficeUser> officeUserFaker = new Faker<OfficeUser>()
            .CustomInstantiator(f => OfficeUser.Create(officeId, userId, ownerId, role));

        OfficeUser newOfficeUser = officeUserFaker.Generate();

        _usersContext.OfficeUsers.Add(newOfficeUser);
        await _usersContext.SaveChangesAsync();

        return newOfficeUser;
    }
    
    public async Task AcceptInvitation(OfficeUser officeUser)
    {
        officeUser.Accept();
        await _usersContext.SaveChangesAsync();
    }
}
