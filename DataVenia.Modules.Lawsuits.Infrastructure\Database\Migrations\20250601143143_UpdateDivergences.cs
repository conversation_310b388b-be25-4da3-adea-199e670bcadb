﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class UpdateDivergences : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "was_accepted",
                schema: "lawsuit",
                table: "data_divergence");

            migrationBuilder.DropColumn(
                name: "was_overwritten",
                schema: "lawsuit",
                table: "data_divergence");

            migrationBuilder.AddColumn<int>(
                name: "status",
                schema: "lawsuit",
                table: "data_divergence",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "status",
                schema: "lawsuit",
                table: "data_divergence");

            migrationBuilder.AddColumn<bool>(
                name: "was_accepted",
                schema: "lawsuit",
                table: "data_divergence",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "was_overwritten",
                schema: "lawsuit",
                table: "data_divergence",
                type: "boolean",
                nullable: true);
        }
    }
}
