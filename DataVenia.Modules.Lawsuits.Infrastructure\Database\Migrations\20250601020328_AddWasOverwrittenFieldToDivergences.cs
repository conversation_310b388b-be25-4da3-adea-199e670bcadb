﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddWasOverwrittenFieldToDivergences : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "reason",
                schema: "lawsuit",
                table: "data_divergence",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "was_overwritten",
                schema: "lawsuit",
                table: "data_divergence",
                type: "boolean",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "reason",
                schema: "lawsuit",
                table: "data_divergence");

            migrationBuilder.DropColumn(
                name: "was_overwritten",
                schema: "lawsuit",
                table: "data_divergence");
        }
    }
}
