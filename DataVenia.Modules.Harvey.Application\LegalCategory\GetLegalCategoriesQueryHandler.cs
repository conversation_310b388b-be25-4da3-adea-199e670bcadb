﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Harvey.Application.Forum;
using DataVenia.Modules.Harvey.Domain.Forum;
using DataVenia.Modules.Harvey.Domain.LegalCategory;
using LegalCategoryDomain = DataVenia.Modules.Harvey.Domain.LegalCategory.LegalCategory;
namespace DataVenia.Modules.Harvey.Application.LegalCategory;

internal sealed class GetLegalCategoriesQueryHandler(
    ILegalCategoryRepository legalCategoryRepository) : IQueryHandler<GetLegalCategoriesQuery, IReadOnlyCollection<GetLegalCategoriesResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetLegalCategoriesResponse>>> Handle(GetLegalCategoriesQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<LegalCategoryDomain> legalCategories = await legalCategoryRepository.GetAllAsync(request.displayName, cancellationToken);

        var legalCategoriesResponse = legalCategories.Select(legalCategory => new GetLegalCategoriesResponse(
            legalCategory.Id,
            legalCategory.DisplayName)).ToList();

        return legalCategoriesResponse;
    }
}
