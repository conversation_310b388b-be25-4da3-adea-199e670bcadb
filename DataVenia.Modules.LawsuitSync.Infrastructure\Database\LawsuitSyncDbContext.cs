﻿using DataVenia.Modules.LawsuitSync.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.LawsuitSync.Infrastructure.Database;

public class LawsuitSyncDbContext(DbContextOptions<LawsuitSyncDbContext> options) : DbContext(options)
{
    public DbSet<LawsuitMonitoringConfiguration> LawsuitMonitoringConfiguration { get; set; }
    public DbSet<LawsuitEventsHistory> LawsuitEventsHistory { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder ?? throw new ArgumentNullException(nameof(modelBuilder)));

        modelBuilder.HasDefaultSchema(Schemas.Lawsuit);

        modelBuilder.Entity<LawsuitMonitoringConfiguration>(b =>
        {
            b.ToTable("tb_lawsuit_monitoring_configuration");
            b.<PERSON>(x => x.Id);

            b.Property(x => x.Id).HasColumnName("id").ValueGeneratedOnAdd();
            b.Property(x => x.ExternalId).HasColumnName("external_id");
            b.Property(x => x.MonitorExternalId).HasColumnName("monitor_external_id");
            b.Property(x => x.Cnj).HasColumnName("cnj");
            b.Property(x => x.CreatedAt).HasColumnName("created_at").HasDefaultValueSql("now()");
            b.Property(x => x.LastUpdate).HasColumnName("last_update");
            b.Property(x => x.LawsuitSyncSource).HasColumnName("lawsuit_sync_source").HasConversion<string>();
            b.Property(x => x.MonitoringType).HasColumnName("monitoring_type").HasConversion<string>();
            b.Property(x => x.CurrentStatus).HasColumnName("current_status").HasConversion<string>();
            b.Property(x => x.Platform).HasColumnName("platform");
            b.Property(x => x.CourtDivision).HasColumnName("court_division");
            b.Property(x => x.LegalInstance).HasColumnName("legal_instance");
            b.Property(x => x.ScheduleCronExpression).HasColumnName("schedule_cron_expression");
            b.Property(x => x.Subscriptions).HasColumnName("subscriptions");

            b.HasIndex(x => x.ExternalId).IsUnique();
            b.HasIndex(x => x.ExternalId);
            b.HasIndex(x => x.Cnj);
        });

        modelBuilder.Entity<LawsuitEventsHistory>(b =>
        {
            b.ToTable("tb_lawsuit_events_history");
            b.HasKey(x => x.Id);
            b.Property(x => x.LawsuitMonitoringConfigurationExternalId).HasColumnName("lawsuit_monitoring_configuration_external_id");
            b.Property(x => x.Cnj).HasColumnName("cnj");
            b.Property(x => x.MonitoringType).HasColumnName("monitoring_type").HasConversion<string>();
            b.Property(x => x.LawsuitStatus).HasColumnName("lawsuit_status").HasConversion<string>();
            b.Property(x => x.EventDate).HasColumnName("event_date");
            b.Property(x => x.Event).HasColumnName("event");
        });
    }
}
