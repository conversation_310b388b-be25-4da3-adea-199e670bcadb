﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.CasesParties;
using DataVenia.Modules.Lawsuits.Domain.CasesResponsibles;
using DataVenia.Modules.Lawsuits.Domain.DTOs;

namespace DataVenia.Modules.Lawsuits.Domain.CasesData;

public class CaseData: Entity
{
    private readonly List<CaseParty> _caseParties = [];
    private readonly List<CaseResponsible> _responsibles = [];
    private CaseData() { }

    public IReadOnlyCollection<CaseParty> CaseParties => _caseParties.AsReadOnly();
    public IReadOnlyCollection<CaseResponsible> Responsibles => _responsibles.AsReadOnly(); 
    public string Title { get; private set; }
    public Guid Id { get; private set; }
    public Guid CaseId { get; private set; }
    public Case Case { get; private set; }

    public Guid? FolderId { get; private set; }
    public Folder.Folder Folder { get; private set; }
    public decimal CauseValue { get; private set; }
    public decimal? ConvictionValue { get; private set; }
    public string Description { get; private set; }
    public string Observations { get; private set; }
    public string Access { get; private set; }
    public DateTime CreatedAt { get; private set; }

    public static CaseData Create(
            string title,
            Guid caseId,
            Guid? folderId,
            decimal causeValue,
            decimal? convictionValue,
            string description,
            string observations,
            string access,
            // List<PartyDto> parties,
            List<Guid> responsiblesIds)
    {
        var newCaseData =  new CaseData
        {
            Title = title,
            Id = Guid.NewGuid(),
            CaseId = caseId,
            FolderId = folderId,
            CauseValue = causeValue,
            ConvictionValue = convictionValue,
            Description = description,
            Observations = observations,
            Access = access,
            CreatedAt = DateTime.UtcNow,
        };
        
        // descomentar quando implementar parties no caso
        // var caseParties = parties.Select(x => CaseParty.Create(newCaseData.Id, x.UserId, x.PartyType, x.IsClient)).ToList();
        // newCaseData._caseParties.AddRange(caseParties);

        var caseResponsibles = responsiblesIds.Select(x => CaseResponsible.Create(caseId, x)).ToList();

        newCaseData._responsibles.AddRange(caseResponsibles);
        
        return newCaseData;
    }
}
