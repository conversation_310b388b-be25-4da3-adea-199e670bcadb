﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Domain.Users;
using DataVenia.Modules.Users.IntegrationEvents;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Presentation.Consumers;

public class LawyerSignedUpEventConsumer : IConsumer<LawyerSignedUpEvent>
{
    private readonly IIdentityProviderService _identityProviderService;
    private readonly ILogger<LawyerSignedUpEventConsumer> _logger;
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;

    public LawyerSignedUpEventConsumer(
        IIdentityProviderService identityProviderService,
        IUserRepository userRepository,
        IUnitOfWork unitOfWork, 
        ILogger<LawyerSignedUpEventConsumer> logger)
    {
        _identityProviderService = identityProviderService;
        _logger = logger;
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
    }
    
    public async Task Consume(ConsumeContext<LawyerSignedUpEvent> context)
    {
        context = context ?? throw new ArgumentNullException(nameof(context));
        
        LawyerSignedUpEvent eventMessage = context.Message;
        
        try
        {
            // Perform the Keycloak registration here.
            Result<string> result = await _identityProviderService.RegisterUserAsync(
                new UserModel(eventMessage.Email, eventMessage.Password, eventMessage.FirstName, eventMessage.LastName),
                context.CancellationToken);

            if(result.IsFailure)
            {
                _logger.LogError("Error registering user in Keycloak: {@Error}", result.Error);
                // Depending on your retry policy, you could throw to trigger a retry.
                throw new Exception("Keycloak registration failed");
            }

            Domain.Users.User? lawyer = await _userRepository.GetAsync(x => x.Email == eventMessage.Email, context.CancellationToken);
            
            if(lawyer is null)
                throw new Exception("Lawyer not found");
            
            lawyer.UpdatePartial(identityId: Guid.Parse(result.Value));
            
            await _unitOfWork.SaveChangesAsync(context.CancellationToken);
        }
        catch(Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while registering lawyer in Keycloak.");
            
            // Throwing an exception will let MassTransit handle the retry as per configuration.
            throw new Exception("Keycloak registration failed");
        }
    }
}
