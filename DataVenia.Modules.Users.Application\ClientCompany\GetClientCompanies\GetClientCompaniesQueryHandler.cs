﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.ClientCompany;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.ClientCompany.GetClientCompanies;

public sealed class GetClientCompaniesQueryHandler(IClientCompanyRepository clientCompanyRepository, ILogger<IClientCompanyRepository> logger) : IQueryHandler<GetClientCompaniesQuery, IReadOnlyCollection<GetClientCompaniesResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetClientCompaniesResponse>>> Handle(GetClientCompaniesQuery request, CancellationToken cancellationToken)
    {
        // buscar todos filtrando por office
        try
        {
            var clients = (await clientCompanyRepository.GetManyAsync(new GetClientCompaniesFilter(request.ClientId, request.CompanyId, request.Role), cancellationToken)).ToList();
            return clients;
        } catch(Exception ex)
        {
            logger.LogError(ex, "Error getting client companies. Request: {@Request}", request);
            return Result.Failure<IReadOnlyCollection<GetClientCompaniesResponse>>(Error.InternalServerError());
        }

    }
}
