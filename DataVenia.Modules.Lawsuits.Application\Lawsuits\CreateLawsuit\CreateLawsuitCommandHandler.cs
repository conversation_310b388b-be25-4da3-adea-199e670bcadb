﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateLawsuit;

public sealed class CreateLawsuitCommandHandler(
    ILawsuitRepository lawsuitRepository,
    ILawsuitDataRepository lawsuitDataRepository,
    IOfficeLawyerFacade officeLawyerFacade,
    IUnitOfWork unitOfWork,
    ILogger<CreateLawsuitCommandHandler> logger) : ICommandHandler<CreateLawsuitCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateLawsuitCommand request, CancellationToken cancellationToken)
    {
        try
        {
            IReadOnlyCollection<OfficeLawyerDto> responsibles =
                await officeLawyerFacade.GetActiveByOfficeAndLawyers(request.OfficeId, request.ResponsibleIds);

            if (responsibles.Count != request.ResponsibleIds.Count)
                return Result.Failure<Guid>(Error.Conflict("NotAuthorized",
                    "There are nonexistent users being attached to the lawsuit"));
            
            var lawsuit = Lawsuit.Create(
                request.Cnj,
                request.OfficeId,
                request.LawsuitTypeId,
                request.ClassId,
                request.DistributedAt
            );

            var lawsuitData = LawsuitData.Create(
                request.Title,
                request.Cnj,
                lawsuit.Id,
                request.FolderId,
                request.LegalInstanceId,
                request.LawsuitStatusId,
                request.TopicIds,
                request.JudgingOrganId,
                request.CauseValue,
                request.ConvictionValue,
                request.CourtHref,
                request.Description,
                request.Observations,
                request.Access,
                // request.Parties,
                request.ResponsibleIds,
                request.EvolvedFromCaseId,
                request.GroupingCaseId,
                true);
            
            if(lawsuitData.IsFailed)
                return Result.Failure<Guid>(new Error(lawsuitData.Errors.FirstOrDefault()!.Message, lawsuitData.Errors.FirstOrDefault()!.Message, ErrorType.Validation));
            
            lawsuitRepository.Insert(lawsuit);

            lawsuitDataRepository.Insert(lawsuitData.Value);

            await unitOfWork.SaveChangesAsync(cancellationToken);
            
            return lawsuit.Id;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving lawsuit to database: {@Request}", request);
            return Result.Failure<Guid>(Error.InternalServerError());
        }
    }
}
