﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyers.RegisterLawyer;
using DataVenia.Modules.Users.Domain.Authorization;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.Users.Presentation.SystemAdministrator;
internal sealed class RegisterLawyer : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("create", async ([FromBody] RegisterLawyersRequest request, ClaimsPrincipal claims,[FromServices] ISender sender) =>
            {
                Result<RegisterLawyersResponse> result = await sender.Send(new RegisterLawyerCommand(
                    request.Lawyers,
                    request.Office,
                    claims.GetUserId()));

            return result.Match(Results.Ok, ApiResults.Problem);
        })
        .RequireAuthorization(Permission.SystemAdministrator.Code)
        .WithTags(Tags.Users);
    }
}

public sealed record RegisterLawyersRequest
{
    public List<LawyerRequest> Lawyers { get; init; }
    public OfficeRequest Office { get; init; }
}
