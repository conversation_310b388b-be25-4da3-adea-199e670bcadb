﻿using DataVenia.Common.Domain;
using LawsuitPartyDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitParties.LawsuitParty;

namespace DataVenia.Modules.Lawsuits.Domain.LawsuitParty;
public interface ILawsuitPartyRepository
{
    void Insert(LawsuitPartyDomain lawsuitParty);

    Task<Result<IReadOnlyCollection<LawsuitPartyDomain>>> GetLawsuitPartiesAsync(Guid lawsuitId,
        CancellationToken cancellationToken = default);
}
