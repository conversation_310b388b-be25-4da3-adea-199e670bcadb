﻿using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.LawsuitSync.Application.AppServices;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Configs;
using DataVenia.Modules.LawsuitSync.Domain.Providers.Codilo.Settings;
using DataVenia.Modules.LawsuitSync.Infrastructure.Database;
using DataVenia.Modules.LawsuitSync.Infrastructure.Database.Repositories;
using DataVenia.Modules.LawsuitSync.Infrastructure.LawsuitProviders.Codilo;
using DataVenia.Modules.LawsuitSync.Infrastructure.LawsuitProviders.Codilo.Handlers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Refit;

namespace DataVenia.Modules.LawsuitSync.Infrastructure;

public static class LawsuitSyncModule
{
    public static void AddLawsuitSyncModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddRefitClients(configuration);
        services.AddServices(configuration ?? throw new ArgumentNullException(nameof(configuration)));
        services.AddEndpoints(Presentation.AssemblyReference.Assembly);
    }

    private static void AddServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IStartLawsuitMonitoringAppService, StartLawsuitMonitoringAppService>();
        services.AddScoped<IReceiveLawsuitCallbackAppService, ReceiveLawsuitCallbackAppService>();
        services.AddScoped<ICodiloRepository, CodiloRepository>();
        services.AddScoped<ILawsuitMonitoringRepository, LawsuitMonitoringRepository>();
        services.AddScoped<IStopLawsuitMonitoringAppService, StopLawsuitMonitoringAppService>();

        services.Configure<CallbackSettings>(configuration.GetSection("LawsuitSync:Callback"));
        services.Configure<CodiloSettings>(configuration.GetSection("LawsuitSync:Providers:Codilo"));

        services.AddDbContext<LawsuitSyncDbContext>((sp, options) =>
            options
                .UseNpgsql(
                    configuration.GetConnectionString("Database"),
                    npgsqlOptions => npgsqlOptions
                        .MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Lawsuit))
                .UseSnakeCaseNamingConvention());
    }

    private static void AddRefitClients(this IServiceCollection services, IConfiguration configuration)
    {
        var codiloSearchBaseAddress =
            configuration.GetValue<string>("LawsuitSync:Providers:Codilo:SearchBaseAddress") ??
            throw new ArgumentNullException(nameof(configuration), "LawsuitSync:Providers:Codilo:SearchBaseAddress");

        var codiloMonitoringBaseAddress =
            configuration.GetValue<string>("LawsuitSync:Providers:Codilo:MonitoringBaseAddress") ??
            throw new ArgumentNullException(nameof(configuration),
                "LawsuitSync:Providers:Codilo:MonitoringBaseAddress");

        var authBaseAddress = configuration.GetValue<string>("LawsuitSync:Providers:Codilo:AuthBaseAddress") ??
                              throw new ArgumentNullException(nameof(configuration),
                                  "LawsuitSync:Providers:Codilo:AuthBaseAddress");

        services.AddHttpClient("LawsuitSync").ConfigureHttpClient(x => x.BaseAddress = new Uri(authBaseAddress));
        services.AddMemoryCache();


        services.AddRefitClient<ICodiloAuthClient>()
            .ConfigureHttpClient(
                c => c.BaseAddress =
                    new Uri(authBaseAddress));

        services.AddRefitClient<ICodiloClient>()
            .ConfigureHttpClient(
                c => c.BaseAddress =
                    new Uri(codiloSearchBaseAddress))
            .AddHttpMessageHandler(sp =>
            {
                var options = sp.GetService<IOptions<CodiloSettings>>() ??
                              throw new ArgumentException("Missing ICodiloSettings");
                var logger = sp.GetService<ILogger<AuthRequestHandler>>() ??
                             throw new ArgumentException("Missing ILogger<AuthRequestHandler>");
                var httpClientFactory = sp.GetService<IHttpClientFactory>() ??
                                        throw new ArgumentException("Missing IHttpClientFactory");
                var memoryCache = sp.GetService<IMemoryCache>() ??
                                  throw new ArgumentException("Missing IMemoryCache");

                return new AuthRequestHandler(options, httpClientFactory, memoryCache, logger);
            });

        services.AddRefitClient<IMonitoringLawsuitsClient>()
            .ConfigureHttpClient(
                c => c.BaseAddress =
                    new Uri(codiloMonitoringBaseAddress))
            .AddHttpMessageHandler(sp =>
            {
                var options = sp.GetService<IOptions<CodiloSettings>>() ??
                              throw new ArgumentException("Missing ICodiloSettings");
                var logger = sp.GetService<ILogger<AuthRequestHandler>>() ??
                             throw new ArgumentException("Missing ILogger<AuthRequestHandler>");
                var httpClientFactory = sp.GetService<IHttpClientFactory>() ??
                                        throw new ArgumentException("Missing IHttpClientFactory");
                var memoryCache = sp.GetService<IMemoryCache>() ??
                                  throw new ArgumentException("Missing IMemoryCache");

                return new AuthRequestHandler(options, httpClientFactory, memoryCache, logger);
            });
    }
}
