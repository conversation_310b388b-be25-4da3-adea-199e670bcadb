namespace DataVenia.Modules.Notification.Worker.Helpers;

public static class Mail<PERSON>elper
{
    public static string CreateMailMessage(string htmlName, string link, string logoLink, string companyName)
    {
        var data = new
        {
            COMPANY_NAME = companyName,
            COMPANY_LOGO = logoLink,
            CODE_CHALLENGE = link
        };

        return CreateMailMessage(htmlName, data);
    }

    public static string CreateMailMessage(string htmlName, dynamic data)
    {
        var template = ReadHtmlTemplate(htmlName);

        return ReplacePlaceholders(template, data);
    }

    private static string ReplacePlaceholders(string template, dynamic data)
    {
        foreach (dynamic? property in data.GetType().GetProperties())
        {
            string placeholder = $"{{|{property.Name}|}}";
            string value = property.GetValue(data)?.ToString() ?? string.Empty;
            template = template.Replace(placeholder, value);
        }

        return template;
    }

    private static string ReadHtmlTemplate(string resourceName)
    {
        string basePath = AppDomain.CurrentDomain.BaseDirectory;
        string resourcePath = Path.Combine(basePath, "Html", resourceName);

        if (!File.Exists(resourcePath))
        {
            throw new FileNotFoundException($"Resource '{resourceName}' not found.");
        }

        return File.ReadAllText(resourcePath);
    }
}
