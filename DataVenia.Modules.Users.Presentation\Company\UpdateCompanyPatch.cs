﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.UpdateClient;
using DataVenia.Modules.Users.Application.Company.UpdateCompanyPatch;
using DataVenia.Modules.Users.Application.Lawyer.UpdateLawyer;
using DataVenia.Modules.Users.Domain.SharedModels;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Client;

internal sealed class UpdateCompanyPatch : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPatch("/offices/{officeId}/companies/{companyId}", async ([FromBody] Request request, Guid companyId, ClaimsPrincipal claims, [FromServices] ISender sender) =>
        {
            Result result = await sender.Send(new UpdateCompanyPatchCommand(
                companyId,
                request.Name,
                request.Cnpj));

            return result.Match(Results.NoContent, ApiResults.Problem);
        })
        .RequireAuthorization("office:clients:update")
        .WithTags(Tags.Company);
    }

    public sealed class Request
    {
        public string? Name { get; init; }
        public string? Cnpj { get; init; }
    }
}
