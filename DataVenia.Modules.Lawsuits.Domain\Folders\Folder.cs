﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Lawsuits.Domain.Folder;
public sealed class Folder : Entity
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Color { get; private set; }
    public Guid? ParentFolderId { get; private set; }
    public Folder? ParentFolder { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public static Folder Create(string name,  Guid? parentFolderId)
    {
        Folder folder = new()
        {
            Id = Guid.CreateVersion7(),
            Name = name,
            ParentFolderId = parentFolderId,
            CreatedAt = DateTime.UtcNow
        };

        return folder;
    }
}
