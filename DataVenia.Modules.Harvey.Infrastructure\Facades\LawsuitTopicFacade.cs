using DataVenia.Common.Contracts.Harvey;
using DataVenia.Modules.Harvey.Domain.LawsuitTopic;

namespace DataVenia.Modules.Harvey.Infrastructure.Facades;

public sealed class LawsuitTopicFacade(ILawsuitTopicRepository lawsuitTopicRepository) : ILawsuitTopicFacade
{
    public async Task<bool> ExistsAsync(int topicId, CancellationToken cancellationToken = default)
    {
        var topic = await lawsuitTopicRepository.GetByIdAsync(topicId, cancellationToken);
        return topic != null;
    }

    public async Task<IReadOnlyCollection<int>> GetExistingIdsAsync(IEnumerable<int> topicIds, CancellationToken cancellationToken = default)
    {
        var existingIds = new List<int>();
        
        foreach (var topicId in topicIds)
        {
            var topic = await lawsuitTopicRepository.GetByIdAsync(topicId, cancellationToken);
            if (topic != null)
            {
                existingIds.Add(topicId);
            }
        }
        
        return existingIds.AsReadOnly();
    }
}
