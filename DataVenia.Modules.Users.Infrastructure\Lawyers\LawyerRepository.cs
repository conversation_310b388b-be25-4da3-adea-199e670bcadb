﻿using System.Linq.Expressions;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Infrastructure.Database;
using DataVenia.Modules.Users.Infrastructure.IntermediateClasses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Infrastructure.Users;

public sealed class LawyerRepository(UsersDbContext context, ILogger<LawyerRepository> logger) : ILawyerRepository
{
    public async Task<Lawyer?> GetSingleByFilterAsync(Expression<Func<Lawyer, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Lawyers
            .Include(l => l.Oab)
            .Include(l => l.Contacts)
            .FirstOrDefaultAsync(filter, cancellationToken);
    }
    
    public async Task<IReadOnlyList<Lawyer>> GetByIdsAsync(List<Guid> ids, CancellationToken cancellationToken = default)
    {
        return await context.Lawyers
            .Where(l => ids.Contains(l.Id))
            .ToListAsync(cancellationToken);
    }

    public async Task<Lawyer?> GetSingleWithOfficesByFilterAsync(Expression<Func<Lawyer, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Lawyers
            .Include(l => l.Oab)
            .Include(l => l.OfficeUsers)
                .ThenInclude(ou => ou.Office)
            .FirstOrDefaultAsync(filter, cancellationToken);
    }
    
    public async Task<List<Lawyer>> GetByEmailsAsync(IEnumerable<string> emails, CancellationToken cancellationToken = default)
    {
        return await context.Lawyers
            .Where(lawyer => emails.Contains(lawyer.Email))
            .Include(lawyer => lawyer.Oab)
            .Include(lawyer => lawyer.Contacts)
            .ToListAsync(cancellationToken);
    }


    public async Task<Result<Lawyer>> GetSingleBySignUpTokenAsync(Guid token, CancellationToken cancellationToken = default)
    {
        Guid? tokenOfficeId = await context.SignUpTokens
            .Where(s => s.Token == token)
            .Select(s => s.OfficeId)
            .FirstOrDefaultAsync(cancellationToken);
        
        Lawyer? lawyer = await context.Lawyers
            .Where(l => l.SignUpToken.Any(s => s.Token == token))
            .Include(l => l.SignUpToken)
            .Include(l => l.OfficeUsers.Where(ou => ou.OfficeId == tokenOfficeId))
            .FirstOrDefaultAsync(cancellationToken);

        if (lawyer is null)
            return Result.Failure<Lawyer>(new Error("Lawyer.NotFound", "Lawyer not found", ErrorType.NotFound));

        return lawyer;
    }
    
    public async Task<Result<List<OfficeUserDto>>> GetOfficesAdministratorsAsync(Guid lawyerId, CancellationToken cancellationToken = default)
    {
        try
        {
            
            List<Guid> offices = await context.OfficeUsers
            .Where(ou => ou.UserId == lawyerId) // Get offices the lawyer belongs to
            .Select(ou => ou.OfficeId)
            .ToListAsync(cancellationToken);

        if (!offices.Any()) return new List<OfficeUserDto>(); // If no offices found, return empty list

        List<OfficeUser> admins = await context.OfficeUsers
            .Where(ou => offices.Contains(ou.OfficeId) && ou.RoleName == Role.OfficeAdministrator.Name && ou.InvitationStatus == InvitationStatus.ACTIVE)
            .ToListAsync(cancellationToken);
        
        return admins.Select(a => new OfficeUserDto(a.UserId, a.OfficeId)).ToList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting office admins for lawyer: {@LawyerId}", lawyerId);
            return Result.Failure<List<OfficeUserDto>>(Error.InternalServerError());
        }
    }
    
    public async Task<Lawyer?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await context.Lawyers.SingleOrDefaultAsync(u => u.Email == email, cancellationToken);
    }

    public void Insert(Lawyer lawyer)
    {
        context.Lawyers.Add(lawyer);
    }
    
    public void Update(Lawyer lawyer)
    {
        context.Lawyers.Update(lawyer);
    }

    public async Task<Result<Role?>> GetRoleByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await context.Roles.SingleOrDefaultAsync(r => r.Name == name, cancellationToken);
    }

    public async Task<IEnumerable<Lawyer>> GetAllByOfficeIdAsync(Guid officeId, CancellationToken cancellationToken = default)
    {
        return await context.Lawyers
            .Include(l => l.SignUpToken.Where(s => s.OfficeId == officeId))
            .Include(l => l.Oab)
            .Include(l => l.OfficeUsers)
                .Where(l => l.OfficeUsers.Any(ou => ou.OfficeId == officeId))
            .ToListAsync(cancellationToken);
    }

    public async Task<Guid?> GetIdentityId(Guid lawyerId, CancellationToken cancellationToken = default)
    {
        return await context.Lawyers
            .Where(l => l.Id == lawyerId)
            .Select(l => l.IdentityId)
            .FirstOrDefaultAsync(cancellationToken);
    }
    
    public void InsertRange(IEnumerable<Lawyer> lawyers)
    {
        context.Lawyers.AddRange(lawyers);
    }

}
