﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class ChangeLocationOfTopicIdsProperty : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "topic_ids",
                schema: "lawsuit",
                table: "lawsuit");

            migrationBuilder.AddColumn<string>(
                name: "topic_ids",
                schema: "lawsuit",
                table: "lawsuit_data",
                type: "jsonb",
                nullable: true,
                defaultValueSql: "'[]'::jsonb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "topic_ids",
                schema: "lawsuit",
                table: "lawsuit_data");

            migrationBuilder.AddColumn<string>(
                name: "topic_ids",
                schema: "lawsuit",
                table: "lawsuit",
                type: "jsonb",
                nullable: true,
                defaultValueSql: "'[]'::jsonb");
        }
    }
}
