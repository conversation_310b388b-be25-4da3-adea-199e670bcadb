﻿using System.Linq.Expressions;
using DataVenia.Modules.Users.Application.ClientCompany.GetClientCompanies;
using DataVenia.Modules.Users.Domain.ClientCompany;

namespace DataVenia.Modules.Users.Application.ClientCompany;

public interface IClientCompanyRepository
{
    Task<IReadOnlyCollection<GetClientCompaniesResponse>> GetManyAsync(GetClientCompaniesFilter filter,
        CancellationToken cancellationToken = default);
    Task<Domain.ClientCompany.ClientCompany?> GetSingleAsync(Expression<Func<Domain.ClientCompany.ClientCompany, bool>> filter, CancellationToken cancellationToken = default);
    void Insert(Domain.ClientCompany.ClientCompany clientCompany);
}
