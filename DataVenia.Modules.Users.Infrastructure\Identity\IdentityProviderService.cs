﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Application.User.Login;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DataVenia.Modules.Users.Infrastructure.Identity;

public sealed class IdentityProviderService(KeyCloakClient keyCloakClient, ILogger<IdentityProviderService> logger)
    : IIdentityProviderService
{
    private const string PasswordCredentialType = "Password";

    // POST /admin/realms/{realm}/users
    public async Task<Result<string>> RegisterUserAsync(UserModel user, CancellationToken cancellationToken = default)
    {
        var userRepresentation = new UserRepresentation(
            user.Email,
            user.Email,
            user.FirstName,
            user.LastName,
            true,
            true,
            [new CredentialRepresentation(PasswordCredentialType, user.Password, false)]);


        Result<string> identityId = await keyCloakClient.RegisterUserAsync(userRepresentation, cancellationToken);

        return identityId;
    }

    public async Task<Result<string?>> GetUserIdByEmailAsync(string email,
        CancellationToken cancellationToken = default)
    {
        try
        {
            UserRepresentation? response = await keyCloakClient.GetUserByEmailAsync(email, cancellationToken);
            return Result.Success(response?.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao buscar usuário no Identity Provider.");
            return Result.Failure<string?>(Error.InternalServerError());
        }
    }

    public async Task<Result<LoginResponse>> AuthenticateAsync(string username, string password,
        CancellationToken cancellationToken)
    {
        var requestData = new Dictionary<string, string>
        {
            { "grant_type", "password" },
            { "client_id", keyCloakClient.PublicClientId },
            { "username", username },
            { "password", password }
        };

#pragma warning disable CA2000
        var requestContent = new FormUrlEncodedContent(requestData);
#pragma warning restore CA2000
        
        try
        {
            Result<KeycloakTokenResponse> loginResponse =
                await keyCloakClient.AuthenticateAsync(requestContent, cancellationToken);

            if (loginResponse.IsFailure)
                return Result.Failure<LoginResponse>(loginResponse.Error);

            return Result.Success(new LoginResponse()
            {
                AccessToken = loginResponse.Value.AccessToken,
                RefreshToken = loginResponse.Value.RefreshToken,
                AccessTokenExpiresIn = loginResponse.Value.AccessTokenExpiresIn,
                RefreshTokenExpiresIn = loginResponse.Value.RefreshTokenExpiresIn,
                TokenType = loginResponse.Value.TokenType,
                IdToken = loginResponse.Value.IdToken,
            });
        }
        catch (Exception e)
        {
            logger.LogError(e, "An error occurred while trying to login");
            return Result.Failure<LoginResponse>(new Error("Internal.Server.Error",
                "An error occurred while trying to login", ErrorType.InternalServerError));
        }
    }

    public async Task<Result> ResetPasswordAsync(Guid identityId, string password, CancellationToken cancellationToken)
    {
        return await keyCloakClient.ResetPasswordAsync(identityId, password, cancellationToken);
    }

    Task<Result<LoginResponse>> IIdentityProviderService.RefreshTokenAsync(string refreshToken,
        CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}
