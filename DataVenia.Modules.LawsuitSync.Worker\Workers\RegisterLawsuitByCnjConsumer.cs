using DataVenia.Common.Contracts.Events.Lawsuit;
using DataVenia.Common.Contracts.Events.LawsuitSync;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.LawsuitSync.Worker.Workers;

public class RegisterLawsuitByCnjConsumer(
    IStartLawsuitMonitoringAppService service,
    ILogger<RegisterLawsuitByCnjConsumer> logger) : IConsumer<RegisterLawsuitByCnj>
{
    public async Task Consume(ConsumeContext<RegisterLawsuitByCnj> context)
    {
        context = context ?? throw new ArgumentNullException(nameof(context));

        await service.ExecuteAsync(context.Message.Cnj, context.Message.SubscriberId);

        logger.LogTrace("Start monitoring cnj {Number}", context.Message.Cnj);
    }
}
