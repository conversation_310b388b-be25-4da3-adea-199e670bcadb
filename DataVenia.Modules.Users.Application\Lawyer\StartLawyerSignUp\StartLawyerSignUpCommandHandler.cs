﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.Events.Notification;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.SharedModels;
using DataVenia.Modules.Users.Domain.SignUpToken;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Lawyer.StartLawyerSignUp;

public class StartLawyerSignUpCommandHandler(
    ILawyerRepository lawyerRepository, 
    IUnitOfWork unitOfWork, 
    IOfficeUserRepository officeUserRepository, 
    IUserGlobalRoleRepository userGlobalRoleRepository, 
    ILogger<StartLawyerSignUpCommandHandler> logger,
    ISignUpTokenRepository signUpTokenRepository,
    IConfiguration configuration,
    IPublishEndpoint publisher
    ) : ICommandHandler<StartLawyerSignUpCommand, Guid>
{
    public async Task<Result<Guid>> Handle(StartLawyerSignUpCommand request, CancellationToken cancellationToken)
    {
        // insert duplicate validation here
        Domain.Lawyers.Lawyer? existentLawyer = await lawyerRepository.GetSingleByFilterAsync(x => x.Email == request.Email, cancellationToken);

        if (existentLawyer is not null)
            return Result.Failure<Guid>(new Error("Lawyer.Duplicate", "Lawyer already exists", ErrorType.Conflict));

        #region  lawyer
        Result<Domain.Lawyers.Lawyer> newLawyer = Domain.Lawyers.Lawyer.Create(
            request.Email,
            request.FirstName,
            request.LastName,
            new List<string>(),
            request.Contacts
        );

        if (newLawyer.IsFailure)
        {
            logger.LogError("It wasn't possible to create the lawyer {LawyerFailure}", newLawyer.Error);
            return Result.Failure<Guid>(newLawyer.Error);
        }

        lawyerRepository.Insert(newLawyer.Value);
        #endregion
        
        # region user_global_role
        Result<Role?> systemMemberRole = await lawyerRepository.GetRoleByNameAsync(Role.SystemMember.Name, cancellationToken);
        
        if (systemMemberRole.IsFailure)
            return Result.Failure<Guid>(new Error("Role.NotFound", "SystemMember role not found", ErrorType.NotFound));

        var userGlobalRole = UserGlobalRole.Create(newLawyer.Value.Id, systemMemberRole.Value.Name);
        userGlobalRoleRepository.Insert(userGlobalRole);
        # endregion
        
        # region officeUser
        Result<Role?> officeMemberRole = await lawyerRepository.GetRoleByNameAsync(Role.OfficeMember.Name, cancellationToken);

        if (officeMemberRole.IsFailure)
        {
            logger.LogError("It wasn't possible to find the OfficeMember role in the Database {MemberRoleFailure}", officeMemberRole.Error);
            return Result.Failure<Guid>(new Error("Role.NotFound", "OfficeMember role not found", ErrorType.NotFound));
        }
        
        var officeUser = OfficeUser.Create(request.OfficeId, newLawyer.Value.Id, request.OwnerId, officeMemberRole.Value.Name);
        
        officeUserRepository.Insert(officeUser);
        # endregion
        
        # region signUpToken
        var signUpToken = SignUpToken.Create(newLawyer.Value.Id, request.OfficeId);
        
        signUpTokenRepository.Insert(signUpToken);
        # endregion
        
        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);

            // enviar o email no notification module
            string? signUpUrl = configuration["Users:SignUpUrl"];

            if (string.IsNullOrWhiteSpace(signUpUrl))
            {
                logger.LogError("SignUpUrl env variable is not configured.");
                return Result.Failure<Guid>(Error.InternalServerError());
            }
            
            var eventMessage = new MailNotificationEvent(
                $"{signUpUrl}{signUpToken.Token}",
                newLawyer.Value.Email,
                newLawyer.Value.FirstName,
                NotificationMailType.MagicLink,
                $"Welcome to DataVenia!"
            );
            
            await publisher.Publish(eventMessage, cancellationToken);
            return Result.Success(signUpToken.Token);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error starting lawyer sign up process in the database.");
            return Result.Failure<Guid>(Error.InternalServerError());
        }
    }
}
