﻿using System.Linq.Expressions;
using DataVenia.Modules.Calendar.Domain.Status;
using DataVenia.Modules.Calendar.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using StatusDomain = DataVenia.Modules.Calendar.Domain.Status.Status;

namespace DataVenia.Modules.Calendar.Infrastructure.Status;

internal sealed class StatusRepository(CalendarDbContext context) : IStatusRepository
{
    public async Task<StatusDomain?> GetSingleAsync(Expression<Func<StatusDomain, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Statuses
            .FirstOrDefaultAsync(filter, cancellationToken);
    }

    public async Task<IReadOnlyCollection<StatusDomain>> GetManyAsync(Expression<Func<StatusDomain, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Statuses
            .Where(filter)
            .ToListAsync(cancellationToken);
    }
}
