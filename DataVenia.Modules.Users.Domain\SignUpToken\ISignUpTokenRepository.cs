﻿using System.Linq.Expressions;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Users.Domain.SignUpToken;

public interface ISignUpTokenRepository
{ 
    void Insert(SignUpToken signUpToken);

    Task<SignUpToken?> GetSingleByFilterAsync(Expression<Func<SignUpToken, bool>> filter,
        CancellationToken cancellationToken = default);

    Task<Result<SignUpToken>> GetSingleByTokenAsync(Guid token, CancellationToken cancellationToken = default);
    Result Update(SignUpToken token);
}
