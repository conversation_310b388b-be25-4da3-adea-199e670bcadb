﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35222.181
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Api", "DataVenia.Api\DataVenia.Api.csproj", "{059A0B6A-90A5-4662-8C8A-59157BBD095A}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{406419EC-DC6A-4540-8FB4-B7E2216285DD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{97F8809A-FB4C-401F-BFE8-E284BC523F4C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Api", "Api", "{92416A4C-D5CE-45FC-878A-6FE7CDFB92E9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Modules", "Modules", "{ED277EE5-1877-4D9B-836A-6786B3C7BD8A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Itens", "Solution Itens", "{0E548EC9-964C-4996-97DF-1608DC42C11C}"
	ProjectSection(SolutionItems) = preProject
		.dockerignore = .dockerignore
		.gitignore = .gitignore
		Directory.Build.props = Directory.Build.props
		UpdateMIgrations.ps1 = UpdateMIgrations.ps1
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Common.Presentation", "DataVenia.Common.Presentation\DataVenia.Common.Presentation.csproj", "{C62F3FD2-A921-4AE6-B7EB-D8E0234897BD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Common.Infrastructure", "DataVenia.Common.Infrastructure\DataVenia.Common.Infrastructure.csproj", "{E0DA8212-4578-4C2C-BD41-B7032D5485B8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Common.Domain", "DataVenia.Common.Domain\DataVenia.Common.Domain.csproj", "{B15E012A-456E-4C29-A986-7399D9874B0A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Common.Application", "DataVenia.Common.Application\DataVenia.Common.Application.csproj", "{658FCCA5-E974-43C0-A9F3-6D93C6651718}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "User", "User", "{0A66BD17-AE1A-4767-8B1A-68559CCBE4DA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Users.Application", "DataVenia.Modules.Users.Application\DataVenia.Modules.Users.Application.csproj", "{185A79E0-90CD-47AD-A009-AE17A6B478C3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Users.Domain", "DataVenia.Modules.Users.Domain\DataVenia.Modules.Users.Domain.csproj", "{88F75C4C-0B5E-4AFF-BB42-F9A16C2701DE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Users.Infrastructure", "DataVenia.Modules.Users.Infrastructure\DataVenia.Modules.Users.Infrastructure.csproj", "{DCDFBC0D-3D4C-4183-876C-C69C2037C871}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Users.Presentation", "DataVenia.Modules.Users.Presentation\DataVenia.Modules.Users.Presentation.csproj", "{9E331734-83CB-4DDA-BA9C-E28C2E5468D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Users.IntegrationEvents", "DataVenia.Modules.Users.IntegrationEvents\DataVenia.Modules.Users.IntegrationEvents.csproj", "{6B940B93-CEFD-485D-95BA-30A080094B84}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Lawsuit", "Lawsuit", "{36EC7AE0-41FA-4742-A2B4-B295CF32FFB5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Lawsuits.Application", "DataVenia.Modules.Lawsuits.Application\DataVenia.Modules.Lawsuits.Application.csproj", "{8EE23549-D27A-484C-812B-A162BCA97D59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Lawsuits.Presentation", "DataVenia.Modules.Lawsuits.Presentation\DataVenia.Modules.Lawsuits.Presentation.csproj", "{DF38B80F-B143-4CAA-AC60-8FF109C0F94D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Lawsuits.Domain", "DataVenia.Modules.Lawsuits.Domain\DataVenia.Modules.Lawsuits.Domain.csproj", "{F54744B0-A3E2-40A6-8FA1-D91F6B23DFEA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Lawsuits.Infrastructure", "DataVenia.Modules.Lawsuits.Infrastructure\DataVenia.Modules.Lawsuits.Infrastructure.csproj", "{FCC00F7F-319D-4BAA-9697-2F658ECF7375}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{14AF5F7E-D1F5-4F5F-8516-52E1B842F010}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		requirements.md = requirements.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Calendar", "Calendar", "{DA342EB4-BCB5-40E3-971E-18E07FF7CAB3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Calendar.Domain", "DataVenia.Modules.Calendar.Domain\DataVenia.Modules.Calendar.Domain.csproj", "{1DCA9E0E-CDF7-414D-A2C4-E930B670E4AC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Calendar.Application", "DataVenia.Modules.Calendar.Application\DataVenia.Modules.Calendar.Application.csproj", "{4FD7A232-A43E-42B9-9F66-EEC4114D8408}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Calendar.Infrastructure", "DataVenia.Modules.Calendar.Infrastructure\DataVenia.Modules.Calendar.Infrastructure.csproj", "{27B85E25-6624-4A7B-B983-529079FDA2AA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.Calendar.Presentation", "DataVenia.Modules.Calendar.Presentation\DataVenia.Modules.Calendar.Presentation.csproj", "{82F7D900-663D-46E9-8455-67A3D93C0D7B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Common.Contracts", "DataVenia.Common.Contracts\DataVenia.Common.Contracts.csproj", "{C98AD7A5-2ABA-4C04-9B13-8663595A80EC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Common.SeedDatabase", "DataVenia.Common.SeedDatabase\DataVenia.Common.SeedDatabase.csproj", "{6A904060-95F8-4EAC-8C98-07108D75DE24}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "LawsuitSync", "LawsuitSync", "{90C71CAC-DF72-4B63-9EC3-F1B0D5D4CB44}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.LawsuitSync.Application", "DataVenia.Modules.LawsuitSync.Application\DataVenia.Modules.LawsuitSync.Application.csproj", "{4D6287DB-784A-4C4B-9AD1-5314137EBE86}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.LawsuitSync.Infrastructure", "DataVenia.Modules.LawsuitSync.Infrastructure\DataVenia.Modules.LawsuitSync.Infrastructure.csproj", "{1E0F78C7-9818-47D0-81B5-586B562D99E7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.LawsuitSync.Domain", "DataVenia.Modules.LawsuitSync.Domain\DataVenia.Modules.LawsuitSync.Domain.csproj", "{258EFEA2-8E82-4B02-AEA4-FF340FFA8D07}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataVenia.Modules.LawsuitSync.Worker", "DataVenia.Modules.LawsuitSync.Worker\DataVenia.Modules.LawsuitSync.Worker.csproj", "{857490CB-8705-4601-AC6E-A7B1A067C6A9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Notification", "Notification", "{12B5333C-1332-4A07-8774-5F1D408368FE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Harvey", "Harvey", "{FFCCBD78-98D7-4C9D-8223-298D62D9D244}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Harvey.Presentation", "DataVenia.Modules.Harvey.Presentation\DataVenia.Modules.Harvey.Presentation.csproj", "{D1CF4D16-71D4-4B56-B90F-B5F47A97605B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Harvey.Domain", "DataVenia.Modules.Harvey.Domain\DataVenia.Modules.Harvey.Domain.csproj", "{302BD6D7-62BA-4153-B9CA-5F8C155A9BB5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Harvey.Application", "DataVenia.Modules.Harvey.Application\DataVenia.Modules.Harvey.Application.csproj", "{9C5E59B1-1437-463C-8071-309F81D71F87}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Harvey.Infrastructure", "DataVenia.Modules.Harvey.Infrastructure\DataVenia.Modules.Harvey.Infrastructure.csproj", "{FC790B75-743F-42AC-ABBE-2E4A6358082E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Notification.Application", "DataVenia.Modules.Notification.Application\DataVenia.Modules.Notification.Application.csproj", "{87B5D9E8-07C4-477D-B608-1079C5CF5603}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Notification.Domain", "DataVenia.Modules.Notification.Domain\DataVenia.Modules.Notification.Domain.csproj", "{45C91397-3196-4F54-9959-55F8362C8399}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Notification.Domain.Infrastructure", "DataVenia.Modules.Notification.Domain.Infrastructure\DataVenia.Modules.Notification.Domain.Infrastructure.csproj", "{6E36D4D0-171C-40C3-A1FA-FC2027CE5CDA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Notification.Worker", "DataVenia.Modules.Notification.Worker\DataVenia.Modules.Notification.Worker.csproj", "{A164B0BF-5E70-4D07-9773-0E326BDD86CC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Users.Worker", "DataVenia.Modules.Users.Worker\DataVenia.Modules.Users.Worker.csproj", "{48A3F19A-4529-4D29-A3D6-9B8D947C467A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.LawsuitSync.Presentation", "DataVenia.Modules.LawsuitSync.Presentation\DataVenia.Modules.LawsuitSync.Presentation.csproj", "{98CAB7C0-A9A7-4620-A78E-07FCCC89AA48}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Common.Test", "DataVenia.Common.Test\DataVenia.Common.Test.csproj", "{B8DF68DF-A0D0-48B3-800F-FA3C291FCE2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.Lawsuits.Worker", "DataVenia.Modules.Lawsuits.Worker\DataVenia.Modules.Lawsuits.Worker.csproj", "{4C4CFCC8-811D-4A8B-B943-4A49ABD552B7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Association", "Association", "{68F66759-E783-454F-ACBF-BBAF2DBCFEF8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.AssociationHub.Presentation", "DataVenia.Modules.AssociationHub.Presentation\DataVenia.Modules.AssociationHub.Presentation.csproj", "{017BDDF9-C8B5-4D58-9E90-24213194507F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.AssociationHub.Domain", "DataVenia.Modules.AssociationHub.Domain\DataVenia.Modules.AssociationHub.Domain.csproj", "{B4E2CB04-1B5F-4C22-8D05-3E87B31478D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.AssociationHub.Application", "DataVenia.Modules.AssociationHub.Application\DataVenia.Modules.AssociationHub.Application.csproj", "{661E5138-B2BB-4B85-96AE-4B0EA7268AAF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataVenia.Modules.AssociationHub.Infrastructure", "DataVenia.Modules.AssociationHub.Infrastructure\DataVenia.Modules.AssociationHub.Infrastructure.csproj", "{BD77F3AD-E1E5-49B2-A5DF-9A52570BFEF7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{059A0B6A-90A5-4662-8C8A-59157BBD095A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{059A0B6A-90A5-4662-8C8A-59157BBD095A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{059A0B6A-90A5-4662-8C8A-59157BBD095A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{059A0B6A-90A5-4662-8C8A-59157BBD095A}.Release|Any CPU.Build.0 = Release|Any CPU
		{406419EC-DC6A-4540-8FB4-B7E2216285DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{406419EC-DC6A-4540-8FB4-B7E2216285DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{406419EC-DC6A-4540-8FB4-B7E2216285DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{406419EC-DC6A-4540-8FB4-B7E2216285DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{C62F3FD2-A921-4AE6-B7EB-D8E0234897BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C62F3FD2-A921-4AE6-B7EB-D8E0234897BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C62F3FD2-A921-4AE6-B7EB-D8E0234897BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C62F3FD2-A921-4AE6-B7EB-D8E0234897BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0DA8212-4578-4C2C-BD41-B7032D5485B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0DA8212-4578-4C2C-BD41-B7032D5485B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0DA8212-4578-4C2C-BD41-B7032D5485B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0DA8212-4578-4C2C-BD41-B7032D5485B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{B15E012A-456E-4C29-A986-7399D9874B0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B15E012A-456E-4C29-A986-7399D9874B0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B15E012A-456E-4C29-A986-7399D9874B0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B15E012A-456E-4C29-A986-7399D9874B0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{658FCCA5-E974-43C0-A9F3-6D93C6651718}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{658FCCA5-E974-43C0-A9F3-6D93C6651718}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{658FCCA5-E974-43C0-A9F3-6D93C6651718}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{658FCCA5-E974-43C0-A9F3-6D93C6651718}.Release|Any CPU.Build.0 = Release|Any CPU
		{185A79E0-90CD-47AD-A009-AE17A6B478C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{185A79E0-90CD-47AD-A009-AE17A6B478C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{185A79E0-90CD-47AD-A009-AE17A6B478C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{185A79E0-90CD-47AD-A009-AE17A6B478C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{88F75C4C-0B5E-4AFF-BB42-F9A16C2701DE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{88F75C4C-0B5E-4AFF-BB42-F9A16C2701DE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{88F75C4C-0B5E-4AFF-BB42-F9A16C2701DE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{88F75C4C-0B5E-4AFF-BB42-F9A16C2701DE}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCDFBC0D-3D4C-4183-876C-C69C2037C871}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCDFBC0D-3D4C-4183-876C-C69C2037C871}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCDFBC0D-3D4C-4183-876C-C69C2037C871}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCDFBC0D-3D4C-4183-876C-C69C2037C871}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E331734-83CB-4DDA-BA9C-E28C2E5468D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E331734-83CB-4DDA-BA9C-E28C2E5468D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E331734-83CB-4DDA-BA9C-E28C2E5468D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E331734-83CB-4DDA-BA9C-E28C2E5468D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B940B93-CEFD-485D-95BA-30A080094B84}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B940B93-CEFD-485D-95BA-30A080094B84}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B940B93-CEFD-485D-95BA-30A080094B84}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B940B93-CEFD-485D-95BA-30A080094B84}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EE23549-D27A-484C-812B-A162BCA97D59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EE23549-D27A-484C-812B-A162BCA97D59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EE23549-D27A-484C-812B-A162BCA97D59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EE23549-D27A-484C-812B-A162BCA97D59}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF38B80F-B143-4CAA-AC60-8FF109C0F94D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF38B80F-B143-4CAA-AC60-8FF109C0F94D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF38B80F-B143-4CAA-AC60-8FF109C0F94D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF38B80F-B143-4CAA-AC60-8FF109C0F94D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F54744B0-A3E2-40A6-8FA1-D91F6B23DFEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F54744B0-A3E2-40A6-8FA1-D91F6B23DFEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F54744B0-A3E2-40A6-8FA1-D91F6B23DFEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F54744B0-A3E2-40A6-8FA1-D91F6B23DFEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCC00F7F-319D-4BAA-9697-2F658ECF7375}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCC00F7F-319D-4BAA-9697-2F658ECF7375}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCC00F7F-319D-4BAA-9697-2F658ECF7375}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCC00F7F-319D-4BAA-9697-2F658ECF7375}.Release|Any CPU.Build.0 = Release|Any CPU
		{1DCA9E0E-CDF7-414D-A2C4-E930B670E4AC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DCA9E0E-CDF7-414D-A2C4-E930B670E4AC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DCA9E0E-CDF7-414D-A2C4-E930B670E4AC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DCA9E0E-CDF7-414D-A2C4-E930B670E4AC}.Release|Any CPU.Build.0 = Release|Any CPU
		{4FD7A232-A43E-42B9-9F66-EEC4114D8408}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4FD7A232-A43E-42B9-9F66-EEC4114D8408}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4FD7A232-A43E-42B9-9F66-EEC4114D8408}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4FD7A232-A43E-42B9-9F66-EEC4114D8408}.Release|Any CPU.Build.0 = Release|Any CPU
		{27B85E25-6624-4A7B-B983-529079FDA2AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27B85E25-6624-4A7B-B983-529079FDA2AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27B85E25-6624-4A7B-B983-529079FDA2AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27B85E25-6624-4A7B-B983-529079FDA2AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{82F7D900-663D-46E9-8455-67A3D93C0D7B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82F7D900-663D-46E9-8455-67A3D93C0D7B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82F7D900-663D-46E9-8455-67A3D93C0D7B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82F7D900-663D-46E9-8455-67A3D93C0D7B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C98AD7A5-2ABA-4C04-9B13-8663595A80EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C98AD7A5-2ABA-4C04-9B13-8663595A80EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C98AD7A5-2ABA-4C04-9B13-8663595A80EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C98AD7A5-2ABA-4C04-9B13-8663595A80EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A904060-95F8-4EAC-8C98-07108D75DE24}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A904060-95F8-4EAC-8C98-07108D75DE24}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A904060-95F8-4EAC-8C98-07108D75DE24}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A904060-95F8-4EAC-8C98-07108D75DE24}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D6287DB-784A-4C4B-9AD1-5314137EBE86}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D6287DB-784A-4C4B-9AD1-5314137EBE86}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D6287DB-784A-4C4B-9AD1-5314137EBE86}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D6287DB-784A-4C4B-9AD1-5314137EBE86}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E0F78C7-9818-47D0-81B5-586B562D99E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E0F78C7-9818-47D0-81B5-586B562D99E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E0F78C7-9818-47D0-81B5-586B562D99E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E0F78C7-9818-47D0-81B5-586B562D99E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{258EFEA2-8E82-4B02-AEA4-FF340FFA8D07}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{258EFEA2-8E82-4B02-AEA4-FF340FFA8D07}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{258EFEA2-8E82-4B02-AEA4-FF340FFA8D07}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{258EFEA2-8E82-4B02-AEA4-FF340FFA8D07}.Release|Any CPU.Build.0 = Release|Any CPU
		{857490CB-8705-4601-AC6E-A7B1A067C6A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{857490CB-8705-4601-AC6E-A7B1A067C6A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{857490CB-8705-4601-AC6E-A7B1A067C6A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{857490CB-8705-4601-AC6E-A7B1A067C6A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1CF4D16-71D4-4B56-B90F-B5F47A97605B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1CF4D16-71D4-4B56-B90F-B5F47A97605B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1CF4D16-71D4-4B56-B90F-B5F47A97605B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1CF4D16-71D4-4B56-B90F-B5F47A97605B}.Release|Any CPU.Build.0 = Release|Any CPU
		{302BD6D7-62BA-4153-B9CA-5F8C155A9BB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{302BD6D7-62BA-4153-B9CA-5F8C155A9BB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{302BD6D7-62BA-4153-B9CA-5F8C155A9BB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{302BD6D7-62BA-4153-B9CA-5F8C155A9BB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C5E59B1-1437-463C-8071-309F81D71F87}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C5E59B1-1437-463C-8071-309F81D71F87}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C5E59B1-1437-463C-8071-309F81D71F87}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C5E59B1-1437-463C-8071-309F81D71F87}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC790B75-743F-42AC-ABBE-2E4A6358082E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC790B75-743F-42AC-ABBE-2E4A6358082E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC790B75-743F-42AC-ABBE-2E4A6358082E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC790B75-743F-42AC-ABBE-2E4A6358082E}.Release|Any CPU.Build.0 = Release|Any CPU
		{87B5D9E8-07C4-477D-B608-1079C5CF5603}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{87B5D9E8-07C4-477D-B608-1079C5CF5603}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{87B5D9E8-07C4-477D-B608-1079C5CF5603}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{87B5D9E8-07C4-477D-B608-1079C5CF5603}.Release|Any CPU.Build.0 = Release|Any CPU
		{45C91397-3196-4F54-9959-55F8362C8399}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45C91397-3196-4F54-9959-55F8362C8399}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45C91397-3196-4F54-9959-55F8362C8399}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45C91397-3196-4F54-9959-55F8362C8399}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E36D4D0-171C-40C3-A1FA-FC2027CE5CDA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E36D4D0-171C-40C3-A1FA-FC2027CE5CDA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E36D4D0-171C-40C3-A1FA-FC2027CE5CDA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E36D4D0-171C-40C3-A1FA-FC2027CE5CDA}.Release|Any CPU.Build.0 = Release|Any CPU
		{A164B0BF-5E70-4D07-9773-0E326BDD86CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A164B0BF-5E70-4D07-9773-0E326BDD86CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A164B0BF-5E70-4D07-9773-0E326BDD86CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A164B0BF-5E70-4D07-9773-0E326BDD86CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{48A3F19A-4529-4D29-A3D6-9B8D947C467A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48A3F19A-4529-4D29-A3D6-9B8D947C467A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48A3F19A-4529-4D29-A3D6-9B8D947C467A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48A3F19A-4529-4D29-A3D6-9B8D947C467A}.Release|Any CPU.Build.0 = Release|Any CPU
		{98CAB7C0-A9A7-4620-A78E-07FCCC89AA48}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98CAB7C0-A9A7-4620-A78E-07FCCC89AA48}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98CAB7C0-A9A7-4620-A78E-07FCCC89AA48}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98CAB7C0-A9A7-4620-A78E-07FCCC89AA48}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8DF68DF-A0D0-48B3-800F-FA3C291FCE2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8DF68DF-A0D0-48B3-800F-FA3C291FCE2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8DF68DF-A0D0-48B3-800F-FA3C291FCE2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8DF68DF-A0D0-48B3-800F-FA3C291FCE2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{4C4CFCC8-811D-4A8B-B943-4A49ABD552B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4C4CFCC8-811D-4A8B-B943-4A49ABD552B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4C4CFCC8-811D-4A8B-B943-4A49ABD552B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4C4CFCC8-811D-4A8B-B943-4A49ABD552B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{017BDDF9-C8B5-4D58-9E90-24213194507F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{017BDDF9-C8B5-4D58-9E90-24213194507F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{017BDDF9-C8B5-4D58-9E90-24213194507F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{017BDDF9-C8B5-4D58-9E90-24213194507F}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4E2CB04-1B5F-4C22-8D05-3E87B31478D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4E2CB04-1B5F-4C22-8D05-3E87B31478D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4E2CB04-1B5F-4C22-8D05-3E87B31478D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4E2CB04-1B5F-4C22-8D05-3E87B31478D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{661E5138-B2BB-4B85-96AE-4B0EA7268AAF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{661E5138-B2BB-4B85-96AE-4B0EA7268AAF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{661E5138-B2BB-4B85-96AE-4B0EA7268AAF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{661E5138-B2BB-4B85-96AE-4B0EA7268AAF}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD77F3AD-E1E5-49B2-A5DF-9A52570BFEF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD77F3AD-E1E5-49B2-A5DF-9A52570BFEF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD77F3AD-E1E5-49B2-A5DF-9A52570BFEF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD77F3AD-E1E5-49B2-A5DF-9A52570BFEF7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{059A0B6A-90A5-4662-8C8A-59157BBD095A} = {92416A4C-D5CE-45FC-878A-6FE7CDFB92E9}
		{92416A4C-D5CE-45FC-878A-6FE7CDFB92E9} = {97F8809A-FB4C-401F-BFE8-E284BC523F4C}
		{A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606} = {97F8809A-FB4C-401F-BFE8-E284BC523F4C}
		{ED277EE5-1877-4D9B-836A-6786B3C7BD8A} = {97F8809A-FB4C-401F-BFE8-E284BC523F4C}
		{C62F3FD2-A921-4AE6-B7EB-D8E0234897BD} = {A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606}
		{E0DA8212-4578-4C2C-BD41-B7032D5485B8} = {A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606}
		{B15E012A-456E-4C29-A986-7399D9874B0A} = {A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606}
		{658FCCA5-E974-43C0-A9F3-6D93C6651718} = {A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606}
		{0A66BD17-AE1A-4767-8B1A-68559CCBE4DA} = {ED277EE5-1877-4D9B-836A-6786B3C7BD8A}
		{185A79E0-90CD-47AD-A009-AE17A6B478C3} = {0A66BD17-AE1A-4767-8B1A-68559CCBE4DA}
		{88F75C4C-0B5E-4AFF-BB42-F9A16C2701DE} = {0A66BD17-AE1A-4767-8B1A-68559CCBE4DA}
		{DCDFBC0D-3D4C-4183-876C-C69C2037C871} = {0A66BD17-AE1A-4767-8B1A-68559CCBE4DA}
		{9E331734-83CB-4DDA-BA9C-E28C2E5468D8} = {0A66BD17-AE1A-4767-8B1A-68559CCBE4DA}
		{6B940B93-CEFD-485D-95BA-30A080094B84} = {0A66BD17-AE1A-4767-8B1A-68559CCBE4DA}
		{36EC7AE0-41FA-4742-A2B4-B295CF32FFB5} = {ED277EE5-1877-4D9B-836A-6786B3C7BD8A}
		{8EE23549-D27A-484C-812B-A162BCA97D59} = {36EC7AE0-41FA-4742-A2B4-B295CF32FFB5}
		{DF38B80F-B143-4CAA-AC60-8FF109C0F94D} = {36EC7AE0-41FA-4742-A2B4-B295CF32FFB5}
		{F54744B0-A3E2-40A6-8FA1-D91F6B23DFEA} = {36EC7AE0-41FA-4742-A2B4-B295CF32FFB5}
		{FCC00F7F-319D-4BAA-9697-2F658ECF7375} = {36EC7AE0-41FA-4742-A2B4-B295CF32FFB5}
		{DA342EB4-BCB5-40E3-971E-18E07FF7CAB3} = {ED277EE5-1877-4D9B-836A-6786B3C7BD8A}
		{1DCA9E0E-CDF7-414D-A2C4-E930B670E4AC} = {DA342EB4-BCB5-40E3-971E-18E07FF7CAB3}
		{4FD7A232-A43E-42B9-9F66-EEC4114D8408} = {DA342EB4-BCB5-40E3-971E-18E07FF7CAB3}
		{27B85E25-6624-4A7B-B983-529079FDA2AA} = {DA342EB4-BCB5-40E3-971E-18E07FF7CAB3}
		{82F7D900-663D-46E9-8455-67A3D93C0D7B} = {DA342EB4-BCB5-40E3-971E-18E07FF7CAB3}
		{C98AD7A5-2ABA-4C04-9B13-8663595A80EC} = {A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606}
		{6A904060-95F8-4EAC-8C98-07108D75DE24} = {A485D1FF-5C8C-4A80-9F13-4C7ED8BC2606}
		{90C71CAC-DF72-4B63-9EC3-F1B0D5D4CB44} = {ED277EE5-1877-4D9B-836A-6786B3C7BD8A}
		{4D6287DB-784A-4C4B-9AD1-5314137EBE86} = {90C71CAC-DF72-4B63-9EC3-F1B0D5D4CB44}
		{1E0F78C7-9818-47D0-81B5-586B562D99E7} = {90C71CAC-DF72-4B63-9EC3-F1B0D5D4CB44}
		{258EFEA2-8E82-4B02-AEA4-FF340FFA8D07} = {90C71CAC-DF72-4B63-9EC3-F1B0D5D4CB44}
		{857490CB-8705-4601-AC6E-A7B1A067C6A9} = {90C71CAC-DF72-4B63-9EC3-F1B0D5D4CB44}
		{12B5333C-1332-4A07-8774-5F1D408368FE} = {ED277EE5-1877-4D9B-836A-6786B3C7BD8A}
		{FFCCBD78-98D7-4C9D-8223-298D62D9D244} = {ED277EE5-1877-4D9B-836A-6786B3C7BD8A}
		{D1CF4D16-71D4-4B56-B90F-B5F47A97605B} = {FFCCBD78-98D7-4C9D-8223-298D62D9D244}
		{302BD6D7-62BA-4153-B9CA-5F8C155A9BB5} = {FFCCBD78-98D7-4C9D-8223-298D62D9D244}
		{9C5E59B1-1437-463C-8071-309F81D71F87} = {FFCCBD78-98D7-4C9D-8223-298D62D9D244}
		{FC790B75-743F-42AC-ABBE-2E4A6358082E} = {FFCCBD78-98D7-4C9D-8223-298D62D9D244}
		{87B5D9E8-07C4-477D-B608-1079C5CF5603} = {12B5333C-1332-4A07-8774-5F1D408368FE}
		{45C91397-3196-4F54-9959-55F8362C8399} = {12B5333C-1332-4A07-8774-5F1D408368FE}
		{6E36D4D0-171C-40C3-A1FA-FC2027CE5CDA} = {12B5333C-1332-4A07-8774-5F1D408368FE}
		{A164B0BF-5E70-4D07-9773-0E326BDD86CC} = {12B5333C-1332-4A07-8774-5F1D408368FE}
		{48A3F19A-4529-4D29-A3D6-9B8D947C467A} = {0A66BD17-AE1A-4767-8B1A-68559CCBE4DA}
		{98CAB7C0-A9A7-4620-A78E-07FCCC89AA48} = {90C71CAC-DF72-4B63-9EC3-F1B0D5D4CB44}
		{4C4CFCC8-811D-4A8B-B943-4A49ABD552B7} = {36EC7AE0-41FA-4742-A2B4-B295CF32FFB5}
		{68F66759-E783-454F-ACBF-BBAF2DBCFEF8} = {ED277EE5-1877-4D9B-836A-6786B3C7BD8A}
		{017BDDF9-C8B5-4D58-9E90-24213194507F} = {68F66759-E783-454F-ACBF-BBAF2DBCFEF8}
		{B4E2CB04-1B5F-4C22-8D05-3E87B31478D1} = {68F66759-E783-454F-ACBF-BBAF2DBCFEF8}
		{661E5138-B2BB-4B85-96AE-4B0EA7268AAF} = {68F66759-E783-454F-ACBF-BBAF2DBCFEF8}
		{BD77F3AD-E1E5-49B2-A5DF-9A52570BFEF7} = {68F66759-E783-454F-ACBF-BBAF2DBCFEF8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {ABE72F96-0987-46DB-AFBB-F59D7A0A7CAE}
	EndGlobalSection
EndGlobal
