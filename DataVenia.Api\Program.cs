using System.Text.Json.Serialization;
using DataVenia.Api.Extensions;
using DataVenia.Api.Middleware;
using DataVenia.Common.Application;
using DataVenia.Common.Infrastructure;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.AssociationHub.Infrastructure;
using DataVenia.Modules.Calendar.Infrastructure;
using DataVenia.Modules.Harvey.Infrastructure;
using DataVenia.Modules.Lawsuits.Infrastructure;
using DataVenia.Modules.LawsuitSync.Infrastructure;
using DataVenia.Modules.LawsuitSync.Worker.IoC;
using DataVenia.Modules.Notification.Worker.IoC;
using DataVenia.Modules.Users.Infrastructure;
using HealthChecks.UI.Client;
using MassTransit;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Events;
using Serilog.Filters;
using Serilog.Formatting.Compact;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

var  MyAllowSpecificOrigins = "_allowFrontendAndLandingPage";
builder.Services.AddCors(options =>
{
    options.AddPolicy(name: MyAllowSpecificOrigins,
        policy  =>
        {
            policy.WithOrigins("https://datavenia.io",
                    "https://app.datavenia.io")
                .AllowAnyHeader()
                .AllowAnyMethod()
                .AllowCredentials();
        });
});

builder.Host
    .UseSerilog(
        new LoggerConfiguration()
            .Filter.ByExcluding(Matching.FromSource("Microsoft"))
            .Enrich.FromLogContext()
            .MinimumLevel.Is(LogEventLevel.Information)
            .WriteTo.Console(new RenderedCompactJsonFormatter(), LogEventLevel.Information)
            .CreateLogger());

builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
builder.Services.AddProblemDetails();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "DataVenia",
        Version = "v1"
    });

    c.CustomSchemaIds(type => type.FullName);

    c.UseInlineDefinitionsForEnums();

    c.SchemaFilter<EnumSchemaFilter>();
});

builder.Services.AddApplication([DataVenia.Modules.Users.Application.AssemblyReference.Assembly]);
builder.Services.AddApplication([DataVenia.Modules.Lawsuits.Application.AssemblyReference.Assembly]);
builder.Services.AddApplication([DataVenia.Modules.Calendar.Application.AssemblyReference.Assembly]);
builder.Services.AddApplication([DataVenia.Modules.LawsuitSync.Application.AssemblyReference.Assembly]);
builder.Services.AddApplication([DataVenia.Modules.Harvey.Application.AssemblyReference.Assembly]);
builder.Services.AddApplication([DataVenia.Modules.Notification.Application.AssemblyReference.Assembly]);
builder.Services.AddApplication([DataVenia.Modules.AssociationHub.Application.AssemblyReference.Assembly]);

builder.Services.AddMassTransit(configure =>
{
    configure.RegisterNotificationModuleConsumer();
    configure.RegisterUsersModuleConsumer();
    configure.RegisterLawsuitSyncModuleConsumer();
    configure.RegisterLawsuitsModuleConsumer();
    configure.RegisterAssociationHubModuleConsumer();

    configure.UsingInMemory((context, cfg) =>
    {
        cfg.ConfigureEndpoints(context);
    });
});

string databaseConnectionString = builder.Configuration.GetConnectionString("Database")!;

builder.Services.AddInfrastructure(builder.Configuration);

string environment = builder.Environment.EnvironmentName;

builder.Configuration.AddModuleConfiguration(["user", "lawsuit", "calendar", "lawsuitSync", "harvey", "notification", "associationHub"], environment);

builder.Services.AddHealthChecks().AddNpgSql(databaseConnectionString);

builder.Services.AddUsersModule(builder.Configuration);
builder.Services.AddLawsuitsModule(builder.Configuration);
builder.Services.AddCalendarModule(builder.Configuration);
builder.Services.AddLawsuitSyncModule(builder.Configuration);
builder.Services.AddHarveyModule(builder.Configuration);
builder.Services.AddNotificationModule(builder.Configuration);
builder.Services.AddAssociationHubModule(builder.Configuration);

builder.Services.AddHostedService<DataVenia.Modules.Users.Outbox.OutboxDispatcher>();
builder.Services.AddHostedService<DataVenia.Modules.Lawsuits.Worker.OutboxDispatcher>();

builder.WebHost.ConfigureKestrel(options =>
{
    options.ListenAnyIP(5163);
});

builder.Services.Configure<JsonOptions>(options =>
{
    options.SerializerOptions.Converters.Add(new JsonStringEnumConverter());
});

WebApplication app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.ApplyMigrations();
    app.MapPost("/admin/seed-database", (IServiceProvider serviceProvider) =>
    {
        try
        {
            using IServiceScope scope = serviceProvider.CreateScope();
            scope.SeedDatabase();

            return Results.Ok("Database seeded successfully!");
        }
        catch (Exception ex)
        {
            return Results.BadRequest($"An error occurred: {ex.Message}");
        }
    });
}

app.MapEndpoints();

app.MapHealthChecks("/api/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.UseSerilogRequestLogging();

app.UseExceptionHandler();

app.UseCors(MyAllowSpecificOrigins);

app.UseAuthentication();

app.UseAuthorization();

app.Run();
