﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.Users;
using OabDomain = DataVenia.Modules.Users.Domain.Oab.Oab;

namespace DataVenia.Modules.Users.Infrastructure.Lawyers;

internal sealed class LawyerConfiguration : IEntityTypeConfiguration<Lawyer>
{
    public void Configure(EntityTypeBuilder<Lawyer> builder)
    {
        builder.HasOne(e => e.Oab)
            .WithOne(o => o.Lawyer)
            .HasForeignKey<OabDomain>(e => e.LawyerId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
