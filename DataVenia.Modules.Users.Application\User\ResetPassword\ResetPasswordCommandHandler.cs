﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.SignUpToken;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.User.ResetPassword;

public sealed class ResetPasswordCommandHandler(
    IIdentityProviderService identityProviderService,
    ISignUpTokenRepository signUpTokenRepository,    
    IUnitOfWork unitOfWork, 
    ILawyerRepository lawyerRepository,
    ILogger<ResetPasswordCommandHandler> logger) : ICommandHandler<ResetPasswordCommand>
{
    public async Task<Result> Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
    {
        try
        {
            Result<SignUpToken> token =
                await signUpTokenRepository.GetSingleByTokenAsync(request.Token, cancellationToken);

            Result validateToken = IsValidResetPasswordToken(token, request.Username);
            if (validateToken.IsFailure)
                return validateToken;

            
            
            
            Guid? identityId = await lawyerRepository.GetIdentityId(token.Value.LawyerId, cancellationToken);

            if (identityId is null)
                return Result.Failure(new Error("User.NotFound", "User not found", ErrorType.NotFound));
            
            Result result = await identityProviderService.ResetPasswordAsync((Guid)identityId, request.Password, cancellationToken);

            if (result.IsSuccess)
            {
                token.Value.SoftDelete();
                token.Value.DeletedBy = token.Value.LawyerId;
                
                Result update = signUpTokenRepository.Update(token.Value);
                
                if(update.IsFailure)
                    logger.LogWarning("Error updating user password {@Update}", update);
                
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error logging in user. {Username}", request.Username);
            return Result.Failure(Error.NotFound("Login.Not.Found", "User and password combination not found"));
        }
    }

    private Result IsValidResetPasswordToken(Result<SignUpToken> signUpTokenResult, string username)
    {
        if (signUpTokenResult.IsFailure)
        {
            return Result.Failure(signUpTokenResult.Error);
        }

        if (signUpTokenResult.Value.Lawyer?.Email == null || signUpTokenResult.Value.Lawyer?.Email != username)
        {
            logger.LogError("Lawyer email is not valid");
            return Result.Failure(new Error("Lawyer.NotFound", "Lawyer email is not valid", ErrorType.Problem));
        }

        if (signUpTokenResult.Value.ExpirationDate != null && signUpTokenResult.Value.ExpirationDate < DateTime.UtcNow)
        {
            logger.LogError("Reset Password Token is expired");
            return Result.Failure(new Error("Token.Expired", "Reset Password Token is expired", ErrorType.Problem));
        }

        if (signUpTokenResult.Value.DeletedAt != null)
        {
            logger.LogError("Reset Password Token was already used");
            return Result.Failure(new Error("Token.Expired", "Reset Password Token was used", ErrorType.Problem));
        }

        return Result.Success();
    }
}
