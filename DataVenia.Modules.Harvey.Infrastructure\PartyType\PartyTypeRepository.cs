﻿using DataVenia.Modules.Harvey.Domain.PartyType;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using PartyTypeDomain = DataVenia.Modules.Harvey.Domain.PartyType.PartyType;

namespace DataVenia.Modules.Harvey.Infrastructure.PartyType;
public class PartyTypeRepository(HarveyDbContext context) : IPartyTypeRepository
{
    public async Task<IReadOnlyCollection<PartyTypeDomain>> GetAllAsync(string? displayName, CancellationToken cancellationToken = default)
    {
        IQueryable<PartyTypeDomain> query = context.PartyTypes;

        if (!string.IsNullOrWhiteSpace(displayName))
            query = query.Where(li => li.DisplayName.Contains(displayName));

        return await query.ToListAsync(cancellationToken);
    }
}
