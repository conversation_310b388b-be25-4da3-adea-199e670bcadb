﻿using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitStepsByCnj;
using DataVenia.Modules.Lawsuits.Domain.LawsuitSteps;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits;

public interface ILawsuitStepRepository
{
    Task<FluentResults.Result<IReadOnlyCollection<LawsuitStepsResponse>>> GetLawsuitStepsByCnjAsync(string cnj, string? instance = null, DateTime? stoppedAt = null, CancellationToken cancellationToken = default);
    void InsertRange(IEnumerable<LawsuitStep> lawsuitSteps);
}
