﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.LawsuitSync.Worker.BackgroundServices;

internal sealed class SyncLawsuitBgService : BackgroundService
{
    private readonly ILogger<SyncLawsuitBgService> _logger;

    public SyncLawsuitBgService(ILogger<SyncLawsuitBgService> logger)
    {
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            DateTime now = DateTime.UtcNow;
            DateTime nextRunTime = now.Date.AddDays(1).AddHours(15);
            TimeSpan delay = nextRunTime - now;

            if (delay.TotalMilliseconds > 0)
                await Task.Delay(delay, stoppingToken);

            await SyncLawsuits("Synchronizing lawsuits");
        }
    }

    private Task SyncLawsuits(string value)
    {
        _logger.LogInformation("useless log. should be deleted in the future. {Value}", value);
        return Task.CompletedTask;
    }
}
