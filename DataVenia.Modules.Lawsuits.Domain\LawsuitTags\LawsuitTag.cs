﻿//using DataVenia.Common.Domain;
//using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
//using TagDomain = DataVenia.Modules.Lawsuits.Domain.Tag.Tag;

//namespace DataVenia.Modules.Lawsuits.Domain.LawsuitTag;
//public sealed class LawsuitTag : Entity
//{
//    public Guid LawsuitId { get; private set; }
//    public Lawsuit Lawsuit { get; private set; }

//    public Guid TagId { get; private set; }
//    public TagDomain Tag{ get; private set; }

//    private LawsuitTag() { }

//    public static LawsuitTag Create(Guid lawsuitId, Guid tagId)
//    {
//        return new LawsuitTag
//        {
//            LawsuitId = lawsuitId,
//            TagId = tagId
//        };
//    }
//}
