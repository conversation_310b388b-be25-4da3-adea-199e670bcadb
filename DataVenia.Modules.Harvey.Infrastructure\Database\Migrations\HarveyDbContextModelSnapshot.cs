﻿// <auto-generated />
using System;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    [DbContext(typeof(HarveyDbContext))]
    partial class HarveyDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("harvey")
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.Action.LawsuitType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_type");

                    b.HasIndex("DisplayName")
                        .IsUnique()
                        .HasDatabaseName("ix_lawsuit_type_display_name");

                    b.ToTable("lawsuit_type", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.CourtDivision.CourtDivision", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_court_division");

                    b.ToTable("court_division", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.Forum.Forum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_forum");

                    b.HasIndex("DisplayName")
                        .IsUnique()
                        .HasDatabaseName("ix_forum_display_name");

                    b.HasIndex("Id")
                        .IsUnique()
                        .HasDatabaseName("ix_forum_id");

                    b.ToTable("forum", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LawsuitClass.LawsuitClass", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Acronym")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("acronym");

                    b.Property<string>("ActivePole")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("active_pole");

                    b.Property<string>("Article")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("article");

                    b.Property<bool>("Cjf")
                        .HasColumnType("boolean")
                        .HasColumnName("cjf");

                    b.Property<bool>("Cnj")
                        .HasColumnType("boolean")
                        .HasColumnName("cnj");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Glossary")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("glossary");

                    b.Property<DateTime?>("IncludedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("included_at");

                    b.Property<string>("IncludedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("included_by");

                    b.Property<bool>("IsCriminal")
                        .HasMaxLength(1)
                        .HasColumnType("boolean")
                        .HasColumnName("is_criminal");

                    b.Property<bool>("IsFirstInstance")
                        .HasColumnType("boolean")
                        .HasColumnName("is_first_instance");

                    b.Property<bool>("IsOwnNumbering")
                        .HasColumnType("boolean")
                        .HasColumnName("is_own_numbering");

                    b.Property<bool>("IsSecondInstance")
                        .HasColumnType("boolean")
                        .HasColumnName("is_second_instance");

                    b.Property<bool>("JustElei1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_elei_1_grau");

                    b.Property<bool>("JustElei2grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_elei_2_grau");

                    b.Property<bool>("JustEleiTse")
                        .HasColumnType("boolean")
                        .HasColumnName("just_elei_tse");

                    b.Property<bool>("JustEs1grauMil")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_1_grau_mil");

                    b.Property<bool>("JustEs2grauMil")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_2_grau_mil");

                    b.Property<bool>("JustEsJuizadoEs")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_juizado_es");

                    b.Property<bool>("JustEsJuizadoEsFp")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_juizado_es_fp");

                    b.Property<bool>("JustEsTurmas")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_turmas");

                    b.Property<bool>("JustFed1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_1_grau");

                    b.Property<bool>("JustFed2grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_2_grau");

                    b.Property<bool>("JustFedJuizadoEs")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_juizado_es");

                    b.Property<bool>("JustFedNacional")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_nacional");

                    b.Property<bool>("JustFedRegional")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_regional");

                    b.Property<bool>("JustFedTurmas")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_turmas");

                    b.Property<bool>("JustMilEst1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_est_1_grau");

                    b.Property<bool>("JustMilEstTjm")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_est_tjm");

                    b.Property<bool>("JustMilUniao1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_uniao_1_grau");

                    b.Property<bool>("JustMilUniaoStm")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_uniao_stm");

                    b.Property<bool>("JustTrab1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_1_grau");

                    b.Property<bool>("JustTrab2grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_2_grau");

                    b.Property<bool>("JustTrabCsjt")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_csjt");

                    b.Property<bool>("JustTrabTst")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_tst");

                    b.Property<bool>("JustTuEsUn")
                        .HasColumnType("boolean")
                        .HasColumnName("just_tu_es_un");

                    b.Property<string>("LegalProvision")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("legal_provision");

                    b.Property<string>("OldAcronym")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("old_acronym");

                    b.Property<string>("PassivePole")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("passive_pole");

                    b.Property<string>("ProcedureId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("procedure_id");

                    b.Property<string>("ProcedureOrigin")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("procedure_origin");

                    b.Property<bool>("Stf")
                        .HasColumnType("boolean")
                        .HasColumnName("stf");

                    b.Property<bool>("Stj")
                        .HasColumnType("boolean")
                        .HasColumnName("stj");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UserIp")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("user_ip");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_class");

                    b.HasIndex("Acronym")
                        .HasDatabaseName("ix_lawsuit_class_acronym");

                    b.ToTable("lawsuit_class", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LawsuitStatus.LawsuitStatus", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_status");

                    b.HasIndex("DisplayName")
                        .IsUnique()
                        .HasDatabaseName("ix_lawsuit_status_display_name");

                    b.HasIndex("Id")
                        .IsUnique()
                        .HasDatabaseName("ix_lawsuit_status_id");

                    b.ToTable("lawsuit_status", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "Pending",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Pendente"
                        },
                        new
                        {
                            Id = "InProgress",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Em progresso"
                        },
                        new
                        {
                            Id = "Suspended",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Suspendido"
                        },
                        new
                        {
                            Id = "AwaitingAppeal",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Aguardando Apelação"
                        },
                        new
                        {
                            Id = "Completed",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Completo"
                        },
                        new
                        {
                            Id = "Closed",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Fechado"
                        },
                        new
                        {
                            Id = "Cancelled",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Cancelado"
                        },
                        new
                        {
                            Id = "AwaitingClient",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Aguardando cliente"
                        },
                        new
                        {
                            Id = "AwaitingDecision",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Aguardando decisão"
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LawsuitTopic.LawsuitTopic", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Article")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("article");

                    b.Property<bool>("Cjf")
                        .HasColumnType("boolean")
                        .HasColumnName("cjf");

                    b.Property<bool>("Cnj")
                        .HasColumnType("boolean")
                        .HasColumnName("cnj");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Glossary")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("glossary");

                    b.Property<bool>("IsFirstInstance")
                        .HasColumnType("boolean")
                        .HasColumnName("is_first_instance");

                    b.Property<bool>("IsSecondInstance")
                        .HasColumnType("boolean")
                        .HasColumnName("is_second_instance");

                    b.Property<bool>("IsSecret")
                        .HasColumnType("boolean")
                        .HasColumnName("is_secret");

                    b.Property<bool>("JustElei1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_elei_1_grau");

                    b.Property<bool>("JustElei2grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_elei_2_grau");

                    b.Property<bool>("JustEleiTse")
                        .HasColumnType("boolean")
                        .HasColumnName("just_elei_tse");

                    b.Property<bool>("JustEs1grauMil")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_1_grau_mil");

                    b.Property<bool>("JustEs2grauMil")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_2_grau_mil");

                    b.Property<bool>("JustEsJuizadoEs")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_juizado_es");

                    b.Property<bool>("JustEsJuizadoEsFp")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_juizado_es_fp");

                    b.Property<bool>("JustEsTurmas")
                        .HasColumnType("boolean")
                        .HasColumnName("just_es_turmas");

                    b.Property<bool>("JustFed1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_1_grau");

                    b.Property<bool>("JustFed2grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_2_grau");

                    b.Property<bool>("JustFedJuizadoEs")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_juizado_es");

                    b.Property<bool>("JustFedNacional")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_nacional");

                    b.Property<bool>("JustFedRegional")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_regional");

                    b.Property<bool>("JustFedTurmas")
                        .HasColumnType("boolean")
                        .HasColumnName("just_fed_turmas");

                    b.Property<bool>("JustMilEst1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_est_1_grau");

                    b.Property<bool>("JustMilEstTjm")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_est_tjm");

                    b.Property<bool>("JustMilUniao1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_uniao_1_grau");

                    b.Property<bool>("JustMilUniaoStm")
                        .HasColumnType("boolean")
                        .HasColumnName("just_mil_uniao_stm");

                    b.Property<bool>("JustTrab1grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_1_grau");

                    b.Property<bool>("JustTrab2grau")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_2_grau");

                    b.Property<bool>("JustTrabCsjt")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_csjt");

                    b.Property<bool>("JustTrabTst")
                        .HasColumnType("boolean")
                        .HasColumnName("just_trab_tst");

                    b.Property<bool>("JustTuEsUn")
                        .HasColumnType("boolean")
                        .HasColumnName("just_tu_es_un");

                    b.Property<string>("LegalProvision")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("legal_provision");

                    b.Property<bool>("PreviousCrime")
                        .HasColumnType("boolean")
                        .HasColumnName("previous_crime");

                    b.Property<bool>("SecondaryTopic")
                        .HasColumnType("boolean")
                        .HasColumnName("secondary_topic");

                    b.Property<bool>("Stf")
                        .HasColumnType("boolean")
                        .HasColumnName("stf");

                    b.Property<bool>("Stj")
                        .HasColumnType("boolean")
                        .HasColumnName("stj");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedById")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("updated_by_id");

                    b.Property<string>("UserIp")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("user_ip");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_topic");

                    b.ToTable("lawsuit_topic", "harvey");
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LegalCategory.LegalCategory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_legal_category");

                    b.HasIndex("DisplayName")
                        .IsUnique()
                        .HasDatabaseName("ix_legal_category_display_name");

                    b.ToTable("legal_category", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "CollectionAction",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Ação coletiva",
                            Order = 1
                        },
                        new
                        {
                            Id = "DivorceAction",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Ação de divórcio",
                            Order = 2
                        },
                        new
                        {
                            Id = "HabeasCorpus",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Habeas Corpus",
                            Order = 6
                        },
                        new
                        {
                            Id = "LaborAction",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Ação trabalhista",
                            Order = 3
                        },
                        new
                        {
                            Id = "TaxEnforcementAction",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Supremo Tribunal Federal",
                            Order = 4
                        },
                        new
                        {
                            Id = "WritOfMandamus",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Mandado de segurança",
                            Order = 5
                        },
                        new
                        {
                            Id = "Other",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Other",
                            Order = 7
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.LegalInstance.LegalInstance", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_legal_instance");

                    b.HasIndex("DisplayName")
                        .IsUnique()
                        .HasDatabaseName("ix_legal_instance_display_name");

                    b.ToTable("legal_instance", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "FirstInstance",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Primeira Instância",
                            Order = 1
                        },
                        new
                        {
                            Id = "SecondInstance",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Segunda Instância",
                            Order = 2
                        },
                        new
                        {
                            Id = "STF",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Supremo Tribunal Federal",
                            Order = 4
                        },
                        new
                        {
                            Id = "STJ",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Superior Tribunal de Justiça",
                            Order = 3
                        },
                        new
                        {
                            Id = "TST",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Tribunal Superior de Trabalho",
                            Order = 5
                        },
                        new
                        {
                            Id = "TSE",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Tribunal Superior Eleitoral",
                            Order = 6
                        },
                        new
                        {
                            Id = "STM",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Superior Tribunal Militar",
                            Order = 7
                        },
                        new
                        {
                            Id = "Other",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Outras",
                            Order = 8
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Harvey.Domain.PartyType.PartyType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("display_name");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_party_type");

                    b.ToTable("party_type", "harvey");

                    b.HasData(
                        new
                        {
                            Id = "Lawyer",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Advogado",
                            Order = 1
                        },
                        new
                        {
                            Id = "Defendant",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Réu",
                            Order = 3
                        },
                        new
                        {
                            Id = "Plaintiff",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Autor",
                            Order = 2
                        },
                        new
                        {
                            Id = "Witness",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Testemunha",
                            Order = 4
                        },
                        new
                        {
                            Id = "Other",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayName = "Outro",
                            Order = 5
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
