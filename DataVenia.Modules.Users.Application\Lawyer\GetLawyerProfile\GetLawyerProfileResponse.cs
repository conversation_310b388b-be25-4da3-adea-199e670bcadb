﻿using DataVenia.Modules.Users.Domain;
using DataVenia.Modules.Users.Domain.SharedModels;
using DataVenia.Modules.Users.Domain.User;
using DataVenia.Modules.Users.Infrastructure.IntermediateClasses;

namespace DataVenia.Modules.Users.Application.Lawyers.GetLawyer;
public sealed record GetLawyerProfileResponse(Guid Id, 
    string Email, 
    string FirstName, 
    string LastName,
    string? Oab, 
    string Cpf, 
    string Rg, 
    string Cnh, 
    string Passport,
    string Ctps, 
    string Pis, 
    string VoterId, 
    IReadOnlyCollection<Contact>? Contacts,
    IReadOnlyCollection<OfficeResponse>? Offices,
    IReadOnlyCollection<OfficeUserDto>? AdminOfficeUsers,
    TermsAndConditions termsAndConditions,
    Preferences preferences);

public sealed record OfficeResponse(
    Guid Id,
    string Name,
    string Cnpj,
    string Website);
