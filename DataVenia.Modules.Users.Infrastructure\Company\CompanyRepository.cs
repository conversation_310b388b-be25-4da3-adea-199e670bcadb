﻿using System.Linq.Expressions;
using DataVenia.Modules.Users.Domain.Company;
using DataVenia.Modules.Users.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Users.Infrastructure.Company;
public sealed class CompanyRepository(UsersDbContext context) : ICompanyRepository
{
    public void Insert(Domain.Company.Company company)
    {
        context.Companies.Add(company);
    }

    public async Task<IEnumerable<Domain.Company.Company>> GetAllByOfficeIdAsync(Guid officeId, CancellationToken cancellationToken = default)
    {
        return await context.Companies
            .Where(l => l.OfficeId == officeId)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyCollection<Domain.Company.Company>> GetManyAsync(Expression<Func<Domain.Company.Company, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Companies
            .Where(filter)
            .ToListAsync(cancellationToken);
    }

    public async Task<Domain.Company.Company?> GetSingleAsync(Expression<Func<Domain.Company.Company, bool>> filter, CancellationToken cancellationToken = default)
    {
        return await context.Companies
            .Where(filter)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
