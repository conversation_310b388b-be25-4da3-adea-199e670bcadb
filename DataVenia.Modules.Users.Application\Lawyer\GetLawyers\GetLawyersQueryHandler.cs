﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Lawyers;
using LawyerDomain = DataVenia.Modules.Users.Domain.Lawyers.Lawyer;

namespace DataVenia.Modules.Users.Application.Lawyer.GetLawyers;
public sealed class GetLawyersQueryHandler(
    ILawyerRepository lawyerRepository) : IQueryHandler<GetLawyersQuery, IReadOnlyCollection<GetLawyersResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetLawyersResponse>>> Handle(GetLawyersQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<LawyerDomain> lawyers = (await lawyerRepository.GetAllByOfficeIdAsync(request.officeId, cancellationToken)).ToList();

        var lawyersResponse = lawyers.Select(lawyer =>
        {
            Domain.IntermediateClasses.OfficeLawyer.OfficeUser? officeUser = lawyer.OfficeUsers
                .FirstOrDefault(ou => ou.OfficeId == request.officeId);

            return new GetLawyersResponse(
                Id: lawyer.Id,
                Email: lawyer.Email,
                FirstName: lawyer.FirstName,
                LastName: lawyer.LastName,
                OabNumber: lawyer.Oab?.Value ?? string.Empty,
                InviteStatus: officeUser?.InvitationStatus,
                SignUpToken: lawyer.SignUpToken.FirstOrDefault()?.Token
            );
        }).ToList();


        return lawyersResponse;
    }
}
