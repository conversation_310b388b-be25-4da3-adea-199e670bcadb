﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Lawyer.GetLawyers;
using DataVenia.Modules.Users.Domain.Lawyers;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Application.Lawyer.GetLawyerBySignUpToken;

public sealed class GetLawyerBySignUpTokenQueryHandler(ILawyerRepository lawyerRepository, ILogger<GetLawyerBySignUpTokenQueryHandler> logger) : IQueryHandler<GetLawyerBySignUpTokenQuery, GetLawyersResponse>
{
    public async Task<Result<GetLawyersResponse>> Handle(GetLawyerBySignUpTokenQuery request, CancellationToken cancellationToken)
    {
        try
        {
            Result<Domain.Lawyers.Lawyer> lawyer = await lawyerRepository.GetSingleBySignUpTokenAsync(request.SignUpToken, cancellationToken);
            if (lawyer.IsFailure)
                return Result.Failure<GetLawyersResponse>(new Error(lawyer.Error.Code, lawyer.Error.Description, lawyer.Error.Type));
            
            return new GetLawyersResponse(
                lawyer.Value.Id,
                lawyer.Value.Email,
                lawyer.Value.FirstName,
                lawyer.Value.LastName,
                lawyer.Value.Oab?.Value,
                lawyer.Value.OfficeUsers.FirstOrDefault()?.InvitationStatus,
                lawyer.Value.SignUpToken.FirstOrDefault()?.Token
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting lawyer by sign up token: {@SignUpToken}", request.SignUpToken);
            return Result.Failure<GetLawyersResponse>(Error.InternalServerError());
        }
    }
}
